#include "ServZeroDialog.h"
#include "ui_ServZeroDialog.h"

ServZeroDialog::ServZeroDialog( QWidget* parent, Communication* comm, MainWidgetData* mainData ) : QDialog( parent ), ui( new Ui::ServZeroDialog ), comm( comm ), mainData( mainData )
{
    ui->setupUi( this );

    // 默认状态 1号checkbox选中
    checkBoxGroup = new QButtonGroup( this );
    checkBoxGroup->addButton( ui->chx_step1, 0 );
    checkBoxGroup->addButton( ui->chx_step2, 1 );
    checkBoxGroup->addButton( ui->chx_step3, 2 );
    checkBoxGroup->setExclusive( true );
    ui->chx_step1->setChecked( true );

    connect( ui->btn_next, &QPushButton::clicked, this, &ServZeroDialog::toNextStep );
    connect( ui->btn_cancel, &QPushButton::clicked, this, [ & ]() { this->close(); } );
    connect( ui->btn_confirm_MachineZero, &QPushButton::clicked, this, &ServZeroDialog::ConfirmMachineZero );
}

ServZeroDialog::~ServZeroDialog()
{
    delete ui;
}

void ServZeroDialog::toNextStep()
{
    // 进入状态1，发送零位校正命令，不允许自行切换
    if ( step == 1 )
        return;

    step++;
    if ( step == 1 )
    {
        ui->chx_step2->setChecked( true );
        // 发送零位校正命令
        quint8 data[ 1 ] = { 0x04 };
        comm->pushDataTobuffer( 0x09, data, 1 );
    }
    //    else if ( step == 2 )
    //    {
    //        ui->chx_step3->setChecked( true );
    //        ui->btn_next->setText( "确认" );
    //    }
    else if ( step == 3 )
    {
        step = 0;
        this->close();
    }
}

void ServZeroDialog::setServZeroCalFinished()
{
    step = 2;
    ui->chx_step3->setChecked( true );
    ui->btn_next->setText( "确认" );
}

void ServZeroDialog::refreshParams( quint16 codeValue, float actAngle )
{
    ui->lbl_ActAngle->setText( QString::number( actAngle, 'f', 1 ) );
    ui->lbl_CodeValue->setText( QString::number( codeValue ) );
    this->zeroPos = codeValue;
}

void ServZeroDialog::closeEvent( QCloseEvent* event )
{
    QDialog::closeEvent( event );
    emit finished();
}

void ServZeroDialog::setStatusLabel( QString str )
{
    ui->label_zeroStatus->setText( str );
}

void ServZeroDialog::ConfirmMachineZero()
{
    if ( mainData == nullptr || mainData->readMachineFileConfig == nullptr )
    {
        QMessageBox::warning( nullptr, "错误", "配置数据不可用" );
        return;
    }

    // 保存机器零位
    ReadMachineFileConfig::MachineFlieConfigItem& item = ( *mainData->readMachineFileConfig->getBasicCfgList() )[ 3 ];
    item.value                                         = zeroPos;
    if ( mainData->readMachineFileConfig->saveMachineConfig( MAIN_CFG_DIR + "MachineFileConfig.json" ) == 1 )
    {
        ui->lbl_ZeroPostion->setText( QString::number( zeroPos ) );
        QMessageBox::information( nullptr, "操作提示", "零位保存成功" );
    }
    else
    {
        QMessageBox::warning( nullptr, "错误", "零位保存失败" );
    }
}
