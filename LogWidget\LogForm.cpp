#include "LogForm.h"
#include "ui_LogForm.h"

LogForm::LogForm( QWidget* parent, MainWidgetData* mainData, Communication* com ) : QWidget( parent ), ui( new Ui::LogForm ), mainData( mainData ), comm( com )
{
    ui->setupUi( this );

    connect( ui->LogFormHome_btn, SIGNAL( clicked() ), this, SLOT( onLogHomeBtnClicked() ) );

    menuBtnGroup = new QButtonGroup( this );
    menuBtnGroup->addButton( ui->Log_warn_btn, 1 );
    menuBtnGroup->addButton( ui->Log_network_btn, 2 );
    menuBtnGroup->addButton( ui->Log_help_btn, 3 );
    connect( menuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onMenuBtnGroupClicked( int ) ) );

    ui->stackedWidget->setCurrentIndex( 0 );
    initWarnLogPage();
    initNetworkPage();
}

LogForm::~LogForm()
{
    delete ui;
}

void LogForm::onLogHomeBtnClicked()
{
    if ( ui->stackedWidget->currentIndex() > 0 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Log_Tiltle_label->setText( "日志与帮助" );
    }
    else
    {
        this->close();
        emit LogFormToMainWinToShowSignal();
    }
}

void LogForm::onMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            ui->Log_Tiltle_label->setText( "报警日志" );
            ui->stackedWidget->setCurrentIndex( 1 );
            refreshWarnLog();
            break;
        case 2:
            ui->Log_Tiltle_label->setText( "网络设置" );
            ui->stackedWidget->setCurrentIndex( 2 );
            break;
        case 3:
            ui->Log_Tiltle_label->setText( "帮助" );
            ui->stackedWidget->setCurrentIndex( 3 );
            break;
    }
}
