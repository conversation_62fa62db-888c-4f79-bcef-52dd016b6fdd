#include "SensorForm.h"
#include "ui_SensorForm.h"

void SensorForm::WarnTestPageInit()
{
    // 批量生成控件
    GenerateSocketInputWidget();
    GenerateSeamHeadInputWidget();
    GenerateMotorZeroInputWidget();
}

void SensorForm::GenerateSocketInputWidget()
{
    quint8                sizeOfline    = 10;
    QMap< int, QString >* sockInputList = mainData->readCfg->getSockInputList();
    quint16               size          = sockInputList->size();
    quint16               lines         = ( size - 1 ) / sizeOfline + 1;

    QHBoxLayout* mainLayout = new QHBoxLayout( this->ui->tab_sockeWarn );

    // 一行只显示5个
    for ( quint8 line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
    {
        QVBoxLayout* columnLayoutx = new QVBoxLayout;

        for ( quint8 col = 0; col < sizeOfline; col++ )
        {
            if ( line * sizeOfline + col < size )
            {
                // 从Cfg.json中获取对应的value值
                QString labelText = sockInputList->value( line * sizeOfline + col + 1 );
                QLabel* label     = new QLabel( labelText );
                label->setAlignment( Qt::AlignCenter );
                label->setStyleSheet( "font-size:8pt;"
                                      "background-color: #ffffff;"
                                      "border: 1px solid #d0d0d0;"
                                      "border-radius: 4px;"
                                      "padding: 4px;"
                                      "margin: 2px;"
                                      "min-height: 30px;"
                                      "min-width: 80px;" );
                columnLayoutx->addWidget( label );

                // Lable 列表的索引从0开始
                sockeWarnLabelList.insert( line * sizeOfline + col, label );
            }
            else
            {
                QWidget* widget = new QWidget();
                columnLayoutx->addWidget( widget );
            }
        }
        mainLayout->addLayout( columnLayoutx );
    }
}

void SensorForm::GenerateSeamHeadInputWidget()
{
    quint8                sizeOfline        = 10;
    QMap< int, QString >* seamHeadInputList = mainData->readCfg->getSeamHeadInputList();
    quint16               size              = seamHeadInputList->size();
    quint16               lines             = ( size - 1 ) / sizeOfline + 1;

    QHBoxLayout* mainLayout = new QHBoxLayout( this->ui->tab_fengtouWarn );

    // 一行只显示5个
    for ( quint8 line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
    {
        QVBoxLayout* columnLayoutx = new QVBoxLayout;

        for ( quint8 col = 0; col < sizeOfline; col++ )
        {
            if ( line * sizeOfline + col < size )
            {
                // 从Cfg.json中获取对应的value值
                QString labelText = seamHeadInputList->value( line * sizeOfline + col + 1 );
                QLabel* label     = new QLabel( labelText );
                label->setAlignment( Qt::AlignCenter );
                label->setStyleSheet( "font-size:8pt;"
                                      "background-color: #ffffff;"
                                      "border: 1px solid #d0d0d0;"
                                      "border-radius: 4px;"
                                      "padding: 4px;"
                                      "margin: 2px;"
                                      "min-height: 30px;"
                                      "min-width: 80px;" );
                columnLayoutx->addWidget( label );

                // Lable 列表的索引从0开始
                fengtouWarnLabelList.insert( line * sizeOfline + col, label );
            }
            else
            {
                QWidget* widget = new QWidget();
                columnLayoutx->addWidget( widget );
            }
        }
        mainLayout->addLayout( columnLayoutx );
    }
}

void SensorForm::GenerateMotorZeroInputWidget()
{
    quint8                sizeOfline         = 10;
    QMap< int, QString >* motorZeroInputList = mainData->readCfg->getMotorZeroInputList();
    quint16               size               = motorZeroInputList->size();
    quint16               lines              = ( size - 1 ) / sizeOfline + 1;

    QHBoxLayout* mainLayout = new QHBoxLayout( this->ui->tab_zeroSensor );

    // 一行只显示5个
    for ( quint8 line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
    {
        QVBoxLayout* columnLayoutx = new QVBoxLayout;

        for ( quint8 col = 0; col < sizeOfline; col++ )
        {
            if ( line * sizeOfline + col < size )
            {
                // 从Cfg.json中获取对应的value值
                QString labelText = motorZeroInputList->value( line * sizeOfline + col + 1 );
                QLabel* label     = new QLabel( labelText );
                label->setAlignment( Qt::AlignCenter );
                label->setStyleSheet( "font-size:8pt;"
                                      "background-color: #ffffff;"
                                      "border: 1px solid #d0d0d0;"
                                      "border-radius: 4px;"
                                      "padding: 4px;"
                                      "margin: 2px;"
                                      "min-height: 30px;"
                                      "min-width: 80px;" );
                columnLayoutx->addWidget( label );

                // Lable 列表的索引从0开始
                zeroLabelList.insert( line * sizeOfline + col, label );
            }
            else
            {
                QWidget* widget = new QWidget();
                columnLayoutx->addWidget( widget );
            }
        }
        mainLayout->addLayout( columnLayoutx );
    }
}

void SensorForm::refreshSocketWarn( qint16 size, quint8* result )
{
    for ( int i = 0; i < sockeWarnLabelList.size(); i++ )
    {
        //真实数据长度不够就不刷新
        if ( i / 8 > size - 1 )
            break;
        quint8  byte  = result[ i / 8 ];
        QLabel* label = sockeWarnLabelList.value( i );
        if ( byte & ( 1 << ( i % 8 ) ) )
        {
            label->setStyleSheet( "font-size:8pt;"
                                  "color: white;"
                                  "background-color: #e74c3c;"
                                  "border: 1px solid #c0392b;"
                                  "border-radius: 4px;"
                                  "padding: 4px;"
                                  "margin: 2px;"
                                  "min-height: 30px;"
                                  "min-width: 80px;" );
        }
        else
        {
            label->setStyleSheet( "font-size:8pt;"
                                  "color: #333333;"
                                  "background-color: #ffffff;"
                                  "border: 1px solid #d0d0d0;"
                                  "border-radius: 4px;"
                                  "padding: 4px;"
                                  "margin: 2px;"
                                  "min-height: 30px;"
                                  "min-width: 80px;" );
        }
    }
}

void SensorForm::refreshFengtouWarn( qint16 size, quint8* result )
{
    for ( int i = 0; i < fengtouWarnLabelList.size(); i++ )
    {
        //真实数据长度不够就不刷新
        if ( i / 8 > size - 1 )
            break;
        quint8  byte  = result[ i / 8 ];
        QLabel* label = fengtouWarnLabelList.value( i );
        if ( byte & ( 1 << ( i % 8 ) ) )
        {
            label->setStyleSheet( "font-size:8pt;"
                                  "color: white;"
                                  "background-color: #e74c3c;"
                                  "border: 1px solid #c0392b;"
                                  "border-radius: 4px;"
                                  "padding: 4px;"
                                  "margin: 2px;"
                                  "min-height: 30px;"
                                  "min-width: 80px;" );
        }
        else
        {
            label->setStyleSheet( "font-size:8pt;"
                                  "color: #333333;"
                                  "background-color: #ffffff;"
                                  "border: 1px solid #d0d0d0;"
                                  "border-radius: 4px;"
                                  "padding: 4px;"
                                  "margin: 2px;"
                                  "min-height: 30px;"
                                  "min-width: 80px;" );
        }
    }
}

void SensorForm::refreshZeroSensor( qint16 size, quint8* result )
{
    for ( int i = 0; i < zeroLabelList.size(); i++ )
    {
        //真实数据长度不够就不刷新
        if ( i / 8 > size - 1 )
            break;
        quint8  byte  = result[ i / 8 ];
        QLabel* label = zeroLabelList.value( i );
        if ( byte & ( 1 << ( i % 8 ) ) )
        {
            label->setStyleSheet( "font-size:8pt;"
                                  "color: white;"
                                  "background-color: #e74c3c;"
                                  "border: 1px solid #c0392b;"
                                  "border-radius: 4px;"
                                  "padding: 4px;"
                                  "margin: 2px;"
                                  "min-height: 30px;"
                                  "min-width: 80px;" );
        }
        else
        {
            label->setStyleSheet( "font-size:8pt;"
                                  "color: #333333;"
                                  "background-color: #ffffff;"
                                  "border: 1px solid #d0d0d0;"
                                  "border-radius: 4px;"
                                  "padding: 4px;"
                                  "margin: 2px;"
                                  "min-height: 30px;"
                                  "min-width: 80px;" );
        }
    }
}
