#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>
#include <QLabel>

// 初始化节约数据框架
void CraftParamForm::initEconomizationFrame()
{
    // 清空之前的控件
    if ( !economizationIndexLabels.isEmpty() )
    {
        for ( auto label : economizationIndexLabels )
        {
            delete label;
        }
        economizationIndexLabels.clear();
    }

    if ( !economizationNameLabels.isEmpty() )
    {
        for ( auto label : economizationNameLabels )
        {
            delete label;
        }
        economizationNameLabels.clear();
    }

    if ( !economizationStepLabels.isEmpty() )
    {
        for ( auto label : economizationStepLabels )
        {
            delete label;
        }
        economizationStepLabels.clear();
    }

    if ( !economizationOldLabels.isEmpty() )
    {
        for ( auto label : economizationOldLabels )
        {
            delete label;
        }
        economizationOldLabels.clear();
    }

    if ( !economizationNewEdits.isEmpty() )
    {
        for ( auto edit : economizationNewEdits )
        {
            delete edit;
        }
        economizationNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout( ui->economizationFrame );

    // 创建标题行
    QLabel* indexHeader = new QLabel( "序号", ui->economizationFrame );
    QLabel* nameHeader  = new QLabel( "步骤名称", ui->economizationFrame );
    QLabel* stepHeader  = new QLabel( "Step", ui->economizationFrame );
    QLabel* oldHeader   = new QLabel( "Old", ui->economizationFrame );
    QLabel* newHeader   = new QLabel( "New", ui->economizationFrame );

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet( headerStyle );
    nameHeader->setStyleSheet( headerStyle );
    stepHeader->setStyleSheet( headerStyle );
    oldHeader->setStyleSheet( headerStyle );
    newHeader->setStyleSheet( headerStyle );
    indexHeader->setAlignment( Qt::AlignCenter );
    nameHeader->setAlignment( Qt::AlignCenter );
    stepHeader->setAlignment( Qt::AlignCenter );
    oldHeader->setAlignment( Qt::AlignCenter );
    newHeader->setAlignment( Qt::AlignCenter );

    // 添加标题到布局
    layout->addWidget( indexHeader, 0, 0 );
    layout->addWidget( nameHeader, 0, 1 );
    layout->addWidget( stepHeader, 0, 2 );
    layout->addWidget( oldHeader, 0, 3 );
    layout->addWidget( newHeader, 0, 4 );

    // 为每一行创建控件
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        // 序号标签
        QLabel* indexLabel = new QLabel( QString::number( i + 1 ), ui->economizationFrame );
        indexLabel->setAlignment( Qt::AlignCenter );
        economizationIndexLabels.append( indexLabel );
        layout->addWidget( indexLabel, i + 1, 0 );

        // 步骤名称标签
        QLabel* nameLabel = new QLabel( "", ui->economizationFrame );
        nameLabel->setAlignment( Qt::AlignCenter );
        economizationNameLabels.append( nameLabel );
        layout->addWidget( nameLabel, i + 1, 1 );

        // Step标签
        QLabel* stepLabel = new QLabel( "", ui->economizationFrame );
        stepLabel->setAlignment( Qt::AlignCenter );
        economizationStepLabels.append( stepLabel );
        layout->addWidget( stepLabel, i + 1, 2 );

        // Old标签
        QLabel* oldLabel = new QLabel( "", ui->economizationFrame );
        oldLabel->setAlignment( Qt::AlignCenter );
        economizationOldLabels.append( oldLabel );
        layout->addWidget( oldLabel, i + 1, 3 );

        // New编辑框
        MyLineEdit* newEdit = new MyLineEdit( ui->economizationFrame );
        newEdit->setAlignment( Qt::AlignCenter );
        newEdit->setReadOnly( true );      // 初始设为只读
        newEdit->setProperty( "row", i );  // 存储行索引
        connect( newEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentEconomizationPage * ITEMS_PER_PAGE + i;

            if ( dataIndex < craftParams.economizationParam.size() )
            {
                // 清空之前的内存
                if ( this->numberInputForm != nullptr )
                {
                    delete this->numberInputForm;
                    this->numberInputForm = nullptr;
                }

                // 创建数字输入表单
                this->numberInputForm = new NumberInputForm( nullptr, "请输入节约次数", 0, 255 );

                // 存储当前编辑的行和列
                tableEditRowIndex = dataIndex;
                tableIndex        = 8;  // 节约数据表格

                // 连接完成信号
                connect( this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished );

                // 显示表单
                this->numberInputForm->show();
            }
        } );
        economizationNewEdits.append( newEdit );
        layout->addWidget( newEdit, i + 1, 4 );
    }

    // 设置布局属性
    layout->setColumnStretch( 0, 1 );  // 序号
    layout->setColumnStretch( 1, 3 );  // 步骤名称
    layout->setColumnStretch( 2, 2 );  // Step
    layout->setColumnStretch( 3, 2 );  // Old
    layout->setColumnStretch( 4, 2 );  // New
    layout->setSpacing( 5 );
    layout->setContentsMargins( 5, 5, 5, 5 );

    // 应用布局
    ui->economizationFrame->setLayout( layout );
}

// 更新节约数据页面
void CraftParamForm::updateEconomizationPage()
{
    // 计算总页数
    totalEconomizationPages = ( craftParams.economizationParam.size() + ITEMS_PER_PAGE - 1 ) / ITEMS_PER_PAGE;

    // 确保当前页在有效范围内
    if ( currentEconomizationPage >= totalEconomizationPages && totalEconomizationPages > 0 )
    {
        currentEconomizationPage = totalEconomizationPages - 1;
    }
    if ( currentEconomizationPage < 0 )
    {
        currentEconomizationPage = 0;
    }

    // 计算当前页的起始索引
    int startIndex = currentEconomizationPage * ITEMS_PER_PAGE;

    // 更新控件显示
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        int dataIndex = startIndex + i;

        if ( dataIndex < craftParams.economizationParam.size() )
        {
            // 有数据，显示
            auto param = craftParams.economizationParam[ dataIndex ];

            economizationIndexLabels[ i ]->setText( QString::number( dataIndex + 1 ) );
            economizationNameLabels[ i ]->setText( QString( "步骤 %1" ).arg( param->step_index ) );

            // 格式化Step字符串
            QString stepText;
            if ( param->forStep == 255 )
            {
                // forStep为255（-1）时，显示"step_index~step_index"
                stepText = QString( "%1~%1" ).arg( param->step_index );
            }
            else if ( param->forStep > 1 )
            {
                // forStep大于1时，显示"step_index~step_index+forStep"
                stepText = QString( "%1~%2" ).arg( param->step_index ).arg( param->step_index + param->forStep );
            }
            else
            {
                // 其他情况，只显示step_index
                stepText = QString::number( param->step_index );
            }
            economizationStepLabels[ i ]->setText( stepText );

            // 显示Old值（当前尺寸对应的节约次数）
            economizationOldLabels[ i ]->setText( QString::number( param->economizations[ currentSize - 1 ] ) );

            // 显示New值（可编辑）
            economizationNewEdits[ i ]->setText( QString::number( param->economizations[ currentSize - 1 ] ) );
            economizationNewEdits[ i ]->setStyleSheet( "background-color: #ffffc8;" );  // 黄色背景表示可编辑

            // 显示所有控件并设置正常样式
            economizationIndexLabels[ i ]->setVisible( true );
            economizationNameLabels[ i ]->setVisible( true );
            economizationStepLabels[ i ]->setVisible( true );
            economizationOldLabels[ i ]->setVisible( true );
            economizationNewEdits[ i ]->setVisible( true );

            // 恢复正常样式（除了New编辑框）
            economizationIndexLabels[ i ]->setStyleSheet( "" );
            economizationNameLabels[ i ]->setStyleSheet( "" );
            economizationStepLabels[ i ]->setStyleSheet( "" );
            economizationOldLabels[ i ]->setStyleSheet( "" );
        }
        else
        {
            // 无数据，但保持控件可见以占据空间
            economizationIndexLabels[ i ]->setText( "" );
            economizationNameLabels[ i ]->setText( "" );
            economizationStepLabels[ i ]->setText( "" );
            economizationOldLabels[ i ]->setText( "" );
            economizationNewEdits[ i ]->setText( "" );

            // 控件保持可见，但设置为透明
            economizationIndexLabels[ i ]->setVisible( true );
            economizationNameLabels[ i ]->setVisible( true );
            economizationStepLabels[ i ]->setVisible( true );
            economizationOldLabels[ i ]->setVisible( true );
            economizationNewEdits[ i ]->setVisible( true );

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            economizationIndexLabels[ i ]->setStyleSheet( transparentStyle );
            economizationNameLabels[ i ]->setStyleSheet( transparentStyle );
            economizationStepLabels[ i ]->setStyleSheet( transparentStyle );
            economizationOldLabels[ i ]->setStyleSheet( transparentStyle );
            economizationNewEdits[ i ]->setStyleSheet( transparentStyle );
        }
    }
}
