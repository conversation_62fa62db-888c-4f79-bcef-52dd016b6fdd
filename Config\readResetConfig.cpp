#include "readResetConfig.h"

ReadResetConfig::ReadResetConfig() {}

ReadResetConfig::~ReadResetConfig() {}

bool ReadResetConfig::parseResetConfig( const QString& fileAddr )
{
    configFileAddr = fileAddr;
    QFile file( fileAddr );

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "ResetConfig Json Open Failed!";
        QMessageBox::warning( nullptr, "错误提示", "ResetConfig.json文件不存在" );
        return false;
    }

    // 读取文件内容
    QByteArray jsonBytes = file.readAll();
    file.close();

    // 解析JSON
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    if ( jsonDocument.isNull() )
    {
        qDebug() << "ResetConfig Json is invalid!";
        return false;
    }

    // 获取根对象
    QJsonObject jsonObject = jsonDocument.object();

    // 清空现有数据
    sockResetList.clear();
    gatherResetList.clear();

    // 解析sockReset数组
    QJsonArray sockResetArray = jsonObject.value( "sockReset" ).toArray();
    sockResetList.resize( sockResetArray.size() );

    for ( int i = 0; i < sockResetArray.size(); ++i )
    {
        QJsonArray resetActions = sockResetArray[ i ].toArray();
        for ( int j = 0; j < resetActions.size(); ++j )
        {
            QJsonObject           actionObj = resetActions[ j ].toObject();
            SockResetActionStruct action;
            action.position = actionObj.value( "position" ).toInt();
            action.type     = actionObj.value( "type" ).toInt();
            action.id       = actionObj.value( "id" ).toInt();
            action.value    = actionObj.value( "value" ).toInt();
            sockResetList[ i ].append( action );
        }
    }

    // 解析gatherReset数组（如果存在）
    if ( jsonObject.contains( "gatherReset" ) )
    {
        QJsonArray gatherResetArray = jsonObject.value( "gatherReset" ).toArray();
        gatherResetList.resize( gatherResetArray.size() );

        for ( int i = 0; i < gatherResetArray.size(); ++i )
        {
            QJsonArray resetActions = gatherResetArray[ i ].toArray();
            for ( int j = 0; j < resetActions.size(); ++j )
            {
                QJsonObject           actionObj = resetActions[ j ].toObject();
                SockResetActionStruct action;
                action.position = actionObj.value( "position" ).toInt();
                action.type     = actionObj.value( "type" ).toInt();
                action.id       = actionObj.value( "id" ).toInt();
                action.value    = actionObj.value( "value" ).toInt();
                gatherResetList[ i ].append( action );
            }
        }
    }

    return true;
}

bool ReadResetConfig::saveConfig()
{
    return saveResetConfig( configFileAddr );
}

bool ReadResetConfig::saveResetConfig( const QString& fileAddr )
{
    // 创建根对象
    QJsonObject rootObject;

    // 创建sockReset数组
    QJsonArray sockResetArray;

    // 填充数据
    for ( int i = 0; i < sockResetList.size(); ++i )
    {
        QJsonArray actionArray;
        for ( int j = 0; j < sockResetList[ i ].size(); ++j )
        {
            QJsonObject actionObj;
            actionObj.insert( "position", sockResetList[ i ][ j ].position );
            actionObj.insert( "type", sockResetList[ i ][ j ].type );
            actionObj.insert( "id", sockResetList[ i ][ j ].id );
            actionObj.insert( "value", sockResetList[ i ][ j ].value );
            actionArray.append( actionObj );
        }
        sockResetArray.append( actionArray );
    }

    // 添加到根对象
    rootObject.insert( "sockReset", sockResetArray );

    // 创建gatherReset数组
    QJsonArray gatherResetArray;

    // 填充gatherReset数据
    for ( int i = 0; i < gatherResetList.size(); ++i )
    {
        QJsonArray actionArray;
        for ( int j = 0; j < gatherResetList[ i ].size(); ++j )
        {
            QJsonObject actionObj;
            actionObj.insert( "position", gatherResetList[ i ][ j ].position );
            actionObj.insert( "type", gatherResetList[ i ][ j ].type );
            actionObj.insert( "id", gatherResetList[ i ][ j ].id );
            actionObj.insert( "value", gatherResetList[ i ][ j ].value );
            actionArray.append( actionObj );
        }
        gatherResetArray.append( actionArray );
    }

    // 添加gatherReset到根对象
    rootObject.insert( "gatherReset", gatherResetArray );

    // 创建JSON文档
    QJsonDocument jsonDocument( rootObject );

    // 写入文件
    QFile file( fileAddr );
    if ( !file.open( QIODevice::WriteOnly ) )
    {
        qDebug() << "Cannot open file for writing:" << fileAddr;
        return false;
    }

    file.write( jsonDocument.toJson() );
    file.close();

    return true;
}

bool ReadResetConfig::addAction( int circleIndex, const SockResetActionStruct& action, ResetType type )
{
    // 根据类型选择操作的列表
    QVector< QVector< SockResetActionStruct > >& targetList = getResetList( type );

    if ( circleIndex < 0 || circleIndex >= targetList.size() )
    {
        // 如果索引超出范围，自动扩展数组
        while ( targetList.size() <= circleIndex )
        {
            targetList.append( QVector< SockResetActionStruct >() );
        }
    }

    // 直接添加到末尾
    targetList[ circleIndex ].append( action );

    // 保存配置
    saveConfig();

    return true;
}

bool ReadResetConfig::removeAction( int circleIndex, int instructionIndex, ResetType type )
{
    // 根据类型选择操作的列表
    QVector< QVector< SockResetActionStruct > >& targetList = getResetList( type );

    if ( circleIndex < 0 || circleIndex >= targetList.size() )
    {
        qDebug() << "Invalid circle index:" << circleIndex;
        return false;
    }

    if ( instructionIndex < 0 || instructionIndex >= targetList[ circleIndex ].size() )
    {
        qDebug() << "Invalid instruction index:" << instructionIndex;
        return false;
    }

    // 直接删除指定索引的指令
    targetList[ circleIndex ].remove( instructionIndex );

    // 保存配置
    saveConfig();

    return true;
}

// 在指定位置插入新圈
void ReadResetConfig::insertCircle( int index, ResetType type )
{
    // 根据类型选择操作的列表
    QVector< QVector< SockResetActionStruct > >& targetList = getResetList( type );

    // 确保索引有效
    if ( index < 0 )
        index = 0;
    if ( index > targetList.size() )
        index = targetList.size();

    // 创建空的圈数据
    QVector< SockResetActionStruct > newCircle;

    // 在指定位置插入
    targetList.insert( index, newCircle );

    // 保存配置
    saveConfig();
}

// 删除指定位置的圈
void ReadResetConfig::deleteCircle( int index, ResetType type )
{
    // 根据类型选择操作的列表
    QVector< QVector< SockResetActionStruct > >& targetList = getResetList( type );

    // 确保索引有效
    if ( index < 0 || index >= targetList.size() )
        return;

    // 删除指定位置的圈
    targetList.removeAt( index );

    // 保存配置
    saveConfig();
}
