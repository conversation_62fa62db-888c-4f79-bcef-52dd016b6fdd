#include "uppermsgmaker.h"
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>

UpperMsgMaker::UpperMsgMaker() {}

UpperMsgMaker::~UpperMsgMaker() {}

QString UpperMsgMaker::MakeProductInfoMsg( MachineInfo* machineInfo )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "01001" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d1", machineInfo->d1 );
    payloadObj.insert( "d2", machineInfo->d2 );
    payloadObj.insert( "d3", machineInfo->d3 );
    payloadObj.insert( "d4", machineInfo->d4 );
    payloadObj.insert( "d5", machineInfo->d5 );
    payloadObj.insert( "d6", machineInfo->d6 );
    payloadObj.insert( "d7", machineInfo->d7 );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeMachineInfoMsg( MachineInfo* machineInfo )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "01002" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d11", machineInfo->d11 );
    payloadObj.insert( "d12", machineInfo->d12 );
    payloadObj.insert( "d13", machineInfo->d13 );
    payloadObj.insert( "d14", machineInfo->d14 );
    payloadObj.insert( "d5", machineInfo->d5 );
    payloadObj.insert( "d15", machineInfo->d15 );
    payloadObj.insert( "d16", machineInfo->d16 );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeRunningStatusMsg( MachineInfo* machineInfo )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "01003" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d17", machineInfo->d17 );
    payloadObj.insert( "d18", machineInfo->d18 );
    payloadObj.insert( "d19", machineInfo->d19 );
    payloadObj.insert( "d20", machineInfo->d20 );
    payloadObj.insert( "d8", machineInfo->d8 );
    payloadObj.insert( "d9", machineInfo->d9 );
    payloadObj.insert( "d10", machineInfo->d10 );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeAlarmMsg( MachineInfo* machineInfo )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "01004" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d8", machineInfo->d8 );
    payloadObj.insert( "d9", machineInfo->d9 );
    payloadObj.insert( "d10", machineInfo->d10 );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakePowerConsumptionMsg( MachineInfo* machineInfo )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "01005" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d21", machineInfo->d21 );
    payloadObj.insert( "d22", machineInfo->d22 );
    payloadObj.insert( "d23", machineInfo->d23 );
    payloadObj.insert( "d24", QString::number( machineInfo->d24 ) );
    payloadObj.insert( "d25", machineInfo->d25 );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeOprationResultMsg( MachineInfo* machineInfo, bool success, quint16 code, QString message )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "03001" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d1", success );
    payloadObj.insert( "d2", code );
    payloadObj.insert( "d3", message );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeFileDownloadingMsg( MachineInfo* machineInfo, QString filename, quint32 fileLen, QString fileContent )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "02003" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d2", filename );
    payloadObj.insert( "d3", QString::number( fileLen ) );
    payloadObj.insert( "d4", fileContent );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeFileListMsg( MachineInfo* machineInfo, QString content )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "02005" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );

    QJsonObject payloadObj;
    payloadObj.insert( "d4", content );
    msg.insert( "payload", payloadObj );

    return QJsonDocument( msg ).toJson();
}

QString UpperMsgMaker::MakeConnectionMsg( MachineInfo* machineInfo, QString message )
{
    QJsonObject msg;
    msg.insert( "deviceID", machineInfo->deviceId );
    msg.insert( "opcode", "03002" );
    // 获取当前机器时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    msg.insert( "time", currentDateTime.toString( "yyyy-MM-dd hh:mm:ss" ) );
    msg.insert( "payload", message );

    return QJsonDocument( msg ).toJson();
}
