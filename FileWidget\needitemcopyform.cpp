﻿#include "needitemcopyform.h"
#include "ui_needitemcopyform.h"
#include <QDebug>
#include <QMessageBox>
#include <QScreen>

NeedItemCopyForm::NeedItemCopyForm( quint8* data, QWidget* parent ) : QWidget( parent ), ui( new Ui::NeedItemCopyForm )
{
    ui->setupUi( this );

    memcpy( _data, data, 25 );
    this->_srcData = data;

    this->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
    this->setWindowTitle( tr( "块复制" ) );
    this->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    connect( ui->pbtn_0, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_1, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_2, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_3, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_4, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_5, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_6, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_7, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_8, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_9, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );
    connect( ui->pbtn_backspace, &QPushButton::clicked, this, &NeedItemCopyForm::onBtnClicked );

    connect( this->ui->le_ncpyFrom1, &MyLineEdit::mouseRelease, this, &NeedItemCopyForm::onLineEditClicked );
    connect( this->ui->le_ncpyFrom2, &MyLineEdit::mouseRelease, this, &NeedItemCopyForm::onLineEditClicked );
    connect( this->ui->le_ncpyTo1, &MyLineEdit::mouseRelease, this, &NeedItemCopyForm::onLineEditClicked );
    connect( this->ui->le_ncpyTo2, &MyLineEdit::mouseRelease, this, &NeedItemCopyForm::onLineEditClicked );

    connect( ui->pbtn_OK, &QPushButton::clicked, this, &NeedItemCopyForm::onPushButtonClicked );
    connect( ui->pbtn_Cancel, &QPushButton::clicked, this, &NeedItemCopyForm::onPushButtonClicked );
    showData();
}

NeedItemCopyForm::~NeedItemCopyForm()
{
    delete ui;
}

void NeedItemCopyForm::setData( quint8* data )
{
    memcpy( _data, data, 25 );
    this->_srcData = data;
    showData();
}

// 无数据显示，只初始化几个输入框为0
void NeedItemCopyForm::showData()
{
    this->ui->le_ncpyFrom1->setText( "1" );
    this->ui->le_ncpyFrom2->setText( "1" );
    this->ui->le_ncpyTo1->setText( "1" );
    this->ui->le_ncpyTo2->setText( "1" );
}

void NeedItemCopyForm::onLineEditClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        // 把上一个的外框效果删除，再赋值新一个
        if ( this->currentLineEdit != nullptr )
        {
            this->currentLineEdit->setStyleSheet( "" );
        }
        this->currentLineEdit = senderLineEdit;
        // 第一次点击后全选，可以全部删除
        senderLineEdit->selectAll();
        this->firsTimeToEditLineEdit = true;
        senderLineEdit->setStyleSheet( "border: 1px solid #FC5531;" );
    }
}

void NeedItemCopyForm::onLineEditFinished()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        senderLineEdit->setStyleSheet( "" );
    }
}

void NeedItemCopyForm::onBtnClicked()
{
    QObject* senderObj = sender();

    if ( this->currentLineEdit != nullptr )
    {
        if ( this->firsTimeToEditLineEdit )
        {
            // 全选时，则删除全部内容
            this->currentLineEdit->setText( "" );
            this->firsTimeToEditLineEdit = false;
        }

        if ( senderObj == ui->pbtn_0 )
        {
            if ( this->currentLineEdit->text().length() >= 1 && this->currentLineEdit->text() != "-" )
                this->currentLineEdit->setText( this->currentLineEdit->text() + '0' );
        }
        else if ( senderObj == ui->pbtn_1 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '1' );
        else if ( senderObj == ui->pbtn_2 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '2' );
        else if ( senderObj == ui->pbtn_3 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '3' );
        else if ( senderObj == ui->pbtn_4 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '4' );
        else if ( senderObj == ui->pbtn_5 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '5' );
        else if ( senderObj == ui->pbtn_6 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '6' );
        else if ( senderObj == ui->pbtn_7 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '7' );
        else if ( senderObj == ui->pbtn_8 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '8' );
        else if ( senderObj == ui->pbtn_9 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '9' );

        else if ( senderObj == ui->pbtn_backspace )
        {
            QString text = this->currentLineEdit->text();
            text.chop( 1 );
            this->currentLineEdit->setText( text );
        }
    }
}

void NeedItemCopyForm::onPushButtonClicked()
{
    QPushButton* button = qobject_cast< QPushButton* >( sender() );
    if ( button == this->ui->pbtn_OK )
    {
        // Save Data
        int from1 = this->ui->le_ncpyFrom1->text().toInt();
        int from2 = this->ui->le_ncpyFrom2->text().toInt();
        int to1   = this->ui->le_ncpyTo1->text().toInt();
        int to2   = this->ui->le_ncpyTo2->text().toInt();
        if ( from2 < from1 || to1 < from1 )
        {
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "针数范围不正确" ) );
            return;
        }
        if ( to2 == 0 )
        {
            to2 = 200;
        }

        // 拷贝数据，输入的第1号，实际索引为0
        int i = from1 - 1, j = to1 - 1;
        while ( j < to2 )
        {
            //            qDebug() << "before" << QString::number( this->_data[ i / 8 ], 2 ) << QString::number( this->_data[ j / 8 ], 2 );
            // 先取form的数据，并判断
            if ( ( this->_data[ i / 8 ] >> ( i % 8 ) ) & 0x01 )
            {
                this->_data[ j / 8 ] |= 0x01 << ( j % 8 );
            }
            else
            {
                this->_data[ j / 8 ] &= ~( 0x01 << ( j % 8 ) );
            }
            //            qDebug() << "after" << QString::number( this->_data[ i / 8 ], 2 ) << QString::number( this->_data[ j / 8 ], 2 );
            // 复制完成后,i和j都要增加
            i++;
            if ( i > from2 - 1 )
            {
                i = from1 - 1;
            }
            j++;
        }

        memcpy( this->_srcData, this->_data, 25 );
    }
    this->close();
    emit finished();
}

void NeedItemCopyForm::closeEvent( QCloseEvent* event )
{
    QWidget::closeEvent( event );
    emit finished();
}
