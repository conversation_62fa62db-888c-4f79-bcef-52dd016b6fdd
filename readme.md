# 安瑞袜机显示控制系统

## 项目概述
本项目是一个基于Qt开发的袜机显示控制系统，用于控制和监控袜机的各项功能和参数。系统提供了友好的用户界面，支持机器参数配置、电眼设置、伺服电机控制等功能。

## 主要功能
- 机器参数配置与保存
- 电眼(EEye)设置与监控
- 伺服电机控制
- 步进电机配置
- 用户参数设置
- 快速复位功能
- 缝头校准

## 系统架构
系统主要由以下几个部分组成：
- 配置管理：负责读取和保存机器配置文件
- 通信模块：实现与袜机控制器的通信
- 用户界面：提供直观的操作界面
- 控制逻辑：实现各种控制功能

## 配置文件
系统使用JSON格式的配置文件存储各种参数，主要包括：
- 基本配置(basic)
- 针筒配置(needle)
- 外设配置(peripheral)
- 位置配置(position)
- 电眼配置(EEye)
- 伺服电机配置(Servo)
- 步进电机配置(socketMotor, fengtouMotor, otherMotor)

## 开发环境
- Qt 5.x
- C++11及以上
- Windows操作系统

## 使用说明
1. 启动程序后，系统会自动加载配置文件
2. 通过界面可以查看和修改各项参数
3. 修改后的参数可以保存到配置文件中
4. 系统支持单独保存某一类配置，如电眼配置、伺服电机配置等

## 注意事项
- 修改参数前请确保了解参数的含义和影响
- 建议在修改重要参数前备份配置文件
- 部分功能可能需要特定的硬件支持