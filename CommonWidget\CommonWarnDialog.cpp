#include "CommonWarnDialog.h"
#include "ui_CommonWarnDialog.h"
#include <QDebug>

CommonWarnDialog::CommonWarnDialog( ReadWarnConfig* warnConfig, QWidget* parent ) : QWidget( parent ), ui( new Ui::CommonWarnDialog ), _warnConfig( warnConfig )
{
    ui->setupUi( this );

    this->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
    this->setWindowTitle( tr( "警告信息" ) );
    this->setWindowFlags(
        /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    // 连接按钮信号
    connect( ui->pbtn_close, &QPushButton::clicked, this, &CommonWarnDialog::hide );
    connect( ui->pbtn_next, &QPushButton::clicked, this, [this]() {
        if ( warnList.isEmpty() )
            return;
        currentIndex = ( currentIndex + 1 ) % warnList.size();
        showWarnDetail();
    } );

    connect( ui->pbtn_del, &QPushButton::clicked, this, [this]() {
        if ( warnList.isEmpty() || currentIndex < 0 )
            return;
        warnList.removeAt( currentIndex );
        if ( warnList.isEmpty() )
        {
            currentIndex = -1;
            hide();
        }
        else
        {
            currentIndex = qMin( currentIndex, warnList.size() - 1 );
            showWarnDetail();
        }
        updateCountLabel();
    } );
}

CommonWarnDialog::~CommonWarnDialog()
{
    delete ui;
}

void CommonWarnDialog::refreshWarnInfo( int type_id, int id, int status )
{
    WarnInfoItem info{ type_id, id };

    if ( status == 0 )
    {
        // 移除报警
        int index = warnList.indexOf( info );
        if ( index != -1 )
        {
            warnList.removeAt( index );
            if ( warnList.isEmpty() )
            {
                currentIndex = -1;
                hide();
            }
            else
            {
                currentIndex = qMin( currentIndex, warnList.size() - 1 );
                showWarnDetail();
            }
        }
    }
    else
    {
        // 添加报警
        if ( !warnList.contains( info ) )
        {
            warnList.append( info );
            if ( currentIndex == -1 )
            {
                currentIndex = warnList.size() - 1;
                showWarnDetail();
                show();
            }
        }
    }

    updateCountLabel();
}

void CommonWarnDialog::updateCountLabel()
{
    if ( warnList.isEmpty() )
    {
        ui->label_count->setText( "0/0" );
    }
    else
    {
        ui->label_count->setText( QString( "%1/%2" ).arg( currentIndex + 1 ).arg( warnList.size() ) );
    }
}

void CommonWarnDialog::showWarnDetail()
{
    if ( currentIndex >= 0 && currentIndex < warnList.size() )
    {
        const WarnInfoItem& info = warnList[ currentIndex ];

        // 从配置中获取警告类别
        auto categoryList = _warnConfig->getList();
        if ( categoryList->contains( info.type_id ) )
        {
            const auto& category = categoryList->value( info.type_id );

            // 获取具体警告项
            if ( category.items.contains( info.id ) )
            {
                const auto& item = category.items.value( info.id );

                // 更新UI显示
                ui->label_type->setText( category.name );
                ui->label_name->setText( item.name );
                ui->label_desc->setText( item.desc );

                // 加载图片（如果需要）
                QString imgPath = QCoreApplication::applicationDirPath() + item.img;
                QPixmap pixmap( imgPath );
                if ( !pixmap.isNull() )
                {
                    ui->label_img->setPixmap( pixmap.scaled( ui->label_img->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation ) );
                }
                else
                {
                    ui->label_img->clear();
                }
            }
        }

        updateCountLabel();
    }
    else
    {
        // 清空显示
        ui->label_type->clear();
        ui->label_name->clear();
        ui->label_desc->clear();
        ui->label_img->clear();
    }
}
