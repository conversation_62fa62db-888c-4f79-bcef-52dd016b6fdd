#include "testform.h"
#include "ui_testform.h"
#include <QDebug>

/* 选针器测试初始化 */
void TestForm::SelectTestInit()
{
    if ( isSelectTestPageInit == false )
    {
        /* 创建实体 */
        SelectKnifeBtnGroup = new QButtonGroup( this );
        SelectBtnGroup      = new QButtonGroup( this );
        SelectCtrlBtnGroup  = new QButtonGroup( this );

        AddSelectRadioBtnToBtnGroup();  // 按键组添加按键
        connect( this->SelectKnifeBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onKnifeBtnGroupClicked( int ) ) );
        AddKnifeCheckBoxToBtnGroup();
        connect( this->SelectBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onSelectBtnGroupClicked( int ) ) );
        AddSelectTestCtrlBtnToBtnGroup();
        connect( this->SelectCtrlBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onSelectCtrlBtnGroupClicked( int ) ) );
        isSelectTestPageInit = true;
    }

    // 频率相关
    this->ui->le_selectorFreq->setText( QString::number( this->selectorTestFreq ) );
}

/* 将选针器刀片Checkbox加入到按键组中 */
void TestForm::AddKnifeCheckBoxToBtnGroup()
{
    SelectKnifeBtnGroup->addButton( ui->rb_knife1, 0 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife2, 1 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife3, 2 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife4, 3 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife5, 4 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife6, 5 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife7, 6 );
    SelectKnifeBtnGroup->addButton( ui->rb_knife8, 7 );
    SelectKnifeBtnGroup->setExclusive( true );
}

/* 将选针器Radiobox加入到按键组中 */
void TestForm::AddSelectRadioBtnToBtnGroup()
{
    SelectBtnGroup->addButton( ui->chx_Selector1, 0 );
    SelectBtnGroup->addButton( ui->chx_Selector2, 1 );
    SelectBtnGroup->addButton( ui->chx_Selector3, 2 );
    SelectBtnGroup->addButton( ui->chx_Selector4, 3 );
    SelectBtnGroup->addButton( ui->chx_Selector5, 4 );
    SelectBtnGroup->addButton( ui->chx_Selector6, 5 );
    SelectBtnGroup->addButton( ui->chx_Selector7, 6 );
    SelectBtnGroup->addButton( ui->chx_Selector8, 7 );
    SelectBtnGroup->addButton( ui->chx_Selector9, 8 );
    SelectBtnGroup->addButton( ui->chx_Selector10, 9 );
    SelectBtnGroup->setExclusive( false );
}

/* 将选针器测试控制按键加入到按键组中 */
void TestForm::AddSelectTestCtrlBtnToBtnGroup()
{
    SelectCtrlBtnGroup->addButton( ui->SelectTest_Auto_btn, 0 );         // 自动测试
    SelectCtrlBtnGroup->addButton( ui->SelectTest_1x1_btn, 1 );          // 1x1
    SelectCtrlBtnGroup->addButton( ui->SelectTest_AllUpDown_btn, 2 );    // 全上全下测试
    SelectCtrlBtnGroup->addButton( ui->SelectTest_SignalKnife_btn, 3 );  // 单刀测试
    SelectCtrlBtnGroup->addButton( ui->SelectTest_Old_btn, 4 );          // 老化测试
    SelectCtrlBtnGroup->addButton( ui->SelectTest_KnifeCheck_btn, 5 );   // 刀片检测
    SelectCtrlBtnGroup->addButton( ui->SelectTest_FreqPlus_btn, 6 );
    SelectCtrlBtnGroup->addButton( ui->SelectTest_FreqDec_btn, 7 );
    SelectCtrlBtnGroup->addButton( ui->SelectTest_SignalLoop_btn, 8 );  // 单刀循环
}

/**************************************************** 选针器测试相关槽函数 **********************************************************************/

/* 刀片按键组按下槽函数 */
void TestForm::onKnifeBtnGroupClicked( int id )
{
    selectedKnife = id;
    // 如果已经是1
    //    if ( this->realSelectState[ this->selectedSelector ] & ( 1 << id ) )
    //    {
    //        this->realSelectState[ this->selectedSelector ] &= ~( 1 << id );
    //    }
    //    else
    //    {
    //        this->realSelectState[ this->selectedSelector ] |= ( 1 << id );
    //    }
    //    sendSelectorState();
}

/* 选针器按键组按下槽函数 */
void TestForm::onSelectBtnGroupClicked( int id )
{
    if ( isSelectorAutoTestingRunning || isKnifeSingleLoopTestingRunning )
    {
        // 恢复原状
        if ( SelectBtnGroup->buttons().at( id )->isChecked() )
        {
            SelectBtnGroup->buttons().at( id )->setChecked( false );
        }
        else
        {
            SelectBtnGroup->buttons().at( id )->setChecked( true );
        }
        QMessageBox::warning( nullptr, "错误提示", "需要先停止连续测试任务" );
        return;
    }
    if ( SelectBtnGroup->buttons().at( id )->isChecked() )
    {
        selectList.insert( std::upper_bound( selectList.begin(), selectList.end(), id ), id );
        setCheckBoxSelectedOnly( id );
        this->selectedSelector = id;
    }
    else
    {
        selectList.removeOne( id );
        if ( selectList.size() > 0 )
        {
            setCheckBoxSelectedOnly( selectList.last() );
            this->selectedSelector = selectList.last();
        }
        else
        {
            setCheckBoxSelectedOnly( -1 );
            this->selectedSelector = -1;
        }
    }
}

/* 选针器控制按键组按下槽函数 */
void TestForm::onSelectCtrlBtnGroupClicked( int id )
{
    // 1X1 reset
    if ( id != 1 && oneplusone > 0 )
    {
        this->realSelectState[ this->selectedSelector ] = 0;
        SelectKnifeBtnGroup->setExclusive( true );
        SelectKnifeBtnGroup->button( 0 )->setChecked( false );
        SelectKnifeBtnGroup->button( 2 )->setChecked( false );
        SelectKnifeBtnGroup->button( 4 )->setChecked( false );
        SelectKnifeBtnGroup->button( 6 )->setChecked( false );
        SelectKnifeBtnGroup->button( 1 )->setChecked( false );
        SelectKnifeBtnGroup->button( 3 )->setChecked( false );
        SelectKnifeBtnGroup->button( 5 )->setChecked( false );
        SelectKnifeBtnGroup->button( 7 )->setChecked( false );
        oneplusone = 0;
    }

    switch ( id )
    {
        case 0:  // 自动按键
            if ( isKnifeSingleLoopTestingRunning || isSelectorAllTestingRunning )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止连续测试任务" );
                return;
            }
            if ( this->testTimer == nullptr )
            {
                this->testTimer = new QTimer( this );
                QObject::connect( this->testTimer, &QTimer::timeout, this, &TestForm::SelectAutoTesting );
                // refresh用于控制线程是否刷新迭代器it，若不刷新，可能还是上一轮的it，会跳出
                this->testTimer->setProperty( "refresh", true );
                this->ui->SelectTest_Auto_btn->setStyleSheet( QString( "QPushButton {"
                                                                       "background-color: #FFA406;"
                                                                       "color: white;"
                                                                       "border-radius: 5px;"
                                                                       "padding: 5px;"
                                                                       "font-size: 10pt;"
                                                                       "}" ) );
                // 频率为1秒多少次
                this->testTimer->setInterval( 1000 / selectorTestFreq );
                this->testTimer->start();
                this->isSelectorAutoTestingRunning = true;
            }
            else
            {
                stopAutoTask();
                this->ui->SelectTest_Auto_btn->setStyleSheet( QString( "QPushButton {"
                                                                       "background-color: #3498db;"
                                                                       "color: white;"
                                                                       "border-radius: 5px;"
                                                                       "padding: 5px;"
                                                                       "font-size: 10pt;"
                                                                       "}" ) );
            }
            break;
        case 1:  // 1*1 A/B
            if ( this->testTimer != nullptr )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止自动任务" );
                return;
            }
            if ( this->selectedSelector < 0 )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先必须先选中选针器" );
                return;
            }
            this->oneplusone++;
            if ( this->oneplusone == 3 )
                this->oneplusone = 0;
            if ( this->oneplusone == 0 )
            {
                this->realSelectState[ this->selectedSelector ] = 0;
                SelectKnifeBtnGroup->setExclusive( false );
                SelectKnifeBtnGroup->button( 1 )->setChecked( false );
                SelectKnifeBtnGroup->button( 3 )->setChecked( false );
                SelectKnifeBtnGroup->button( 5 )->setChecked( false );
                SelectKnifeBtnGroup->button( 7 )->setChecked( false );
                SelectKnifeBtnGroup->button( 0 )->setChecked( false );
                SelectKnifeBtnGroup->button( 2 )->setChecked( false );
                SelectKnifeBtnGroup->button( 4 )->setChecked( false );
                SelectKnifeBtnGroup->button( 6 )->setChecked( false );
            }
            else if ( this->oneplusone == 1 )
            {
                this->realSelectState[ this->selectedSelector ] = 0x55;
                // 允许多个刀片选中
                SelectKnifeBtnGroup->setExclusive( false );
                SelectKnifeBtnGroup->button( 0 )->setChecked( true );
                SelectKnifeBtnGroup->button( 2 )->setChecked( true );
                SelectKnifeBtnGroup->button( 4 )->setChecked( true );
                SelectKnifeBtnGroup->button( 6 )->setChecked( true );
                SelectKnifeBtnGroup->button( 1 )->setChecked( false );
                SelectKnifeBtnGroup->button( 3 )->setChecked( false );
                SelectKnifeBtnGroup->button( 5 )->setChecked( false );
                SelectKnifeBtnGroup->button( 7 )->setChecked( false );
            }
            else if ( this->oneplusone == 2 )
            {
                this->realSelectState[ this->selectedSelector ] = 0xaa;
                // 允许多个刀片选中
                SelectKnifeBtnGroup->setExclusive( false );
                SelectKnifeBtnGroup->button( 0 )->setChecked( false );
                SelectKnifeBtnGroup->button( 2 )->setChecked( false );
                SelectKnifeBtnGroup->button( 4 )->setChecked( false );
                SelectKnifeBtnGroup->button( 6 )->setChecked( false );
                SelectKnifeBtnGroup->button( 1 )->setChecked( true );
                SelectKnifeBtnGroup->button( 3 )->setChecked( true );
                SelectKnifeBtnGroup->button( 5 )->setChecked( true );
                SelectKnifeBtnGroup->button( 7 )->setChecked( true );
            }
            sendSelectorState();
            break;
        case 2:  // 全上/全下
            if ( this->testTimer != nullptr )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止自动任务" );
                return;
            }
            qDebug() << "Flipping";
            if ( this->isFlipping )
            {
                this->realSelectState[ this->selectedSelector ] = 0;
                this->isFlipping                                = false;
                SelectKnifeBtnGroup->setExclusive( false );
                SelectKnifeBtnGroup->button( 1 )->setChecked( false );
                SelectKnifeBtnGroup->button( 3 )->setChecked( false );
                SelectKnifeBtnGroup->button( 5 )->setChecked( false );
                SelectKnifeBtnGroup->button( 7 )->setChecked( false );
                SelectKnifeBtnGroup->button( 0 )->setChecked( false );
                SelectKnifeBtnGroup->button( 2 )->setChecked( false );
                SelectKnifeBtnGroup->button( 4 )->setChecked( false );
                SelectKnifeBtnGroup->button( 6 )->setChecked( false );
            }
            else
            {
                this->realSelectState[ this->selectedSelector ] = 0xff;
                this->isFlipping                                = true;
                setCheckBoxSelectedOnly( this->selectedSelector );
                SelectKnifeBtnGroup->setExclusive( false );
                SelectKnifeBtnGroup->button( 1 )->setChecked( true );
                SelectKnifeBtnGroup->button( 3 )->setChecked( true );
                SelectKnifeBtnGroup->button( 5 )->setChecked( true );
                SelectKnifeBtnGroup->button( 7 )->setChecked( true );
                SelectKnifeBtnGroup->button( 0 )->setChecked( true );
                SelectKnifeBtnGroup->button( 2 )->setChecked( true );
                SelectKnifeBtnGroup->button( 4 )->setChecked( true );
                SelectKnifeBtnGroup->button( 6 )->setChecked( true );
            }
            sendSelectorState();
            break;
        case 3:  // 单刀测试
        {
            if ( this->testTimer != nullptr )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止自动任务" );
                return;
            }
            if ( selectedKnife < 0 || selectedSelector < 0 )
            {
                QMessageBox::warning( nullptr, "错误提示", "请先设置选针器和刀片选中" );
                return;
            }
            // 检查SelectKnifeBtnGroup的按钮选中是不是多于1个，是则设置0号默认选中，其他不选中
            int checkedCount = 0;
            for ( int i = 0; i < SelectKnifeBtnGroup->buttons().size(); i++ )
            {
                if ( SelectKnifeBtnGroup->button( i )->isChecked() )
                {
                    checkedCount++;
                }
            }
            if ( checkedCount > 1 )
            {
                SelectKnifeBtnGroup->setExclusive( false );
                for ( int i = 0; i < SelectKnifeBtnGroup->buttons().size(); i++ )
                {
                    SelectKnifeBtnGroup->button( i )->setChecked( false );
                }
                SelectKnifeBtnGroup->button( 0 )->setChecked( true );
            }
            SelectKnifeBtnGroup->setExclusive( true );
            if ( this->isFlipping )
            {
                this->realSelectState[ this->selectedSelector ] &= ~( 1 << selectedKnife );
                this->isFlipping = false;
            }
            else
            {
                this->realSelectState[ this->selectedSelector ] |= ( 1 << selectedKnife );
                this->isFlipping = true;
            }
            sendSelectorState();
        }
        break;
        case 4:  // 老化测试
            SelectKnifeBtnGroup->setExclusive( false );
            if ( isKnifeSingleLoopTestingRunning || isSelectorAutoTestingRunning )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止连续测试任务" );
                return;
            }
            if ( this->testTimer == nullptr )
            {
                this->testTimer = new QTimer( this );
                QObject::connect( this->testTimer, &QTimer::timeout, this, &TestForm::SelectAllTesting );
                // refresh用于控制线程是否刷新迭代器it，若不刷新，可能还是上一轮的it，会跳出
                this->testTimer->setProperty( "refresh", true );
                this->ui->SelectTest_Old_btn->setStyleSheet( QString( "QPushButton {"
                                                                      "background-color: #FFA406;"
                                                                      "color: white;"
                                                                      "border-radius: 5px;"
                                                                      "padding: 5px;"
                                                                      "font-size: 10pt;"
                                                                      "}" ) );

                // 频率为1秒多少次
                this->testTimer->setInterval( 1000 / selectorTestFreq );
                this->testTimer->start();
                this->isSelectorAllTestingRunning = true;
            }
            else
            {
                stopAutoTask();
                this->ui->SelectTest_Old_btn->setStyleSheet( QString( "QPushButton {"
                                                                      "background-color: #3498db;"
                                                                      "color: white;"
                                                                      "border-radius: 5px;"
                                                                      "padding: 5px;"
                                                                      "font-size: 10pt;"
                                                                      "}" ) );
            }
            break;
        case 5:  // 刀片检测
            break;
        case 6:  // 频率加按键
            this->selectorTestFreq++;
            this->ui->le_selectorFreq->setText( QString::number( this->selectorTestFreq ) );
            if ( this->testTimer != nullptr )
            {
                this->testTimer->setInterval( 1000 / selectorTestFreq );
            }
            break;
        case 7:  // 频率减按键
            if ( this->selectorTestFreq > 1 )
            {
                this->selectorTestFreq--;
                this->ui->le_selectorFreq->setText( QString::number( this->selectorTestFreq ) );
            }
            if ( this->testTimer != nullptr )
            {
                this->testTimer->setInterval( 1000 / selectorTestFreq );
            }
            break;
        case 8:  // 单刀循环
            if ( isSelectorAutoTestingRunning || isSelectorAllTestingRunning )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止连续测试任务" );
                return;
            }
            if ( this->selectedSelector < 0 )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先必须先选中选针器" );
                return;
            }
            if ( this->testTimer == nullptr )
            {
                this->testTimer = new QTimer( this );
                // Clear
                memset( this->realSelectState, 0, 10 );
                QObject::connect( this->testTimer, &QTimer::timeout, this, &TestForm::SingleKnifeLoop );
                // refresh用于控制线程是否刷新迭代器it，若不刷新，可能还是上一轮的it，会跳出
                this->testTimer->setProperty( "refresh", true );
                this->ui->SelectTest_SignalLoop_btn->setStyleSheet( QString( "QPushButton {"
                                                                             "background-color: #FFA406;"
                                                                             "color: white;"
                                                                             "border-radius: 5px;"
                                                                             "padding: 5px;"
                                                                             "font-size: 10pt;"
                                                                             "}" ) );
                // 频率为1秒多少次
                this->testTimer->setInterval( 1000 / selectorTestFreq );
                this->testTimer->start();
                this->isKnifeSingleLoopTestingRunning = true;

                SelectKnifeBtnGroup->setExclusive( true );
            }
            else
            {
                stopAutoTask();
                this->ui->SelectTest_SignalLoop_btn->setStyleSheet( QString( "QPushButton {"
                                                                             "background-color: #3498db;"
                                                                             "color: white;"
                                                                             "border-radius: 5px;"
                                                                             "padding: 5px;"
                                                                             "font-size: 10pt;"
                                                                             "}" ) );
            }
            break;
    }
}

void TestForm::SelectAutoTesting()
{
    static QList< int >::iterator it         = selectList.begin();
    static bool                   changeHigh = false;
    qDebug() << "SelectAutoTesting " << *it;
    QVariant VRefresh = this->testTimer->property( "refresh" );
    if ( VRefresh.isValid() )
    {
        if ( VRefresh.toBool() == true )
        {
            it         = this->selectList.begin();
            changeHigh = false;
            this->testTimer->setProperty( "refresh", false );
        }
    }

    if ( it != selectList.end() )
    {
        if ( changeHigh == false )
        {
            memset( this->realSelectState, 0, 10 );
            changeHigh = true;
            sendSelectorState();
        }
        else
        {
            this->realSelectState[ *it ] = 0xff;
            changeHigh                   = false;
            sendSelectorState();

            qDebug() << "it" << *it;
            setCheckBoxSelectedOnly( *it );

            this->selectedSelector = *it;
            ++it;
            if ( it == selectList.end() )
            {
                it = selectList.begin();
            }
        }
    }
}

void TestForm::SingleKnifeLoop()
{
    static int knife_index = 0;
    QVariant   VRefresh    = this->testTimer->property( "refresh" );
    if ( VRefresh.isValid() )
    {
        if ( VRefresh.toBool() == true )
        {
            knife_index = 0;
            this->testTimer->setProperty( "refresh", false );
        }
    }

    if ( knife_index < 8 )
    {
        this->realSelectState[ this->selectedSelector ] = 0;
        this->realSelectState[ this->selectedSelector ] |= ( 1 << knife_index );
        sendSelectorState();

        // 设置控件选中
        SelectKnifeBtnGroup->buttons().at( knife_index )->setChecked( true );
        selectedKnife = knife_index;

        ++knife_index;
        if ( knife_index == 8 )
        {
            knife_index = 0;
        }
    }
}

void TestForm::SelectAllTesting()
{
    static bool changeHigh = false;
    QVariant    VRefresh   = this->testTimer->property( "refresh" );
    if ( VRefresh.isValid() )
    {
        if ( VRefresh.toBool() == true )
        {
            changeHigh = false;
            this->testTimer->setProperty( "refresh", false );
        }
    }

    // 已选刀片逐个测试
    // 哪些刀片选中了
    quint8 value = 0x00;
    for ( int i = 0; i < 8; i++ )
    {
        if ( SelectKnifeBtnGroup->button( i )->isChecked() )
        {
            value |= 0x01 << i;
        }
    }
    if ( changeHigh == false )
    {
        memset( this->realSelectState, 0, 10 );
        changeHigh = true;
        sendSelectorState();
    }
    else
    {
        memset( this->realSelectState, value, 10 );
        changeHigh = false;
        sendSelectorState();
    }
}

void TestForm::setCheckBoxSelectedOnly( int id )
{
    if ( this->selectedSelector >= 0 )
    {
        this->SelectBtnGroup->buttons()
            .at( this->selectedSelector )
            ->setStyleSheet( QString( "QCheckBox{"
                                      "background-color: #ffffff;"
                                      "width: 70px;"
                                      "height: 40px;"
                                      "border-radius: 5px;"
                                      "padding: 5px;"
                                      "margin: 2px;"
                                      "font-size: 8pt;"
                                      "}"
                                      "QCheckBox:hover {"
                                      "background-color: #e8f4fc;"
                                      "border: 1px solid #3498db;"
                                      "}"
                                      "QCheckBox::indicator:unchecked{"
                                      "border-image: url(:/checkbox.png);"
                                      "Width: 30px;"
                                      "Height: 30px;"
                                      "}"
                                      "QCheckBox::indicator:checked{"
                                      "border-image: url(:/checkbox_checked.png);"
                                      "Width: 30px;"
                                      "Height: 30px;"
                                      "}" ) );
    }
    if ( id >= 0 )
    {
        SelectBtnGroup->buttons().at( id )->setStyleSheet( QString( "QCheckBox{"
                                                                    "background-color: #ffffff;"
                                                                    "width: 70px;"
                                                                    "height: 40px;"
                                                                    "border-radius: 5px;"
                                                                    "padding: 5px;"
                                                                    "margin: 2px;"
                                                                    "font-size: 8pt;"
                                                                    "border: 2px solid #3498db;"
                                                                    "}"
                                                                    "QCheckBox:hover {"
                                                                    "background-color: #e8f4fc;"
                                                                    "border: 2px solid #3498db;"
                                                                    "}"
                                                                    "QCheckBox::indicator:unchecked{"
                                                                    "border-image: url(:/checkbox.png);"
                                                                    "Width: 30px;"
                                                                    "Height: 30px;"
                                                                    "}"
                                                                    "QCheckBox::indicator:checked{"
                                                                    "border-image: url(:/checkbox_checked.png);"
                                                                    "Width: 30px;"
                                                                    "Height: 30px;"
                                                                    "}" ) );
    }
}

void TestForm::setKnifeSelectedOnly( int id )
{
    // 将其他已选的恢复原状
    for ( int i = 0; i < 8; i++ )
    {
        if ( SelectKnifeBtnGroup->button( i )->isChecked() && i != id )
        {
            SelectKnifeBtnGroup->buttons().at( i )->setStyleSheet( QString( "QRadioButton {"
                                                                            "background-color: #ffffff;"
                                                                            "border-radius: 5px;"
                                                                            "padding: 5px;"
                                                                            "margin: 2px;"
                                                                            "font-size: 8pt;"
                                                                            "}"
                                                                            "QRadioButton:hover {"
                                                                            "background-color: #e8f4fc;"
                                                                            "border: 1px solid #3498db;"
                                                                            "}"
                                                                            "QRadioButton::indicator:unchecked{"
                                                                            "border-image: url(:/radio_button);"
                                                                            "Width: 30px;"
                                                                            "Height: 30px;"
                                                                            "}"
                                                                            "QRadioButton::indicator:checked{"
                                                                            "border-image: url(:/radio-button-checked.png);"
                                                                            "Width: 30px;"
                                                                            "Height: 30px;"
                                                                            "}" ) );
        }
    }
    // 当前的设置边框
    if ( id >= 0 )
    {
        SelectKnifeBtnGroup->buttons().at( id )->setStyleSheet( QString( "QRadioButton {"
                                                                         "background-color: #ffffff;"
                                                                         "border-radius: 5px;"
                                                                         "padding: 5px;"
                                                                         "margin: 2px;"
                                                                         "font-size: 8pt;"
                                                                         "border: 2px solid #3498db;"
                                                                         "}"
                                                                         "QRadioButton:hover {"
                                                                         "background-color: #e8f4fc;"
                                                                         "border: 2px solid #3498db;"
                                                                         "}"
                                                                         "QRadioButton::indicator:unchecked{"
                                                                         "border-image: url(:/radio_button);"
                                                                         "Width: 30px;"
                                                                         "Height: 30px;"
                                                                         "}"
                                                                         "QRadioButton::indicator:checked{"
                                                                         "border-image: url(:/radio-button-checked.png);"
                                                                         "Width: 30px;"
                                                                         "Height: 30px;"
                                                                         "}" ) );
    }
}

void TestForm::sendSelectorState()
{
    //    QTextStream cout( stdout );
    //    cout << this->selectedSelector << " ";
    //    for ( unsigned int i = 0; i < sizeof( this->realSelectState ); ++i )
    //    {
    //        quint8 byte = realSelectState[ i ];
    //        for ( int bit = 7; bit >= 0; --bit )
    //        {
    //            cout << ( ( byte >> bit ) & 1 );
    //        }
    //        cout << " ";  // 每个字节后添加空格分隔
    //    }
    //    cout << endl;

    comm->pushDataTobuffer( 0x08, this->realSelectState, sizeof( this->realSelectState ) );
}
