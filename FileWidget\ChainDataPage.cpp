#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "fileform.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

// 链条编辑
void FileForm::initChainDataPage()
{
    // Fill gbox_steps StepName
    ui->listWidget_Stepname->clear();
    int index = 0;
    for ( QVector< QSharedPointer< FATParser::SockMachineStepMsg > >::const_iterator it = fatDataMain->StepVector.begin(); it != fatDataMain->StepVector.end(); ++it )
    {
        QString stepName = this->linkCfg->getStepNameList()->value( it->data()->main_param.step_name );
        //        qDebug() << "step" << it->data()->main_param.step_name << stepName;
        QListWidgetItem* item = new QListWidgetItem( ui->listWidget_Stepname );
        item->setText( stepName );
        ui->listWidget_Stepname->insertItem( index, item );
        index++;
    }
    // 第一次要链接，不然后一行无法emit
    if ( !isChainDagePageInited )
        connect( ui->listWidget_Stepname, &QListWidget::itemClicked, this, &FileForm::ShowBuweiParam );
    // 默认选中第一行
    ui->listWidget_Stepname->setCurrentRow( 0 );
    QModelIndex test = ui->listWidget_Stepname->currentIndex();
    emit        ui->listWidget_Stepname->clicked( test );

    // 以下不重复初始化
    if ( isChainDagePageInited )
        return;

    // Modify StepName
    connect( this->ui->le_StepName, &MyLineEdit::mouseRelease, this, [&]() {
        this->ui->le_StepName->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );
        //        this->ui->le_StepName->selectAll();

        if ( this->_stepName_comboFrm == nullptr )
        {
            this->_stepName_comboFrm = new ComboForm( nullptr, this->linkCfg->getStepNameList() );  //不能有父类
            this->_stepName_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                    true );  //属性设置true:模态;false:非模态
            this->_stepName_comboFrm->setWindowTitle( tr( "选择步骤名称" ) );
            this->_stepName_comboFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_stepName_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                //                qDebug() << id;
                this->_stepName_comboFrm->close();
                this->ui->le_StepName->setText( this->linkCfg->getStepNameList()->value( id ) );
                // 保存数据
                quint16                     step_index = ui->listWidget_Stepname->currentIndex().row();
                FATParser::MainProgamParam* main_param = &this->fatDataMain->StepVector.at( step_index )->main_param;
                main_param->step_name                  = id;
            } );
            connect( this->_stepName_comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_StepName->setStyleSheet( "font-size:8pt;" ); } );
        }
        this->_stepName_comboFrm->show();
    } );

    // 动作栏上一页和下一页按钮槽函数
    connect( this->ui->pbtn_ActionNextPage, &QPushButton::clicked, this, [&]() {
        this->currentActionPage++;
        ShowActionList();
    } );
    connect( this->ui->pbtn_ActionPrevtPage, &QPushButton::clicked, this, [&]() {
        if ( this->currentActionPage > 0 )
        {
            this->currentActionPage--;
            ShowActionList();
        }
        else
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到第1页" ) );
    } );

    // 密度设置弹窗
    connect( this->ui->pbtn_Density, &QPushButton::clicked, this, [&]() {
        if ( this->_densityFrm == nullptr )
        {
            this->_densityFrm = new DensityForm( nullptr, this->fatDataMain->DentisyData );
            this->_densityFrm->setAttribute( Qt::WA_ShowModal,
                                             true );  //属性设置true:模态;false:非模态
            this->_densityFrm->setWindowTitle( tr( "密度设置" ) );
            this->_densityFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标
            //            connect( this->_densityFrm, &DensityForm::finished, this, [&]() { this->_densityFrm = nullptr; } );
        }
        this->_densityFrm->show();
        // 必须刷新下数据，避免LineEdit修改造成显示与实际数值不一致
        this->_densityFrm->setDensityData( this->fatDataMain->DentisyData );
        this->_densityFrm->showDensity();
    } );

    // 进入动作
    connect( this->ui->pbtn_toVAction, &QPushButton::clicked, this, [&]() {
        if ( this->currentSelectedMyLineEdit != nullptr )
        {
            const QVariant vaction_index = this->currentSelectedMyLineEdit->property( "cy_num" );
            qDebug() << vaction_index;
            if ( !vaction_index.isValid() )
            {
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "请先选中动作" ) );
                return;
            }
            else
            {
                this->currentActionDetailIndex = vaction_index.toInt() - 1;  // 这里有个坑，第1号动作实际是存到0号位置,没有第0号动作
                this->ui->File_stackedWidget->setCurrentIndex( 3 );
                ui->File_Tiltle_label->setText( ( "文件/动作编辑---动作库编号: " ) + QString::number( vaction_index.toInt() ) );
                this->initActionViewPage();
            }
        }
        else
        {
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "请先选中动作" ) );
            return;
        }
    } );

    // 圈控制插入、删除、清除
    connect( this->ui->pbtn_actionSAdd, &QPushButton::clicked, this, [&]() {
        // 最大不超过100个
        if ( this->currentChainStepStruct->action_cnt < 99 )
        {
            this->currentChainStepStruct->action_cnt++;
            // 拿到旧的指针
            FATParser::CYActionStruct* cy_ptr_old = this->currentChainStepStruct->cy_action_msg;
            /*申请新的数据内存*/
            this->currentChainStepStruct->cy_action_msg = new FATParser::CYActionStruct[ this->currentChainStepStruct->action_cnt ];
            /*将旧的动作信息数据拷贝到新的*/
            memcpy( ( char* )this->currentChainStepStruct->cy_action_msg, cy_ptr_old, ( ( this->currentChainStepStruct->action_cnt - 1 ) * sizeof( FATParser::CYActionStruct ) ) );
            // 删除旧的
            delete cy_ptr_old;
            this->ShowActionList();
        }
    } );
    connect( this->ui->pbtn_actionSDelete, &QPushButton::clicked, this, [&]() {
        if ( this->currentChainStepStruct->action_cnt > 0 )
        {
            this->currentChainStepStruct->action_cnt--;
            // 拿到旧的指针
            FATParser::CYActionStruct* cy_ptr_old = this->currentChainStepStruct->cy_action_msg;
            /*申请新的数据内存*/
            this->currentChainStepStruct->cy_action_msg = new FATParser::CYActionStruct[ this->currentChainStepStruct->action_cnt ];
            /*将旧的动作信息数据拷贝到新的*/
            memcpy( ( char* )this->currentChainStepStruct->cy_action_msg, cy_ptr_old, ( ( this->currentChainStepStruct->action_cnt ) * sizeof( FATParser::CYActionStruct ) ) );
            // 删除旧的
            delete cy_ptr_old;
            this->ShowActionList();
        }
    } );
    connect( this->ui->pbtn_actionSClear, &QPushButton::clicked, this, [&]() {
        if ( this->currentChainStepStruct->action_cnt > 1 )
        {
            this->currentChainStepStruct->action_cnt = 1;
            // 拿到旧的指针
            FATParser::CYActionStruct* cy_ptr_old = this->currentChainStepStruct->cy_action_msg;
            /*申请新的数据内存*/
            this->currentChainStepStruct->cy_action_msg = new FATParser::CYActionStruct[ this->currentChainStepStruct->action_cnt ];
            /*将旧的动作信息数据拷贝到新的*/
            memcpy( ( char* )this->currentChainStepStruct->cy_action_msg, cy_ptr_old, ( ( this->currentChainStepStruct->action_cnt ) * sizeof( FATParser::CYActionStruct ) ) );
            // 删除旧的
            delete cy_ptr_old;
            this->ShowActionList();
        }
        else
        {
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "最后1个不允许清空" ) );
        }
    } );

    // 指令插入、删除、清除
    connect( this->ui->pbtn_FTCommandAdd, &QPushButton::clicked, this, [&]() {
        FATParser::CYActionStruct* cy = this->currentChainStepStruct->cy_action_msg + this->currentActionIndex;
        // 最大不超16个
        if ( cy->cmd_cnt < 15 )
        {
            cy->cmd_cnt++;
            this->ShowFTCommand();
        }
    } );
    connect( this->ui->pbtn_FTCommandDelete, &QPushButton::clicked, this, [&]() {
        FATParser::CYActionStruct* cy = this->currentChainStepStruct->cy_action_msg + this->currentActionIndex;

        if ( cy->cmd_cnt > 0 )
        {
            cy->cmd_cnt--;
            qDebug() << "cmd_cnt" << cy->cmd_cnt;
            this->ShowFTCommand();
        }
    } );
    connect( this->ui->pbtn_FTCommandClear, &QPushButton::clicked, this, [&]() {
        FATParser::CYActionStruct* cy = this->currentChainStepStruct->cy_action_msg + this->currentActionIndex;

        cy->cmd_cnt = 0;
        this->ShowFTCommand();
    } );

    // Save
    connect( ui->pbtn_PatternSave, &QPushButton::clicked, this, &FileForm::saveDataToMWWFile );

    this->isChainDagePageInited = true;
}

void FileForm::ShowBuweiParam( QListWidgetItem* item )
{
    // 按index来查找，而不是按照值来查找，因为值有可能是相同的
    quint16 step_index                    = ui->listWidget_Stepname->currentIndex().row();
    this->currentChainStepStruct          = this->fatDataMain->StepVector.at( step_index );
    FATParser::MainProgamParam main_param = this->currentChainStepStruct->main_param;

    this->ui->le_StepName->setText( this->linkCfg->getStepNameList()->value( main_param.step_name ) );

    this->ui->le_size->setText( QString::number( main_param.chicun, 10 ) );
    this->ui->le_size->setProperty( "widget", "le_size" );
    connect( this->ui->le_size, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_speed->setText( QString::number( main_param.speed, 10 ) );
    this->ui->le_speed->setProperty( "widget", "le_speed" );
    connect( this->ui->le_speed, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_xiangjin1->setText( QString::number( main_param.xiangjin.start_value, 10 ) );
    this->ui->le_xiangjin1->setProperty( "widget", "le_xiangjin1" );
    connect( this->ui->le_xiangjin1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_xiangjin2->setText( QString::number( main_param.xiangjin.end_value, 10 ) );
    this->ui->le_xiangjin2->setProperty( "widget", "le_xiangjin2" );
    connect( this->ui->le_xiangjin2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_xiangjin3->setText( main_param.xiangjin.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_ktf11->setText( QString::number( main_param.ktf1.start_value, 10 ) );
    this->ui->le_ktf11->setProperty( "widget", "le_ktf11" );
    connect( this->ui->le_ktf11, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_ktf12->setText( QString::number( main_param.ktf1.end_value, 10 ) );
    this->ui->le_ktf12->setProperty( "widget", "le_ktf12" );
    connect( this->ui->le_ktf12, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_ktf13->setText( main_param.ktf1.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_ktf21->setText( QString::number( main_param.ktf2.start_value, 10 ) );
    this->ui->le_ktf21->setProperty( "widget", "le_ktf21" );
    connect( this->ui->le_ktf21, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_ktf22->setText( QString::number( main_param.ktf2.end_value, 10 ) );
    this->ui->le_ktf22->setProperty( "widget", "le_ktf22" );
    connect( this->ui->le_ktf22, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_ktf23->setText( main_param.ktf2.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_zhengtong1->setText( QString::number( main_param.zhentong_Density.start_value, 10 ) );
    this->ui->le_zhengtong1->setProperty( "widget", "le_zhengtong1" );
    connect( this->ui->le_zhengtong1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_zhengtong2->setText( QString::number( main_param.zhentong_Density.end_value, 10 ) );
    this->ui->le_zhengtong2->setProperty( "widget", "le_zhengtong2" );
    connect( this->ui->le_zhengtong2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_zhengtong3->setText( main_param.zhentong_Density.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_chenjiang1->setText( QString::number( main_param.chenjiang_Density.start_value, 10 ) );
    this->ui->le_chenjiang1->setProperty( "widget", "le_chenjiang1" );
    connect( this->ui->le_chenjiang1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_chenjiang2->setText( QString::number( main_param.chenjiang_Density.end_value, 10 ) );
    this->ui->le_chenjiang2->setProperty( "widget", "le_chenjiang2" );
    connect( this->ui->le_chenjiang2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_chenjiang3->setText( main_param.chenjiang_Density.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_shengke1->setText( QString::number( main_param.shengke_Density.start_value, 10 ) );
    this->ui->le_shengke1->setProperty( "widget", "le_shengke1" );
    connect( this->ui->le_shengke1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_shengke2->setText( QString::number( main_param.shengke_Density.end_value, 10 ) );
    this->ui->le_shengke2->setProperty( "widget", "le_shengke2" );
    connect( this->ui->le_shengke2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_shengke3->setText( main_param.shengke_Density.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_leftlj1->setText( QString::number( main_param.zuo_lengjiao.start_value, 10 ) );
    this->ui->le_leftlj1->setProperty( "widget", "le_leftlj1" );
    connect( this->ui->le_leftlj1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_leftlj2->setText( QString::number( main_param.zuo_lengjiao.end_value, 10 ) );
    this->ui->le_leftlj2->setProperty( "widget", "le_leftlj2" );
    connect( this->ui->le_leftlj2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_leftlj3->setText( main_param.zuo_lengjiao.state == 1 ? QString( "渐进" ) : QString( "常规" ) );

    this->ui->le_rightlj1->setText( QString::number( main_param.you_lengjiao.start_value, 10 ) );
    this->ui->le_rightlj1->setProperty( "widget", "le_rightlj1" );
    connect( this->ui->le_rightlj1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_rightlj2->setText( QString::number( main_param.you_lengjiao.end_value, 10 ) );
    this->ui->le_rightlj2->setProperty( "widget", "le_rightlj2" );
    connect( this->ui->le_rightlj2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_rightlj3->setText( main_param.you_lengjiao.state == 1 ? QString( "渐进" ) : QString( "常规" ) );
    this->ui->le_huaixing->setText( main_param.huaxing_shezhi == 1 ? QString( "有花型" ) : QString( "无花型" ) );
    this->ui->le_lamao->setText( main_param.lamao_shezhi == 1 ? QString( "有花型" ) : QString( "无花型" ) );

    this->ui->le_wagen1->setText( QString::number( main_param.dagen_zhongxin, 10 ) );
    this->ui->le_wagen1->setProperty( "widget", "le_wagen1" );
    connect( this->ui->le_wagen1, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_wagen2->setText( QString::number( main_param.dagen_pianyi, 10 ) );
    this->ui->le_wagen2->setProperty( "widget", "le_wagen2" );
    connect( this->ui->le_wagen2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_wagen3->setText( QString::number( main_param.fujia, 10 ) );
    this->ui->le_wagen3->setProperty( "widget", "le_wagen3" );
    connect( this->ui->le_wagen3, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    // chose Motor Type
    connect( this->ui->le_xiangjin3, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_ktf13, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_ktf23, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_zhengtong3, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_chenjiang3, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_shengke3, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_leftlj3, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );
    connect( this->ui->le_rightlj3, &MyLineEdit::mouseRelease, this, &FileForm::onMotorTypeClicked );

    // Pattern Chose
    connect( this->ui->le_huaixing, &MyLineEdit::mouseRelease, this, &FileForm::onPatternTypeClicked );
    connect( this->ui->le_lamao, &MyLineEdit::mouseRelease, this, &FileForm::onPatternTypeClicked );

    // 每次点击切换主步骤后，默认显示第1个圈控制的动作
    this->currentActionIndex = 0;
    /*显示动作*/
    ShowActionList();
}

void FileForm::onMotorTypeClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;

    if ( _motorPattern_comboFrm == nullptr )
    {
        QMap< int, QString >* list = new QMap< int, QString >();
        list->insert( 0, "常规" );
        list->insert( 1, "渐进" );
        _motorPattern_comboFrm = new ComboForm( nullptr, list );
        _motorPattern_comboFrm->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
        _motorPattern_comboFrm->setWindowTitle( tr( "选择类型" ) );
        _motorPattern_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标
        connect( this->_motorPattern_comboFrm, &ComboForm::itemSelected, this, &FileForm::onMotorTypeSelected );
    }
    _motorPattern_comboFrm->show();
}

void FileForm::onMotorTypeSelected( int itemKey )
{
    this->currentSelectedMyLineEdit->setText( itemKey == 1 ? QString( "渐进" ) : QString( "常规" ) );
    FATParser::MainProgamParam* main_param = &this->currentChainStepStruct->main_param;

    if ( this->currentSelectedMyLineEdit == ui->le_xiangjin3 )
    {
        main_param->xiangjin.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_ktf13 )
    {
        main_param->ktf1.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_ktf23 )
    {
        main_param->ktf2.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_zhengtong3 )
    {
        main_param->zhentong_Density.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_chenjiang3 )
    {
        main_param->chenjiang_Density.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_shengke3 )
    {
        main_param->shengke_Density.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_leftlj3 )
    {
        main_param->zuo_lengjiao.state = itemKey;
    }
    else if ( this->currentSelectedMyLineEdit == ui->le_rightlj3 )
    {
        main_param->you_lengjiao.state = itemKey;
    }
}

void FileForm::onPatternTypeClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;

    if ( _chainPatternType_comboFrm == nullptr )
    {
        QMap< int, QString >* list = new QMap< int, QString >();
        list->insert( 0, "无花型" );
        list->insert( 1, "有花型" );
        _chainPatternType_comboFrm = new ComboForm( nullptr, list );
        _chainPatternType_comboFrm->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
        _chainPatternType_comboFrm->setWindowTitle( tr( "选择类型" ) );
        _chainPatternType_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标
        connect( this->_chainPatternType_comboFrm, &ComboForm::itemSelected, this, &FileForm::onPatternTypeSelected );
    }
    _chainPatternType_comboFrm->show();
}

void FileForm::onPatternTypeSelected( int itemKey )
{
    this->currentSelectedMyLineEdit->setText( itemKey == 1 ? QString( "有花型" ) : QString( "无花型" ) );

    FATParser::MainProgamParam* main_param = &this->currentChainStepStruct->main_param;

    if ( currentSelectedMyLineEdit == ui->le_huaixing )
    {
        main_param->huaxing_shezhi = itemKey;
    }
    else if ( currentSelectedMyLineEdit == ui->le_lamao )
    {
        main_param->lamao_shezhi = itemKey;
    }
}

void FileForm::ShowActionList()
{
    quint16                    cy_cnt     = this->currentChainStepStruct->action_cnt;
    FATParser::CYActionStruct* cy_ptr     = this->currentChainStepStruct->cy_action_msg;
    quint16                    step_index = ui->listWidget_Stepname->currentIndex().row();

    // 先判断页码是否准确
    if ( this->currentActionPage * 8 >= cy_cnt )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到最后1页" ) );
        this->currentActionPage--;
        return;
    }

    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_actions->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QHBoxLayout* mainLayout = new QHBoxLayout( this->ui->gbox_actions );

    QVBoxLayout* column1Layout = new QVBoxLayout;
    QLabel*      label1        = new QLabel( "圈控制" );
    QLabel*      label2        = new QLabel( "动作号" );
    QLabel*      label3        = new QLabel( "圈    数" );
    QLabel*      label4        = new QLabel( "特征码" );
    label1->setStyleSheet( "font-size:8pt;" );
    label2->setStyleSheet( "font-size:8pt;" );
    label3->setStyleSheet( "font-size:8pt;" );
    label4->setStyleSheet( "font-size:8pt;" );
    column1Layout->addWidget( label1 );
    column1Layout->addWidget( label2 );
    column1Layout->addWidget( label3 );
    column1Layout->addWidget( label4 );
    column1Layout->setMargin( 0 );
    column1Layout->setSpacing( 0 );
    mainLayout->addLayout( column1Layout );

    // 只显示8个
    for ( quint8 index = 0; index < 8; index++ )
    {
        QVBoxLayout* columnLayoutx = new QVBoxLayout;
        QLabel*      label         = new QLabel( "CY" + QString::number( index + 1 ) );
        label->setAlignment( Qt::AlignHCenter );
        label->setStyleSheet( "font-size:8pt;" );
        columnLayoutx->addWidget( label );
        MyLineEdit* lineEdit1 = new MyLineEdit;
        MyLineEdit* lineEdit2 = new MyLineEdit;
        MyLineEdit* lineEdit3 = new MyLineEdit;
        lineEdit1->setStyleSheet( "font-size:8pt;" );
        lineEdit2->setStyleSheet( "font-size:8pt;" );
        lineEdit3->setStyleSheet( "font-size:8pt;" );
        lineEdit1->setAlignment( Qt::AlignHCenter );
        lineEdit2->setAlignment( Qt::AlignHCenter );
        lineEdit3->setAlignment( Qt::AlignHCenter );
        if ( cy_cnt > 0 && this->currentActionPage * 8 + index <= cy_cnt - 1 )
        {
            lineEdit1->setText( QString::number( ( cy_ptr + this->currentActionPage * 8 + index )->cy_num, 10 ) );
            lineEdit1->setProperty( "cy_num", ( cy_ptr + this->currentActionPage * 8 + index )->cy_num );
            lineEdit2->setText( QString::number( ( cy_ptr + this->currentActionPage * 8 + index )->cy_circle_cnt, 10 ) );
            lineEdit3->setText( this->linkCfg->getCYStateList()->value( ( cy_ptr + this->currentActionPage * 8 + index )->cy_state ) );
            lineEdit1->setProperty( "step_index", QVariant::fromValue( step_index ) );
            lineEdit1->setProperty( "action_index", QVariant::fromValue( this->currentActionPage * 8 + index ) );
            lineEdit2->setProperty( "step_index", QVariant::fromValue( step_index ) );
            lineEdit2->setProperty( "action_index", QVariant::fromValue( this->currentActionPage * 8 + index ) );
            lineEdit3->setProperty( "step_index", QVariant::fromValue( step_index ) );
            lineEdit3->setProperty( "action_index", QVariant::fromValue( this->currentActionPage * 8 + index ) );
            connect( lineEdit1, &MyLineEdit::mouseRelease, this, &FileForm::onActionLineEditClicked );
            connect( lineEdit2, &MyLineEdit::mouseRelease, this, &FileForm::onActionLineEditClicked );
            connect( lineEdit3, &MyLineEdit::mouseRelease, this, &FileForm::onActionLineEditClicked );
            lineEdit1->setProperty( "widget", "cy_num" );
            connect( lineEdit1, &MyLineEdit::mouseDoubleClick, this, &FileForm::onShowNumberInputForm );
            lineEdit2->setProperty( "widget", "circle_cnt" );
            connect( lineEdit2, &MyLineEdit::mouseDoubleClick, this, &FileForm::onShowNumberInputForm );
            connect( lineEdit3, &MyLineEdit::mouseDoubleClick, this, &FileForm::onCyStateClicked );
        }
        else
        {
            lineEdit1->setText( "-" );
            lineEdit2->setText( "-" );
            lineEdit3->setText( "-" );
        }
        columnLayoutx->addWidget( lineEdit1 );
        columnLayoutx->addWidget( lineEdit2 );
        columnLayoutx->addWidget( lineEdit3 );
        columnLayoutx->setMargin( 0 );
        columnLayoutx->setSpacing( 0 );
        mainLayout->addLayout( columnLayoutx );
        mainLayout->setSpacing( 1 );
        mainLayout->setContentsMargins( 6, 1, 6, 1 );
    }
    // 默认显示指令页第1项
    ShowFTCommand();
}

void FileForm::onActionLineEditClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    //    int         step_index          = senderLineEdit->property( "step_index" ).toInt();
    int action_index                = senderLineEdit->property( "action_index" ).toInt();
    this->currentSelectedMyLineEdit = senderLineEdit;
    this->currentActionIndex        = action_index;

    //    qDebug() << ( senderLineEdit == nullptr ) << step_index << action_index;
    ShowFTCommand();
}

void FileForm::ShowFTCommand()
{
    //    qDebug() << step_index << action_index;
    FATParser::CYActionStruct cy = *( this->currentChainStepStruct->cy_action_msg + this->currentActionIndex );
    /*有效指令数*/
    quint32 cmd_cnt = cy.cmd_cnt;
    //    qDebug() << "cmd_cnt" << cmd_cnt;

    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_instructions->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_instructions );

    QHBoxLayout* rowLayout = new QHBoxLayout;
    QLabel*      label1    = new QLabel( "序号" );
    QLabel*      label2    = new QLabel( "指令" );
    QLabel*      label3    = new QLabel( "设置" );
    QLabel*      label4    = new QLabel( "状态" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    label1->setStyleSheet( "font-size:8pt;" );
    label2->setStyleSheet( "font-size:8pt;" );
    label3->setStyleSheet( "font-size:8pt;" );
    label4->setStyleSheet( "font-size:8pt;" );
    rowLayout->addWidget( label1 );
    rowLayout->addWidget( label2 );
    rowLayout->addWidget( label3 );
    rowLayout->addWidget( label4 );
    rowLayout->setStretch( 0, 1 );
    rowLayout->setStretch( 1, 2 );
    rowLayout->setStretch( 2, 1 );
    rowLayout->setStretch( 3, 1 );
    rowLayout->setMargin( 0 );
    rowLayout->setSpacing( 1 );
    mainLayout->addLayout( rowLayout );

    // 只显示8个
    for ( quint8 index = 0; index < 12; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( "M-" + QString( "%1" ).arg( ( index + 1 ), 2, 10, QChar( '0' ) ) );
        label->setAlignment( Qt::AlignHCenter );
        rowLayoutx->addWidget( label );
        MyLineEdit* lineEdit1 = new MyLineEdit;
        MyLineEdit* lineEdit2 = new MyLineEdit;
        MyLineEdit* lineEdit3 = new MyLineEdit;
        lineEdit1->setAlignment( Qt::AlignHCenter );
        lineEdit2->setAlignment( Qt::AlignHCenter );
        lineEdit3->setAlignment( Qt::AlignHCenter );
        lineEdit1->setStyleSheet( "font-size:8pt;" );
        lineEdit2->setStyleSheet( "font-size:8pt;" );
        lineEdit3->setStyleSheet( "font-size:8pt;" );
        if ( cmd_cnt > 0 && index <= cmd_cnt - 1 )
        {
            FATParser::CmdStruct cmd = cy.cmd_msg[ index ];
            lineEdit1->setText( this->linkCfg->getFTCommandList()->value( cmd.cmd_num ) );
            QString sign = cmd.sign == 0 ? QString( "" ) : QString( "-" );
            lineEdit2->setText( QString( "%1%2" ).arg( sign ).arg( cmd.cmd_param ) );
            lineEdit3->setText( QString::number( cmd.cmd_state, 10 ) );
            lineEdit1->setProperty( "ft_index", index );
            // 点击跳出修改窗口
            connect( lineEdit1, &MyLineEdit::mouseRelease, this, &FileForm::onFTCommandNameClicked );
            lineEdit2->setProperty( "ft_index", index );
            lineEdit2->setProperty( "widget", "ft_cmd_param" );
            connect( lineEdit2, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );
            lineEdit3->setProperty( "ft_index", index );
            lineEdit3->setProperty( "widget", "ft_cmd_state" );
            connect( lineEdit3, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );
        }
        else
        {
            lineEdit1->setText( "-" );
            lineEdit2->setText( "-" );
            lineEdit3->setText( "-" );
        }
        rowLayoutx->addWidget( lineEdit1 );
        rowLayoutx->addWidget( lineEdit2 );
        rowLayoutx->addWidget( lineEdit3 );
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 2 );
        rowLayoutx->setStretch( 2, 1 );
        rowLayoutx->setStretch( 3, 1 );
        rowLayoutx->setMargin( 0 );
        rowLayoutx->setSpacing( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setSpacing( 1 );
        mainLayout->setContentsMargins( 6, 1, 6, 1 );
    }
}

void FileForm::onCyStateClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );
    //    senderLineEdit->selectAll();
    this->currentSelectedMyLineEdit = senderLineEdit;

    if ( this->_cyState_comboFrm == nullptr )
    {
        this->_cyState_comboFrm = new ComboForm( nullptr, this->linkCfg->getCYStateList() );  //不能有父类
        this->_cyState_comboFrm->setAttribute( Qt::WA_ShowModal, true );                      //属性设置true:模态;false:非模态
        this->_cyState_comboFrm->setWindowTitle( tr( "选择状态" ) );
        this->_cyState_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_cyState_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            qDebug() << id;
            this->_cyState_comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->linkCfg->getCYStateList()->value( id ) );
            this->currentSelectedMyLineEdit->setStyleSheet( "" );
            QVariant var = this->currentSelectedMyLineEdit->property( "action_index" );
            if ( var.isValid() )
            {
                int                        action_index                           = var.toInt();
                FATParser::CYActionStruct* cy_ptr                                 = this->currentChainStepStruct->cy_action_msg;
                ( cy_ptr + this->currentActionPage * 8 + action_index )->cy_state = id;
                qDebug() << "cy_state" << ( cy_ptr + this->currentActionPage * 8 + action_index )->cy_state;
            }
        } );
        connect( this->_cyState_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
    }
    this->_cyState_comboFrm->show();
}

void FileForm::onFTCommandNameClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );
    //    senderLineEdit->selectAll();
    this->currentSelectedMyLineEdit = senderLineEdit;

    if ( this->_FACommandName_comboFrm == nullptr )
    {
        this->_FACommandName_comboFrm = new ComboForm( nullptr, this->linkCfg->getFTCommandList(), 8 );  //不能有父类
        this->_FACommandName_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                     true );  //属性设置true:模态;false:非模态
        this->_FACommandName_comboFrm->setWindowTitle( tr( "选择指令" ) );
        this->_FACommandName_comboFrm->setWindowFlags(
            /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_FACommandName_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            qDebug() << id;
            this->_FACommandName_comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->linkCfg->getFTCommandList()->value( id ) );
            this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" );
            QVariant var = this->currentSelectedMyLineEdit->property( "ft_index" );
            if ( var.isValid() )
            {
                int                        ft_index = var.toInt();
                FATParser::CYActionStruct* cy       = this->currentChainStepStruct->cy_action_msg + this->currentActionIndex;
                FATParser::CmdStruct*      cmd      = &cy->cmd_msg[ ft_index ];
                cmd->cmd_num                        = id;
            }
        } );
        connect( this->_FACommandName_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
    }
    this->_FACommandName_comboFrm->show();
}

void FileForm::onShowNumberInputForm()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    if ( _numberInputFrm == nullptr )
    {
        _numberInputFrm = new NumberInputForm();
        connect( this->_numberInputFrm, &NumberInputForm::InputFinished, this, &FileForm::onNumberInputFormFinished );
    }

    if ( senderLineEdit == ui->le_zhengtong1 || senderLineEdit == ui->le_zhengtong2 || senderLineEdit == ui->le_chenjiang1 || senderLineEdit == ui->le_chenjiang2 || senderLineEdit == ui->le_shengke1
         || senderLineEdit == ui->le_shengke2 || senderLineEdit == ui->le_leftlj1 || senderLineEdit == ui->le_leftlj2 || senderLineEdit == ui->le_rightlj1 || senderLineEdit == ui->le_rightlj2 )
        _numberInputFrm->setValueLimit( 0, 24 );
    else
        _numberInputFrm->setValueLimit( 0, 1000 );
    _numberInputFrm->show();
}

void FileForm::onNumberInputFormFinished( QString str )
{
    if ( this->currentSelectedMyLineEdit != nullptr )
    {
        this->currentSelectedMyLineEdit->setText( str );
        QVariant var = this->currentSelectedMyLineEdit->property( "widget" );
        if ( var.isValid() )
        {
            QString widget = var.toString();
            if ( widget == "warnDelay" )
            {
                this->currentFengtouStepStruct->baojing_yanshi = str.toInt();
            }
            else if ( widget == "actionDelay" )
            {
                this->currentFengtouStepStruct->dongzuo_yanshi = str.toInt();
            }
            else if ( widget == "le_size" )
            {
                this->currentChainStepStruct->main_param.chicun = str.toInt();
            }
            else if ( widget == "le_speed" )
            {
                this->currentChainStepStruct->main_param.speed = str.toInt();
            }
            else if ( widget == "le_xiangjin1" )
            {
                this->currentChainStepStruct->main_param.xiangjin.start_value = str.toInt();
            }
            else if ( widget == "le_xiangjin2" )
            {
                this->currentChainStepStruct->main_param.xiangjin.end_value = str.toInt();
            }
            else if ( widget == "le_ktf11" )
            {
                this->currentChainStepStruct->main_param.ktf1.start_value = str.toInt();
            }
            else if ( widget == "le_ktf12" )
            {
                this->currentChainStepStruct->main_param.ktf1.end_value = str.toInt();
            }
            else if ( widget == "le_ktf21" )
            {
                this->currentChainStepStruct->main_param.ktf2.start_value = str.toInt();
            }
            else if ( widget == "le_ktf22" )
            {
                this->currentChainStepStruct->main_param.ktf2.end_value = str.toInt();
            }
            else if ( widget == "le_zhengtong1" )
            {
                this->currentChainStepStruct->main_param.zhentong_Density.start_value = str.toInt();
            }
            else if ( widget == "le_zhengtong2" )
            {
                this->currentChainStepStruct->main_param.zhentong_Density.end_value = str.toInt();
            }
            else if ( widget == "le_chenjiang1" )
            {
                this->currentChainStepStruct->main_param.chenjiang_Density.start_value = str.toInt();
            }
            else if ( widget == "le_chenjiang2" )
            {
                this->currentChainStepStruct->main_param.chenjiang_Density.end_value = str.toInt();
            }
            else if ( widget == "le_shengke1" )
            {
                this->currentChainStepStruct->main_param.shengke_Density.start_value = str.toInt();
            }
            else if ( widget == "le_shengke2" )
            {
                this->currentChainStepStruct->main_param.shengke_Density.end_value = str.toInt();
            }
            else if ( widget == "le_leftlj1" )
            {
                this->currentChainStepStruct->main_param.zuo_lengjiao.start_value = str.toInt();
            }
            else if ( widget == "le_leftlj2" )
            {
                this->currentChainStepStruct->main_param.zuo_lengjiao.end_value = str.toInt();
            }
            else if ( widget == "le_rightlj1" )
            {
                this->currentChainStepStruct->main_param.you_lengjiao.start_value = str.toInt();
            }
            else if ( widget == "le_rightlj2" )
            {
                this->currentChainStepStruct->main_param.you_lengjiao.end_value = str.toInt();
            }
            else if ( widget == "le_wagen1" )
            {
                this->currentChainStepStruct->main_param.dagen_zhongxin = str.toInt();
            }
            else if ( widget == "le_wagen2" )
            {
                this->currentChainStepStruct->main_param.dagen_pianyi = str.toInt();
            }
            else if ( widget == "le_wagen3" )
            {
                this->currentChainStepStruct->main_param.fujia = str.toInt();
            }
            else if ( widget == "ft_cmd_param" )
            {
                QVariant var = this->currentSelectedMyLineEdit->property( "ft_index" );
                if ( var.isValid() )
                {
                    int                        ft_index = var.toInt();
                    FATParser::CYActionStruct* cy       = this->currentChainStepStruct->cy_action_msg + this->currentActionIndex;
                    FATParser::CmdStruct*      cmd      = &cy->cmd_msg[ ft_index ];
                    if ( str.startsWith( "-" ) )
                    {
                        cmd->sign = 1;
                        str.remove( 0, 1 );
                    }
                    cmd->cmd_param = str.toInt();
                    qDebug() << "cmd_param" << cmd->sign << cmd->cmd_param;
                }
            }
            else if ( widget == "ft_cmd_state" )
            {
                QVariant var = this->currentSelectedMyLineEdit->property( "ft_index" );
                if ( var.isValid() )
                {
                    int                        ft_index = var.toInt();
                    FATParser::CYActionStruct* cy       = this->currentChainStepStruct->cy_action_msg + this->currentActionIndex;
                    FATParser::CmdStruct*      cmd      = &cy->cmd_msg[ ft_index ];
                    cmd->cmd_state                      = str.toInt();
                    qDebug() << "cmd_state" << cmd->cmd_state;
                }
            }
            else if ( widget == "cy_num" )
            {
                QVariant var = this->currentSelectedMyLineEdit->property( "action_index" );
                if ( var.isValid() )
                {
                    int                        action_index                         = var.toInt();
                    FATParser::CYActionStruct* cy_ptr                               = this->currentChainStepStruct->cy_action_msg;
                    ( cy_ptr + this->currentActionPage * 8 + action_index )->cy_num = str.toInt();
                    qDebug() << "action_num" << ( cy_ptr + this->currentActionPage * 8 + action_index )->cy_num;
                }
            }
            else if ( widget == "circle_cnt" )
            {
                QVariant var = this->currentSelectedMyLineEdit->property( "action_index" );
                if ( var.isValid() )
                {
                    int                        action_index                                = var.toInt();
                    FATParser::CYActionStruct* cy_ptr                                      = this->currentChainStepStruct->cy_action_msg;
                    ( cy_ptr + this->currentActionPage * 8 + action_index )->cy_circle_cnt = str.toInt();
                    qDebug() << "circle_cnt" << ( cy_ptr + this->currentActionPage * 8 + action_index )->cy_circle_cnt;
                }
            }
        }
    }
}
