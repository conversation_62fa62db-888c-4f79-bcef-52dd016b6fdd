#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"

void CraftParamForm::initCraftPage()
{
    if ( isCraftPageInited == false )
    {
        connect( ui->pbtn_craft_save, &QPushButton::clicked, this, [&]() { saveCraftParams(); } );

        // 尺寸选择框，只有一个comboForm，因此只单独设置
        if ( comboForm == nullptr )
        {
            QMap< int, QString > list;
            list.insert( 1, "尺寸1" );
            list.insert( 2, "尺寸2" );
            list.insert( 3, "尺寸3" );
            list.insert( 4, "尺寸4" );
            list.insert( 5, "尺寸5" );
            list.insert( 6, "尺寸6" );
            list.insert( 7, "尺寸7" );
            list.insert( 8, "尺寸8" );
            comboForm = new ComboForm( nullptr, &list );
        }
        connect( ui->le_craft_size, &MyLineEdit::mouseRelease, this, [&]() { comboForm->show(); } );
        connect( comboForm, &ComboForm::itemSelected, this, [&]( int itemKey ) {
            currentSize = itemKey;
            ui->le_craft_size->setText( QString( "尺寸%1" ).arg( QString::number( itemKey ) ) );
            showCraftParams();
        } );

        // 初始化速度框架
        initSpeedFrame();
        // 初始化毕业电机框架
        initGraduationFrame();
        // 初始化橡筋电机1框架
        initElastic1Frame();
        // 初始化橡筋电机2框架
        initElastic2Frame();
        // 初始化圆盘剪刀框架
        initSawFrame();
        // 初始化生克密度框架
        initSinkerFrame();
        // 初始化生克罩摆动框架
        initSinkerAngularFrame();
        // 初始化节约数据框架
        initEconomizationFrame();

        // 翻页按钮事件处理
        connect( ui->pbtn_prev, &QPushButton::clicked, this, &CraftParamForm::onPrevPageClicked );
        connect( ui->pbtn_next, &QPushButton::clicked, this, &CraftParamForm::onNextPageClicked );

        // 标签页切换事件处理
        connect( ui->stackedWidget_small, &QStackedWidget::currentChanged, this, &CraftParamForm::onTabWidgetCurrentChanged );

        connect( ui->lbl_SOKFile, &ClickableLabel::doubleClicked, this, [&]() {
            if ( fileSelectForm == nullptr )
            {
                this->fileSelectForm = new FileSelectForm( nullptr, SOK_DIR, 0 );
            }
            this->fileSelectForm->show();
            connect( this->fileSelectForm, &FileSelectForm::itemSelected, this, &CraftParamForm::onFileNameClicked );
            connect( this->fileSelectForm, &FileSelectForm::finished, this, [&]() { this->fileSelectForm = nullptr; } );
        } );

        connect( ui->lbl_CFTFile, &ClickableLabel::doubleClicked, this, [&]() {
            if ( fileSelectForm == nullptr )
            {
                this->fileSelectForm = new FileSelectForm( nullptr, CFT_DIR, 1 );
            }
            this->fileSelectForm->show();
            connect( this->fileSelectForm, &FileSelectForm::itemSelected, this, &CraftParamForm::onFileNameClicked );
            connect( this->fileSelectForm, &FileSelectForm::finished, this, [&]() { this->fileSelectForm = nullptr; } );
        } );

        // pbtn_speed_inc和pbtn_speed_dec事件处理
        connect( ui->pbtn_speed_inc, &QPushButton::clicked, this, &CraftParamForm::onSpeedIncClicked );
        connect( ui->pbtn_speed_dec, &QPushButton::clicked, this, &CraftParamForm::onSpeedDecClicked );

        // 检查ini配置文件中lastCraft是否存在，否则则检查lastProgram，然后执行onFileNameClicked
        QString lastCraftFile = mainData->configManager->getLastCraftFile();
        if ( !lastCraftFile.isEmpty() )
        {
            onFileNameClicked( lastCraftFile );
        }
        else
        {
            QString lastProgramFile = mainData->configManager->getLastProgramFile();
            if ( !lastProgramFile.isEmpty() )
            {
                onFileNameClicked( lastProgramFile );
            }
        }

        isCraftPageInited = true;
    }
    ui->le_craft_size->setText( QString( "尺寸%1" ).arg( QString::number( currentSize ) ) );
    updatePageButtonStatus();
}

void CraftParamForm::showCraftParams()
{
    if ( craftParams.graduationMotorParam.size() == 0 )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "没有选择SOK文件或CFT文件" ) );
        return;
    }
    showCraftSpeed();
    showCraftGraduation();
    showCraftElastic();
    showCraftSinker();
    showCraftEconomization();
}

void CraftParamForm::showCraftSpeed()
{
    // 检查数据是否为空
    if ( craftParams.speedParam.isEmpty() )
    {
        return;
    }

    // 重置当前页和总页数
    currentSpeedPage = 0;

    // 更新页面显示
    updateSpeedPage();
}

void CraftParamForm::showCraftElastic()
{
    // 检查数据是否为空
    if ( craftParams.elasticMotorParam.isEmpty() )
    {
        return;
    }

    // 橡筋电机1
    // 重置当前页和总页数
    currentElastic1Page = 0;

    // 更新页面显示
    updateElastic1Page();

    // 橡筋电机2
    // 重置当前页和总页数
    currentElastic2Page = 0;

    // 更新页面显示
    updateElastic2Page();

    // 圆盘剪刀
    // 重置当前页和总页数
    currentSawPage = 0;

    // 更新页面显示
    updateSawPage();
}

void CraftParamForm::showCraftGraduation()
{
    // 检查数据是否为空
    if ( craftParams.graduationMotorParam.isEmpty() )
    {
        return;
    }

    // 重置当前页和总页数
    currentGraduationPage = 0;

    // 更新页面显示
    updateGraduationPage();
}

void CraftParamForm::showCraftSinker()
{
    // 检查数据是否为空
    if ( craftParams.sinkerMotorParam.isEmpty() )
    {
        return;
    }

    // 生克密度
    // 重置当前页和总页数
    currentSinkerPage = 0;

    // 更新页面显示
    updateSinkerPage();

    // 生克罩摆动
    // 重置当前页和总页数
    currentSinkerAngularPage = 0;

    // 更新页面显示
    updateSinkerAngularPage();
}

void CraftParamForm::showCraftEconomization()
{
    // 检查数据是否为空
    if ( craftParams.economizationParam.isEmpty() )
    {
        return;
    }

    // 重置当前页和总页数
    currentEconomizationPage = 0;

    // 更新页面显示
    updateEconomizationPage();
}

void CraftParamForm::onNumberInputFormFinished( QString value )
{
    if ( tableIndex == 1 )
    {
        // 更新数据模型
        craftParams.speedParam[ tableEditRowIndex ]->speed_new = value.toInt();

        // 更新界面显示
        updateSpeedPage();
    }
    else if ( tableIndex == 2 )
    {
        // 毕业电机表格
        if ( tableEditColIndex == 7 )
        {  // N起始值列
            // 更新数据模型
            craftParams.graduationMotorParam[ tableEditRowIndex ]->cylinderStart[ currentSize - 1 ] = value.toInt();

            // 看craftParams.graduationMotorParam[tableEditRowIndex]的graduationId是否为'A'~'M'
            char graduationId = craftParams.graduationMotorParam[ tableEditRowIndex ]->graduationId;
            if ( graduationId >= 'A' && graduationId <= 'M' )
            {
                for ( int i = 0; i < craftParams.graduationMotorParam.size(); i++ )
                {
                    if ( craftParams.graduationMotorParam[ i ]->graduationId == graduationId && i != tableEditRowIndex && !craftParams.graduationMotorParam[ i ]->prohibitsValueChange )
                    {

                        // 更新其他相同ID的数据
                        craftParams.graduationMotorParam[ i ]->cylinderStart[ currentSize - 1 ] = value.toInt();
                    }
                }
            }
        }
        else if ( tableEditColIndex == 8 )
        {  // N结束值列
            // 更新数据模型
            craftParams.graduationMotorParam[ tableEditRowIndex ]->cylinderEnd[ currentSize - 1 ] = value.toInt();

            // 看craftParams.graduationMotorParam[tableEditRowIndex]的graduationId是否为'A'~'M'
            char graduationId = craftParams.graduationMotorParam[ tableEditRowIndex ]->graduationId;
            if ( graduationId >= 'A' && graduationId <= 'M' )
            {
                for ( int i = 0; i < craftParams.graduationMotorParam.size(); i++ )
                {
                    if ( craftParams.graduationMotorParam[ i ]->graduationId == graduationId && i != tableEditRowIndex && !craftParams.graduationMotorParam[ i ]->prohibitsValueChange )
                    {

                        // 更新其他相同ID的数据
                        craftParams.graduationMotorParam[ i ]->cylinderEnd[ currentSize - 1 ] = value.toInt();
                    }
                }
            }
        }

        // 更新界面显示
        updateGraduationPage();
    }
    else if ( tableIndex == 3 )
    {
        // 橡筋电机1表格
        int dataIndex = currentElastic1Page * ITEMS_PER_PAGE + tableEditRowIndex;
        if ( dataIndex < craftParams.elasticMotorParam.size() )
        {
            if ( tableEditColIndex == 6 )
            {  // N起始值列
                // 更新数据模型
                craftParams.elasticMotorParam[ dataIndex ]->elasticStart1[ currentSize - 1 ] = value.toInt();

                // 更新界面显示
                elastic1StartNewEdits[ tableEditRowIndex ]->setText( value );
            }
            else if ( tableEditColIndex == 7 )
            {  // N结束值列
                // 更新数据模型
                craftParams.elasticMotorParam[ dataIndex ]->elasticEnd1[ currentSize - 1 ] = value.toInt();

                // 更新界面显示
                elastic1EndNewEdits[ tableEditRowIndex ]->setText( value );
            }

            // 更新页面显示
            updateElastic1Page();
        }
    }
    else if ( tableIndex == 4 )
    {
        // 橡筋电机2表格
        int dataIndex = currentElastic2Page * ITEMS_PER_PAGE + tableEditRowIndex;
        if ( dataIndex < craftParams.elasticMotorParam.size() )
        {
            if ( tableEditColIndex == 6 )
            {  // N起始值列
                // 更新数据模型
                craftParams.elasticMotorParam[ dataIndex ]->elasticStart2[ currentSize - 1 ] = value.toInt();

                // 更新界面显示
                elastic2StartNewEdits[ tableEditRowIndex ]->setText( value );
            }
            else if ( tableEditColIndex == 7 )
            {  // N结束值列
                // 更新数据模型
                craftParams.elasticMotorParam[ dataIndex ]->elasticEnd2[ currentSize - 1 ] = value.toInt();

                // 更新界面显示
                elastic2EndNewEdits[ tableEditRowIndex ]->setText( value );
            }

            // 更新页面显示
            updateElastic2Page();
        }
    }
    else if ( tableIndex == 5 )
    {
        // 圆盘剪刀表格
        int dataIndex = currentSawPage * ITEMS_PER_PAGE + tableEditRowIndex;
        if ( dataIndex < craftParams.elasticMotorParam.size() )
        {
            if ( tableEditColIndex == 6 )
            {  // N起始值列
                // 更新数据模型
                craftParams.elasticMotorParam[ dataIndex ]->sawStart[ currentSize - 1 ] = value.toInt();

                // 更新界面显示
                sawStartNewEdits[ tableEditRowIndex ]->setText( value );
            }
            else if ( tableEditColIndex == 7 )
            {  // N结束值列
                // 更新数据模型
                craftParams.elasticMotorParam[ dataIndex ]->sawEnd[ currentSize - 1 ] = value.toInt();

                // 更新界面显示
                sawEndNewEdits[ tableEditRowIndex ]->setText( value );
            }

            // 更新页面显示
            updateSawPage();
        }
    }
    else if ( tableIndex == 6 )
    {
        // 生克密度表格
        int dataIndex = currentSinkerPage * ITEMS_PER_PAGE + tableEditRowIndex;
        if ( dataIndex < craftParams.sinkerMotorParam.size() )
        {
            if ( tableEditColIndex == 7 )
            {  // N起始值列
                // 更新数据模型
                craftParams.sinkerMotorParam[ dataIndex ]->sinkerStart[ currentSize - 1 ] = value.toInt();

                // 更新相同ID的数据
                char sinkerId = craftParams.sinkerMotorParam[ dataIndex ]->sinkerId;
                if ( sinkerId >= 'A' && sinkerId <= 'M' )
                {
                    for ( int i = 0; i < craftParams.sinkerMotorParam.size(); i++ )
                    {
                        if ( craftParams.sinkerMotorParam[ i ]->sinkerId == sinkerId && i != dataIndex && !craftParams.sinkerMotorParam[ i ]->prohibitsSinkerValueChange )
                        {
                            craftParams.sinkerMotorParam[ i ]->sinkerStart[ currentSize - 1 ] = value.toInt();
                        }
                    }
                }

                // 更新界面显示
                sinkerStartNewEdits[ tableEditRowIndex ]->setText( value );
            }
            else if ( tableEditColIndex == 8 )
            {  // N结束值列
                // 更新数据模型
                craftParams.sinkerMotorParam[ dataIndex ]->sinkerEnd[ currentSize - 1 ] = value.toInt();

                // 更新相同ID的数据
                char sinkerId = craftParams.sinkerMotorParam[ dataIndex ]->sinkerId;
                if ( sinkerId >= 'A' && sinkerId <= 'M' )
                {
                    for ( int i = 0; i < craftParams.sinkerMotorParam.size(); i++ )
                    {
                        if ( craftParams.sinkerMotorParam[ i ]->sinkerId == sinkerId && i != dataIndex && !craftParams.sinkerMotorParam[ i ]->prohibitsSinkerValueChange )
                        {
                            craftParams.sinkerMotorParam[ i ]->sinkerEnd[ currentSize - 1 ] = value.toInt();
                        }
                    }
                }

                // 更新界面显示
                sinkerEndNewEdits[ tableEditRowIndex ]->setText( value );
            }

            // 更新页面显示
            updateSinkerPage();
        }
    }
    else if ( tableIndex == 7 )
    {
        // 生克罩摆动表格
        int dataIndex = currentSinkerAngularPage * ITEMS_PER_PAGE + tableEditRowIndex;
        if ( dataIndex < craftParams.sinkerMotorParam.size() )
        {
            if ( tableEditColIndex == 7 )
            {  // N起始值列
                // 更新数据模型
                craftParams.sinkerMotorParam[ dataIndex ]->angularStart[ currentSize - 1 ] = value.toInt();

                // 更新相同ID的数据
                char sinkerId = craftParams.sinkerMotorParam[ dataIndex ]->sinkerId;
                if ( sinkerId >= 'A' && sinkerId <= 'M' )
                {
                    for ( int i = 0; i < craftParams.sinkerMotorParam.size(); i++ )
                    {
                        if ( craftParams.sinkerMotorParam[ i ]->sinkerId == sinkerId && i != dataIndex && !craftParams.sinkerMotorParam[ i ]->prohibitsAngularValueChange )
                        {
                            craftParams.sinkerMotorParam[ i ]->angularStart[ currentSize - 1 ] = value.toInt();
                        }
                    }
                }

                // 更新界面显示
                sinkerAngularStartNewEdits[ tableEditRowIndex ]->setText( value );
            }
            else if ( tableEditColIndex == 8 )
            {  // N结束值列
                // 更新数据模型
                craftParams.sinkerMotorParam[ dataIndex ]->angularEnd[ currentSize - 1 ] = value.toInt();

                // 更新相同ID的数据
                char sinkerId = craftParams.sinkerMotorParam[ dataIndex ]->sinkerId;
                if ( sinkerId >= 'A' && sinkerId <= 'M' )
                {
                    for ( int i = 0; i < craftParams.sinkerMotorParam.size(); i++ )
                    {
                        if ( craftParams.sinkerMotorParam[ i ]->sinkerId == sinkerId && i != dataIndex && !craftParams.sinkerMotorParam[ i ]->prohibitsAngularValueChange )
                        {
                            craftParams.sinkerMotorParam[ i ]->angularEnd[ currentSize - 1 ] = value.toInt();
                        }
                    }
                }

                // 更新界面显示
                sinkerAngularEndNewEdits[ tableEditRowIndex ]->setText( value );
            }

            // 更新页面显示
            updateSinkerAngularPage();
        }
    }
    else if ( tableIndex == 8 )
    {
        // 节约数据表格
        if ( tableEditRowIndex >= 0 && tableEditRowIndex < craftParams.economizationParam.size() )
        {
            // 更新数据模型
            craftParams.economizationParam[ tableEditRowIndex ]->economizations[ currentSize - 1 ] = value.toInt();

            // 更新界面显示
            updateEconomizationPage();
        }
    }
}

void CraftParamForm::saveCraftParams()
{
    if ( inputDialog == nullptr )
    {
        inputDialog = new InputDialog( this, "输入文本", "文件名:" );
    }
    else
    {
        inputDialog->changeHint( "文件名:" );
    }

    int result = inputDialog->exec();
    if ( result == QDialog::Accepted )
    {
        QString inputText = inputDialog->getInputText();
        if ( !inputText.isEmpty() )
        {
            // 确保目录存在
            QDir dir( CFT_DIR );
            if ( !dir.exists() )
            {
                if ( !dir.mkpath( "." ) )
                {
                    QMessageBox::warning( nullptr, "错误提示", QString( "无法创建目录: %1" ).arg( CFT_DIR ) );
                    return;
                }
            }

            // 构建完整的文件路径
            QString filePath = CFT_DIR + inputText + ".CFT";

            // 如果文件已存在，询问是否覆盖
            if ( QFile::exists( filePath ) )
            {
                QMessageBox::StandardButton reply;
                reply = QMessageBox::question( nullptr, "确认覆盖", "文件已存在，是否覆盖？", QMessageBox::Yes | QMessageBox::No );

                if ( reply == QMessageBox::No )
                {
                    return;
                }
            }

            // 尝试打开文件
            QFile file( filePath );
            if ( !file.open( QIODevice::WriteOnly ) )
            {
                QMessageBox::warning( nullptr, "错误提示", QString( "文件保存失败: %1\n错误: %2" ).arg( filePath ).arg( file.errorString() ) );
                return;
            }

            // 文件打开成功，继续保存数据
            QDataStream out( &file );
            out.setByteOrder( QDataStream::LittleEndian );

            // 保存craftParam到文件
            // 保存craftParams.speedParam，先输出size，然后输出每个speedParam
            out << craftParams.speedParam.size();
            for ( const auto& param : craftParams.speedParam )
            {
                out << param->speed << param->speed_new;
            }
            // 保存craftParams.graduationMotorParam，先输出size，然后输出每个graduationMotorParam
            out << craftParams.graduationMotorParam.size();
            for ( const auto& param : craftParams.graduationMotorParam )
            {
                out.writeRawData( param->blockName, 21 );  // 使用writeRawData写入固定长度字符数组
                out << param->stepStart << param->stepEnd;
                out << static_cast< qint8 >( param->graduationId );
                out << param->prohibitsValueChange;
                for ( int i = 0; i < 8; i++ )
                {
                    out << param->cylinderStart[ i ] << param->cylinderEnd[ i ];
                }
            }
            // 保存craftParams.elasticMotorParam，先输出size，然后输出每个elasticMotorParam
            out << craftParams.elasticMotorParam.size();
            for ( const auto& param : craftParams.elasticMotorParam )
            {
                out.writeRawData( param->blockName, 21 );  // 使用writeRawData写入固定长度字符数组
                out << param->stepStart << param->stepEnd << param->prohibitsValueChange1 << param->prohibitsValueChange2 << param->prohibitsSawValueChange;
                for ( int i = 0; i < 8; i++ )
                {
                    out << param->elasticStart1[ i ] << param->elasticEnd1[ i ] << param->elasticStart2[ i ] << param->elasticEnd2[ i ] << param->sawStart[ i ] << param->sawEnd[ i ];
                }
            }
            // 保存craftParams.sinkerMotorParam，先输出size，然后输出每个sinkerMotorParam
            out << craftParams.sinkerMotorParam.size();
            for ( const auto& param : craftParams.sinkerMotorParam )
            {
                out.writeRawData( param->blockName, 21 );  // 使用writeRawData写入固定长度字符数组
                out << param->stepStart << param->stepEnd;
                out << static_cast< qint8 >( param->sinkerId );
                out << param->prohibitsAngularValueChange << param->prohibitsSinkerValueChange;
                for ( int i = 0; i < 8; i++ )
                {
                    out << param->angularStart[ i ] << param->angularEnd[ i ] << param->sinkerStart[ i ] << param->sinkerEnd[ i ];
                }
            }
            file.close();
            ui->lbl_CFTFile->setText( inputText + ".CFT" );
            ui->lbl_SOKFile->setText( "SOK文件" );

            QMessageBox::information( nullptr, "提示", "文件保存成功" );
        }
    }
}

void CraftParamForm::onFileNameClicked( QString name )
{
    // 获取文件扩展名
    QFileInfo fileInfo( name );
    QString   extension = fileInfo.suffix().toLower();

    if ( extension == "cft" )
    {
        // 原有的 CFT 文件处理逻辑
        QFile file( name );
        if ( !file.open( QIODevice::ReadOnly ) )
        {
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "文件打开失败" ) );
            return;
        }
        // 清空craftParams
        craftParams.sinkerMotorParam.clear();
        craftParams.elasticMotorParam.clear();
        craftParams.graduationMotorParam.clear();
        craftParams.speedParam.clear();

        QDataStream in( &file );
        in.setByteOrder( QDataStream::LittleEndian );
        // 读取craftParams.speedParam，先读取size，然后读取每个speedParam
        quint32 size;
        in >> size;
        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< SpeedParam > item = QSharedPointer< SpeedParam >( new SpeedParam() );
            in >> item->speed >> item->speed_new;
            craftParams.speedParam.append( item );
        }
        // 读取craftParams.graduationMotorParam，先读取size，然后读取每个graduationMotorParam
        in >> size;
        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< GraduationMotorParamItem > item = QSharedPointer< GraduationMotorParamItem >( new GraduationMotorParamItem() );
            in.readRawData( item->blockName, 21 );  // 使用readRawData读取固定长度字符数组
            in >> item->stepStart >> item->stepEnd;
            qint8 gradId;
            in >> gradId;
            item->graduationId = static_cast< char >( gradId );
            in >> item->prohibitsValueChange;
            for ( int j = 0; j < 8; j++ )
            {
                in >> item->cylinderStart[ j ] >> item->cylinderEnd[ j ];
            }
            craftParams.graduationMotorParam.append( item );
        }
        // 读取craftParams.elasticMotorParam，先读取size，然后读取每个elasticMotorParam
        in >> size;
        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< ElasticMotorParamItem > item = QSharedPointer< ElasticMotorParamItem >( new ElasticMotorParamItem() );
            in.readRawData( item->blockName, 21 );  // 使用readRawData读取固定长度字符数组
            in >> item->stepStart >> item->stepEnd >> item->prohibitsValueChange1 >> item->prohibitsValueChange2 >> item->prohibitsSawValueChange;
            for ( int j = 0; j < 8; j++ )
            {
                in >> item->elasticStart1[ j ] >> item->elasticEnd1[ j ] >> item->elasticStart2[ j ] >> item->elasticEnd2[ j ] >> item->sawStart[ j ] >> item->sawEnd[ j ];
            }
            craftParams.elasticMotorParam.append( item );
        }
        // 读取craftParams.sinkerMotorParam，先读取size，然后读取每个sinkerMotorParam
        in >> size;
        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< SinkerMotorParamItem > item = QSharedPointer< SinkerMotorParamItem >( new SinkerMotorParamItem() );
            in.readRawData( item->blockName, 21 );  // 使用readRawData读取固定长度字符数组
            in >> item->stepStart >> item->stepEnd;
            qint8 sinkId;
            in >> sinkId;
            item->sinkerId = static_cast< char >( sinkId );
            in >> item->prohibitsAngularValueChange >> item->prohibitsSinkerValueChange;
            for ( int j = 0; j < 8; j++ )
            {
                in >> item->angularStart[ j ] >> item->angularEnd[ j ] >> item->sinkerStart[ j ] >> item->sinkerEnd[ j ];
            }
            craftParams.sinkerMotorParam.append( item );
        }
        file.close();
        ui->lbl_CFTFile->setText( fileInfo.fileName() );
    }
    else if ( extension == "sok" )
    {
        // 新增的 SOK 文件处理逻辑
        QFile file( name );
        if ( !file.open( QIODevice::ReadOnly ) )
        {
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "文件打开失败" ) );
            return;
        }

        QByteArray data = file.readAll();
        file.close();

        // 创建 SokParser 实例
        SokParser sokParser;

        // 获取三种电机参数
        auto graduationParams = sokParser.getGraduationMotorParamVector( &data );
        auto elasticParams    = sokParser.getElasticMotorParamVector( &data );
        auto sinkerParams     = sokParser.getSinkerMotorParamVector( &data );

        // 修正排序语句，正确处理 QSharedPointer
        std::sort( graduationParams.begin(), graduationParams.end(),
                   []( const QSharedPointer< SokParser::GraduationMotorParamItem >& a, const QSharedPointer< SokParser::GraduationMotorParamItem >& b ) { return a->stepStart < b->stepStart; } );

        std::sort( elasticParams.begin(), elasticParams.end(),
                   []( const QSharedPointer< SokParser::ElasticMotorParamItem >& a, const QSharedPointer< SokParser::ElasticMotorParamItem >& b ) { return a->stepStart < b->stepStart; } );

        std::sort( sinkerParams.begin(), sinkerParams.end(),
                   []( const QSharedPointer< SokParser::SinkerMotorParamItem >& a, const QSharedPointer< SokParser::SinkerMotorParamItem >& b ) { return a->stepStart < b->stepStart; } );

        // 清除原来的 craftParams
        craftParams.sinkerMotorParam.clear();
        craftParams.elasticMotorParam.clear();
        craftParams.graduationMotorParam.clear();

        // 将电机参数转换并存入 craftParams
        // Convert graduation motor parameters
        for ( const auto& param : graduationParams )
        {
            QSharedPointer< GraduationMotorParamItem > item = QSharedPointer< GraduationMotorParamItem >( new GraduationMotorParamItem() );
            memcpy( item->blockName, param->blockName, 21 );
            item->stepStart            = param->stepStart;
            item->stepEnd              = param->stepEnd;
            item->graduationId         = param->graduationId;
            item->prohibitsValueChange = param->prohibitsValueChange;
            for ( int i = 0; i < 8; i++ )
            {
                item->cylinderStart[ i ] = param->cylinderStart[ i ];
                item->cylinderEnd[ i ]   = param->cylinderEnd[ i ];
            }
            craftParams.graduationMotorParam.append( item );
        }

        // Convert elastic motor parameters
        for ( const auto& param : elasticParams )
        {
            QSharedPointer< ElasticMotorParamItem > item = QSharedPointer< ElasticMotorParamItem >( new ElasticMotorParamItem() );
            memcpy( item->blockName, param->blockName, 21 );
            item->stepStart               = param->stepStart;
            item->stepEnd                 = param->stepEnd;
            item->prohibitsValueChange1   = param->prohibitsValueChange1;
            item->prohibitsValueChange2   = param->prohibitsValueChange2;
            item->prohibitsSawValueChange = param->prohibitsSawValueChange;
            for ( int i = 0; i < 8; i++ )
            {
                item->elasticStart1[ i ] = param->elasticStart1[ i ];
                item->elasticEnd1[ i ]   = param->elasticEnd1[ i ];
                item->elasticStart2[ i ] = param->elasticStart2[ i ];
                item->elasticEnd2[ i ]   = param->elasticEnd2[ i ];
                item->sawStart[ i ]      = param->sawStart[ i ];
                item->sawEnd[ i ]        = param->sawEnd[ i ];
            }
            craftParams.elasticMotorParam.append( item );
        }

        // Convert sinker motor parameters
        for ( const auto& param : sinkerParams )
        {
            QSharedPointer< SinkerMotorParamItem > item = QSharedPointer< SinkerMotorParamItem >( new SinkerMotorParamItem() );
            memcpy( item->blockName, param->blockName, 21 );
            item->stepStart                   = param->stepStart;
            item->stepEnd                     = param->stepEnd;
            item->sinkerId                    = param->sinkerId;
            item->prohibitsAngularValueChange = param->prohibitsAngularValueChange;
            item->prohibitsSinkerValueChange  = param->prohibitsSinkerValueChange;
            for ( int i = 0; i < 8; i++ )
            {
                item->angularStart[ i ] = param->angularStart[ i ];
                item->angularEnd[ i ]   = param->angularEnd[ i ];
                item->sinkerStart[ i ]  = param->sinkerStart[ i ];
                item->sinkerEnd[ i ]    = param->sinkerEnd[ i ];
            }
            craftParams.sinkerMotorParam.append( item );
        }

        // 获取sok链条数据，然后解析出其中的speedParam
        craftParams.speedParam.clear();
        // 文件保存模式，默认是0 顺序，读取文件后判断是顺序还是逆序
        quint8 fileSaveType = 0;
        // 搜索类型 G616，前面05是长度
        const QByteArray searchFor = QByteArray::fromHex( "05 00 47 36 31 36 00" );  // 要搜索的字节序列
        int              index     = data.indexOf( searchFor );                      // 搜索字节序列
        if ( index == -1 )
        {
            qDebug() << "Machine Type not found.";
            return;
        }
        else
        {
            qDebug() << "Machine Type Founded: " << index;
            // 在文件尾部，则说明是逆序
            if ( index >= data.length() - 10 )
            {
                fileSaveType = 1;
            }
        }
        auto chainData = sokParser.GetChainData( &data, fileSaveType, index );
        for ( auto it = chainData.begin(); it != chainData.end(); ++it )
        {
            int stepIndex = it.key();
            for ( const auto& subParam : it.value() )
            {
                if ( subParam->type == 1 )
                {
                    quint16 speed = subParam->ItemUnion.chainSpeedParam.speed;
                    // 查找craftParams.speedParam是否已经存在speed，如果存在，则更新count
                    bool found = false;
                    for ( const auto& speedParam : craftParams.speedParam )
                    {
                        if ( speedParam->speed == speed )
                        {
                            speedParam->count++;
                            found = true;
                            break;
                        }
                    }
                    if ( !found )
                    {
                        QSharedPointer< SpeedParam > item = QSharedPointer< SpeedParam >( new SpeedParam() );
                        item->speed                       = speed;
                        item->count                       = 1;
                        item->speed_new                   = speed;
                        craftParams.speedParam.append( item );
                    }
                }
                // Economization数据获取
                else if ( subParam->type == 2 )
                {
                    QSharedPointer< EconomizationParamItem > item = QSharedPointer< EconomizationParamItem >( new EconomizationParamItem() );
                    item->step_index                              = stepIndex;
                    item->forStep                                 = subParam->ItemUnion.economizationParam.forStep;
                    for ( int i = 0; i < 8; i++ )
                    {
                        item->economizations[ i ] = subParam->ItemUnion.economizationParam.economizations[ i ];
                    }
                    craftParams.economizationParam.append( item );
                }
            }
        }
        // 排序
        std::sort( craftParams.speedParam.begin(), craftParams.speedParam.end(), []( const QSharedPointer< SpeedParam >& a, const QSharedPointer< SpeedParam >& b ) { return a->speed < b->speed; } );
        std::sort( craftParams.economizationParam.begin(), craftParams.economizationParam.end(),
                   []( const QSharedPointer< EconomizationParamItem >& a, const QSharedPointer< EconomizationParamItem >& b ) { return a->step_index < b->step_index; } );

        ui->lbl_SOKFile->setText( fileInfo.fileName() );
    }
    // 更新表格显示
    showCraftParams();
}

void CraftParamForm::onLetterInputDialogFinished( QString text )
{
    // 检查输入是否有效（A-M的大写字母）
    if ( text.length() != 1 || text[ 0 ] < 'A' || text[ 0 ] > 'M' )
    {
        QMessageBox::warning( this, "错误", "请输入A-M之间的大写字母" );
        return;
    }

    // 根据当前的tableIndex处理不同表格的数据
    switch ( tableIndex )
    {
        case 2:  // 毕业表格
            if ( tableEditColIndex == 3 )
            {  // ID列
                // 更新数据模型
                if ( craftParams.graduationMotorParam.size() > tableEditRowIndex )
                {
                    craftParams.graduationMotorParam[ tableEditRowIndex ]->graduationId = text[ 0 ].toLatin1();
                }
                else
                {
                    // 如果blockName是字符数组，确保以null结尾
                    craftParams.graduationMotorParam[ tableEditRowIndex ]->graduationId = '\0';
                }

                // 更新界面显示
                updateGraduationPage();
            }
            break;
        case 6:  // 生克密度表格
            if ( tableEditColIndex == 3 )
            {  // ID列
                // 更新数据模型
                int dataIndex = currentSinkerPage * ITEMS_PER_PAGE + tableEditRowIndex;
                if ( dataIndex < craftParams.sinkerMotorParam.size() )
                {
                    craftParams.sinkerMotorParam[ dataIndex ]->sinkerId = text[ 0 ].toLatin1();

                    // 更新界面显示
                    sinkerIdEdits[ tableEditRowIndex ]->setText( text );

                    // 更新页面显示
                    updateSinkerPage();
                }
            }
            break;
        case 7:  // 生克罩摆动表格
            if ( tableEditColIndex == 3 )
            {  // ID列
                // 更新数据模型
                int dataIndex = currentSinkerAngularPage * ITEMS_PER_PAGE + tableEditRowIndex;
                if ( dataIndex < craftParams.sinkerMotorParam.size() )
                {
                    craftParams.sinkerMotorParam[ dataIndex ]->sinkerId = text[ 0 ].toLatin1();

                    // 更新界面显示
                    sinkerAngularIdEdits[ tableEditRowIndex ]->setText( text );

                    // 更新页面显示
                    updateSinkerAngularPage();
                }
            }
            break;
        // 如果需要处理其他表格，可以添加更多case
        default:
            qDebug() << "Unhandled table index:" << tableIndex;
            break;
    }

    // 关闭对话框
    if ( letterInputDialog )
    {
        letterInputDialog->close();
    }
}

// 更新速度页面
void CraftParamForm::updateSpeedPage()
{
    // 计算总页数
    totalSpeedPages = ( craftParams.speedParam.size() + ITEMS_PER_PAGE - 1 ) / ITEMS_PER_PAGE;

    // 确保当前页在有效范围内
    if ( currentSpeedPage >= totalSpeedPages )
    {
        currentSpeedPage = totalSpeedPages - 1;
    }
    if ( currentSpeedPage < 0 )
    {
        currentSpeedPage = 0;
    }

    // 计算当前页的起始索引
    int startIndex = currentSpeedPage * ITEMS_PER_PAGE;

    // 更新控件显示
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        int dataIndex = startIndex + i;

        if ( dataIndex < craftParams.speedParam.size() )
        {
            // 有数据，显示
            indexLabels[ i ]->setText( QString::number( dataIndex + 1 ) );
            speedLabels[ i ]->setText( QString::number( craftParams.speedParam[ dataIndex ]->speed ) );
            countLabels[ i ]->setText( QString::number( craftParams.speedParam[ dataIndex ]->count ) );
            speedNewEdits[ i ]->setText( QString::number( craftParams.speedParam[ dataIndex ]->speed_new ) );

            // 显示所有控件并设置正常样式
            indexLabels[ i ]->setVisible( true );
            speedLabels[ i ]->setVisible( true );
            countLabels[ i ]->setVisible( true );
            speedNewEdits[ i ]->setVisible( true );

            // 恢复正常样式
            indexLabels[ i ]->setStyleSheet( "" );
            speedLabels[ i ]->setStyleSheet( "" );
            countLabels[ i ]->setStyleSheet( "" );
            speedNewEdits[ i ]->setStyleSheet( "" );
        }
        else
        {
            // 无数据，但保持控件可见以占据空间
            indexLabels[ i ]->setText( "" );
            speedLabels[ i ]->setText( "" );
            countLabels[ i ]->setText( "" );
            speedNewEdits[ i ]->setText( "" );

            // 控件保持可见，但设置为透明
            indexLabels[ i ]->setVisible( true );
            speedLabels[ i ]->setVisible( true );
            countLabels[ i ]->setVisible( true );
            speedNewEdits[ i ]->setVisible( true );

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            indexLabels[ i ]->setStyleSheet( transparentStyle );
            speedLabels[ i ]->setStyleSheet( transparentStyle );
            countLabels[ i ]->setStyleSheet( transparentStyle );
            speedNewEdits[ i ]->setStyleSheet( transparentStyle );
        }
    }
}

// 标签页切换事件
void CraftParamForm::onTabWidgetCurrentChanged( int index )
{
    // 更新翻页按钮状态
    updatePageButtonStatus();
}

// 更新翻页按钮状态
void CraftParamForm::updatePageButtonStatus()
{
    int currentIndex = ui->stackedWidget_small->currentIndex();

    // 根据当前选中的标签页决定翻页按钮的状态
    if ( currentIndex == 0 )  // 速度标签页
    {
        ui->pbtn_prev->setEnabled( currentSpeedPage > 0 );
        ui->pbtn_next->setEnabled( currentSpeedPage < totalSpeedPages - 1 );
    }
    else if ( currentIndex == 1 )  // 毕业电机标签页
    {
        ui->pbtn_prev->setEnabled( currentGraduationPage > 0 );
        ui->pbtn_next->setEnabled( currentGraduationPage < totalGraduationPages - 1 );
    }
    else if ( currentIndex == 2 )  // 橡筋电机标签页
    {
        ui->pbtn_prev->setEnabled( currentElastic1Page > 0 );
        ui->pbtn_next->setEnabled( currentElastic1Page < totalElastic1Pages - 1 );
    }
    else if ( currentIndex == 3 )  // 橡筋电机2标签页
    {
        ui->pbtn_prev->setEnabled( currentElastic2Page > 0 );
        ui->pbtn_next->setEnabled( currentElastic2Page < totalElastic2Pages - 1 );
    }
    else if ( currentIndex == 4 )  // 圆盘剪刀标签页
    {
        ui->pbtn_prev->setEnabled( currentSawPage > 0 );
        ui->pbtn_next->setEnabled( currentSawPage < totalSawPages - 1 );
    }
    else if ( currentIndex == 5 )  // 生克密度标签页
    {
        ui->pbtn_prev->setEnabled( currentSinkerPage > 0 );
        ui->pbtn_next->setEnabled( currentSinkerPage < totalSinkerPages - 1 );
    }
    else if ( currentIndex == 6 )  // 生克罩摆动标签页
    {
        ui->pbtn_prev->setEnabled( currentSinkerAngularPage > 0 );
        ui->pbtn_next->setEnabled( currentSinkerAngularPage < totalSinkerAngularPages - 1 );
    }
    else if ( currentIndex == 7 )  // 节约数据标签页
    {
        ui->pbtn_prev->setEnabled( currentEconomizationPage > 0 );
        ui->pbtn_next->setEnabled( currentEconomizationPage < totalEconomizationPages - 1 );
    }
    else
    {
        // 其他标签页禁用翻页按钮
        ui->pbtn_prev->setEnabled( false );
        ui->pbtn_next->setEnabled( false );
    }
}

// 上一页按钮点击
void CraftParamForm::onPrevPageClicked()
{
    // 根据当前选中的标签页决定执行哪个翻页操作
    if ( ui->stackedWidget_small->currentIndex() == 0 )
    {  // 速度标签页
        if ( currentSpeedPage > 0 )
        {
            currentSpeedPage--;
            updateSpeedPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 1 )
    {  // 毕业电机标签页
        if ( currentGraduationPage > 0 )
        {
            currentGraduationPage--;
            updateGraduationPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 2 )
    {  // 橡筋电机1标签页
        if ( currentElastic1Page > 0 )
        {
            currentElastic1Page--;
            updateElastic1Page();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 3 )
    {  // 橡筋电机2标签页
        if ( currentElastic2Page > 0 )
        {
            currentElastic2Page--;
            updateElastic2Page();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 4 )
    {  // 圆盘剪刀标签页
        if ( currentSawPage > 0 )
        {
            currentSawPage--;
            updateSawPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 5 )
    {  // 生克密度标签页
        if ( currentSinkerPage > 0 )
        {
            currentSinkerPage--;
            updateSinkerPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 6 )
    {  // 生克罩摆动标签页
        if ( currentSinkerAngularPage > 0 )
        {
            currentSinkerAngularPage--;
            updateSinkerAngularPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 7 )
    {  // 节约数据标签页
        if ( currentEconomizationPage > 0 )
        {
            currentEconomizationPage--;
            updateEconomizationPage();
        }
    }

    // 更新翻页按钮状态
    updatePageButtonStatus();
}

// 下一页按钮点击
void CraftParamForm::onNextPageClicked()
{
    // 根据当前选中的标签页决定执行哪个翻页操作
    if ( ui->stackedWidget_small->currentIndex() == 0 )
    {  // 速度标签页
        if ( currentSpeedPage < totalSpeedPages - 1 )
        {
            currentSpeedPage++;
            updateSpeedPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 1 )
    {  // 毕业电机标签页
        if ( currentGraduationPage < totalGraduationPages - 1 )
        {
            currentGraduationPage++;
            updateGraduationPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 2 )
    {  // 橡筋电机1标签页
        if ( currentElastic1Page < totalElastic1Pages - 1 )
        {
            currentElastic1Page++;
            updateElastic1Page();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 3 )
    {  // 橡筋电机2标签页
        if ( currentElastic2Page < totalElastic2Pages - 1 )
        {
            currentElastic2Page++;
            updateElastic2Page();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 4 )
    {  // 圆盘剪刀标签页
        if ( currentSawPage < totalSawPages - 1 )
        {
            currentSawPage++;
            updateSawPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 5 )
    {  // 生克密度标签页
        if ( currentSinkerPage < totalSinkerPages - 1 )
        {
            currentSinkerPage++;
            updateSinkerPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 6 )
    {  // 生克罩摆动标签页
        if ( currentSinkerAngularPage < totalSinkerAngularPages - 1 )
        {
            currentSinkerAngularPage++;
            updateSinkerAngularPage();
        }
    }
    else if ( ui->stackedWidget_small->currentIndex() == 7 )
    {  // 节约数据标签页
        if ( currentEconomizationPage < totalEconomizationPages - 1 )
        {
            currentEconomizationPage++;
            updateEconomizationPage();
        }
    }

    // 更新翻页按钮状态
    updatePageButtonStatus();
}
