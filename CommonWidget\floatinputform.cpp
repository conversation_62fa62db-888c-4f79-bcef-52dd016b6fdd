#include "floatinputform.h"
#include "ui_floatinputform.h"
#include <QMessageBox>
#include <QScreen>

FloatInputForm::FloatInputForm( QWidget* parent, QString title, double minValue, double maxValue ) : QWidget( parent ), ui( new Ui::FloatInputForm )
{
    ui->setupUi( this );

    this->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
    this->setWindowTitle( tr( "请输入数值" ) );
    this->setWindowFlags(
        /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    this->ui->lbl_title->setText( title );
    this->_min_Value = minValue;
    this->_max_Value = maxValue;
    this->ui->lbl_range->setText( QString( "[%1,%2]" ).arg( this->_min_Value ).arg( this->_max_Value ) );

    connect( ui->pbtn_0, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_1, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_2, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_3, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_4, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_5, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_6, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_7, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_8, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_9, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_dot, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_minus, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );
    connect( ui->pbtn_backspace, &QPushButton::clicked, this, &FloatInputForm::onBtnClicked );

    // 确定按钮
    connect( ui->pbtn_ok, &QPushButton::clicked, this, [&]() {
        bool  ok;                                              // 用于存储转换结果的布尔变量
        float intValue = ui->lineEdit->text().toFloat( &ok );  // 尝试将字符串转换为整数，并将结果存储在intValue中
        if ( ok )
        {
            if ( intValue < this->_min_Value || intValue > this->_max_Value )
            {
                QMessageBox::information( nullptr, "错误提示", "超出了数值上下限" );
            }
            else
            {
                emit InputFinished( intValue );
                this->close();
            }
        }
        else
        {
            QMessageBox::information( nullptr, "错误提示", "不是正确的数字类型" );
        }
    } );
    connect( ui->pbtn_cancel, &QPushButton::clicked, this, [&]() { this->close(); } );
}

FloatInputForm::~FloatInputForm()
{
    delete ui;
}

void FloatInputForm::showEvent( QShowEvent* event )
{
    this->ui->lineEdit->setText( "" );
}

void FloatInputForm::closeEvent( QCloseEvent* event )
{
    QWidget::closeEvent( event );
    emit finished();
}

void FloatInputForm::onBtnClicked()
{
    QObject* senderObj = sender();
    if ( senderObj == ui->pbtn_0 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '0' );
    else if ( senderObj == ui->pbtn_1 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '1' );
    else if ( senderObj == ui->pbtn_2 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '2' );
    else if ( senderObj == ui->pbtn_3 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '3' );
    else if ( senderObj == ui->pbtn_4 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '4' );
    else if ( senderObj == ui->pbtn_5 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '5' );
    else if ( senderObj == ui->pbtn_6 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '6' );
    else if ( senderObj == ui->pbtn_7 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '7' );
    else if ( senderObj == ui->pbtn_8 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '8' );
    else if ( senderObj == ui->pbtn_9 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '9' );
    else if ( senderObj == ui->pbtn_dot )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '.' );

    else if ( senderObj == ui->pbtn_backspace )
    {
        QString text = this->ui->lineEdit->text();
        text.chop( 1 );
        this->ui->lineEdit->setText( text );
    }
    else if ( senderObj == ui->pbtn_minus )
    {
        if ( this->ui->lineEdit->text().length() == 0 )
        {
            this->ui->lineEdit->setText( "-" );
        }
    }
    // 0 开头
    if ( this->ui->lineEdit->text().length() > 1 && this->ui->lineEdit->text().startsWith( "0" ) )
    {
        this->ui->lineEdit->setText( this->ui->lineEdit->text().remove( 0, 1 ) );
    }
    // -0开头
    else if ( this->ui->lineEdit->text().length() > 2 && this->ui->lineEdit->text().startsWith( "-0" ) )
    {
        this->ui->lineEdit->setText( this->ui->lineEdit->text().remove( 1, 1 ) );
    }
    // .开头
    else if ( this->ui->lineEdit->text().length() > 1 && this->ui->lineEdit->text().startsWith( "." ) )
    {
        this->ui->lineEdit->setText( "0" + this->ui->lineEdit->text() );
    }
}

QString FloatInputForm::getInputText()
{
    return ui->lineEdit->text();
}

void FloatInputForm::setInputText( QString str )
{
    ui->lineEdit->setText( str );
}
