#include "CommCan.h"
#include <QCanBusFrame>
#include <QVariant>
#include <QMetaEnum>

CommCan::CommCan(QObject *parent) : QObject(parent),
    m_canDevice(nullptr),
    m_isConnected(false),
    m_interface("socketcan"),
    m_deviceName("can0"),
    m_bitRate(500000)
{
}

CommCan::~CommCan()
{
    closeCan();
}

bool CommCan::initCan(const QString &interface, const QString &device, int bitRate)
{
    // 如果已经连接，先关闭
    if (m_isConnected) {
        closeCan();
    }
    
    m_interface = interface;
    m_deviceName = device;
    m_bitRate = bitRate;
    
    // 检查是否支持指定的CAN总线接口
    if (!QCanBus::instance()->plugins().contains(interface)) {
        m_lastError = QString("不支持的CAN总线接口: %1").arg(interface);
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        return false;
    }
    
    // 创建CAN总线设备
    QString errorString;
    m_canDevice = QCanBus::instance()->createDevice(interface, device, &errorString);
    
    if (!m_canDevice) {
        m_lastError = QString("创建CAN设备失败: %1").arg(errorString);
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        return false;
    }
    
    // 连接信号槽
    connect(m_canDevice, &QCanBusDevice::framesReceived, this, &CommCan::processReceivedFrames);
    connect(m_canDevice, &QCanBusDevice::errorOccurred, this, &CommCan::handleDeviceError);
    connect(m_canDevice, &QCanBusDevice::stateChanged, this, &CommCan::handleDeviceStateChange);
    
    // 配置设备
    if (!configureDevice()) {
        delete m_canDevice;
        m_canDevice = nullptr;
        return false;
    }
    
    // 连接设备
    if (!m_canDevice->connectDevice()) {
        m_lastError = QString("连接CAN设备失败: %1").arg(m_canDevice->errorString());
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        delete m_canDevice;
        m_canDevice = nullptr;
        return false;
    }
    
    m_isConnected = true;
    qDebug() << "CAN设备连接成功: " << device;
    return true;
}

void CommCan::closeCan()
{
    if (m_canDevice) {
        m_canDevice->disconnectDevice();
        delete m_canDevice;
        m_canDevice = nullptr;
    }
    m_isConnected = false;
}

bool CommCan::sendFrame(quint32 frameId, const QByteArray &payload, bool isExtended)
{
    if (!m_isConnected || !m_canDevice) {
        m_lastError = "CAN设备未连接";
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        return false;
    }
    
    QCanBusFrame frame;
    frame.setFrameId(frameId);
    
    if (isExtended) {
        frame.setExtendedFrameFormat(true);
    } else {
        frame.setExtendedFrameFormat(false);
    }
    
    frame.setPayload(payload);
    
    bool result = m_canDevice->writeFrame(frame);
    if (result) {
        emit frameSent(frame);
    } else {
        m_lastError = QString("发送CAN帧失败: %1").arg(m_canDevice->errorString());
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
    }
    
    return result;
}

bool CommCan::sendRemoteRequestFrame(quint32 frameId, bool isExtended)
{
    if (!m_isConnected || !m_canDevice) {
        m_lastError = "CAN设备未连接";
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        return false;
    }
    
    QCanBusFrame frame;
    frame.setFrameId(frameId);
    
    if (isExtended) {
        frame.setExtendedFrameFormat(true);
    } else {
        frame.setExtendedFrameFormat(false);
    }
    
    frame.setFrameType(QCanBusFrame::RemoteRequestFrame);
    
    bool result = m_canDevice->writeFrame(frame);
    if (result) {
        emit frameSent(frame);
    } else {
        m_lastError = QString("发送远程请求帧失败: %1").arg(m_canDevice->errorString());
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
    }
    
    return result;
}

void CommCan::registerFrameHandler(quint32 frameId, std::function<void(const QCanBusFrame &)> handler)
{
    QMutexLocker locker(&m_mutex);
    m_frameHandlers[frameId] = handler;
}

void CommCan::unregisterFrameHandler(quint32 frameId)
{
    QMutexLocker locker(&m_mutex);
    m_frameHandlers.remove(frameId);
}

void CommCan::unregisterLargeFrameHandler(quint32 frameId)
{
    QMutexLocker locker(&m_mutex);
    m_largeFrameHandlers.remove(frameId);
}

bool CommCan::isConnected() const
{
    return m_isConnected;
}

QString CommCan::getLastError() const
{
    return m_lastError;
}

bool CommCan::setBitRate(int bitRate)
{
    if (!m_canDevice) {
        m_lastError = "CAN设备未创建";
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        return false;
    }
    
    if (m_isConnected) {
        m_lastError = "设置位速率前请先关闭CAN设备";
        qDebug() << m_lastError;
        emit errorOccurred(m_lastError);
        return false;
    }
    
    m_bitRate = bitRate;
    return true;
}

void CommCan::processReceivedFrames()
{
    if (!m_canDevice) {
        return;
    }
    
    // 清理超时的多帧缓冲区
    cleanupTimeoutBuffers();
    
    while (m_canDevice->framesAvailable()) {
        const QCanBusFrame frame = m_canDevice->readFrame();
        
        // 发送接收到帧的信号
        emit frameReceived(frame);
        
        // 处理可能的多帧数据
        QByteArray payload = frame.payload();
        if (payload.size() > 0) {
            quint8 firstByte = static_cast<quint8>(payload.at(0));
            
            // 检查是否是多帧数据
            if (firstByte & (MULTI_FRAME_START | MULTI_FRAME_CONTINUE | MULTI_FRAME_END | MULTI_FRAME_SINGLE)) {
                processMultiFrameData(frame.frameId(), payload);
            }
        }
        
        // 调用注册的帧处理函数
        QMutexLocker locker(&m_mutex);
        auto it = m_frameHandlers.find(frame.frameId());
        if (it != m_frameHandlers.end()) {
            it.value()(frame);
        }
    }
}

void CommCan::processMultiFrameData(quint32 frameId, const QByteArray &payload)
{
    quint8 firstByte = static_cast<quint8>(payload.at(0));
    quint8 sequenceNumber = firstByte & 0x0F; // 取低4位作为序列号
    
    // 单帧数据直接处理
    if (firstByte & MULTI_FRAME_SINGLE) {
        QByteArray data = payload.mid(1); // 去掉第一个字节
        
        QMutexLocker locker(&m_mutex);
        auto it = m_largeFrameHandlers.find(frameId);
        if (it != m_largeFrameHandlers.end()) {
            it.value()(data);
        }
        
        emit largeFrameReceived(frameId, data);
        return;
    }
    
    // 处理多帧数据
    QMutexLocker locker(&m_mutex);
    
    // 如果是起始帧，创建新的缓冲区
    if (firstByte & MULTI_FRAME_START) {
        if (payload.size() < 3) {
            qDebug() << "起始帧数据格式错误";
            return;
        }
        
        // 获取总长度
        quint16 totalLength = static_cast<quint8>(payload.at(1)) | (static_cast<quint8>(payload.at(2)) << 8);
        
        // 计算总帧数
        quint8 totalFrames = (totalLength + 5) / 7;
        if (totalFrames < 2) {
            qDebug() << "数据长度错误";
            return;
        }
        
        // 创建新的缓冲区
        MultiFrameBuffer buffer;
        buffer.data.resize(totalLength);
        buffer.expectedFrames = totalFrames;
        buffer.receivedFrames = 1;
        buffer.receivedFrameFlags.resize(totalFrames);
        buffer.receivedFrameFlags[0] = true;
        buffer.timer.start();
        
        // 复制第一帧的数据
        QByteArray firstFrameData = payload.mid(3);
        for (int i = 0; i < firstFrameData.size() && i < 5; i++) {
            buffer.data[i] = firstFrameData.at(i);
        }
        
        // 存储缓冲区
        m_multiFrameBuffers[frameId] = buffer;
    }
    // 处理中间帧或结束帧
    else if ((firstByte & MULTI_FRAME_CONTINUE) || (firstByte & MULTI_FRAME_END)) {
        // 检查是否存在对应的缓冲区
        if (!m_multiFrameBuffers.contains(frameId)) {
            qDebug() << "收到中间帧或结束帧，但没有对应的起始帧";
            return;
        }
        
        MultiFrameBuffer &buffer = m_multiFrameBuffers[frameId];
        
        // 检查序列号是否有效
        if (sequenceNumber >= buffer.expectedFrames) {
            qDebug() << "序列号超出范围";
            return;
        }
        
        // 检查是否已经接收过该帧
        if (buffer.receivedFrameFlags[sequenceNumber]) {
            qDebug() << "重复接收帧，序列号:" << sequenceNumber;
            return;
        }
        
        // 计算数据在缓冲区中的偏移量
        int offset;
        if (sequenceNumber == 0) {
            offset = 0; // 第一帧
        } else {
            offset = 5 + (sequenceNumber - 1) * 7; // 其他帧
        }
        
        // 复制数据
        QByteArray frameData = payload.mid(1);
        for (int i = 0; i < frameData.size() && offset + i < buffer.data.size(); i++) {
            buffer.data[offset + i] = frameData.at(i);
        }
        
        // 更新接收状态
        buffer.receivedFrameFlags[sequenceNumber] = true;
        buffer.receivedFrames++;
        
        // 检查是否接收完成
        if (buffer.receivedFrames == buffer.expectedFrames) {
            // 所有帧都已接收，发送完整数据
            QByteArray completeData = buffer.data;
            
            // 调用注册的处理函数
            auto it = m_largeFrameHandlers.find(frameId);
            if (it != m_largeFrameHandlers.end()) {
                it.value()(completeData);
            }
            
            emit largeFrameReceived(frameId, completeData);
            
            // 移除缓冲区
            m_multiFrameBuffers.remove(frameId);
        }
    }
}

void CommCan::cleanupTimeoutBuffers()
{
    QMutexLocker locker(&m_mutex);
    QList<quint32> keysToRemove;
    
    for (auto it = m_multiFrameBuffers.begin(); it != m_multiFrameBuffers.end(); ++it) {
        if (it.value().timer.elapsed() > MULTI_FRAME_TIMEOUT) {
            keysToRemove.append(it.key());
        }
    }
    
    for (quint32 key : keysToRemove) {
        qDebug() << "多帧数据接收超时，丢弃帧ID:" << key;
        m_multiFrameBuffers.remove(key);
    }
}

void CommCan::handleDeviceError(QCanBusDevice::CanBusError error)
{
    QMetaEnum metaEnum = QMetaEnum::fromType<QCanBusDevice::CanBusError>();
    QString errorString = QString("CAN设备错误: %1").arg(metaEnum.valueToKey(error));
    
    if (m_canDevice) {
        errorString += QString(" - %1").arg(m_canDevice->errorString());
    }
    
    m_lastError = errorString;
    qDebug() << m_lastError;
    emit errorOccurred(m_lastError);
}

void CommCan::handleDeviceStateChange(QCanBusDevice::CanBusDeviceState state)
{
    QMetaEnum metaEnum = QMetaEnum::fromType<QCanBusDevice::CanBusDeviceState>();
    qDebug() << "CAN设备状态变化: " << metaEnum.valueToKey(state);
    
    emit stateChanged(state);
    
    if (state == QCanBusDevice::ConnectedState) {
        m_isConnected = true;
    } else if (state == QCanBusDevice::UnconnectedState) {
        m_isConnected = false;
    }
}

bool CommCan::configureDevice()
{
    if (!m_canDevice) {
        return false;
    }
    
    // 设置位速率
    if (m_interface == "socketcan") {
        // SocketCAN接口不需要在这里设置位速率，通常在系统级别配置
    } else {
        // 对于其他接口，如PEAK-CAN、Vector等，可以设置位速率
        m_canDevice->setConfigurationParameter(QCanBusDevice::BitRateKey, QVariant(m_bitRate));
    }
    
    // 设置接收自己发送的帧
    m_canDevice->setConfigurationParameter(QCanBusDevice::ReceiveOwnKey, QVariant(false));
    
    // 设置CAN FD标志（如果需要）
    // m_canDevice->setConfigurationParameter(QCanBusDevice::CanFdKey, QVariant(true));
    
    return true;
}
