#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>

// 初始化橡筋电机2框架
void CraftParamForm::initElastic2Frame()
{
    // 清空之前的控件列表
    if (!elastic2IndexLabels.isEmpty()) {
        for (auto label : elastic2IndexLabels) {
            delete label;
        }
        elastic2IndexLabels.clear();
    }
    if (!elastic2NameLabels.isEmpty()) {
        for (auto label : elastic2NameLabels) {
            delete label;
        }
        elastic2NameLabels.clear();
    }
    if (!elastic2StepLabels.isEmpty()) {
        for (auto label : elastic2StepLabels) {
            delete label;
        }
        elastic2StepLabels.clear();
    }
    if (!elastic2ProhibitLabels.isEmpty()) {
        for (auto label : elastic2ProhibitLabels) {
            delete label;
        }
        elastic2ProhibitLabels.clear();
    }
    if (!elastic2StartLabels.isEmpty()) {
        for (auto label : elastic2StartLabels) {
            delete label;
        }
        elastic2StartLabels.clear();
    }
    if (!elastic2EndLabels.isEmpty()) {
        for (auto label : elastic2EndLabels) {
            delete label;
        }
        elastic2EndLabels.clear();
    }
    if (!elastic2StartNewEdits.isEmpty()) {
        for (auto edit : elastic2StartNewEdits) {
            delete edit;
        }
        elastic2StartNewEdits.clear();
    }
    if (!elastic2EndNewEdits.isEmpty()) {
        for (auto edit : elastic2EndNewEdits) {
            delete edit;
        }
        elastic2EndNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout(ui->elastic2Frame);

    // 创建标题行
    QLabel* indexHeader = new QLabel("序号", ui->elastic2Frame);
    QLabel* nameHeader = new QLabel("步骤名称", ui->elastic2Frame);
    QLabel* stepHeader = new QLabel("步", ui->elastic2Frame);
    QLabel* prohibitHeader = new QLabel("P", ui->elastic2Frame);
    QLabel* startHeader = new QLabel("起始值", ui->elastic2Frame);
    QLabel* endHeader = new QLabel("结束值", ui->elastic2Frame);
    QLabel* startNewHeader = new QLabel("N起始值", ui->elastic2Frame);
    QLabel* endNewHeader = new QLabel("N结束值", ui->elastic2Frame);

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet(headerStyle);
    nameHeader->setStyleSheet(headerStyle);
    stepHeader->setStyleSheet(headerStyle);
    prohibitHeader->setStyleSheet(headerStyle);
    startHeader->setStyleSheet(headerStyle);
    endHeader->setStyleSheet(headerStyle);
    startNewHeader->setStyleSheet(headerStyle);
    endNewHeader->setStyleSheet(headerStyle);

    // 设置标题对齐方式
    indexHeader->setAlignment(Qt::AlignCenter);
    nameHeader->setAlignment(Qt::AlignCenter);
    stepHeader->setAlignment(Qt::AlignCenter);
    prohibitHeader->setAlignment(Qt::AlignCenter);
    startHeader->setAlignment(Qt::AlignCenter);
    endHeader->setAlignment(Qt::AlignCenter);
    startNewHeader->setAlignment(Qt::AlignCenter);
    endNewHeader->setAlignment(Qt::AlignCenter);

    // 添加标题到布局
    layout->addWidget(indexHeader, 0, 0);
    layout->addWidget(nameHeader, 0, 1);
    layout->addWidget(stepHeader, 0, 2);
    layout->addWidget(prohibitHeader, 0, 3);
    layout->addWidget(startHeader, 0, 4);
    layout->addWidget(endHeader, 0, 5);
    layout->addWidget(startNewHeader, 0, 6);
    layout->addWidget(endNewHeader, 0, 7);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->elastic2Frame);
        indexLabel->setAlignment(Qt::AlignCenter);
        elastic2IndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);
        
        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->elastic2Frame);
        nameLabel->setAlignment(Qt::AlignCenter);
        elastic2NameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);
        
        // 步标签
        QLabel* stepLabel = new QLabel("", ui->elastic2Frame);
        stepLabel->setAlignment(Qt::AlignCenter);
        elastic2StepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);
        
        // 禁止修改标签
        QLabel* prohibitLabel = new QLabel("", ui->elastic2Frame);
        prohibitLabel->setAlignment(Qt::AlignCenter);
        elastic2ProhibitLabels.append(prohibitLabel);
        layout->addWidget(prohibitLabel, i + 1, 3);
        
        // 起始值标签
        QLabel* startLabel = new QLabel("", ui->elastic2Frame);
        startLabel->setAlignment(Qt::AlignCenter);
        elastic2StartLabels.append(startLabel);
        layout->addWidget(startLabel, i + 1, 4);
        
        // 结束值标签
        QLabel* endLabel = new QLabel("", ui->elastic2Frame);
        endLabel->setAlignment(Qt::AlignCenter);
        elastic2EndLabels.append(endLabel);
        layout->addWidget(endLabel, i + 1, 5);
        
        // 新起始值编辑框
        MyLineEdit* startNewEdit = new MyLineEdit(ui->elastic2Frame);
        startNewEdit->setAlignment(Qt::AlignCenter);
        startNewEdit->setReadOnly(true); // 初始设为只读
        startNewEdit->setProperty("row", i); // 存储行索引
        startNewEdit->setProperty("column", 6); // 存储列索引
        connect(startNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentElastic2Page * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.elasticMotorParam.size() && !craftParams.elasticMotorParam[dataIndex]->prohibitsValueChange2) {
                tableIndex = 4; // 橡筋电机2表格
                tableEditRowIndex = i;
                tableEditColIndex = 6; // 新起始值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "橡筋2新起始值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        elastic2StartNewEdits.append(startNewEdit);
        layout->addWidget(startNewEdit, i + 1, 6);
        
        // 新结束值编辑框
        MyLineEdit* endNewEdit = new MyLineEdit(ui->elastic2Frame);
        endNewEdit->setAlignment(Qt::AlignCenter);
        endNewEdit->setReadOnly(true); // 初始设为只读
        endNewEdit->setProperty("row", i); // 存储行索引
        endNewEdit->setProperty("column", 7); // 存储列索引
        connect(endNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentElastic2Page * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.elasticMotorParam.size() && !craftParams.elasticMotorParam[dataIndex]->prohibitsValueChange2) {
                tableIndex = 4; // 橡筋电机2表格
                tableEditRowIndex = i;
                tableEditColIndex = 7; // 新结束值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "橡筋2新结束值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        elastic2EndNewEdits.append(endNewEdit);
        layout->addWidget(endNewEdit, i + 1, 7);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 1);  // P
    layout->setColumnStretch(4, 2);  // 起始值
    layout->setColumnStretch(5, 2);  // 结束值
    layout->setColumnStretch(6, 2);  // N起始值
    layout->setColumnStretch(7, 2);  // N结束值
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->elastic2Frame->setLayout(layout);
}

// 更新橡筋电机2页面
void CraftParamForm::updateElastic2Page()
{
    // 计算总页数
    totalElastic2Pages = (craftParams.elasticMotorParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;
    
    // 确保当前页在有效范围内
    if (currentElastic2Page >= totalElastic2Pages && totalElastic2Pages > 0) {
        currentElastic2Page = totalElastic2Pages - 1;
    }
    
    // 计算当前页的起始索引
    int startIndex = currentElastic2Page * ITEMS_PER_PAGE;
    
    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.elasticMotorParam.size()) {
            // 有数据，显示
            auto param = craftParams.elasticMotorParam[dataIndex];

            elastic2IndexLabels[i]->setText(QString::number(dataIndex + 1));
            elastic2NameLabels[i]->setText(QString::fromLatin1(param->blockName));
            elastic2StepLabels[i]->setText(QString::number(param->stepStart) + "~" + QString::number(param->stepEnd));
            elastic2ProhibitLabels[i]->setText(param->prohibitsValueChange2 ? "N" : "Y");
            elastic2StartLabels[i]->setText(QString::number(param->elasticStart2[currentSize - 1]));
            elastic2EndLabels[i]->setText(QString::number(param->elasticEnd2[currentSize - 1]));
            elastic2StartNewEdits[i]->setText(QString::number(param->elasticStart2[currentSize - 1]));
            elastic2EndNewEdits[i]->setText(QString::number(param->elasticEnd2[currentSize - 1]));

            // 设置可编辑状态
            bool editable = !param->prohibitsValueChange2;
            elastic2StartNewEdits[i]->setEnabled(editable);
            elastic2EndNewEdits[i]->setEnabled(editable);

            // 设置背景色
            if (editable) {
                elastic2StartNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
                elastic2EndNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
            } else {
                elastic2StartNewEdits[i]->setStyleSheet("");
                elastic2EndNewEdits[i]->setStyleSheet("");
            }

            // 显示所有控件
            elastic2IndexLabels[i]->setVisible(true);
            elastic2NameLabels[i]->setVisible(true);
            elastic2StepLabels[i]->setVisible(true);
            elastic2ProhibitLabels[i]->setVisible(true);
            elastic2StartLabels[i]->setVisible(true);
            elastic2EndLabels[i]->setVisible(true);
            elastic2StartNewEdits[i]->setVisible(true);
            elastic2EndNewEdits[i]->setVisible(true);

            // 恢复正常样式（除了可编辑的控件）
            elastic2IndexLabels[i]->setStyleSheet("");
            elastic2NameLabels[i]->setStyleSheet("");
            elastic2StepLabels[i]->setStyleSheet("");
            elastic2ProhibitLabels[i]->setStyleSheet("");
            elastic2StartLabels[i]->setStyleSheet("");
            elastic2EndLabels[i]->setStyleSheet("");
        } else {
            // 无数据，但保持控件可见以占据空间
            elastic2IndexLabels[i]->setText("");
            elastic2NameLabels[i]->setText("");
            elastic2StepLabels[i]->setText("");
            elastic2ProhibitLabels[i]->setText("");
            elastic2StartLabels[i]->setText("");
            elastic2EndLabels[i]->setText("");
            elastic2StartNewEdits[i]->setText("");
            elastic2EndNewEdits[i]->setText("");

            // 控件保持可见，但设置为透明
            elastic2IndexLabels[i]->setVisible(true);
            elastic2NameLabels[i]->setVisible(true);
            elastic2StepLabels[i]->setVisible(true);
            elastic2ProhibitLabels[i]->setVisible(true);
            elastic2StartLabels[i]->setVisible(true);
            elastic2EndLabels[i]->setVisible(true);
            elastic2StartNewEdits[i]->setVisible(true);
            elastic2EndNewEdits[i]->setVisible(true);

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            elastic2IndexLabels[i]->setStyleSheet(transparentStyle);
            elastic2NameLabels[i]->setStyleSheet(transparentStyle);
            elastic2StepLabels[i]->setStyleSheet(transparentStyle);
            elastic2ProhibitLabels[i]->setStyleSheet(transparentStyle);
            elastic2StartLabels[i]->setStyleSheet(transparentStyle);
            elastic2EndLabels[i]->setStyleSheet(transparentStyle);
            elastic2StartNewEdits[i]->setStyleSheet(transparentStyle);
            elastic2EndNewEdits[i]->setStyleSheet(transparentStyle);

            // 禁用编辑
            elastic2StartNewEdits[i]->setEnabled(false);
            elastic2EndNewEdits[i]->setEnabled(false);
        }
    }

    // 更新翻页按钮状态
    ui->pbtn_prev->setEnabled(currentElastic2Page > 0);
    ui->pbtn_next->setEnabled(currentElastic2Page < totalElastic2Pages - 1);
}
