#ifndef ZHUSUO2CFGDIALOG_H
#define ZHUSUO2CFGDIALOG_H

#include "CommonWidget/mylineedit.h"
#include "FATParser.h"
#include "PatternViewer.h"
#include <QDebug>
#include <QDialog>
#include <QMessageBox>
#include <QTableWidget>

namespace Ui
{
class Zhusuo2CfgDialog;
}

class Zhusuo2CfgDialog : public QDialog
{
    Q_OBJECT

public:
    explicit Zhusuo2CfgDialog( QWidget* parent = nullptr, FATParser::PatternExtraData* patternExtra = nullptr );
    ~Zhusuo2CfgDialog();

private:
    Ui::Zhusuo2CfgDialog*        ui;
    FATParser::PatternExtraData* patternExtraData;
    FATParser::Zhusuo2           zhusuo2[ 4 ];

private slots:
    void onCheckBoxStateChanged( int state );
};

#endif  // ZHUSUO2CFGDIALOG_H
