#include "WorkForm.h"
#include "ui_WorkForm.h"

void WorkForm::initWarnPage()
{
    if ( isWarnPageInited == false )
    {
        // 每个tab的显示
        GenerateWarnWidget( 1 );
        GenerateWarnWidget( 2 );
        GenerateWarnWidget( 3 );
        GenerateWarnWidget( 4 );
        GenerateWarnWidget( 5 );
        GenerateWarnWidget( 6 );
        GenerateWarnWidget( 7 );
        GenerateWarnWidget( 8 );
        GenerateWarnWidget( 9 );
        GenerateWarnWidget( 10 );
        GenerateWarnWidget( 11 );
        connect( ui->WorkFormHome_pbtn_back, &QPushButton::clicked, this, [&]() { ui->stackedWidget->setCurrentIndex( 0 ); } );
        isWarnPageInited = true;
    }
}

void WorkForm::GenerateWarnWidget( quint8 type_id )
{
    quint8 sizeOfline = 10;

    QMap< quint16, ReadWarnConfig::WarnItem > list  = mainData->readWarnConfig->getList()->value( type_id ).items;
    quint16                                   size  = list.size();
    quint16                                   lines = ( size - 1 ) / sizeOfline + 1;

    QHBoxLayout* mainLayout;
    if ( type_id == 1 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn1 );
    else if ( type_id == 2 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn2 );
    else if ( type_id == 3 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn3 );
    else if ( type_id == 4 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn4 );
    else if ( type_id == 5 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn5 );
    else if ( type_id == 6 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn6 );
    else if ( type_id == 7 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn7 );
    else if ( type_id == 8 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn8 );
    else if ( type_id == 9 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn9 );
    else if ( type_id == 10 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn10 );
    else if ( type_id == 11 )
        mainLayout = new QHBoxLayout( this->ui->tabWarn11 );

    // 将 QMap 转换为 QList
    QMap< quint16, ReadWarnConfig::WarnItem >::Iterator iter = list.begin();

    // 一行只显示5个
    for ( quint8 line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
    {
        QVBoxLayout* columnLayoutx = new QVBoxLayout;

        for ( quint8 col = 0; col < sizeOfline; col++ )
        {
            if ( line * sizeOfline + col < size )
            {
                QLabel* label = new QLabel( iter.value().name );
                label->setProperty( "id", iter.key() );
                iter++;
                label->setAlignment( Qt::AlignCenter );
                label->setStyleSheet( "font-size:6pt;"
                                      "background-color: rgb(255, 255 ,255);" );
                label->setWordWrap( true );
                columnLayoutx->addWidget( label );
            }
            else if ( type_id == 7 && line * sizeOfline + col == size )
            {
                servoWarnLabel = new QLabel( "报警号: 000" );
                servoWarnLabel->setAlignment( Qt::AlignCenter );
                servoWarnLabel->setStyleSheet( "font-size:6pt;"
                                               "background-color: rgb(255, 255 ,255);" );
                servoWarnLabel->setWordWrap( true );
                columnLayoutx->addWidget( servoWarnLabel );
            }
            else
            {
                QWidget* widget = new QWidget();
                columnLayoutx->addWidget( widget );
            }
        }
        mainLayout->addLayout( columnLayoutx );
    }
    // eye
    if ( type_id == 10 )
    {
        // 增加EyeWarnView控件列
        QVBoxLayout* columnLayoutx = new QVBoxLayout;
        yarnBreakEWV               = new EyeWarnView( "断纱" );
        yarnNightEWV               = new EyeWarnView( "缠纱" );
        codeTimeoutEWV             = new EyeWarnView( "编号超时" );
        sampleTimeoutEWV           = new EyeWarnView( "采样超时" );
        codeErrorEWV               = new EyeWarnView( "应答错" );
        columnLayoutx->addWidget( yarnBreakEWV );
        columnLayoutx->addWidget( yarnNightEWV );
        columnLayoutx->addWidget( codeTimeoutEWV );
        columnLayoutx->addWidget( sampleTimeoutEWV );
        columnLayoutx->addWidget( codeErrorEWV );
        mainLayout->addLayout( columnLayoutx );
    }
}

void WorkForm::refreshWarnWidget( qint8 type_id, quint16 id, quint8 status )
{
    // 如果不是显示报警页，则不刷新
    if ( ui->stackedWidget->currentIndex() != 1 )
        return;

    QWidget* tabWarn;
    if ( type_id == 1 )
        tabWarn = this->ui->tabWarn1;
    else if ( type_id == 2 )
        tabWarn = this->ui->tabWarn2;
    else if ( type_id == 3 )
        tabWarn = this->ui->tabWarn3;
    else if ( type_id == 4 )
        tabWarn = this->ui->tabWarn4;
    else if ( type_id == 5 )
        tabWarn = this->ui->tabWarn5;
    else if ( type_id == 6 )
        tabWarn = this->ui->tabWarn6;
    else if ( type_id == 7 )
        tabWarn = this->ui->tabWarn7;
    else if ( type_id == 8 )
        tabWarn = this->ui->tabWarn8;
    else if ( type_id == 9 )
        tabWarn = this->ui->tabWarn9;
    else if ( type_id == 10 )
        tabWarn = this->ui->tabWarn10;
    else if ( type_id == 11 )
        tabWarn = this->ui->tabWarn11;

    quint8  sizeOfline = 10;
    quint16 size       = mainData->readWarnConfig->getList()->value( type_id ).items.size();
    quint16 lines      = ( size - 1 ) / sizeOfline + 1;

    for ( quint8 line = 0; line < lines; line++ )
    {
        for ( quint8 col = 0; col < sizeOfline; col++ )
        {
            if ( line * sizeOfline + col < size )
            {
                QLabel* label = dynamic_cast< QLabel* >( tabWarn->layout()->itemAt( line )->layout()->itemAt( col )->widget() );
                if ( label )
                {
                    QVariant var = label->property( "id" );
                    if ( var.isValid() )
                    {
                        quint16 varInt = var.toUInt();
                        if ( varInt == id )
                        {
                            if ( status == 1 )
                            {
                                label->setStyleSheet( "font-size:6pt;"
                                                      "color: rgb(255,255,255);"
                                                      "background-color: rgb(255, 0 ,0);" );
                            }
                            else
                            {
                                label->setStyleSheet( "font-size:6pt;"
                                                      "background-color: rgb(255, 255 ,255);" );
                            }
                            return;
                        }
                    }
                }
            }
        }
    }
}

void WorkForm::refreshAllWarnWidgets()
{
    // 如果不是显示报警页，则不刷新
    if ( ui->stackedWidget->currentIndex() != 1 )
        return;

    // 遍历所有报警类型(1-11)
    const quint8* lastWarnBytes = nullptr;
    int           byteCount     = 0;
    for ( int type_id = 1; type_id <= 11; type_id++ )
    {
        QWidget* tabWarn = nullptr;
        // 获取对应类型的tab页面
        switch ( type_id )
        {
            case 1:
                tabWarn       = this->ui->tabWarn1;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.zhi_qifa;
                byteCount     = sizeof( lastUpInfoMessage->warn.zhi_qifa );
                break;
            case 2:
                tabWarn       = this->ui->tabWarn2;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.triangle;
                byteCount     = sizeof( lastUpInfoMessage->warn.triangle );
                break;
            case 3:
                tabWarn       = this->ui->tabWarn3;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.shuttle;
                byteCount     = sizeof( lastUpInfoMessage->warn.shuttle );
                break;
            case 4:
                tabWarn       = this->ui->tabWarn4;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.needle;
                byteCount     = sizeof( lastUpInfoMessage->warn.needle );
                break;
            case 5:
                tabWarn       = this->ui->tabWarn5;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.input;
                byteCount     = sizeof( lastUpInfoMessage->warn.input );
                break;
            case 6:
                tabWarn       = this->ui->tabWarn6;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.step_motor;
                byteCount     = sizeof( lastUpInfoMessage->warn.step_motor );
                break;
            case 7:
                tabWarn       = this->ui->tabWarn7;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.servo.warns;
                byteCount     = sizeof( lastUpInfoMessage->warn.servo.warns );
                servoWarnLabel->setText( QString( "报警号:%1" ).arg( QString::number( lastUpInfoMessage->warn.servo.error_code ) ) );
                break;
            case 8:
                tabWarn       = this->ui->tabWarn8;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.can;
                byteCount     = sizeof( lastUpInfoMessage->warn.can );
                break;
            case 9:
                tabWarn       = this->ui->tabWarn9;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.seam;
                byteCount     = sizeof( lastUpInfoMessage->warn.seam );
                break;
            case 10:
                tabWarn       = this->ui->tabWarn10;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.photo_eye + 20;
                byteCount     = sizeof( lastUpInfoMessage->warn.photo_eye ) - 20;
                yarnBreakEWV->setValue( lastUpInfoMessage->warn.photo_eye.yarn_break );
                yarnNightEWV->setValue( lastUpInfoMessage->warn.photo_eye.yarnight );
                codeTimeoutEWV->setValue( lastUpInfoMessage->warn.photo_eye.codeimeout );
                sampleTimeoutEWV->setValue( lastUpInfoMessage->warn.photo_eye.sampleimeout );
                codeErrorEWV->setValue( lastUpInfoMessage->warn.photo_eye.code_err );
                break;
            case 11:
                tabWarn       = this->ui->tabWarn11;
                lastWarnBytes = ( const quint8* )&lastUpInfoMessage->warn.software;
                byteCount     = sizeof( lastUpInfoMessage->warn.software );
                break;
        }

        if ( !tabWarn || !tabWarn->layout() )
            continue;

        // 获取当前类型的报警项列表
        const auto& warnItems = mainData->readWarnConfig->getList()->value( type_id ).items;

        // 遍历布局中的每一列
        for ( int col = 0; col < tabWarn->layout()->count(); col++ )
        {
            QLayoutItem* columnItem = tabWarn->layout()->itemAt( col );
            if ( !columnItem || !columnItem->layout() )
                continue;

            // 遍历每一列中的每个控件
            for ( int row = 0; row < columnItem->layout()->count(); row++ )
            {
                QLabel* label = dynamic_cast< QLabel* >( columnItem->layout()->itemAt( row )->widget() );
                if ( !label )
                    continue;

                // 获取控件关联的报警ID
                QVariant idVar = label->property( "id" );
                if ( !idVar.isValid() )
                    continue;

                quint16 warnId = idVar.toUInt();

                // 在报警列表中查找对应ID的报警状态
                if ( warnItems.contains( warnId ) && ( warnId - 1 ) / 8 <= byteCount )
                {
                    // 根据报警状态更新显示样式
                    bool isWarning = ( *( lastWarnBytes + ( warnId - 1 ) / 8 ) >> ( warnId - 1 ) % 8 ) & 0x01;
                    if ( isWarning )
                    {
                        label->setStyleSheet( "font-size:6pt;"
                                              "color: rgb(255,255,255);"
                                              "background-color: rgb(255, 0 ,0);" );
                    }
                    else
                    {
                        label->setStyleSheet( "font-size:6pt;"
                                              "background-color: rgb(255, 255 ,255);" );
                    }
                }
            }
        }
    }
}

void WorkForm::refreshServoWarnLabel( quint8 errorCode )
{
    if ( servoWarnLabel != nullptr )
    {
        servoWarnLabel->setText( QString( "报警号:%1" ).arg( QString::number( errorCode ) ) );
    }
}

void WorkForm::refreshEyeWarnView( quint8 id, quint32 value )
{
    EyeWarnView* eyeWarnView = nullptr;
    switch ( id )
    {
        case 0:
            eyeWarnView = yarnBreakEWV;
            break;
        case 1:
            eyeWarnView = yarnNightEWV;
            break;
        case 2:
            eyeWarnView = codeTimeoutEWV;
            break;
        case 3:
            eyeWarnView = sampleTimeoutEWV;
            break;
        case 4:
            eyeWarnView = codeErrorEWV;
            break;
    }
    if ( eyeWarnView != nullptr )
    {
        eyeWarnView->setValue( value );
    }
}
