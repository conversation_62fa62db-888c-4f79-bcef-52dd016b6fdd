#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/FlowerAndLamaoLoopDialog.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "fileform.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

void FileForm::initPatternPage()
{
    if ( this->isPatternDagePageInited == false )
    {
        connect( this->ui->pbtn_pattern0, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 0;
            this->ShowPatternImage();
        } );
        connect( this->ui->pbtn_pattern1, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 1;
            this->ShowPatternImage();
        } );
        connect( this->ui->pbtn_pattern2, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 2;
            this->ShowPatternImage();
        } );
        connect( this->ui->pbtn_pattern3, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 3;
            this->ShowPatternImage();
        } );
        connect( this->ui->pbtn_pattern4, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 4;
            this->ShowPatternImage();
        } );
        connect( this->ui->pbtn_pattern5, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 5;
            this->ShowPatternImage();
        } );
        connect( this->ui->pbtn_pattern6, &QPushButton::clicked, this, [&]() {
            this->currentPatternType = 6;
            this->ShowPatternImage();
        } );

        connect( this->ui->pbtn_patternScale0, &QPushButton::clicked, this, [&]() { this->ScalePatternImage( 1 ); } );
        connect( this->ui->pbtn_patternScale1, &QPushButton::clicked, this, [&]() { this->ScalePatternImage( 2 ); } );
        connect( this->ui->pbtn_patternScale2, &QPushButton::clicked, this, [&]() { this->ScalePatternImage( 4 ); } );
        connect( this->ui->pbtn_patternScale3, &QPushButton::clicked, this, [&]() { this->ScalePatternImage( 8 ); } );
        connect( this->ui->pbtn_patternScale4, &QPushButton::clicked, this, [&]() { this->ScalePatternImage( 12 ); } );
        connect( this->ui->pbtn_patternScale5, &QPushButton::clicked, this, [&]() { this->ScalePatternImage( 16 ); } );

        // 点击颜色框，出现选择框
        connect( this->ui->widget_patternColor, &MyWidget::mouseRelease, this, &FileForm::onColorPadClicked );
        // save Button
        connect( this->ui->pbtn_patternSave, &QPushButton::clicked, this, [&]() {
            this->patternViewer->saveImage( getPatternImageAddr() );
            restoreImageToPatterData();
            saveDataToMWWFile();
        } );

        connect( this->ui->pbtn_patternLoop, &QPushButton::clicked, this, [&]() {
            if ( this->_flowerAndLamaoLoopDialog != nullptr )
            {
                delete this->_flowerAndLamaoLoopDialog;
                this->_flowerAndLamaoLoopDialog = nullptr;
            }
            this->_flowerAndLamaoLoopDialog = new FlowerAndLamaoLoopDialog( this, this->fatDataMain->PatternExtraData.data() );
            this->_flowerAndLamaoLoopDialog->setWindowTitle( tr( "花型循环/拉毛循环" ) );
            this->_flowerAndLamaoLoopDialog->show();
        } );

        connect( this->ui->pbtn_patternZhusuo, &QPushButton::clicked, this, [&]() {
            if ( this->_zhusuoCfg_comboFrm == nullptr )
            {
                QMap< int, QString >* list = new QMap< int, QString >();
                list->insert( 1, "主梭配置" );
                list->insert( 2, "袜跟换色" );
                list->insert( 3, "主梭2配置" );
                list->insert( 4, "袜跟换色2" );
                this->_zhusuoCfg_comboFrm = new ComboForm( this, list );
                this->_zhusuoCfg_comboFrm->setAttribute( Qt::WA_ShowModal, true );
                this->_zhusuoCfg_comboFrm->setWindowTitle( tr( "选择类型" ) );
                this->_zhusuoCfg_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

                connect( this->_zhusuoCfg_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                    this->_zhusuoCfg_comboFrm->close();
                    qDebug() << id;
                    if ( id == 1 )
                    {
                        if ( this->_zhusuoCfgDialog != nullptr )
                        {
                            delete this->_zhusuoCfgDialog;
                            this->_zhusuoCfgDialog = nullptr;
                        }
                        this->_zhusuoCfgDialog = new ZhusuoCfgDialog( this, this->fatDataMain->PatternExtraData.data() );
                        this->_zhusuoCfgDialog->setWindowTitle( tr( "主梭配置" ) );
                        this->_zhusuoCfgDialog->show();
                    }
                    else if ( id == 3 )
                    {
                        if ( this->_zhusuo2CfgDialog != nullptr )
                        {
                            delete this->_zhusuo2CfgDialog;
                            this->_zhusuo2CfgDialog = nullptr;
                        }
                        this->_zhusuo2CfgDialog = new Zhusuo2CfgDialog( this, this->fatDataMain->PatternExtraData.data() );
                        this->_zhusuo2CfgDialog->setWindowTitle( tr( "主梭2配置" ) );
                        this->_zhusuo2CfgDialog->show();
                    }
                } );
            }
            this->_zhusuoCfg_comboFrm->show();
        } );
        this->isPatternDagePageInited = true;
    }
    this->currentPatternType = 0;
    ShowPatternImage();
}

QString FileForm::getPatternImageAddr()
{
    QString addr = "";
    if ( this->currentPatternType == 0 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "tiansha.png" ) );
    }
    else if ( this->currentPatternType == 1 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "zhusuo.png" ) );
    }
    else if ( this->currentPatternType == 2 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "sudu.png" ) );
    }
    else if ( this->currentPatternType == 3 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "xiangjin.png" ) );
    }
    else if ( this->currentPatternType == 4 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "lamao.png" ) );
    }
    else if ( this->currentPatternType == 5 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "wagen.png" ) );
    }
    else if ( this->currentPatternType == 6 )
    {
        addr = QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "dasongsanjiao.png" ) );
    }
    return addr;
}

void FileForm::ShowPatternImage()
{
    QString addr = getPatternImageAddr();

    if ( this->patternViewer == nullptr )
    {
        this->patternViewer = new PatternViewer();
        this->ui->scrollAreaImage->setWidgetResizable( true );
        this->ui->scrollAreaImage->setWidget( this->patternViewer );
        connect( this->patternViewer, &PatternViewer::mouseMove, this, [&]( quint32 x, quint32 y ) { this->ui->label_patternPos->setText( QString( "X=%1,Y=%2" ).arg( x ).arg( y ) ); } );
    }
    if ( this->patternPixmap != nullptr )
    {
        delete this->patternPixmap;
    }
    this->patternPixmap = new QPixmap( addr );
    this->patternViewer->setFixedWidth( this->patternPixmap->width() );
    this->patternViewer->setFixedHeight( this->patternPixmap->height() );
    this->patternViewer->setPixmap( *this->patternPixmap );
    this->patternViewer->scale( 1 );
    this->patternViewer->clearUserImage();
    this->patternViewer->setPenColor( SuoziColorMap[ 0 ] );

    this->ShowPatternColorPad();
}

void FileForm::ScalePatternImage( quint8 factor )
{
    this->patternViewer->setFixedWidth( factor * this->patternPixmap->width() );
    this->patternViewer->setFixedHeight( factor * this->patternPixmap->height() );
    //    qDebug() << "size" << this->patternViewer->width() << this->patternViewer->height();
    this->patternViewer->scale( factor );
}

void FileForm::ShowPatternColorPad()
{
    // Clear QGroupBox first
    QLayout* layout = this->ui->widget_patternColor->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->widget_patternColor );
    if ( this->currentPatternType == 0 )
    {
        for ( quint8 index = 0; index < 3; index++ )
        {
            QHBoxLayout* rowLayoutx = new QHBoxLayout;
            QLabel*      label1     = new QLabel( QString::number( index * 2 + 1 ) + "C" );
            label1->setAlignment( Qt::AlignCenter );
            ColorBlockWidget* widget1 = new ColorBlockWidget( nullptr, SuoziColorMap[ index * 6 + 1 ], QString::number( index * 6 + 1 ) );
            ColorBlockWidget* widget2 = new ColorBlockWidget( nullptr, SuoziColorMap[ index * 6 + 2 ], QString::number( index * 6 + 2 ) );
            ColorBlockWidget* widget3 = new ColorBlockWidget( nullptr, SuoziColorMap[ index * 6 + 3 ], QString::number( index * 6 + 3 ) );

            QLabel* label2 = new QLabel( QString::number( index * 2 + 2 ) + "C" );
            label2->setAlignment( Qt::AlignCenter );
            ColorBlockWidget* widget4 = new ColorBlockWidget( nullptr, SuoziColorMap[ index * 6 + 4 ], QString::number( index * 6 + 4 ) );
            ColorBlockWidget* widget5 = new ColorBlockWidget( nullptr, SuoziColorMap[ index * 6 + 5 ], QString::number( index * 6 + 5 ) );
            ColorBlockWidget* widget6 = new ColorBlockWidget( nullptr, SuoziColorMap[ index * 6 + 6 ], QString::number( index * 6 + 6 ) );

            label1->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            label2->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            widget1->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            widget2->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            widget3->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            widget4->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            widget5->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
            widget6->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );

            rowLayoutx->addWidget( label1 );
            rowLayoutx->addWidget( widget1 );
            rowLayoutx->addWidget( widget2 );
            rowLayoutx->addWidget( widget3 );
            rowLayoutx->addWidget( label2 );
            rowLayoutx->addWidget( widget4 );
            rowLayoutx->addWidget( widget5 );
            rowLayoutx->addWidget( widget6 );

            rowLayoutx->setAlignment( Qt::AlignTop | Qt::AlignLeft );
            rowLayoutx->setSpacing( 5 );
            rowLayoutx->setMargin( 1 );
            mainLayout->addLayout( rowLayoutx );
        }
        QHBoxLayout*      rowLayoutx = new QHBoxLayout;
        QLabel*           label3     = new QLabel( "M" );
        ColorBlockWidget* widget1    = new ColorBlockWidget( nullptr, SuoziColorMap[ 19 ], QString::number( 19 ) );
        ColorBlockWidget* widget2    = new ColorBlockWidget( nullptr, SuoziColorMap[ 20 ], QString::number( 20 ) );
        ColorBlockWidget* widget3    = new ColorBlockWidget( nullptr, SuoziColorMap[ 21 ], QString::number( 21 ) );
        ColorBlockWidget* widget4    = new ColorBlockWidget( nullptr, SuoziColorMap[ 22 ], QString::number( 22 ) );
        ColorBlockWidget* widget5    = new ColorBlockWidget( nullptr, SuoziColorMap[ 23 ], QString::number( 23 ) );
        ColorBlockWidget* widget6    = new ColorBlockWidget( nullptr, SuoziColorMap[ 24 ], QString::number( 24 ) );
        ColorBlockWidget* widget7    = new ColorBlockWidget( nullptr, SuoziColorMap[ 25 ], QString::number( 25 ) );
        ColorBlockWidget* widget8    = new ColorBlockWidget( nullptr, SuoziColorMap[ 26 ], QString::number( 26 ) );

        label3->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget1->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget2->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget3->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget4->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget5->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget6->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget7->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget8->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );

        rowLayoutx->addWidget( label3 );
        rowLayoutx->addWidget( widget1 );
        rowLayoutx->addWidget( widget2 );
        rowLayoutx->addWidget( widget3 );
        rowLayoutx->addWidget( widget4 );
        rowLayoutx->addWidget( widget5 );
        rowLayoutx->addWidget( widget6 );
        rowLayoutx->addWidget( widget7 );
        rowLayoutx->addWidget( widget8 );

        rowLayoutx->setAlignment( Qt::AlignTop | Qt::AlignLeft );
        rowLayoutx->setSpacing( 2 );
        rowLayoutx->setMargin( 1 );
        mainLayout->addLayout( rowLayoutx );

        QHBoxLayout*      rowLayout = new QHBoxLayout;
        QLabel*           label4    = new QLabel( "T" );
        ColorBlockWidget* widget9   = new ColorBlockWidget( nullptr, SuoziColorMap[ 0x01000000 ] );
        ColorBlockWidget* widget10  = new ColorBlockWidget( nullptr, SuoziColorMap[ 0x00100000 ] );
        ColorBlockWidget* widget11  = new ColorBlockWidget( nullptr, SuoziColorMap[ 0x8000001c ] );

        label4->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget9->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget10->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget11->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );

        rowLayout->addWidget( label4 );
        rowLayout->addWidget( widget9 );
        rowLayout->addWidget( widget10 );
        rowLayout->addWidget( widget11 );

        rowLayout->setAlignment( Qt::AlignTop | Qt::AlignLeft );
        rowLayout->setSpacing( 5 );
        rowLayout->setMargin( 1 );
        mainLayout->addLayout( rowLayout );
    }
    else if ( this->currentPatternType == 1 || this->currentPatternType == 2 )
    {
        QHBoxLayout*      rowLayoutx = new QHBoxLayout;
        ColorBlockWidget* widget0    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 0 ], QString::number( 0 ) );
        ColorBlockWidget* widget1    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 1 ], QString::number( 1 ) );
        ColorBlockWidget* widget2    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 2 ], QString::number( 2 ) );
        ColorBlockWidget* widget3    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 3 ], QString::number( 3 ) );
        ColorBlockWidget* widget4    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 4 ], QString::number( 4 ) );
        ColorBlockWidget* widget5    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 5 ], QString::number( 5 ) );
        ColorBlockWidget* widget6    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 6 ], QString::number( 6 ) );
        ColorBlockWidget* widget7    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 7 ], QString::number( 7 ) );

        widget0->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget1->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget2->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget3->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget4->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget5->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget6->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget7->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );

        rowLayoutx->addWidget( widget0 );
        rowLayoutx->addWidget( widget1 );
        rowLayoutx->addWidget( widget2 );
        rowLayoutx->addWidget( widget3 );
        rowLayoutx->addWidget( widget4 );
        rowLayoutx->addWidget( widget5 );
        rowLayoutx->addWidget( widget6 );
        rowLayoutx->addWidget( widget7 );

        rowLayoutx->setAlignment( Qt::AlignTop | Qt::AlignLeft );
        rowLayoutx->setSpacing( 5 );
        rowLayoutx->setMargin( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setStretch( 0, 0 );
    }
    else if ( this->currentPatternType == 3 )
    {
        QHBoxLayout*      rowLayoutx = new QHBoxLayout;
        ColorBlockWidget* widget0    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 0 ], QString::number( 0 ) );
        ColorBlockWidget* widget1    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 1 ], QString::number( 1 ) );
        ColorBlockWidget* widget2    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 2 ], QString::number( 2 ) );
        ColorBlockWidget* widget3    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 3 ], QString::number( 3 ) );

        widget0->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget1->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget2->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget3->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );

        rowLayoutx->addWidget( widget0 );
        rowLayoutx->addWidget( widget1 );
        rowLayoutx->addWidget( widget2 );
        rowLayoutx->addWidget( widget3 );

        rowLayoutx->setAlignment( Qt::AlignTop | Qt::AlignLeft );
        rowLayoutx->setSpacing( 5 );
        rowLayoutx->setMargin( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setStretch( 0, 0 );
    }
    else if ( this->currentPatternType == 4 || this->currentPatternType == 5 || this->currentPatternType == 6 )
    {
        QHBoxLayout*      rowLayoutx = new QHBoxLayout;
        ColorBlockWidget* widget0    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 0 ], QString::number( 0 ) );
        ColorBlockWidget* widget1    = new ColorBlockWidget( nullptr, ZhusuoColorMap[ 1 ], QString::number( 1 ) );

        widget0->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );
        widget1->setSizePolicy( QSizePolicy::Fixed, QSizePolicy::Fixed );

        rowLayoutx->addWidget( widget0 );
        rowLayoutx->addWidget( widget1 );

        rowLayoutx->setAlignment( Qt::AlignTop | Qt::AlignLeft );
        rowLayoutx->setSpacing( 5 );
        rowLayoutx->setMargin( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setStretch( 0, 0 );
    }
}

void FileForm::onColorPadClicked()
{
    if ( this->currentPatternType == 0 )
    {
        if ( this->_tianshaColor_comboFrm == nullptr )
        {
            this->_tianshaColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getTianshaColorList() );
            this->_tianshaColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_tianshaColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_tianshaColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_tianshaColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_tianshaColor_comboFrm->close();
                this->patternViewer->setPenColor( SuoziColorMap[ id ] );
            } );
        }
        this->_tianshaColor_comboFrm->show();
    }
    else if ( this->currentPatternType == 1 )
    {
        if ( this->_zhusuoColor_comboFrm == nullptr )
        {
            this->_zhusuoColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getZhusuoColorList() );
            this->_zhusuoColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_zhusuoColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_zhusuoColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_zhusuoColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_zhusuoColor_comboFrm->close();
                this->patternViewer->setPenColor( ZhusuoColorMap[ id ] );
            } );
        }
        this->_zhusuoColor_comboFrm->show();
    }
    else if ( this->currentPatternType == 2 )
    {
        if ( this->_suduColor_comboFrm == nullptr )
        {
            this->_suduColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getSuduColorList() );
            this->_suduColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_suduColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_suduColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_suduColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_suduColor_comboFrm->close();
                this->patternViewer->setPenColor( ZhusuoColorMap[ id ] );
            } );
        }
        this->_suduColor_comboFrm->show();
    }
    else if ( this->currentPatternType == 3 )
    {
        if ( this->_xiangjinColor_comboFrm == nullptr )
        {
            this->_xiangjinColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getXiangjinColorList() );
            this->_xiangjinColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_xiangjinColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_xiangjinColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_xiangjinColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_xiangjinColor_comboFrm->close();
                this->patternViewer->setPenColor( ZhusuoColorMap[ id ] );
            } );
        }
        this->_xiangjinColor_comboFrm->show();
    }
    else if ( this->currentPatternType == 4 )
    {
        if ( this->_lamaoColor_comboFrm == nullptr )
        {
            this->_lamaoColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getLamaoColorList() );
            this->_lamaoColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_lamaoColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_lamaoColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_lamaoColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_lamaoColor_comboFrm->close();
                this->patternViewer->setPenColor( ZhusuoColorMap[ id ] );
            } );
        }
        this->_lamaoColor_comboFrm->show();
    }
    else if ( this->currentPatternType == 5 )
    {
        if ( this->_wagenlamaoColor_comboFrm == nullptr )
        {
            this->_wagenlamaoColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getWagenLamaoColorList() );
            this->_wagenlamaoColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_wagenlamaoColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_wagenlamaoColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_wagenlamaoColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_wagenlamaoColor_comboFrm->close();
                this->patternViewer->setPenColor( ZhusuoColorMap[ id ] );
            } );
        }
        this->_wagenlamaoColor_comboFrm->show();
    }
    else if ( this->currentPatternType == 6 )
    {
        if ( this->_dasongsanjiaoColor_comboFrm == nullptr )
        {
            this->_dasongsanjiaoColor_comboFrm = new ComboForm( nullptr, this->linkCfg->getDasongSanjiaoColorList() );
            this->_dasongsanjiaoColor_comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->_dasongsanjiaoColor_comboFrm->setWindowTitle( tr( "选择类型" ) );
            this->_dasongsanjiaoColor_comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_dasongsanjiaoColor_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_dasongsanjiaoColor_comboFrm->close();
                this->patternViewer->setPenColor( ZhusuoColorMap[ id ] );
            } );
        }
        this->_dasongsanjiaoColor_comboFrm->show();
    }
}

void FileForm::savePatternImage()
{
    QSharedPointer< FATParser::PatternData > pd = this->fatDataMain->PatternData;

    // 1 创建绘图设备 指定透明背景且只能保存为原来格式才能透明 --例如本来png改成jpg就不是透明
    QImage image( pd->width, pd->height, QImage::Format_ARGB32 );  //比QPixmap多一个参数 参三格式设置为透明
    QImage image_zhusuo( pd->width, pd->height, QImage::Format_ARGB32 );
    QImage image_xiangjin( pd->width, pd->height, QImage::Format_ARGB32 );
    QImage image_lamao( pd->width, pd->height, QImage::Format_ARGB32 );
    QImage image_wagenlamao( pd->width, pd->height, QImage::Format_ARGB32 );
    QImage image_dasongsanjiao( pd->width, pd->height, QImage::Format_ARGB32 );
    QImage image_sudu( pd->width, pd->height, QImage::Format_ARGB32 );
    for ( int i = 0; i < pd->height; i++ )
    {
        for ( int j = 0; j < pd->width; j++ )
        {
            /*1F*/
            image.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
            //主梭
            image_zhusuo.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
            /*速度*/
            image_sudu.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
            /*橡筋*/
            image_xiangjin.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
            /*拉毛*/
            image_lamao.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
            /*袜跟拉毛*/
            image_wagenlamao.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
            /*打松*/
            image_dasongsanjiao.setPixelColor( QPoint( j, i ), QColor( 255, 255, 255 ) );
        }
    }

    for ( int i = 0; i < pd->height; i++ )
    {
        for ( int j = 0; j < pd->width; j++ )
        {
            quint32 value = *( ( quint32* )&pd->pattern_data[ i ][ j ] );

            if ( value == 0 )
            {
                continue;
            }
            else if ( value == 0x1000000 )
            {
                /*集圈*/
                image.setPixelColor( QPoint( j, i ), QColor( 0, 0, 0 ) );
            }
            else if ( value == 0x8000001C )
            {
                /*浮线*/
                image.setPixelColor( QPoint( j, i ), QColor( 100, 100, 100 ) );
            }
            else
            {
                /*int xxxx = pd->pattern_data[i][j].tian_sha;
                if ( !SuoziColorMap.keys().contains(pd->pattern_data[i][j].tian_sha))
                {
                    int xxx = pd->pattern_data[i][j].tian_sha;
                }*/
                //添纱
                image.setPixelColor( QPoint( j, i ), SuoziColorMap[ pd->pattern_data[ i ][ j ].tian_sha ] );
                //主梭
                image_zhusuo.setPixelColor( QPoint( j, i ), ZhusuoColorMap[ pd->pattern_data[ i ][ j ].zhu_suo >> 4 ] );
                /*速度*/
                image_sudu.setPixelColor( QPoint( j, i ), ZhusuoColorMap[ pd->pattern_data[ i ][ j ].speed ] );
                /*橡筋*/
                image_xiangjin.setPixelColor( QPoint( j, i ), ZhusuoColorMap[ pd->pattern_data[ i ][ j ].xiangjin / 2 ] );
                /*拉毛*/
                image_lamao.setPixelColor( QPoint( j, i ), ZhusuoColorMap[ pd->pattern_data[ i ][ j ].la_mao ] );
                /*袜跟拉毛*/
                image_wagenlamao.setPixelColor( QPoint( j, i ), ZhusuoColorMap[ pd->pattern_data[ i ][ j ].wa_gen_lamao ] );
                /*打松*/
                image_dasongsanjiao.setPixelColor( QPoint( j, i ), ZhusuoColorMap[ pd->pattern_data[ i ][ j ].dasong_sanjiao ] );
            }
        }
    }

    image.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "tiansha.png" ) ) );
    image_zhusuo.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "zhusuo.png" ) ) );
    image_sudu.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "sudu.png" ) ) );
    image_xiangjin.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "xiangjin.png" ) ) );
    image_lamao.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "lamao.png" ) ) );
    image_wagenlamao.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "wagen.png" ) ) );
    image_dasongsanjiao.save( QString( "%1/%2" ).arg( QApplication::applicationDirPath() ).arg( QString( "dasongsanjiao.png" ) ) );
}

void FileForm::restoreImageToPatterData()
{
    // 文件保存的位置
    QString fileAddr = getPatternImageAddr();
    QImage  image( fileAddr );
    if ( image.isNull() )
    {
        qDebug() << "无法打开图像文件";
        QMessageBox::information( nullptr, "错误提示", "保存数据失败" );
        return;
    }

    QSharedPointer< FATParser::PatternData > pd = this->fatDataMain->PatternData;
    if ( this->currentPatternType == 0 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = SuoziColorMap.constBegin();
                while ( it != SuoziColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].tian_sha = it.key();
                        break;
                    }
                    ++it;
                }
            }
        }
    }
    else if ( this->currentPatternType == 1 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = ZhusuoColorMap.constBegin();
                while ( it != ZhusuoColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].zhu_suo = it.key() << 4;
                        break;
                    }
                    ++it;
                }
            }
        }
    }
    else if ( this->currentPatternType == 2 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = ZhusuoColorMap.constBegin();
                while ( it != ZhusuoColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].speed = it.key();
                        break;
                    }
                    ++it;
                }
            }
        }
    }
    else if ( this->currentPatternType == 3 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = ZhusuoColorMap.constBegin();
                while ( it != ZhusuoColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].xiangjin = it.key() * 2;
                        break;
                    }
                    ++it;
                }
            }
        }
    }
    else if ( this->currentPatternType == 4 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = ZhusuoColorMap.constBegin();
                while ( it != ZhusuoColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].la_mao = it.key();
                        break;
                    }
                    ++it;
                }
            }
        }
    }
    else if ( this->currentPatternType == 5 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = ZhusuoColorMap.constBegin();
                while ( it != ZhusuoColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].wa_gen_lamao = it.key();
                        break;
                    }
                    ++it;
                }
            }
        }
    }
    else if ( this->currentPatternType == 6 )
    {
        for ( int y = 0; y < image.height(); ++y )
        {
            for ( int x = 0; x < image.width(); ++x )
            {
                QColor color = image.pixelColor( x, y );

                // 在颜色映射中查找匹配的颜色并获取索引
                QMap< quint32, QColor >::const_iterator it = ZhusuoColorMap.constBegin();
                while ( it != ZhusuoColorMap.constEnd() )
                {
                    if ( it.value() == color )
                    {
                        //                        qDebug() << "找到匹配的颜色，Key: " << it.key();
                        pd->pattern_data[ y ][ x ].dasong_sanjiao = it.key();
                        break;
                    }
                    ++it;
                }
            }
        }
    }
}
