﻿#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"

void CraftParamForm::initUSBCFTTransferPage()
{
    // 按键只能connect1次
    if ( !isUSBTransferPageInited )
    {
        connect( ui->pbtn_toThis, &QPushButton::clicked, this, [&]() {
            qDebug() << "currentCFTUSBFile" << currentCFTUSBFile;
            if ( this->currentCFTUSBFile == "" )
            {
                QMessageBox::information( nullptr, "错误提示", "请先选中U盘中的文件" );
                return;
            }
            else
            {
                QString targetFile = CFT_DIR + currentCFTUSBFile;
                // 重名文件检查
                if (QFile::exists(targetFile))
                {
                    QMessageBox::StandardButton reply;
                    reply = QMessageBox::question(nullptr, "文件已存在", 
                        QString("文件 %1 已存在，是否覆盖？").arg(currentCFTUSBFile),
                        QMessageBox::Yes | QMessageBox::No);

                    if (reply == QMessageBox::No)
                    {
                        return;
                    }
                    // 如果文件存在且用户确认覆盖，先删除原文件
                    QFile::remove(targetFile);
                }

                // COPY
                if (QFile::copy(CFTUSBBaseAddr + currentCFTUSBFile, targetFile))
                {
                    this->refreshCFTFileList(CFT_DIR);
                    this->initCFTFileThisTableView();
                }
                else
                {
                    QMessageBox::warning(nullptr, "错误提示", "文件复制失败");
                }
            }
        } );
        connect( ui->pbtn_toUSB, &QPushButton::clicked, this, [&]() {
            qDebug() << "currentCFTThisFile" << currentCFTThisFile;
            if ( this->currentCFTThisFile == "" )
            {
                QMessageBox::information( nullptr, "错误提示", "请先选中机器中的文件" );
                return;
            }
            else
            {
                QString targetFile = CFTUSBBaseAddr + currentCFTThisFile;
                // 重名文件检查
                if (QFile::exists(targetFile))
                {
                    QMessageBox::StandardButton reply;
                    reply = QMessageBox::question(nullptr, "文件已存在", 
                        QString("文件 %1 已存在，是否覆盖？").arg(currentCFTThisFile),
                        QMessageBox::Yes | QMessageBox::No);

                    if (reply == QMessageBox::No)
                    {
                        return;
                    }
                    // 如果文件存在且用户确认覆盖，先删除原文件
                    QFile::remove(targetFile);
                }

                // COPY
                if (QFile::copy(CFT_DIR + currentCFTThisFile, targetFile))
                {
                    this->refreshCFTUSBFileList(CFTUSBBaseAddr);
                    this->initCFTFileUSBTableView();
                }
                else
                {
                    QMessageBox::warning(nullptr, "错误提示", "文件复制失败");
                }
            }
        } );
    }

    isUSBTransferPageInited = true;

    // 显示两个文件夹内部的CFT文件
    this->refreshCFTFileList( CFT_DIR );
    this->initCFTFileThisTableView();

    // 检测USB文件夹是否存在
    CFTUSBBaseAddr = checkCFTUSBDirExists();
    qDebug() << "usbFileDdir" << CFTUSBBaseAddr;
    this->refreshCFTUSBFileList( CFTUSBBaseAddr );
    this->initCFTFileUSBTableView();
}

void CraftParamForm::initCFTFileThisTableView()
{
    if ( this->_db_cft_table_this_model == nullptr )
    {
        this->_db_cft_table_this_model = new QStandardItemModel();
        this->ui->File_tableView_this->setModel( this->_db_cft_table_this_model );

        // 单击选中事件
        // 连接 clicked 信号到槽函数
        connect( this->ui->File_tableView_this, &QTableView::doubleClicked, this, [=]( const QModelIndex& index ) {
            // 获取点击的行号
            int row = index.row();
            //            qDebug() << "Table" << row;
            if ( row >= 0 )
            {
                // 赋值
                QString    utf8String    = this->_db_cft_table_this_model->data( _db_cft_table_this_model->index( row, 0 ) ).toString();
                QByteArray array         = utf8String.toUtf8();
                this->currentCFTThisFile = QString::fromLocal8Bit( array );
            }
        } );
    }
    else
    {
        this->_db_cft_table_this_model->clear();
    }

    // 添加数据
    if ( this->CFTFileList != nullptr )
    {
        for ( int i = 0; i < this->CFTFileList->length(); i++ )
        {
            QString    string = CFTFileList->at( i ).fileName;
            QByteArray array  = string.toLocal8Bit();
            //            qDebug() << array;
            QString utf8String = QString::fromUtf8( array );

            QList< QStandardItem* > add_items;
            add_items << new QStandardItem( utf8String );
            add_items << new QStandardItem( CFTFileList->at( i ).fileSize );
            add_items << new QStandardItem( CFTFileList->at( i ).lastModified );
            this->_db_cft_table_this_model->appendRow( add_items );
        }
    }

    QStringList table_h_headers;
    table_h_headers << "文件名"
                    << "文件大小"
                    << "上次修改时间";
    this->_db_cft_table_this_model->setHorizontalHeaderLabels( table_h_headers );

    //设置选中模式为选中行
    this->ui->File_tableView_this->setSelectionBehavior( QAbstractItemView::SelectRows );
    // 表格宽度随内容自动扩展
    this->ui->File_tableView_this->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    // 隐藏网格线
    this->ui->File_tableView_this->setShowGrid( false );
    // 启用排序
    this->ui->File_tableView_this->setSortingEnabled( true );
}

void CraftParamForm::refreshCFTUSBFileList( QString fileCFTbaseAddr )
{
    if ( this->CFTUSBFileList == nullptr )
    {
        this->CFTUSBFileList = new QList< CraftParamForm::FileInfoItem >();
    }
    else
    {
        this->CFTUSBFileList->clear();
    }

    // 遍历文件夹
    QDir        dir( fileCFTbaseAddr );
    QStringList filters;
    filters << "*.CFT";  // 只匹配CFT文件
    dir.setNameFilters( filters );
    dir.setFilter( QDir::Files | QDir::NoSymLinks );

    QFileInfoList fileInfoList = dir.entryInfoList();
    for ( const QFileInfo& fileInfo : fileInfoList )
    {
        CraftParamForm::FileInfoItem item;
        item.fileName     = fileInfo.fileName();
        item.fileSize     = QString::number( fileInfo.size() / 2024.0, 'f', 2 ) + " KB";
        item.lastModified = fileInfo.lastModified().toString( "yyyy-MM-dd HH:mm:ss" );
        this->CFTUSBFileList->append( item );
    }
}

void CraftParamForm::initCFTFileUSBTableView()
{
    if ( this->_db_cft_table_usb_model == nullptr )
    {
        this->_db_cft_table_usb_model = new QStandardItemModel();
        this->ui->File_tableView_usb->setModel( this->_db_cft_table_usb_model );

        // 单击选中事件
        // 连接 clicked 信号到槽函数
        connect( this->ui->File_tableView_usb, &QTableView::doubleClicked, this, [=]( const QModelIndex& index ) {
            // 获取点击的行号
            int row = index.row();
            //            qDebug() << "Table" << row;
            if ( row >= 0 )
            {
                // 赋值
                QString    utf8String   = this->_db_cft_table_usb_model->data( _db_cft_table_usb_model->index( row, 0 ) ).toString();
                QByteArray array        = utf8String.toUtf8();
                this->currentCFTUSBFile = QString::fromLocal8Bit( array );
            }
        } );
    }
    else
    {
        this->_db_cft_table_usb_model->clear();
    }

    // 添加数据
    if ( this->CFTUSBFileList != nullptr )
    {
        for ( int i = 0; i < this->CFTUSBFileList->length(); i++ )
        {
            QString    string = CFTUSBFileList->at( i ).fileName;
            QByteArray array  = string.toLocal8Bit();
            //            qDebug() << array;
            QString utf8String = QString::fromUtf8( array );

            QList< QStandardItem* > add_items;
            add_items << new QStandardItem( utf8String );
            add_items << new QStandardItem( CFTUSBFileList->at( i ).fileSize );
            add_items << new QStandardItem( CFTUSBFileList->at( i ).lastModified );
            this->_db_cft_table_usb_model->appendRow( add_items );
        }
    }

    QStringList table_h_headers;
    table_h_headers << "文件名"
                    << "文件大小"
                    << "上次修改时间";
    this->_db_cft_table_usb_model->setHorizontalHeaderLabels( table_h_headers );

    //设置选中模式为选中行
    this->ui->File_tableView_usb->setSelectionBehavior( QAbstractItemView::SelectRows );
    // 表格宽度随内容自动扩展
    this->ui->File_tableView_usb->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    // 隐藏网格线
    this->ui->File_tableView_usb->setShowGrid( false );
    // 启用排序
    this->ui->File_tableView_usb->setSortingEnabled( true );
}

QString CraftParamForm::checkCFTUSBDirExists()
{
    QString usbDirSt = "/run/media/sd";
    char    tag      = 'a';

    for ( int i = 0; i < 26; i++ )
    {
        // 修改为检测 sda1~sdz1
        QDir dir( QString( "%1%2%3/" ).arg( usbDirSt ).arg( tag ).arg( "1" ) );
        if ( !dir.exists() )
            break;
        else
        {
            tag++;
        }
    }

    // 返回找到的路径，如 /run/media/sda1/
    return QString( "%1%2%3/" ).arg( usbDirSt ).arg( --tag ).arg( "1" );
}

void CraftParamForm::refreshCFTFileList( QString fileCFTbaseAddr )
{
    if ( this->CFTFileList == nullptr )
    {
        this->CFTFileList = new QList< FileInfoItem >();
    }
    else
    {
        this->CFTFileList->clear();
    }

    // 遍历文件夹
    QDir        dir( fileCFTbaseAddr );
    QStringList filters;
    filters << "*.CFT";  // 只匹配CFT文件
    dir.setNameFilters( filters );
    dir.setFilter( QDir::Files | QDir::NoSymLinks );

    QFileInfoList fileInfoList = dir.entryInfoList();
    for ( const QFileInfo& fileInfo : fileInfoList )
    {
        FileInfoItem item;
        item.fileName     = fileInfo.fileName();
        item.fileSize     = QString::number( fileInfo.size() / 2024.0, 'f', 2 ) + " KB";
        item.lastModified = fileInfo.lastModified().toString( "yyyy-MM-dd HH:mm:ss" );
        this->CFTFileList->append( item );
    }
}
