#ifndef LOGFORM_H
#define LOGFORM_H

#include "Common/defines.h"
#include "CommonWidget/comboform.h"
#include "CommonWidget/inputdialog.h"
#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "Communicate/Communication.h"
#include <QButtonGroup>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QWidget>

namespace Ui
{
class LogForm;
}

class LogForm : public QWidget
{
    Q_OBJECT

public:
    explicit LogForm( QWidget* parent = nullptr, MainWidgetData* mainData = nullptr, Communication* com = nullptr );
    ~LogForm();

private:
    Ui::LogForm* ui;

    MainWidgetData* mainData;
    /************* 422通信相关 *************/
    Communication* comm;

    // 菜单页面
    QButtonGroup*    menuBtnGroup;  //菜单按键组
    ComboForm*       comboForm               = nullptr;
    NumberInputForm* numberInputForm         = nullptr;
    InputDialog*     inputDialog             = nullptr;
    MyLineEdit*      currentSelectedLineEdit = nullptr;

    // Warn Log
    quint16 curWarnLogPage      = 0;
    bool    isWarnLogPageInited = false;
    bool    markSameWarnLog     = false;
    void    initWarnLogPage();
    void    refreshWarnLog();

    // 网络配置页面
    QButtonGroup* networkBtnGroup;  //网络配置页按钮
    bool          isNetworkPageInited = false;
    void          initNetworkPage();
    void          loadNetworkConfig();

signals:
    void LogFormToMainWinToShowSignal();

private slots:
    void onLogHomeBtnClicked();            //测试界面返回 主菜单键按下槽函数
    void onMenuBtnGroupClicked( int id );  //菜单按键组按下槽函数

    void markSameTypeWarnLog();

    // network
    void onNetworkBtnGroupClicked( int id );  // 网络页按钮按下槽函数
    void showNumberInputForm();
    void onNumberInputFinished( QString text );
    void saveNetworkConfig();
};

#endif  // LOGFORM_H
