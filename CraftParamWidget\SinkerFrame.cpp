#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>

// 初始化生克密度框架
void CraftParamForm::initSinkerFrame()
{
    // 清空之前的控件列表
    if (!sinkerIndexLabels.isEmpty()) {
        for (auto label : sinkerIndexLabels) {
            delete label;
        }
        sinkerIndexLabels.clear();
    }
    if (!sinkerNameLabels.isEmpty()) {
        for (auto label : sinkerNameLabels) {
            delete label;
        }
        sinkerNameLabels.clear();
    }
    if (!sinkerStepLabels.isEmpty()) {
        for (auto label : sinkerStepLabels) {
            delete label;
        }
        sinkerStepLabels.clear();
    }
    if (!sinkerIdEdits.isEmpty()) {
        for (auto edit : sinkerIdEdits) {
            delete edit;
        }
        sinkerIdEdits.clear();
    }
    if (!sinkerProhibitLabels.isEmpty()) {
        for (auto label : sinkerProhibitLabels) {
            delete label;
        }
        sinkerProhibitLabels.clear();
    }
    if (!sinkerStartLabels.isEmpty()) {
        for (auto label : sinkerStartLabels) {
            delete label;
        }
        sinkerStartLabels.clear();
    }
    if (!sinkerEndLabels.isEmpty()) {
        for (auto label : sinkerEndLabels) {
            delete label;
        }
        sinkerEndLabels.clear();
    }
    if (!sinkerStartNewEdits.isEmpty()) {
        for (auto edit : sinkerStartNewEdits) {
            delete edit;
        }
        sinkerStartNewEdits.clear();
    }
    if (!sinkerEndNewEdits.isEmpty()) {
        for (auto edit : sinkerEndNewEdits) {
            delete edit;
        }
        sinkerEndNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout(ui->sinkerFrame);

    // 创建标题行
    QLabel* indexHeader = new QLabel("序号", ui->sinkerFrame);
    QLabel* nameHeader = new QLabel("步骤名称", ui->sinkerFrame);
    QLabel* stepHeader = new QLabel("步", ui->sinkerFrame);
    QLabel* idHeader = new QLabel("ID", ui->sinkerFrame);
    QLabel* prohibitHeader = new QLabel("P", ui->sinkerFrame);
    QLabel* startHeader = new QLabel("起始值", ui->sinkerFrame);
    QLabel* endHeader = new QLabel("结束值", ui->sinkerFrame);
    QLabel* startNewHeader = new QLabel("N起始值", ui->sinkerFrame);
    QLabel* endNewHeader = new QLabel("N结束值", ui->sinkerFrame);

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet(headerStyle);
    nameHeader->setStyleSheet(headerStyle);
    stepHeader->setStyleSheet(headerStyle);
    idHeader->setStyleSheet(headerStyle);
    prohibitHeader->setStyleSheet(headerStyle);
    startHeader->setStyleSheet(headerStyle);
    endHeader->setStyleSheet(headerStyle);
    startNewHeader->setStyleSheet(headerStyle);
    endNewHeader->setStyleSheet(headerStyle);

    // 设置标题对齐方式
    indexHeader->setAlignment(Qt::AlignCenter);
    nameHeader->setAlignment(Qt::AlignCenter);
    stepHeader->setAlignment(Qt::AlignCenter);
    idHeader->setAlignment(Qt::AlignCenter);
    prohibitHeader->setAlignment(Qt::AlignCenter);
    startHeader->setAlignment(Qt::AlignCenter);
    endHeader->setAlignment(Qt::AlignCenter);
    startNewHeader->setAlignment(Qt::AlignCenter);
    endNewHeader->setAlignment(Qt::AlignCenter);

    // 添加标题到布局
    layout->addWidget(indexHeader, 0, 0);
    layout->addWidget(nameHeader, 0, 1);
    layout->addWidget(stepHeader, 0, 2);
    layout->addWidget(idHeader, 0, 3);
    layout->addWidget(prohibitHeader, 0, 4);
    layout->addWidget(startHeader, 0, 5);
    layout->addWidget(endHeader, 0, 6);
    layout->addWidget(startNewHeader, 0, 7);
    layout->addWidget(endNewHeader, 0, 8);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->sinkerFrame);
        indexLabel->setAlignment(Qt::AlignCenter);
        sinkerIndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);
        
        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->sinkerFrame);
        nameLabel->setAlignment(Qt::AlignCenter);
        sinkerNameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);
        
        // 步标签
        QLabel* stepLabel = new QLabel("", ui->sinkerFrame);
        stepLabel->setAlignment(Qt::AlignCenter);
        sinkerStepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);
        
        // ID编辑框
        MyLineEdit* idEdit = new MyLineEdit(ui->sinkerFrame);
        idEdit->setAlignment(Qt::AlignCenter);
        idEdit->setReadOnly(true); // 初始设为只读
        idEdit->setProperty("row", i); // 存储行索引
        idEdit->setProperty("column", 3); // 存储列索引
        connect(idEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSinkerPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.sinkerMotorParam.size()) {
                tableIndex = 6; // 生克密度表格
                tableEditRowIndex = i;
                tableEditColIndex = 3; // ID列
                
                // 创建字母输入对话框
                if (letterInputDialog != nullptr) {
                    delete letterInputDialog;
                    letterInputDialog = nullptr;
                }
                letterInputDialog = new LetterInputDialog(nullptr, "生克ID", "ID:");
                connect(this->letterInputDialog, &LetterInputDialog::InputFinished, this, &CraftParamForm::onLetterInputDialogFinished);
                letterInputDialog->show();
            }
        });
        sinkerIdEdits.append(idEdit);
        layout->addWidget(idEdit, i + 1, 3);
        
        // 禁止修改标签
        QLabel* prohibitLabel = new QLabel("", ui->sinkerFrame);
        prohibitLabel->setAlignment(Qt::AlignCenter);
        sinkerProhibitLabels.append(prohibitLabel);
        layout->addWidget(prohibitLabel, i + 1, 4);
        
        // 起始值标签
        QLabel* startLabel = new QLabel("", ui->sinkerFrame);
        startLabel->setAlignment(Qt::AlignCenter);
        sinkerStartLabels.append(startLabel);
        layout->addWidget(startLabel, i + 1, 5);
        
        // 结束值标签
        QLabel* endLabel = new QLabel("", ui->sinkerFrame);
        endLabel->setAlignment(Qt::AlignCenter);
        sinkerEndLabels.append(endLabel);
        layout->addWidget(endLabel, i + 1, 6);
        
        // 新起始值编辑框
        MyLineEdit* startNewEdit = new MyLineEdit(ui->sinkerFrame);
        startNewEdit->setAlignment(Qt::AlignCenter);
        startNewEdit->setReadOnly(true); // 初始设为只读
        startNewEdit->setProperty("row", i); // 存储行索引
        startNewEdit->setProperty("column", 7); // 存储列索引
        connect(startNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSinkerPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.sinkerMotorParam.size() && !craftParams.sinkerMotorParam[dataIndex]->prohibitsSinkerValueChange) {
                tableIndex = 6; // 生克密度表格
                tableEditRowIndex = i;
                tableEditColIndex = 7; // 新起始值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "生克新起始值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        sinkerStartNewEdits.append(startNewEdit);
        layout->addWidget(startNewEdit, i + 1, 7);
        
        // 新结束值编辑框
        MyLineEdit* endNewEdit = new MyLineEdit(ui->sinkerFrame);
        endNewEdit->setAlignment(Qt::AlignCenter);
        endNewEdit->setReadOnly(true); // 初始设为只读
        endNewEdit->setProperty("row", i); // 存储行索引
        endNewEdit->setProperty("column", 8); // 存储列索引
        connect(endNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSinkerPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.sinkerMotorParam.size() && !craftParams.sinkerMotorParam[dataIndex]->prohibitsSinkerValueChange) {
                tableIndex = 6; // 生克密度表格
                tableEditRowIndex = i;
                tableEditColIndex = 8; // 新结束值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "生克新结束值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        sinkerEndNewEdits.append(endNewEdit);
        layout->addWidget(endNewEdit, i + 1, 8);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 1);  // ID
    layout->setColumnStretch(4, 1);  // P
    layout->setColumnStretch(5, 2);  // 起始值
    layout->setColumnStretch(6, 2);  // 结束值
    layout->setColumnStretch(7, 2);  // N起始值
    layout->setColumnStretch(8, 2);  // N结束值
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->sinkerFrame->setLayout(layout);
}

// 更新生克密度页面
void CraftParamForm::updateSinkerPage()
{
    // 计算总页数
    totalSinkerPages = (craftParams.sinkerMotorParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;
    
    // 确保当前页在有效范围内
    if (currentSinkerPage >= totalSinkerPages && totalSinkerPages > 0) {
        currentSinkerPage = totalSinkerPages - 1;
    }
    
    // 计算当前页的起始索引
    int startIndex = currentSinkerPage * ITEMS_PER_PAGE;
    
    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.sinkerMotorParam.size()) {
            // 有数据，显示
            auto param = craftParams.sinkerMotorParam[dataIndex];

            sinkerIndexLabels[i]->setText(QString::number(dataIndex + 1));
            sinkerNameLabels[i]->setText(QString::fromLatin1(param->blockName));
            sinkerStepLabels[i]->setText(QString::number(param->stepStart) + "~" + QString::number(param->stepEnd));
            sinkerIdEdits[i]->setText(QString(param->sinkerId));
            sinkerProhibitLabels[i]->setText(param->prohibitsSinkerValueChange ? "N" : "Y");
            sinkerStartLabels[i]->setText(QString::number(param->sinkerStart[currentSize - 1]));
            sinkerEndLabels[i]->setText(QString::number(param->sinkerEnd[currentSize - 1]));
            sinkerStartNewEdits[i]->setText(QString::number(param->sinkerStart[currentSize - 1]));
            sinkerEndNewEdits[i]->setText(QString::number(param->sinkerEnd[currentSize - 1]));

            // 设置可编辑状态
            bool editable = !param->prohibitsSinkerValueChange;
            sinkerIdEdits[i]->setEnabled(true); // ID始终可编辑
            sinkerStartNewEdits[i]->setEnabled(editable);
            sinkerEndNewEdits[i]->setEnabled(editable);

            // 设置背景色
            sinkerIdEdits[i]->setStyleSheet("background-color: #ffffc8;");
            if (editable) {
                sinkerStartNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
                sinkerEndNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
            } else {
                sinkerStartNewEdits[i]->setStyleSheet("");
                sinkerEndNewEdits[i]->setStyleSheet("");
            }

            // 显示所有控件
            sinkerIndexLabels[i]->setVisible(true);
            sinkerNameLabels[i]->setVisible(true);
            sinkerStepLabels[i]->setVisible(true);
            sinkerIdEdits[i]->setVisible(true);
            sinkerProhibitLabels[i]->setVisible(true);
            sinkerStartLabels[i]->setVisible(true);
            sinkerEndLabels[i]->setVisible(true);
            sinkerStartNewEdits[i]->setVisible(true);
            sinkerEndNewEdits[i]->setVisible(true);

            // 恢复正常样式（除了可编辑的控件）
            sinkerIndexLabels[i]->setStyleSheet("");
            sinkerNameLabels[i]->setStyleSheet("");
            sinkerStepLabels[i]->setStyleSheet("");
            sinkerProhibitLabels[i]->setStyleSheet("");
            sinkerStartLabels[i]->setStyleSheet("");
            sinkerEndLabels[i]->setStyleSheet("");
        } else {
            // 无数据，但保持控件可见以占据空间
            sinkerIndexLabels[i]->setText("");
            sinkerNameLabels[i]->setText("");
            sinkerStepLabels[i]->setText("");
            sinkerIdEdits[i]->setText("");
            sinkerProhibitLabels[i]->setText("");
            sinkerStartLabels[i]->setText("");
            sinkerEndLabels[i]->setText("");
            sinkerStartNewEdits[i]->setText("");
            sinkerEndNewEdits[i]->setText("");

            // 控件保持可见，但设置为透明
            sinkerIndexLabels[i]->setVisible(true);
            sinkerNameLabels[i]->setVisible(true);
            sinkerStepLabels[i]->setVisible(true);
            sinkerIdEdits[i]->setVisible(true);
            sinkerProhibitLabels[i]->setVisible(true);
            sinkerStartLabels[i]->setVisible(true);
            sinkerEndLabels[i]->setVisible(true);
            sinkerStartNewEdits[i]->setVisible(true);
            sinkerEndNewEdits[i]->setVisible(true);

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            sinkerIndexLabels[i]->setStyleSheet(transparentStyle);
            sinkerNameLabels[i]->setStyleSheet(transparentStyle);
            sinkerStepLabels[i]->setStyleSheet(transparentStyle);
            sinkerIdEdits[i]->setStyleSheet(transparentStyle);
            sinkerProhibitLabels[i]->setStyleSheet(transparentStyle);
            sinkerStartLabels[i]->setStyleSheet(transparentStyle);
            sinkerEndLabels[i]->setStyleSheet(transparentStyle);
            sinkerStartNewEdits[i]->setStyleSheet(transparentStyle);
            sinkerEndNewEdits[i]->setStyleSheet(transparentStyle);

            // 禁用编辑
            sinkerIdEdits[i]->setEnabled(false);
            sinkerStartNewEdits[i]->setEnabled(false);
            sinkerEndNewEdits[i]->setEnabled(false);
        }
    }
}
