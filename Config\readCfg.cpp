#include "readCfg.h"
#include <QDebug>
#include <QFile>

ReadCfg::ReadCfg() {}

ReadCfg::~ReadCfg() {}

void ReadCfg::parseCfgConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        // 文件打开失败
        qDebug() << "Cfg Json Open Failed!";
        QMessageBox::warning( nullptr, "错误提示", "Cfg.json文件不存在" );
        return;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        // Json文档无效
        qDebug() << "Cfg Json is inValid!";
        return;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObject = jsonDocument.object();

    // 获取SpecialFun数组
    QJsonArray specialFunArray = jsonObject[ "SpecialFun" ].toArray();
    // 遍历SpecialFun数组
    for ( int i = 0; i < specialFunArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = specialFunArray.at( i ).toObject();

        // 获取key和value属性
        ValveStruct valveStruct;
        valveStruct.key      = item[ "key" ].toInt();
        valveStruct.value    = item[ "value" ].toString();
        valveStruct.mappedId = item[ "mappedId" ].toInt();
        valveStruct.position = item[ "position" ].toString();

        // 对key和value进行处理
        this->_specialFunList.insert( valveStruct.key, valveStruct );
    }

    // 获取Cam数组
    QJsonArray camArray = jsonObject[ "Cam" ].toArray();
    // 遍历Cam数组
    for ( int i = 0; i < camArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = camArray.at( i ).toObject();

        // 获取key和value属性
        ValveStruct valveStruct;
        valveStruct.key      = item[ "key" ].toInt();
        valveStruct.value    = item[ "value" ].toString();
        valveStruct.mappedId = item[ "mappedId" ].toInt();
        valveStruct.position = item[ "position" ].toString();

        // 对key和value进行处理
        this->_camList.insert( valveStruct.key, valveStruct );
    }

    // 获取yarnFinger数组
    QJsonArray yarnFingerArray = jsonObject[ "yarnFinger" ].toArray();
    // 遍历yarnFinger数组
    for ( int i = 0; i < yarnFingerArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = yarnFingerArray.at( i ).toObject();

        // 获取key和value属性
        ValveStruct valveStruct;
        valveStruct.key      = item[ "key" ].toInt();
        valveStruct.value    = item[ "value" ].toString();
        valveStruct.mappedId = item[ "mappedId" ].toInt();
        valveStruct.position = item[ "position" ].toString();

        // 对key和value进行处理
        this->_yarnFingerList.insert( valveStruct.key, valveStruct );
    }

    // 获取sockInput数组
    QJsonArray sockInputArray = jsonObject[ "sockInput" ].toArray();
    // 遍历sockInput数组
    for ( int i = 0; i < sockInputArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = sockInputArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = item[ "key" ].toInt();
        QString value = item[ "value" ].toString();

        // 对key和value进行处理
        this->_sockInputList.insert( key, value );
    }

    // 获取seamHeadInput数组
    QJsonArray seamHeadInputArray = jsonObject[ "seamHeadInput" ].toArray();
    // 遍历seamHeadInput数组
    for ( int i = 0; i < seamHeadInputArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = seamHeadInputArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = item[ "key" ].toInt();
        QString value = item[ "value" ].toString();

        // 对key和value进行处理
        this->_seamHeadInputList.insert( key, value );
    }

    // 获取motorZeroInput数组
    QJsonArray motorZeroInputArray = jsonObject[ "motorZeroInput" ].toArray();
    // 遍历motorZeroInput数组
    for ( int i = 0; i < motorZeroInputArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = motorZeroInputArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = item[ "key" ].toInt();
        QString value = item[ "value" ].toString();

        // 对key和value进行处理
        this->_motorZeroInputList.insert( key, value );
    }

    // 解析 yarn2 段
    QJsonArray yarn2Array = jsonObject.value( "yarn2" ).toArray();
    for ( int i = 0; i < yarn2Array.size(); ++i )
    {
        QJsonObject obj = yarn2Array[ i ].toObject();
        ValveStruct valve;
        valve.key      = obj.value( "key" ).toInt();
        valve.mappedId = obj.value( "mappedId" ).toInt();
        valve.value    = obj.value( "value" ).toString();
        valve.position = obj.value( "position" ).toString();
        _yarn2List.insert( valve.key, valve );
    }

    // 解析 extend 段
    QJsonArray extendArray = jsonObject.value( "extend" ).toArray();
    for ( int i = 0; i < extendArray.size(); ++i )
    {
        QJsonObject obj = extendArray[ i ].toObject();
        ValveStruct valve;
        valve.key      = obj.value( "key" ).toInt();
        valve.mappedId = obj.value( "mappedId" ).toInt();
        valve.value    = obj.value( "value" ).toString();
        valve.position = obj.value( "position" ).toString();
        _extendList.insert( valve.key, valve );
    }

    // 获取function数组
    QJsonArray functionArray = jsonObject[ "function" ].toArray();
    // 遍历function数组
    for ( int i = 0; i < functionArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject item = functionArray.at( i ).toObject();

        // 获取key和value属性
        FunctionStruct functionStruct;
        functionStruct.key   = item[ "key" ].toInt();
        functionStruct.value = item[ "value" ].toString();

        // 获取actions数组
        QJsonArray actionsArray = item[ "actions" ].toArray();
        for ( int j = 0; j < actionsArray.size(); ++j )
        {
            QJsonObject actionItem = actionsArray.at( j ).toObject();

            ActionStruct action;
            action.step  = actionItem[ "step" ].toInt();  // 新增step字段的处理逻辑，默认为0，表示不分步，1表示第一步，-1表示最后一步，-2表示倒数第二步，以此类推。
            action.type  = actionItem[ "type" ].toInt();  // 新增type字段的处理逻辑，0表示气阀，1表示电机，2表示主电机，3表示选针器。
            action.id    = actionItem[ "id" ].toInt();  // 新增id字段的处理逻辑，0表示气阀1，1表示气阀2，以此类推。
            action.delay = actionItem[ "delay" ].toInt();  // 新增delay字段的处理逻辑，表示延迟时间，单位毫秒。
            action.position = actionItem[ "position" ].toInt();  // 新增position字段的处理逻辑
            action.value = actionItem[ "value" ].toInt();  // 新增value字段的处理逻辑，表示电机转动角度或电机转动速度。
            functionStruct.actions.append( action );
        }

        // 对key和value进行处理
        this->_functionList.insert( functionStruct.key, functionStruct );
    }

    // 只解析 sockMotor 段
    QJsonArray sockMotorArray = jsonObject.value( "sockMotor" ).toArray();
    for ( int i = 0; i < sockMotorArray.size(); ++i )
    {
        QJsonObject     obj = sockMotorArray[ i ].toObject();
        SockMotorStruct motor;
        motor.id   = obj.value( "id" ).toInt();
        motor.name = obj.value( "name" ).toString();
        _sockMotorList.insert( motor.id, motor );
    }

    file.close();  // 关闭文件
}
