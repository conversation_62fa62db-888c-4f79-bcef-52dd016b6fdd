#ifndef LETTERINPUTDIALOG_H
#define LETTERINPUTDIALOG_H

#include <QDialog>

namespace Ui
{
class LetterInputDialog;
}

class LetterInputDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LetterInputDialog( QWidget* parent = nullptr, QString title = "Title", QString hint = "Input:" );
    ~LetterInputDialog();
    QString getInputText();
    void    changeHint( QString hint );

signals:
    void InputFinished( QString text );

private:
    Ui::LetterInputDialog* ui;
    void                   onBtnClicked();
};

#endif  // LETTERINPUTDIALOG_H
