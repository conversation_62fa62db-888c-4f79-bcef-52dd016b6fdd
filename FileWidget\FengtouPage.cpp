#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "fileform.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

// 缝头参数
void FileForm::initFengtouPage()
{
    if ( isFengtouPageInited )
        return;
    // 保存
    connect( ui->pbtn_FengtouSave, &QPushButton::clicked, this, [&]() { saveDataToMWWFile(); } );
    // 动作插入、删除、清除
    connect( this->ui->pbtn_actionAdd, &QPushButton::clicked, this, [&]() {
        // 最大不超过12个
        if ( this->currentFengtouStepStruct->Valve_cnt < 11 )
        {
            this->currentFengtouStepStruct->Valve_cnt++;
            this->ShowFengtouValve();
        }
    } );
    connect( this->ui->pbtn_actionDelete, &QPushButton::clicked, this, [&]() {
        if ( this->currentFengtouStepStruct->Valve_cnt > 0 )
        {
            this->currentFengtouStepStruct->Valve_cnt--;
            this->ShowFengtouValve();
        }
    } );
    connect( this->ui->pbtn_actionClear, &QPushButton::clicked, this, [&]() {
        this->currentFengtouStepStruct->Valve_cnt = 0;
        this->ShowFengtouValve();
    } );
    // 电机插入、删除、清除
    connect( this->ui->pbtn_motorAdd, &QPushButton::clicked, this, [&]() {
        // 最大不超过6个
        if ( this->currentFengtouStepStruct->motor_cnt < 5 )
        {
            this->currentFengtouStepStruct->motor_cnt++;
            this->ShowFengtouMotor();
        }
    } );
    connect( this->ui->pbtn_motorDelete, &QPushButton::clicked, this, [&]() {
        if ( this->currentFengtouStepStruct->motor_cnt > 0 )
        {
            this->currentFengtouStepStruct->motor_cnt--;
            this->ShowFengtouMotor();
        }
    } );
    connect( this->ui->pbtn_motorClear, &QPushButton::clicked, this, [&]() {
        this->currentFengtouStepStruct->motor_cnt = 0;
        this->ShowFengtouMotor();
    } );
    // 缝头组合名称修改，修改后会重新填充数据
    connect( this->ui->le_fengtouStepName, &MyLineEdit::mouseRelease, this, [&]() {
        this->ui->le_fengtouStepName->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );
        //        this->ui->le_StepName->selectAll();

        if ( this->_fengtouStepName_comboFrm == nullptr )
        {
            this->_fengtouStepName_comboFrm = new ComboForm( nullptr, this->linkCfg->getFengtouList() );  //不能有父类
            this->_fengtouStepName_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                           true );  //属性设置true:模态;false:非模态
            this->_fengtouStepName_comboFrm->setWindowTitle( tr( "选择组合名称" ) );
            this->_fengtouStepName_comboFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_fengtouStepName_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                qDebug() << id;
                this->_fengtouStepName_comboFrm->close();
                this->ui->le_fengtouStepName->setText( this->linkCfg->getFengtouList()->value( id ) );
                this->ShowFengtouStep( id );
            } );
            connect( this->_fengtouStepName_comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_fengtouStepName->setStyleSheet( "font-size:8pt;" ); } );
        }
        this->_fengtouStepName_comboFrm->show();
    } );
    // 缝头动作命令修改
    connect( this->ui->le_ftACommand, &MyLineEdit::mouseRelease, this, [&]() {
        this->ui->le_ftACommand->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );
        //        this->ui->le_StepName->selectAll();

        if ( this->_fengtouAction_comboFrm == nullptr )
        {
            this->_fengtouAction_comboFrm = new ComboForm( nullptr, this->linkCfg->getFengtouCommandList() );  //不能有父类
            this->_fengtouAction_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                         true );  //属性设置true:模态;false:非模态
            this->_fengtouAction_comboFrm->setWindowTitle( tr( "选择动作命令" ) );
            this->_fengtouAction_comboFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_fengtouAction_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                this->_fengtouAction_comboFrm->close();
                this->ui->le_ftACommand->setText( this->linkCfg->getFengtouCommandList()->value( id ) );
                this->currentFengtouStepStruct->dongzuo_zhiling = id;
            } );
            connect( this->_fengtouAction_comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_ftACommand->setStyleSheet( "font-size:8pt;" ); } );
        }
        this->_fengtouAction_comboFrm->show();
    } );
    // 缝头复位动作修改
    connect( this->ui->le_ftResetAction, &MyLineEdit::mouseRelease, this, [&]() {
        this->ui->le_ftResetAction->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );
        //        this->ui->le_StepName->selectAll();

        if ( this->_fengtouReset_comboFrm == nullptr )
        {
            this->_fengtouReset_comboFrm = new ComboForm( nullptr, this->linkCfg->getFengtouResetList() );  //不能有父类
            this->_fengtouReset_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                        true );  //属性设置true:模态;false:非模态
            this->_fengtouReset_comboFrm->setWindowTitle( tr( "选择复位动作" ) );
            this->_fengtouReset_comboFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_fengtouReset_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                this->_fengtouReset_comboFrm->close();
                this->ui->le_ftResetAction->setText( this->linkCfg->getFengtouResetList()->value( id ) );
                this->currentFengtouStepStruct->fuwei_dongzuo = id;
            } );
            connect( this->_fengtouReset_comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_ftResetAction->setStyleSheet( "font-size:8pt;" ); } );
        }
        this->_fengtouReset_comboFrm->show();
    } );

    this->ui->le_ft_WarnDelay->setProperty( "widget", "warnDelay" );
    connect( this->ui->le_ft_WarnDelay, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    this->ui->le_ftActionDelay->setProperty( "widget", "actionDelay" );
    connect( this->ui->le_ftActionDelay, &MyLineEdit::mouseRelease, this, &FileForm::onShowNumberInputForm );

    connect( ui->lw_FtStepList, &QListWidget::itemClicked, this, &FileForm::ShowFengtouParam );

    this->ui->le_fengtouStepName->setText( this->linkCfg->getFengtouList()->value( 0 ) );
    this->ShowFengtouStep( 0 );

    isFengtouPageInited = true;
}

void FileForm::ShowFengtouStep( int index )
{
    quint32 size = fatDataMain->FengtouVector.size();
    if ( index >= size || index < 0 )
        return;

    this->currentFengtouStruct = fatDataMain->FengtouVector.at( index );
    this->ui->lw_FtStepList->clear();
    for ( int i = 0; i < this->currentFengtouStruct->step_cnt; i++ )
    {
        QListWidgetItem* configItem = new QListWidgetItem( ui->lw_FtStepList );
        configItem->setText( QString( "%1" ).arg( i + 1 ) );
        ui->lw_FtStepList->insertItem( index, configItem );
    }

    // 默认选中第一行
    ui->lw_FtStepList->setCurrentRow( 0 );
    QModelIndex test = ui->lw_FtStepList->currentIndex();
    //    qDebug() << "click" << test;
    ui->lw_FtStepList->clicked( test );
}

void FileForm::ShowFengtouParam( QListWidgetItem* item )
{
    int step_index = ui->lw_FtStepList->currentIndex().row();
    ShowFengtouMsg( step_index );
}

void FileForm::ShowFengtouMsg( quint32 step_index )
{
    if ( step_index >= this->currentFengtouStruct->step_cnt || step_index < 0 )
        return;

    this->currentFengtouStepStruct = &this->currentFengtouStruct->step[ step_index ];
    this->ui->le_ftStepId->setText( QString( "%1" ).arg( step_index + 1 ) );
    this->ui->le_ft_WarnDelay->setText( QString( "%1" ).arg( this->currentFengtouStepStruct->baojing_yanshi ) );
    this->ui->le_ftActionDelay->setText( QString( "%1" ).arg( this->currentFengtouStepStruct->dongzuo_yanshi ) );
    this->ui->le_ftACommand->setText( this->linkCfg->getFengtouCommandList()->value( this->currentFengtouStepStruct->dongzuo_zhiling ) );
    this->ui->le_ftResetAction->setText( this->linkCfg->getFengtouResetList()->value( this->currentFengtouStepStruct->fuwei_dongzuo ) );

    this->ShowFengtouMotor();
    this->ShowFengtouValve();
}

void FileForm::ShowFengtouMotor()
{
    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_MotorList->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_MotorList );

    QHBoxLayout* rowLayout = new QHBoxLayout;
    QLabel*      label1    = new QLabel( "电机" );
    QLabel*      label2    = new QLabel( "名称" );
    QLabel*      label3    = new QLabel( "命令" );
    QLabel*      label4    = new QLabel( "设置" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    rowLayout->addWidget( label1 );
    rowLayout->addWidget( label2 );
    rowLayout->addWidget( label3 );
    rowLayout->addWidget( label4 );
    rowLayout->setStretch( 0, 1 );
    rowLayout->setStretch( 1, 2 );
    rowLayout->setStretch( 2, 1 );
    rowLayout->setStretch( 3, 1 );
    rowLayout->setMargin( 0 );
    rowLayout->setSpacing( 1 );
    mainLayout->addLayout( rowLayout );

    // 只显示6个
    for ( quint8 index = 0; index < 6; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString( "%1" ).arg( ( index + 1 ), 2, 10, QChar( '0' ) ) + "." );
        label->setAlignment( Qt::AlignHCenter );
        rowLayoutx->addWidget( label );
        MyLineEdit* lineEdit1 = new MyLineEdit;
        MyLineEdit* lineEdit2 = new MyLineEdit;
        MyLineEdit* lineEdit3 = new MyLineEdit;
        lineEdit1->setAlignment( Qt::AlignHCenter );
        lineEdit2->setAlignment( Qt::AlignHCenter );
        lineEdit3->setAlignment( Qt::AlignHCenter );
        if ( this->currentFengtouStepStruct->motor_cnt > 0 && index <= this->currentFengtouStepStruct->motor_cnt - 1 )
        {
            FATParser::FengtouMotorStruct cmd = this->currentFengtouStepStruct->motor_array[ index ];
            lineEdit1->setText( this->linkCfg->getMotorMapList()->value( cmd.num + 1 ) );
            //            qDebug() << "motorName" << this->linkCfg->getMotorMapList()->value( cmd.num + 1 ) << cmd.num + 1;
            lineEdit2->setText( this->linkCfg->getMotorPatternList()->value( cmd.pattern ) );
            lineEdit3->setText( QString( "%1" ).arg( cmd.val ) );
            lineEdit1->setProperty( "index", index );
            lineEdit2->setProperty( "index", index );
            // 点击跳出修改窗口
            connect( lineEdit1, &MyLineEdit::mouseRelease, this, &FileForm::onMotorNameClicked );
            connect( lineEdit2, &MyLineEdit::mouseRelease, this, &FileForm::onMotorPatternClicked );
        }
        else
        {
            lineEdit1->setText( "-" );
            lineEdit2->setText( "-" );
            lineEdit3->setText( "-" );
        }
        rowLayoutx->addWidget( lineEdit1 );
        rowLayoutx->addWidget( lineEdit2 );
        rowLayoutx->addWidget( lineEdit3 );
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 2 );
        rowLayoutx->setStretch( 2, 1 );
        rowLayoutx->setStretch( 3, 1 );
        rowLayoutx->setMargin( 0 );
        rowLayoutx->setSpacing( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setSpacing( 4 );
        mainLayout->setContentsMargins( 6, 1, 6, 1 );
    }
}

void FileForm::onMotorNameClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );

    if ( this->_motormap_comboFrm == nullptr )
    {
        this->_motormap_comboFrm = new ComboForm( nullptr, this->linkCfg->getMotorMapList() );  //不能有父类
        this->_motormap_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                true );  //属性设置true:模态;false:非模态
        this->_motormap_comboFrm->setWindowTitle( tr( "选择电机类型" ) );
        this->_motormap_comboFrm->setWindowFlags(
            /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_motormap_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            //            qDebug() << id;
            this->_motormap_comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->linkCfg->getMotorMapList()->value( id ) );
            QVariant var = this->currentSelectedMyLineEdit->property( "index" );
            if ( var.isValid() )
            {
                int                            index = var.toInt();
                FATParser::FengtouMotorStruct* cmd   = &this->currentFengtouStepStruct->motor_array[ index ];
                cmd->num                             = id - 1;
            }
        } );
        connect( this->_motormap_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
    }
    this->_motormap_comboFrm->show();
}

void FileForm::onMotorPatternClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );

    if ( this->_motorPattern_comboFrm == nullptr )
    {
        this->_motorPattern_comboFrm = new ComboForm( nullptr, this->linkCfg->getMotorPatternList() );  //不能有父类
        this->_motorPattern_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                    true );  //属性设置true:模态;false:非模态
        this->_motorPattern_comboFrm->setWindowTitle( tr( "选择电机命令" ) );
        this->_motorPattern_comboFrm->setWindowFlags(
            /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_motorPattern_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            //            qDebug() << id;
            this->_motorPattern_comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->linkCfg->getMotorPatternList()->value( id ) );
            QVariant var = this->currentSelectedMyLineEdit->property( "index" );
            if ( var.isValid() )
            {
                int                            index = var.toInt();
                FATParser::FengtouMotorStruct* cmd   = &this->currentFengtouStepStruct->motor_array[ index ];
                cmd->pattern                         = id;
            }
        } );
        connect( this->_motorPattern_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
    }
    this->_motorPattern_comboFrm->show();
}

void FileForm::ShowFengtouValve()
{
    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_valveList->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_valveList );

    QHBoxLayout* rowLayout = new QHBoxLayout;
    QLabel*      label1    = new QLabel( "动作" );
    QLabel*      label2    = new QLabel( "类型" );
    QLabel*      label3    = new QLabel( "名称" );
    QLabel*      label4    = new QLabel( "状态" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    rowLayout->addWidget( label1 );
    rowLayout->addWidget( label2 );
    rowLayout->addWidget( label3 );
    rowLayout->addWidget( label4 );
    rowLayout->setStretch( 0, 1 );
    rowLayout->setStretch( 1, 1 );
    rowLayout->setStretch( 2, 2 );
    rowLayout->setStretch( 3, 1 );
    rowLayout->setMargin( 0 );
    rowLayout->setSpacing( 1 );
    mainLayout->addLayout( rowLayout );
    //    mainLayout->setStretch( 0, 1 );

    // 只显示12个
    for ( quint8 index = 0; index < 12; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString( "%1" ).arg( ( index + 1 ), 2, 10, QChar( '0' ) ) + "." );
        label->setAlignment( Qt::AlignHCenter );
        rowLayoutx->addWidget( label );
        MyLineEdit* lineEdit1 = new MyLineEdit;
        MyLineEdit* lineEdit2 = new MyLineEdit;
        MyLineEdit* lineEdit3 = new MyLineEdit;
        lineEdit1->setAlignment( Qt::AlignHCenter );
        lineEdit2->setAlignment( Qt::AlignHCenter );
        lineEdit3->setAlignment( Qt::AlignHCenter );
        if ( this->currentFengtouStepStruct->Valve_cnt > 0 && index <= this->currentFengtouStepStruct->Valve_cnt - 1 )
        {
            FATParser::FengtouSignalValveStruct cmd = this->currentFengtouStepStruct->Valve_array[ index ];
            lineEdit1->setText( cmd.type == 0 ? QString( "气阀" ) : QString( "信号" ) );
            lineEdit2->setText( this->linkCfg->getAirValveList()->value( cmd.num ) );
            QString state = cmd.type == 0 ? cmd.state == 0 ? QString( "退" ) : QString( "进" ) : cmd.state == 0 ? QString( "无效" ) : QString( "有效" );
            lineEdit3->setText( state );
            lineEdit3->setProperty( "type", cmd.type );
            lineEdit1->setProperty( "index", index );
            lineEdit2->setProperty( "index", index );
            lineEdit3->setProperty( "index", index );
            // 点击跳出修改窗口
            connect( lineEdit1, &MyLineEdit::mouseRelease, this, &FileForm::onFengtouVSTypeClicked );
            connect( lineEdit2, &MyLineEdit::mouseRelease, this, &FileForm::onFengtouVSNameClicked );
            connect( lineEdit3, &MyLineEdit::mouseRelease, this, &FileForm::onFengtouVSStateClicked );
        }
        else
        {
            lineEdit1->setText( "-" );
            lineEdit2->setText( "-" );
            lineEdit3->setText( "-" );
        }
        rowLayoutx->addWidget( lineEdit1 );
        rowLayoutx->addWidget( lineEdit2 );
        rowLayoutx->addWidget( lineEdit3 );
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 1 );
        rowLayoutx->setStretch( 2, 2 );
        rowLayoutx->setStretch( 3, 1 );
        rowLayoutx->setMargin( 0 );
        rowLayoutx->setSpacing( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setSpacing( 7 );
        mainLayout->setContentsMargins( 6, 1, 1, 6 );
    }
}

void FileForm::onFengtouVSTypeClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );

    if ( this->_fengtouVSType_comboFrm == nullptr )
    {
        this->_fengtouVSType_comboFrm = new ComboForm( nullptr, this->linkCfg->getFengtouVSTypeList() );  //不能有父类
        this->_fengtouVSType_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                     true );  //属性设置true:模态;false:非模态
        this->_fengtouVSType_comboFrm->setWindowTitle( tr( "选择气阀或信号" ) );
        this->_fengtouVSType_comboFrm->setWindowFlags(
            /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_fengtouVSType_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            qDebug() << id;
            this->_fengtouVSType_comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->linkCfg->getFengtouVSTypeList()->value( id ) );
            QVariant var = this->currentSelectedMyLineEdit->property( "index" );
            if ( var.isValid() )
            {
                int                                  index = var.toInt();
                FATParser::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
                cmd->type                                  = id;
            }

            // 找到后面第2个LineEdit并添加type属性
            QLayout* vBoxLayout = this->ui->gbox_valveList->layout();
            for ( int i = 0; i < vBoxLayout->count(); ++i )
            {
                QHBoxLayout* hBoxLayout = qobject_cast< QHBoxLayout* >( vBoxLayout->itemAt( i )->layout() );
                if ( hBoxLayout )
                {
                    // 遍历当前 QHBoxLayout 中的 QLineEdit
                    for ( int j = 0; j < hBoxLayout->count(); ++j )
                    {
                        QLineEdit* lineEdit = qobject_cast< QLineEdit* >( hBoxLayout->itemAt( j )->widget() );
                        if ( lineEdit == this->currentSelectedMyLineEdit )
                        {
                            // 添加type属性
                            //                            qDebug() << "LineEdit text:" << lineEdit->text();
                            QLineEdit* nextLineEdit = qobject_cast< QLineEdit* >( hBoxLayout->itemAt( j + 2 )->widget() );
                            nextLineEdit->setProperty( "type", id );
                            //                            qDebug() << "LineEdit text:" << nextLineEdit->text();
                        }
                    }
                }
            }
        } );
        connect( this->_fengtouVSType_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
    }
    this->_fengtouVSType_comboFrm->show();
}

void FileForm::onFengtouVSNameClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );

    if ( this->_fengtouVSName_comboFrm == nullptr )
    {
        this->_fengtouVSName_comboFrm = new ComboForm( nullptr, this->linkCfg->getAirValveList(), 10 );  //不能有父类
        this->_fengtouVSName_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                     true );  //属性设置true:模态;false:非模态
        this->_fengtouVSName_comboFrm->setWindowTitle( tr( "选择气阀或信号" ) );
        this->_fengtouVSName_comboFrm->setWindowFlags(
            /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_fengtouVSName_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            //            qDebug() << id;
            this->_fengtouVSName_comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->linkCfg->getAirValveList()->value( id ) );
            QVariant var = this->currentSelectedMyLineEdit->property( "index" );
            if ( var.isValid() )
            {
                int                                  index = var.toInt();
                FATParser::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
                cmd->num                                   = id;
            }
        } );
        connect( this->_fengtouVSName_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
    }
    this->_fengtouVSName_comboFrm->show();
}

void FileForm::onFengtouVSStateClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:8pt;border: 1px solid #FC5531;" );

    int type = senderLineEdit->property( "type" ).toInt();
    if ( type == 0 )
    {
        if ( this->_fengtouVState_comboFrm == nullptr )
        {
            this->_fengtouVState_comboFrm = new ComboForm( nullptr, this->linkCfg->getFengtouVStateList() );  //不能有父类
            this->_fengtouVState_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                         true );  //属性设置true:模态;false:非模态
            this->_fengtouVState_comboFrm->setWindowTitle( tr( "选择气阀状态" ) );
            this->_fengtouVState_comboFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_fengtouVState_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                //                qDebug() << id;
                this->_fengtouVState_comboFrm->close();
                this->currentSelectedMyLineEdit->setText( this->linkCfg->getFengtouVStateList()->value( id ) );
                QVariant var = this->currentSelectedMyLineEdit->property( "index" );
                if ( var.isValid() )
                {
                    int                                  index = var.toInt();
                    FATParser::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
                    cmd->state                                 = id;
                }
            } );
            connect( this->_fengtouVState_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
        }
        this->_fengtouVState_comboFrm->show();
    }
    else
    {
        if ( this->_fengtouSState_comboFrm == nullptr )
        {
            this->_fengtouSState_comboFrm = new ComboForm( nullptr, this->linkCfg->getFengtouSStateList() );  //不能有父类
            this->_fengtouSState_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                         true );  //属性设置true:模态;false:非模态
            this->_fengtouSState_comboFrm->setWindowTitle( tr( "选择信号状态" ) );
            this->_fengtouSState_comboFrm->setWindowFlags(
                /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

            connect( this->_fengtouSState_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                //                qDebug() << id;
                this->_fengtouSState_comboFrm->close();
                this->currentSelectedMyLineEdit->setText( this->linkCfg->getFengtouSStateList()->value( id ) );
                QVariant var = this->currentSelectedMyLineEdit->property( "index" );
                if ( var.isValid() )
                {
                    int                                  index = var.toInt();
                    FATParser::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
                    cmd->state                                 = id;
                }
            } );
            connect( this->_fengtouSState_comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:8pt;" ); } );
        }
        this->_fengtouSState_comboFrm->show();
    }
}
