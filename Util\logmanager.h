#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QMutex>

class LogManager : public QObject
{
    Q_OBJECT
public:
    static LogManager* getInstance();
    void initLogFile();
    void messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg);

private:
    explicit LogManager(QObject *parent = nullptr);
    ~LogManager();
    
    static LogManager* instance;
    QFile* logFile;
    QTextStream* textStream;
    QMutex mutex;
};

#endif // LOGMANAGER_H