#include "testform.h"
#include "ui_testform.h"
#include <QDebug>
#include <QHBoxLayout>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QVBoxLayout>

void TestForm::FunctionTestInit( void )
{
    if ( isFunctionTestPageInit == false )
    {
        // 每行显示的按钮数量
        const int buttonsPerRow = 10;

        // 获取function列表
        QMap< int, ReadCfg::FunctionStruct >* functionList = mainData->readCfg->getFunctionList();

        if ( functionList != nullptr && !functionList->isEmpty() )
        {
            // 创建水平布局作为主布局
            QHBoxLayout* functionLayout = new QHBoxLayout( this->ui->frame_function );

            int size  = functionList->size();
            int lines = ( size - 1 ) / buttonsPerRow + 1;  // 计算需要多少列

            auto iter = functionList->begin();
            for ( int line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
            {
                // 为每列创建一个垂直布局
                QVBoxLayout* columnLayout = new QVBoxLayout;

                for ( int col = 0; col < buttonsPerRow; col++ )
                {
                    if ( line * buttonsPerRow + col < size )
                    {
                        // 创建按钮
                        QPushButton* btn = new QPushButton( iter.value().value );
                        btn->setProperty( "id", iter.value().key );
                        iter++;

                        // 设置按钮样式
                        btn->setStyleSheet( "QPushButton { "
                                            "font-size:8pt;"
                                            "color: #333333;"
                                            "background-color: #ffffff;"
                                            "border: 1px solid #d0d0d0;"
                                            "border-radius: 4px;"
                                            "padding: 4px;"
                                            "margin: 2px;"
                                            "min-height: 30px;"
                                            "min-width: 80px;"
                                            "}" );
                        btn->setMinimumHeight( 40 );

                        // 连接点击信号
                        connect( btn, &QPushButton::clicked, this, &TestForm::onFunctionButtonClicked );

                        columnLayout->addWidget( btn );
                    }
                    else
                    {
                        // 如果没有更多按钮，添加空白widget保持布局
                        QWidget* widget = new QWidget();
                        columnLayout->addWidget( widget );
                        widget->setMinimumHeight( 40 );
                    }
                }
                functionLayout->addLayout( columnLayout );
            }
        }

        // 初始化当前选中的功能按钮为nullptr
        currentSelectedFunctionBtn = nullptr;

        isFunctionTestPageInit = true;
    }
}

// 按钮点击回调函数
void TestForm::onFunctionButtonClicked()
{
    QPushButton* btn = qobject_cast< QPushButton* >( sender() );
    if ( btn )
    {
        int id = btn->property( "id" ).toInt();
        qDebug() << "Function button clicked, id:" << id;

        // 如果有之前选中的按钮，恢复其样式
        if ( currentSelectedFunctionBtn )
        {
            currentSelectedFunctionBtn->setStyleSheet( "QPushButton { "
                                                       "font-size:8pt;"
                                                       "color: #333333;"
                                                       "background-color: #ffffff;"
                                                       "border: 1px solid #d0d0d0;"
                                                       "border-radius: 4px;"
                                                       "padding: 4px;"
                                                       "margin: 2px;"
                                                       "min-height: 30px;"
                                                       "min-width: 80px;"
                                                       "}" );
        }

        // 设置当前按钮为绿色背景
        btn->setStyleSheet( "QPushButton { "
                            "font-size:8pt;"
                            "color: white;"
                            "background-color: #3498db;"
                            "border: 1px solid rgb(25, 146, 226);"
                            "border-radius: 4px;"
                            "padding: 4px;"
                            "margin: 2px;"
                            "min-height: 30px;"
                            "min-width: 80px;"
                            "}" );

        // 更新当前选中的按钮
        currentSelectedFunctionBtn = btn;

        // 执行功能对应的动作
        executeFunctionAction( id );
    }
}

// 执行功能对应的动作
void TestForm::executeFunctionAction( int functionId )
{
    // 获取功能列表
    QMap< int, ReadCfg::FunctionStruct >* functionList = mainData->readCfg->getFunctionList();

    if ( functionList && functionList->contains( functionId ) )
    {
        ReadCfg::FunctionStruct function = functionList->value( functionId );

        // 准备发送数据
        int actionsCount = function.actions.size();
        if ( actionsCount > 0 )
        {
            // 创建数据缓冲区
            QByteArray buffer;

            // 添加actions数量（1字节）
            buffer.append( static_cast< char >( actionsCount ) );

            // 遍历功能中的所有动作并添加到缓冲区
            for ( int i = 0; i < actionsCount; i++ )
            {
                ReadCfg::ActionStruct* action = &function.actions[ i ];
                // 添加动作类型（1字节）
                buffer.append( static_cast< char >( action->type ) );
                // 添加动作ID（1字节）
                buffer.append( static_cast< char >( action->id ) );
                // 添加动作值（1字节）
                buffer.append( static_cast< char >( action->value ) );
                // 添加延迟值（2字节）
                buffer.append( static_cast< quint16 >( action->delay ) );
                // qDebug() << "添加动作:"
                //          << "类型=" << action->type << "ID=" << action->id << "值=" << action->value << "延迟=" << action->delay;
            }

            // 发送数据到缓冲区
            comm->pushDataTobuffer( 0x0E, reinterpret_cast< quint8* >( buffer.data() ), buffer.size() );
            // qDebug() << "发送功能ID:" << functionId << "，共" << actionsCount << "个动作";
        }
    }
}
