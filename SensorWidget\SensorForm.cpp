#include "SensorForm.h"
#include "ui_SensorForm.h"

SensorForm::SensorForm( QWidget* parent, MainWidgetData* mainData, Communication* com ) : QWidget( parent ), ui( new Ui::SensorForm ), mainData( mainData ), comm( com )
{
    ui->setupUi( this );

    connect( ui->SensorFormHome_btn, SIGNAL( clicked() ), this, SLOT( onSensorHomeBtnClicked() ) );

    /* 报警测试初始化 */
    this->WarnTestPageInit();
}

/* 测试界面返回 主菜单键按下槽函数 */
void SensorForm::onSensorHomeBtnClicked()
{
    this->close();
    emit SensorFormToMainWinToShowSignal();
}

SensorForm::~SensorForm()
{
    delete ui;
}
