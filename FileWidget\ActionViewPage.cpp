#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "fileform.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

// 动作与选针
void FileForm::initActionViewPage()
{
    if ( this->isActionPageInited == false )
    {
        // 返回链条编辑
        connect( this->ui->pbtn_toChainEdit, &QPushButton::clicked, this, [&]() {
            this->ui->File_stackedWidget->setCurrentIndex( 1 );
            this->ui->File_Tiltle_label->setText( ( "文件/缝头动作" ) );
        } );

        // to选针资料
        connect( this->ui->pbtn_toNeedleEdit, &QPushButton::clicked, this, [&]() {
            this->ui->File_stackedWidget->setCurrentIndex( 4 );
            this->ui->File_Tiltle_label->setText( ( "文件/选针资料编辑" ) );
            initNeedleEditPage();
        } );

        // 动作上一页和下一页按钮槽函数
        connect( this->ui->pbtn_ValveNext, &QPushButton::clicked, this, [&]() {
            this->currentVActionPage++;
            ShowVActionList();
        } );
        connect( this->ui->pbtn_ValvePrev, &QPushButton::clicked, this, [&]() {
            if ( this->currentVActionPage > 0 )
            {
                this->currentVActionPage--;
                ShowVActionList();
            }
            else
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到第1页" ) );
        } );

        // 选针上一页和下一页按钮槽函数
        connect( this->ui->pbtn_NeedleNext, &QPushButton::clicked, this, [&]() {
            this->currentNeedlePage++;
            ShowNeedleView();
        } );
        connect( this->ui->pbtn_NeedlePrev, &QPushButton::clicked, this, [&]() {
            if ( this->currentNeedlePage > 0 )
            {
                this->currentNeedlePage--;
                ShowNeedleView();
            }
            else
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到第1页" ) );
        } );

        this->isActionPageInited = true;
    }
    ShowVActionList();
    ShowNeedleView();
}

void FileForm::ShowVActionList()
{
    FATParser::ActionStruct action = this->fatDataMain->NeedleActionDB.data()->action_data[ this->currentActionDetailIndex ];
    quint8                  count  = action.valid_Valve_cnt;
    // 先判断页码是否准确
    if ( this->currentVActionPage * 40 >= count )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到最后1页" ) );
        this->currentVActionPage--;
        return;
    }

    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_VActions->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_VActions );
    // 只显示4Line
    for ( quint8 index = 0; index < 4; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString( "序号:\n位置:" ) );
        label->setAlignment( Qt::AlignCenter );
        rowLayoutx->addWidget( label );
        for ( quint8 row_index = 0; row_index < 10; row_index++ )
        {
            MyPlainTextEdit* plainEdit = new MyPlainTextEdit;
            if ( this->currentVActionPage * 40 + index * 10 + row_index < count )
            {
                // data
                quint8  valve_num = action.Valve[ this->currentVActionPage * 40 + index * 10 + row_index ].Valve_num;
                quint8  sign      = action.Valve[ this->currentVActionPage * 40 + index * 10 + row_index ].sign;
                quint16 pos       = action.Valve[ this->currentVActionPage * 40 + index * 10 + row_index ].Valve_pos / 2;  //这里有个坑，位置要除以2才准确
                quint8  state     = action.Valve[ this->currentVActionPage * 40 + index * 10 + row_index ].Valve_state;
                plainEdit->insertPlainText( QString::number( valve_num ) + "\n" + ( sign == 1 ? QString( "-" ) : QString( "" ) ) + QString::number( pos ) );
                plainEdit->setProperty( "currentIndex", this->currentVActionPage * 40 + index * 10 + row_index );
            }
            else
            {
                plainEdit->insertPlainText( "0\n0" );
            }
            QFont currentFont = plainEdit->font();
            currentFont.setPointSize( 6 );
            plainEdit->setFont( currentFont );
            QTextDocument* doc = plainEdit->document();
            doc->setDefaultTextOption( QTextOption( Qt::AlignCenter ) );
            rowLayoutx->addWidget( plainEdit );
            // 点击跳出修改窗口
            connect( plainEdit, &MyPlainTextEdit::mouseReleased, this, &FileForm::onValveItemClicked );
        }
        mainLayout->addLayout( rowLayoutx );
    }
}

void FileForm::ShowNeedleView()
{
    // 总共是10个选针数据
    quint8 count = 10;
    // 先判断页码是否准确,这里每页显示了6行
    if ( this->currentNeedlePage * 6 >= count )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到最后1页" ) );
        this->currentNeedlePage--;
        return;
    }

    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_NeedleView->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_NeedleView );
    // 只显示6Line
    for ( quint8 index = 0; index < 6; index++ )
    {
        FATParser::GudingNeedleIndex gudingNeedleIndex = this->fatDataMain->NeedleActionDB.data()->action_data[ this->currentActionDetailIndex ].Needle_index[ this->currentNeedlePage * 6 + index ];
        QString                      state;
        switch ( gudingNeedleIndex.state )
        {
            case 0:
                state = QString( "" );
                break;
            case 1:
                state = QString( "*" );
                break;
            case 2:
                state = QString( "&" );
                break;
            case 3:
                state = QString( "#" );
                break;
            default:
                break;
        }
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( this->linkCfg->getNeedleNameList()->value( this->currentNeedlePage * 6 + index ) );
        label->setAlignment( Qt::AlignCenter );
        rowLayoutx->addWidget( label );

        MyLineEdit* lineEdit = new MyLineEdit();
        if ( this->currentNeedlePage * 6 + index < count )
        {
            lineEdit->setText( state );
            // 保存index，用于保存数据时候用
            lineEdit->setProperty( "index", this->currentNeedlePage * 6 + index );
            connect( lineEdit, &MyLineEdit::mouseRelease, this, &FileForm::onNeedleStateClicked );
        }
        else
            lineEdit->setText( "" );
        lineEdit->setAlignment( Qt::AlignCenter );
        rowLayoutx->addWidget( lineEdit );

        quint8      needle_index = gudingNeedleIndex.Needle_index;
        MyLineEdit* lineEdit1    = new MyLineEdit();
        if ( this->currentNeedlePage * 6 + index < count )
            lineEdit1->setText( "No." + QString::number( needle_index, 10 ) );
        else
            lineEdit1->setText( "" );
        lineEdit1->setAlignment( Qt::AlignCenter );
        rowLayoutx->addWidget( lineEdit1 );

        quint8                          data[ 25 ];
        FATParser::NeedleValveDataBase* db = this->fatDataMain->NeedleActionDB.data();
        memcpy( data, ( quint8* )&db->Needle_data[ needle_index - 1 ][ 0 ], 25 );

        if ( this->currentNeedlePage * 6 + index < count )
        {
            NeedleView* myNeedleView = new NeedleView( data, nullptr );
            rowLayoutx->addWidget( myNeedleView );
            // 选中时有2px宽的边框，因此宽为604
            myNeedleView->setMinimumWidth( 604 );
            myNeedleView->setMinimumHeight( 40 );
        }
        else
        {
            QLabel* qLabel = new QLabel( "" );
            rowLayoutx->addWidget( qLabel );
            qLabel->setMinimumWidth( 604 );
            qLabel->setMinimumHeight( 40 );
        }
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 1 );
        rowLayoutx->setStretch( 2, 1 );
        rowLayoutx->setStretch( 3, 4 );

        mainLayout->addLayout( rowLayoutx );
    }
}

void FileForm::onNeedleStateClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    senderLineEdit->setStyleSheet( "border: 1px solid #FC5531;" );
    this->currentSelectedMyLineEdit = senderLineEdit;

    if ( this->_needleState_comboFrm == nullptr )
    {
        this->_needleState_comboFrm = new ComboForm( nullptr, this->linkCfg->getNeedleStateList() );  //不能有父类
        this->_needleState_comboFrm->setAttribute( Qt::WA_ShowModal,
                                                   true );  //属性设置true:模态;false:非模态
        this->_needleState_comboFrm->setWindowTitle( tr( "选择选针状态" ) );
        this->_needleState_comboFrm->setWindowFlags(
            /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

        connect( this->_needleState_comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            //            qDebug() << id;
            this->_needleState_comboFrm->close();
            if ( this->currentSelectedMyLineEdit != nullptr )
            {
                this->currentSelectedMyLineEdit->setText( this->linkCfg->getNeedleStateList()->value( id ) );
                // 保存选针状态
                QVariant var = this->currentSelectedMyLineEdit->property( "index" );
                if ( var.isValid() )
                {
                    int index = var.toInt();
                    // 利用指针来修改
                    FATParser::GudingNeedleIndex* gudingNeedleIndex = &this->fatDataMain->NeedleActionDB.data()->action_data[ this->currentActionDetailIndex ].Needle_index[ index ];
                    gudingNeedleIndex->state                        = id;
                    //                    qDebug() << "needleState" << id << index << this->currentActionDetailIndex;
                }
            }
        } );
        connect( this->_needleState_comboFrm, &ComboForm::finished, this, [&]() {
            if ( this->currentSelectedMyLineEdit != nullptr )
            {
                this->currentSelectedMyLineEdit->setStyleSheet( "" );
            }
        } );
    }
    this->_needleState_comboFrm->show();
}

void FileForm::onValveItemClicked()
{
    MyPlainTextEdit* senderTextEdit = qobject_cast< MyPlainTextEdit* >( sender() );
    if ( senderTextEdit != nullptr )
    {
        QVariant var = senderTextEdit->property( "currentIndex" );
        if ( var.isValid() )
        {
            qDebug() << var.toInt();
            // 此处用的是正式数据的原地址指针，保存时直接修改
            FATParser::ActionStruct* action = &this->fatDataMain->NeedleActionDB.data()->action_data[ this->currentActionDetailIndex ];
            FATParser::ValveData*    valve  = &action->Valve[ var.toInt() ];
            if ( this->_valveEditFrm == nullptr )
            {
                this->_valveEditFrm = new ValveEditForm( valve, this->linkCfg->getAirValveList(), nullptr );
                connect( this->_valveEditFrm, &ValveEditForm::finished, this, [&]() { this->ShowVActionList(); } );
            }
            else
            {
                this->_valveEditFrm->setValveData( valve );
            }
            this->_valveEditFrm->show();
        }
    }
}
