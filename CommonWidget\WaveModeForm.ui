<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WaveModeForm</class>
 <widget class="QWidget" name="WaveModeForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>250</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>250</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>编织模式选择</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#WaveModeForm {
    background-color: #2C3E50;
    border: 2px solid #3498DB;
    border-radius: 8px;
}

QPushButton {
    background-color: #3498DB;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980B9;
}

QPushButton:pressed {
    background-color: #1A5276;
}

QLabel {
    color: #ECF0F1;
    font-weight: bold;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item>
    <widget class="QLabel" name="lblTitle">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <pointsize>14</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #3498DB;
background-color: #34495E;
border-radius: 5px;
padding: 5px;</string>
     </property>
     <property name="text">
      <string>请选择编织方式</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>10</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btnContinue">
       <property name="minimumSize">
        <size>
         <width>140</width>
         <height>50</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #27AE60;
border: 1px solid #1E8449;</string>
       </property>
       <property name="text">
        <string>断电续织</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/continue.png</normaloff>:/icons/continue.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btnRestart">
       <property name="minimumSize">
        <size>
         <width>140</width>
         <height>50</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #E74C3C;
border: 1px solid #C0392B;</string>
       </property>
       <property name="text">
        <string>重新编织</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/icons/restart.png</normaloff>:/icons/restart.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>10</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
