#include "sokparser.h"

SokParser::SokParser() {}

int SokParser::Parser( QString filePath, SokFileData* sokFileData )
{
    QFile file( filePath );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "Open File Failed!\n";
        return -1;
    }

    QByteArray data = file.readAll();
    if ( data.isEmpty() )
    {
        qDebug() << "File is empty!\n";
        return -1;
    }

    // 文件保存模式，默认是0 顺序，读取文件后判断是顺序还是逆序
    quint8 fileSaveType = 0;
    // 搜索类型 G616，前面05是长度
    const QByteArray searchFor = QByteArray::fromHex( "05 00 47 36 31 36 00" );  // 要搜索的字节序列
    int              index     = data.indexOf( searchFor );                      // 搜索字节序列

    if ( index == -1 )
    {
        qDebug() << "Machine Type not found.";
        return -1;
    }
    else
    {
        qDebug() << "Machine Type Founded: " << index;
        // 在文件尾部，则说明是逆序
        if ( index >= data.length() - 10 )
        {
            fileSaveType = 1;
        }
    }

    /*读取头部数据*/
    sokFileData->headParam = GetHeadParam( &data );
    /*读取缝头数据*/
    sokFileData->closedToeParam = GetClosedToeParam( &data );
    /*读取链条数据*/
    sokFileData->chainData = GetChainData( &data, fileSaveType, index );
    /* Graduation Motor密度数据 */
    sokFileData->graduationMotorParam = getGraduationMotorParamVector( &data );
    /* Elastic Motor 密度数据 */
    sokFileData->elasticMotorParam = getElasticMotorParamVector( &data );
    /*Sinker Motor密度数据*/
    sokFileData->sinkerMotorParam = getSinkerMotorParamVector( &data );
    /* 模块数据*/
    sokFileData->moduleDataVector = getModuleDataVector( &data );

    file.close();
    return 1;
}

QSharedPointer< SokParser::HeadParam > SokParser::GetHeadParam( QByteArray* data )
{
    if ( data->data() == Q_NULLPTR )
    {
        return Q_NULLPTR;
    }
    QSharedPointer< SokParser::HeadParam > db = QSharedPointer< SokParser::HeadParam >( new HeadParam );

    // 搜索类型 0E 01 02，作用不详，疑似固定字符
    const QByteArray searchFor = QByteArray::fromHex( "0E 01 02" );  // 要搜索的字节序列
    int              index     = data->indexOf( searchFor );         // 搜索字节序列
    if ( index == -1 )
    {
        qDebug() << "HeadParam not found.";
        return db;
    }
    else if ( index > 0x300 )
    {
        qDebug() << "HeadParam not correct.";
        return db;
    }

    char*   buffer = data->data();
    quint16 offset = index + 4;
    // 针数
    if ( *( buffer + offset ) == 0x00 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i         = 0;
        db->needleNum = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->needleNum = db->needleNum * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // 选针器段数
    if ( *( buffer + offset ) == 0x01 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i                  = 0;
        db->jackButtsDiagonals = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->jackButtsDiagonals = db->jackButtsDiagonals * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // 加油编程
    if ( *( buffer + offset ) == 0x02 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i          = 0;
        db->oilProgram = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->oilProgram = db->oilProgram * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // 直径
    if ( *( buffer + offset ) == 0x03 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i        = 0;
        db->diameter = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->diameter = db->diameter * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // 哈夫盘位置表
    if ( *( buffer + offset ) == 0x04 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i     = 0;
        int index = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->dialMotorPos[ index ] = 0;
            while ( *( buffer + offset + 4 + i ) != 0x20 && *( buffer + offset + 4 + i ) != 0x00 )
            {
                db->dialMotorPos[ index ] = db->dialMotorPos[ index ] * 10 + *( buffer + offset + 4 + i ) - 0x30;
                i++;
            }
            if ( *( buffer + offset + 4 + i ) == 0x20 )
                i++;  // 跳过0x20
            index++;
        }
        offset += i + 7;
    }
    // 花型开始位置
    if ( *( buffer + offset ) == 0x05 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i               = 0;
        db->patternStartPos = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->patternStartPos = db->patternStartPos * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // stitch Cam Gauge
    if ( *( buffer + offset ) == 0x06 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i              = 0;
        db->stitchCamGauge = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->stitchCamGauge = db->stitchCamGauge * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // Size to active machine
    if ( *( buffer + offset ) == 0x07 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i                   = 0;
        db->sizeToActiveMachine = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->sizeToActiveMachine = db->sizeToActiveMachine * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // reverse Valve Regulation
    if ( *( buffer + offset ) == 0x08 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i                  = 0;
        db->revValveRegulation = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            db->revValveRegulation = db->revValveRegulation * 10 + *( buffer + offset + 4 + i ) - 0x30;
            i++;
        }
        offset += i + 7;
    }
    // Grams YOYO Reset Machine
    if ( *( buffer + offset ) == 0x09 && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i     = 0;
        int index = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            if ( index >= 8 )
            {
                i++;
                continue;
            }
            db->yoyoParamReset[ index ].yarnGrams     = 0;
            db->yoyoParamReset[ index ].tenthsOfAGram = 0;
            while ( *( buffer + offset + 4 + i ) != 0x20 && *( buffer + offset + 4 + i ) != 0x00 )
            {
                db->yoyoParamReset[ index ].yarnGrams = db->yoyoParamReset[ index ].yarnGrams * 10 + *( buffer + offset + 4 + i ) - 0x30;
                i++;
            }
            if ( *( buffer + offset + 4 + i ) == 0x20 )
                i++;  // 跳过0x20
            while ( *( buffer + offset + 4 + i ) != 0x20 && *( buffer + offset + 4 + i ) != 0x00 )
            {
                db->yoyoParamReset[ index ].tenthsOfAGram = db->yoyoParamReset[ index ].tenthsOfAGram * 10 + *( buffer + offset + 4 + i ) - 0x30;
                i++;
            }
            if ( *( buffer + offset + 4 + i ) == 0x20 )
                i++;  // 跳过0x20
            index++;
        }
        offset += i + 7;
    }
    // Grams YOYO Zero Machine
    if ( *( buffer + offset ) == 0x0A && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i     = 0;
        int index = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            if ( index >= 8 )
            {
                i++;
                continue;
            }
            db->yoyoParamZero[ index ].yarnGrams     = 0;
            db->yoyoParamZero[ index ].tenthsOfAGram = 0;
            while ( *( buffer + offset + 4 + i ) != 0x20 && *( buffer + offset + 4 + i ) != 0x00 )
            {
                db->yoyoParamZero[ index ].yarnGrams = db->yoyoParamZero[ index ].yarnGrams * 10 + *( buffer + offset + 4 + i ) - 0x30;
                i++;
            }
            if ( *( buffer + offset + 4 + i ) == 0x20 )
                i++;  // 跳过0x20
            while ( *( buffer + offset + 4 + i ) != 0x20 && *( buffer + offset + 4 + i ) != 0x00 )
            {
                db->yoyoParamZero[ index ].tenthsOfAGram = db->yoyoParamZero[ index ].tenthsOfAGram * 10 + *( buffer + offset + 4 + i ) - 0x30;
                i++;
            }
            if ( *( buffer + offset + 4 + i ) == 0x20 )
                i++;  // 跳过0x20
            index++;
        }
        offset += i + 7;
    }
    // YOYO Motor
    if ( *( buffer + offset ) == 0x0B && *( buffer + offset + 1 ) == 0x00 && *( buffer + offset + 2 ) == 0x01 && *( buffer + offset + 3 ) == 0x00 )
    {
        int i     = 0;
        int index = 0;
        while ( *( buffer + offset + 4 + i ) != 0x00 )
        {
            if ( index >= 9 )
            {
                i++;
                continue;
            }
            db->yarnForYOYOMotors[ index ] = 0;
            while ( *( buffer + offset + 4 + i ) != 0x20 && *( buffer + offset + 4 + i ) != 0x00 )
            {
                db->yarnForYOYOMotors[ index ] = db->yarnForYOYOMotors[ index ] * 10 + *( buffer + offset + 4 + i ) - 0x30;
                i++;
            }
            if ( *( buffer + offset + 4 + i ) == 0x20 )
                i++;  // 跳过0x20
            index++;
        }
        //        offset += i + 7;
    }

    return db;
}

QSharedPointer< SokParser::ClosedToeParam > SokParser::GetClosedToeParam( QByteArray* data )
{
    if ( data->data() == Q_NULLPTR )
    {
        return Q_NULLPTR;
    }
    QSharedPointer< SokParser::ClosedToeParam > db = QSharedPointer< SokParser::ClosedToeParam >( new ClosedToeParam );

    // 搜索类型 05 01 0C，作用不详，疑似固定字符
    const QByteArray searchFor = QByteArray::fromHex( "05 01 FC" );  // 要搜索的字节序列
    int              index     = data->indexOf( searchFor );         // 搜索字节序列
    if ( index == -1 )
    {
        qDebug() << "ClosedToeParam not found.";
        return db;
    }
    else if ( index > 0x300 )
    {
        qDebug() << "ClosedToeParam not correct.";
        return db;
    }

    char*   buffer = data->data();
    quint16 offset = index + 8;

    int i = 0;
    // TODO: 可能需要补充初始化参数值为0
    while ( *( buffer + offset + i ) != 0x00 )
    {
        i++;
    }
    QByteArray  subArr   = data->mid( offset, i );
    QString     str      = QString( subArr );
    QStringList dataList = str.split( 0x20 );

    if ( dataList.length() >= 102 )
    {
        db->stitchingType              = dataList[ 3 ].toUShort();
        db->stitchingSpeed             = dataList[ 4 ].toUShort();
        db->suctionType                = dataList[ 5 ].toUShort();
        db->pickUpDevice               = dataList[ 6 ].toUShort();
        db->OnboardDataModification    = dataList[ 7 ].toUShort();
        db->numOfPinAtStartPerSeaming3 = dataList[ 8 ].toUShort();
        db->linkTransfer               = dataList[ 10 ].toUShort();
        db->revSockEjection            = dataList[ 11 ].toUShort();
        db->sockPusherSpeed            = dataList[ 12 ].toUShort();

        db->sizeParam[ 0 ].turningDeviceInclination      = dataList[ 14 ].toUShort();
        db->sizeParam[ 0 ].turningDeviceTubeOutlet       = dataList[ 15 ].toUShort();
        db->sizeParam[ 0 ].tiltingDeviceInclinationStage = dataList[ 16 ].toUShort();
        db->sizeParam[ 0 ].airBlastEnabled               = dataList[ 17 ].toUShort();

        db->sizeParam[ 1 ].turningDeviceInclination      = dataList[ 20 ].toUShort();
        db->sizeParam[ 1 ].turningDeviceTubeOutlet       = dataList[ 21 ].toUShort();
        db->sizeParam[ 1 ].tiltingDeviceInclinationStage = dataList[ 22 ].toUShort();
        db->sizeParam[ 1 ].airBlastEnabled               = dataList[ 23 ].toUShort();

        db->sizeParam[ 2 ].turningDeviceInclination      = dataList[ 26 ].toUShort();
        db->sizeParam[ 2 ].turningDeviceTubeOutlet       = dataList[ 27 ].toUShort();
        db->sizeParam[ 2 ].tiltingDeviceInclinationStage = dataList[ 28 ].toUShort();
        db->sizeParam[ 2 ].airBlastEnabled               = dataList[ 29 ].toUShort();

        db->sizeParam[ 3 ].turningDeviceInclination      = dataList[ 32 ].toUShort();
        db->sizeParam[ 3 ].turningDeviceTubeOutlet       = dataList[ 33 ].toUShort();
        db->sizeParam[ 3 ].tiltingDeviceInclinationStage = dataList[ 34 ].toUShort();
        db->sizeParam[ 3 ].airBlastEnabled               = dataList[ 35 ].toUShort();

        db->sizeParam[ 4 ].turningDeviceInclination      = dataList[ 38 ].toUShort();
        db->sizeParam[ 4 ].turningDeviceTubeOutlet       = dataList[ 39 ].toUShort();
        db->sizeParam[ 4 ].tiltingDeviceInclinationStage = dataList[ 40 ].toUShort();
        db->sizeParam[ 4 ].airBlastEnabled               = dataList[ 41 ].toUShort();

        db->sizeParam[ 5 ].turningDeviceInclination      = dataList[ 44 ].toUShort();
        db->sizeParam[ 5 ].turningDeviceTubeOutlet       = dataList[ 45 ].toUShort();
        db->sizeParam[ 5 ].tiltingDeviceInclinationStage = dataList[ 46 ].toUShort();
        db->sizeParam[ 5 ].airBlastEnabled               = dataList[ 47 ].toUShort();

        db->sizeParam[ 6 ].turningDeviceInclination      = dataList[ 50 ].toUShort();
        db->sizeParam[ 6 ].turningDeviceTubeOutlet       = dataList[ 51 ].toUShort();
        db->sizeParam[ 6 ].tiltingDeviceInclinationStage = dataList[ 52 ].toUShort();
        db->sizeParam[ 6 ].airBlastEnabled               = dataList[ 53 ].toUShort();

        db->sizeParam[ 7 ].turningDeviceInclination      = dataList[ 56 ].toUShort();
        db->sizeParam[ 7 ].turningDeviceTubeOutlet       = dataList[ 57 ].toUShort();
        db->sizeParam[ 7 ].tiltingDeviceInclinationStage = dataList[ 58 ].toUShort();
        db->sizeParam[ 7 ].airBlastEnabled               = dataList[ 59 ].toUShort();

        db->suction[ 2 ] = dataList[ 65 ].toUShort();
        db->suction[ 3 ] = dataList[ 68 ].toUShort();
        db->suction[ 1 ] = dataList[ 71 ].toUShort();
        db->suction[ 5 ] = dataList[ 74 ].toUShort();
        db->suction[ 6 ] = dataList[ 77 ].toUShort();
        db->suction[ 4 ] = dataList[ 80 ].toUShort();
        db->suction[ 0 ] = dataList[ 93 ].mid( 0, dataList[ 93 ].length() - 1 ).toUShort();

        db->sizeParam[ 0 ].tiltingDeviceDownDelay = dataList[ 94 ].toUShort();
        db->sizeParam[ 1 ].tiltingDeviceDownDelay = dataList[ 95 ].toUShort();
        db->sizeParam[ 2 ].tiltingDeviceDownDelay = dataList[ 96 ].toUShort();
        db->sizeParam[ 3 ].tiltingDeviceDownDelay = dataList[ 97 ].toUShort();
        db->sizeParam[ 4 ].tiltingDeviceDownDelay = dataList[ 98 ].toUShort();
        db->sizeParam[ 5 ].tiltingDeviceDownDelay = dataList[ 99 ].toUShort();
        db->sizeParam[ 6 ].tiltingDeviceDownDelay = dataList[ 100 ].toUShort();
        db->sizeParam[ 7 ].tiltingDeviceDownDelay = dataList[ 101 ].toUShort();
    }
    return db;
}

SokParser::ChainDataMap SokParser::GetChainData( QByteArray* data, quint8 fileSaveType, int typeIndex )
{
    SokParser::ChainDataMap chainMap;
    if ( data->data() == Q_NULLPTR )
        return chainMap;

    int index = 0;
    // 逆序
    if ( fileSaveType == 1 )
    {
        // 搜索最后一个 AUTO开始的字符， 内容从此处开始计算
        const QByteArray searchFor = QByteArray::fromHex( "41 55 54 4F 00 00 00 00 00 00 00 00 00 00 00 "
                                                          "67 36 00 0A 00 2A" );  // 要搜索的字节序列
        index                      = data->lastIndexOf( searchFor );              // 搜索字节序列
        if ( index == -1 )
        {
            qDebug() << "ChainData not found.";
            return chainMap;
        }
        index = index + 28;
    }
    else if ( fileSaveType == 0 )
    {
        index = typeIndex + 13;
    }

    char* buffer = data->data();
    while ( index < data->length() - 1 )
    {
        quint16 stepIndex = static_cast< quint8 >( *( buffer + index ) ) + static_cast< quint8 >( *( buffer + index + 1 ) ) * 256;
        quint16 length    = static_cast< quint8 >( *( buffer + index + 2 ) ) + static_cast< quint8 >( *( buffer + index + 3 ) ) * 256;

        // 数值不对，跳出循环
        if ( chainMap.contains( stepIndex ) || length == 0 )
            break;

        // step下面有多个littleStep，需要逐个解析
        StepDataMap stepDataMap;
        int         offset = 0;

        // 逐条解析
        while ( offset < length )
        {
            // buffer + index + 4 是第一条的开始
            int                      littleIndex = static_cast< quint8 >( *( buffer + index + 4 + offset + 2 ) ) + static_cast< quint8 >( *( buffer + index + 4 + offset + 3 ) ) * 256;
            SokParser::ChainDataItem item;

            // dataLen为数据段长度，不包含前面的开头，和尾部的00
            int dataLen = 0;
            // 设置速度
            if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x9D )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                ChainSpeedParamItem speedParamItem;
                speedParamItem.position = dataList[ 0 ].toUShort();
                speedParamItem.speed    = dataList[ 1 ].toUShort();

                item.type                      = 1;
                item.ItemUnion.chainSpeedParam = speedParamItem;
            }
            // 设置备注
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x01 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen = endIndex - ( index + 4 + offset + 6 );
                // 赋值
                item.type = 0;
                memset( item.ItemUnion.stepDescription, 0, 60 );
                memcpy( item.ItemUnion.stepDescription, ( buffer + index + 4 + offset + 6 ), dataLen );
            }
            // 特殊功能
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x09 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 5;
                SpecialFunctionParamItem specialParamItem;
                specialParamItem.specialFunctionId  = dataList[ 0 ].toUShort();
                specialParamItem.position           = dataList[ 1 ].toUShort();
                specialParamItem.status             = dataList[ 2 ] == "-" ? 0 : 1;
                item.ItemUnion.specialFunctionParam = specialParamItem;
            }
            // Cams 三角
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x42 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 6;
                CamParamItem camParamItem;
                camParamItem.camId      = dataList[ 0 ].toUShort();
                camParamItem.position   = dataList[ 1 ].toUShort();
                camParamItem.status     = dataList[ 2 ] == "-" ? 0 : 1;
                item.ItemUnion.camParam = camParamItem;
            }
            // Control Valve
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x07 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 8;
                ControlValeParamItem controlParamItem;
                controlParamItem.movement       = dataList[ 0 ].toUShort();
                controlParamItem.step           = dataList[ 1 ].toUShort();
                controlParamItem.position       = dataList[ 2 ].toUShort();
                item.ItemUnion.controlValeParam = controlParamItem;
            }
            // Function
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x08 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen            = endIndex - ( index + 4 + offset + 6 );
                QByteArray stepArr = data->mid( index + 4 + offset + 6, dataLen );
                QString    str     = QString( stepArr );
                // 赋值
                item.type                   = 3;
                item.ItemUnion.functionPram = str.toUShort();
            }
            // Gusset
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x0F )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 15;
                GussetParamItem gussetParamItem;
                gussetParamItem.drum     = dataList[ 0 ].toUShort();
                gussetParamItem.position = dataList[ 2 ].toUShort();
                gussetParamItem.quantity = dataList[ 3 ].toUShort();
                gussetParamItem.piority  = dataList[ 4 ].toUShort();
                gussetParamItem.status   = dataList[ 5 ] == "-" ? 0 : 1;
                memset( gussetParamItem.name, 0, 30 );
                memcpy( gussetParamItem.name, ( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 ), dataList[ 1 ].length() );
                item.ItemUnion.gussetParam = gussetParamItem;
            }
            // Finger
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x20 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 7;
                FingerParamItem fingerParamItem;
                if ( dataList[ 0 ] == "1" )
                {
                    fingerParamItem.fingerId = dataList[ 1 ].toUShort();
                }
                else if ( dataList[ 0 ] == "E" )
                {
                    fingerParamItem.fingerId = dataList[ 1 ].toUShort() + 256;
                }
                fingerParamItem.position   = dataList[ 2 ].toUShort();
                fingerParamItem.status     = dataList[ 3 ] == "-" ? 0 : 1;
                item.ItemUnion.fingerParam = fingerParamItem;
            }
            // Welt Raising Motor
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x58 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 16;
                RaisingDialMotorParamItem raisingMotorParamItem;
                raisingMotorParamItem.motorId        = dataList[ 0 ].toUShort();
                raisingMotorParamItem.movementCode   = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 );
                raisingMotorParamItem.motorSteps     = dataList[ 2 ].toUShort();
                raisingMotorParamItem.position       = dataList[ 3 ].toUShort();
                item.ItemUnion.raisingDialMotorParam = raisingMotorParamItem;
            }
            // Economizations
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x06 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 2;
                EconomizationParamItem economizationParamItem;
                economizationParamItem.forStep             = static_cast< quint8 >( *( buffer + index + 4 + offset + 6 ) ) == 250 ? 255 : dataList[ 0 ].toUShort();  // 250代表为空
                economizationParamItem.economizations[ 0 ] = dataList[ 1 ].toUShort();
                economizationParamItem.economizations[ 1 ] = dataList[ 2 ].toUShort();
                economizationParamItem.economizations[ 2 ] = dataList[ 3 ].toUShort();
                economizationParamItem.economizations[ 3 ] = dataList[ 4 ].toUShort();
                economizationParamItem.economizations[ 4 ] = dataList[ 5 ].toUShort();
                economizationParamItem.economizations[ 5 ] = dataList[ 6 ].toUShort();
                economizationParamItem.economizations[ 6 ] = dataList[ 7 ].toUShort();
                economizationParamItem.economizations[ 7 ] = dataList[ 8 ].toUShort();
                item.ItemUnion.economizationParam          = economizationParamItem;
            }
            // Pattern
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x64 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                PatternParamItem patternParamItem;
                memset( patternParamItem.codifyCode, 0, 30 );
                memcpy( patternParamItem.codifyCode, ( buffer + index + 4 + offset + 6 ), dataList[ 0 ].length() );
                patternParamItem.adjustmentCode      = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 );
                patternParamItem.adjustmentNeedleNum = dataList[ 2 ].toUShort();
                patternParamItem.alternationCode     = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 );
                patternParamItem.status              = dataList[ 4 ] == "-" ? 0 : 1;

                // 计算前面的数据长度，便于取后续8个size的数据
                int curDataLen = dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 + dataList[ 3 ].length() + 1 + dataList[ 4 ].length() + 1;
                memset( patternParamItem.patternName[ 0 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 0 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 5 ].length() );
                curDataLen += dataList[ 5 ].length() + 1;

                memset( patternParamItem.patternName[ 1 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 1 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 6 ].length() );
                curDataLen += dataList[ 6 ].length() + 1;

                memset( patternParamItem.patternName[ 2 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 2 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 7 ].length() );
                curDataLen += dataList[ 7 ].length() + 1;

                memset( patternParamItem.patternName[ 3 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 3 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 8 ].length() );
                curDataLen += dataList[ 8 ].length() + 1;

                memset( patternParamItem.patternName[ 4 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 4 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 9 ].length() );
                curDataLen += dataList[ 9 ].length() + 1;

                memset( patternParamItem.patternName[ 5 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 5 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 10 ].length() );
                curDataLen += dataList[ 10 ].length() + 1;

                memset( patternParamItem.patternName[ 6 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 6 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 11 ].length() );
                curDataLen += dataList[ 11 ].length() + 1;

                memset( patternParamItem.patternName[ 7 ], 0, 30 );
                memcpy( patternParamItem.patternName[ 7 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 12 ].length() );
                //                curDataLen += dataList[ 12 ].length();

                item.type                   = 9;
                item.ItemUnion.patternParam = patternParamItem;
            }
            // SLZ
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x4A )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                item.type = 14;
                SelectionParamItem selectionParam;
                selectionParam.drum = dataList[ 0 ].toUShort();
                memset( selectionParam.name, 0, 30 );
                memcpy( selectionParam.name, ( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 ), dataList[ 1 ].length() );
                selectionParam.type     = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 );
                selectionParam.position = dataList[ 3 ].toUShort();
                selectionParam.status   = dataList[ 4 ] == "-" ? 0 : 1;

                item.ItemUnion.selectionParam = selectionParam;
            }
            // Heel Size
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x74 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen            = endIndex - ( index + 4 + offset + 6 );
                QByteArray stepArr = data->mid( index + 4 + offset + 6, dataLen );
                QString    str     = QString( stepArr );
                // 赋值
                item.type                       = 4;
                item.ItemUnion.heelSizeVaration = str.toUShort();
            }
            // Elastic Pattern
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0xCE )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                ElasticPatternParamItem elasticParamItem;
                memset( elasticParamItem.codifyCode, 0, 30 );
                memcpy( elasticParamItem.codifyCode, ( buffer + index + 4 + offset + 6 ), dataList[ 0 ].length() );
                elasticParamItem.adjustmentCode      = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 );
                elasticParamItem.adjustmentNeedleNum = dataList[ 2 ].toUShort();
                elasticParamItem.alternationCode     = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 );
                elasticParamItem.status              = dataList[ 4 ] == "-" ? 0 : 1;

                // 计算前面的数据长度，便于取后续8个size的数据
                int curDataLen = dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 + dataList[ 3 ].length() + 1 + dataList[ 4 ].length() + 1;
                memset( elasticParamItem.patternName[ 0 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 0 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 5 ].length() );
                curDataLen += dataList[ 5 ].length() + 1;

                memset( elasticParamItem.patternName[ 1 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 1 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 6 ].length() );
                curDataLen += dataList[ 6 ].length() + 1;

                memset( elasticParamItem.patternName[ 2 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 2 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 7 ].length() );
                curDataLen += dataList[ 7 ].length() + 1;

                memset( elasticParamItem.patternName[ 3 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 3 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 8 ].length() );
                curDataLen += dataList[ 8 ].length() + 1;

                memset( elasticParamItem.patternName[ 4 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 4 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 9 ].length() );
                curDataLen += dataList[ 9 ].length() + 1;

                memset( elasticParamItem.patternName[ 5 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 5 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 10 ].length() );
                curDataLen += dataList[ 10 ].length() + 1;

                memset( elasticParamItem.patternName[ 6 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 6 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 11 ].length() );
                curDataLen += dataList[ 11 ].length() + 1;

                memset( elasticParamItem.patternName[ 7 ], 0, 30 );
                memcpy( elasticParamItem.patternName[ 7 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 12 ].length() );
                //                curDataLen += dataList[ 12 ].length();

                item.type                          = 10;
                item.ItemUnion.elasticPatternParam = elasticParamItem;
            }
            // Superposed Pattern
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0xCC )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                SuperposedPatternParamItem superposedParamItem;
                superposedParamItem.status = dataList[ 0 ] == "-" ? 0 : 1;
                memset( superposedParamItem.codifyCode, 0, 30 );
                memcpy( superposedParamItem.codifyCode, ( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 ), dataList[ 1 ].length() );
                superposedParamItem.widthNeedles  = dataList[ 2 ].toUShort();
                superposedParamItem.mirroringCode = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 );
                superposedParamItem.needlesStart  = dataList[ 7 ].toUShort();

                // 计算前面的数据长度，便于取后续8个size的数据
                int curDataLen = dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 + dataList[ 3 ].length() + 1 + dataList[ 4 ].length() + 1 + dataList[ 5 ].length()
                                 + 1 + dataList[ 6 ].length() + 1 + dataList[ 7 ].length() + 1 + dataList[ 8 ].length() + 1 + dataList[ 9 ].length() + 1 + dataList[ 10 ].length() + 1;
                memset( superposedParamItem.patternName[ 0 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 0 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 11 ].length() );
                curDataLen += dataList[ 11 ].length() + 1;

                memset( superposedParamItem.patternName[ 1 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 1 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 12 ].length() );
                curDataLen += dataList[ 12 ].length() + 1;

                memset( superposedParamItem.patternName[ 2 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 2 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 13 ].length() );
                curDataLen += dataList[ 13 ].length() + 1;

                memset( superposedParamItem.patternName[ 3 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 3 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 14 ].length() );
                curDataLen += dataList[ 14 ].length() + 1;

                memset( superposedParamItem.patternName[ 4 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 4 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 15 ].length() );
                curDataLen += dataList[ 15 ].length() + 1;

                memset( superposedParamItem.patternName[ 5 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 5 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 16 ].length() );
                curDataLen += dataList[ 16 ].length() + 1;

                memset( superposedParamItem.patternName[ 6 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 6 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 17 ].length() );
                curDataLen += dataList[ 17 ].length() + 1;

                memset( superposedParamItem.patternName[ 7 ], 0, 30 );
                memcpy( superposedParamItem.patternName[ 7 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 18 ].length() );
                //                curDataLen += dataList[ 12 ].length();

                item.type                             = 11;
                item.ItemUnion.superposedPatternParam = superposedParamItem;
            }
            // Ducking Stich Cam
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0xCF )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                DuckingStitchParamItem duckingParamItem;
                memset( duckingParamItem.codifyCode, 0, 30 );
                memcpy( duckingParamItem.codifyCode, ( buffer + index + 4 + offset + 6 ), dataList[ 0 ].length() );
                duckingParamItem.adjustmentCode      = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 );
                duckingParamItem.adjustmentNeedleNum = dataList[ 2 ].toUShort();
                duckingParamItem.status              = dataList[ 4 ] == "-" ? 0 : 1;

                // 计算前面的数据长度，便于取后续8个size的数据
                int curDataLen = dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 + dataList[ 2 ].length() + 1 + dataList[ 3 ].length() + 1 + dataList[ 4 ].length() + 1;
                memset( duckingParamItem.patternName[ 0 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 0 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 5 ].length() );
                curDataLen += dataList[ 5 ].length() + 1;

                memset( duckingParamItem.patternName[ 1 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 1 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 6 ].length() );
                curDataLen += dataList[ 6 ].length() + 1;

                memset( duckingParamItem.patternName[ 2 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 2 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 7 ].length() );
                curDataLen += dataList[ 7 ].length() + 1;

                memset( duckingParamItem.patternName[ 3 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 3 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 8 ].length() );
                curDataLen += dataList[ 8 ].length() + 1;

                memset( duckingParamItem.patternName[ 4 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 4 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 9 ].length() );
                curDataLen += dataList[ 9 ].length() + 1;

                memset( duckingParamItem.patternName[ 5 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 5 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 10 ].length() );
                curDataLen += dataList[ 10 ].length() + 1;

                memset( duckingParamItem.patternName[ 6 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 6 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 11 ].length() );
                curDataLen += dataList[ 11 ].length() + 1;

                memset( duckingParamItem.patternName[ 7 ], 0, 30 );
                memcpy( duckingParamItem.patternName[ 7 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 12 ].length() );
                //                curDataLen += dataList[ 12 ].length();

                item.type                         = 11;
                item.ItemUnion.duckingStitchParam = duckingParamItem;
            }
            // Heel Pattern
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x6E )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                HeelPatternParamItem heelParamItem;
                heelParamItem.heelCode = *( buffer + index + 4 + offset + 6 );
                memset( heelParamItem.patternName, 0, 30 );
                memcpy( heelParamItem.patternName, ( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 ), dataList[ 1 ].length() );
                memset( heelParamItem.codifyCode, 0, 30 );
                memcpy( heelParamItem.codifyCode, ( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 ), dataList[ 2 ].length() );
                heelParamItem.status = dataList[ 5 ] == "-" ? 0 : 1;

                item.type                       = 13;
                item.ItemUnion.heelPatternParam = heelParamItem;
            }
            // Motorized stitch cams patter
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0x75 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                MotorizedStitchCamsParamItem motorizedParamItem;
                memset( motorizedParamItem.codifyCode, 0, 30 );
                memcpy( motorizedParamItem.codifyCode, ( buffer + index + 4 + offset + 6 ), dataList[ 0 ].length() );
                motorizedParamItem.status = dataList[ 1 ] == "-" ? 0 : 1;

                // 计算前面的数据长度，便于取后续8个size的数据
                int curDataLen = dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1;
                memset( motorizedParamItem.patternName[ 0 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 0 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 2 ].length() );
                curDataLen += dataList[ 2 ].length() + 1;

                memset( motorizedParamItem.patternName[ 1 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 1 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 3 ].length() );
                curDataLen += dataList[ 3 ].length() + 1;

                memset( motorizedParamItem.patternName[ 2 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 2 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 4 ].length() );
                curDataLen += dataList[ 4 ].length() + 1;

                memset( motorizedParamItem.patternName[ 3 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 3 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 5 ].length() );
                curDataLen += dataList[ 5 ].length() + 1;

                memset( motorizedParamItem.patternName[ 4 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 4 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 6 ].length() );
                curDataLen += dataList[ 6 ].length() + 1;

                memset( motorizedParamItem.patternName[ 5 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 5 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 7 ].length() );
                curDataLen += dataList[ 7 ].length() + 1;

                memset( motorizedParamItem.patternName[ 6 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 6 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 8 ].length() );
                curDataLen += dataList[ 8 ].length() + 1;

                memset( motorizedParamItem.patternName[ 7 ], 0, 30 );
                memcpy( motorizedParamItem.patternName[ 7 ], ( buffer + index + 4 + offset + 6 + curDataLen ), dataList[ 9 ].length() );

                item.type                               = 17;
                item.ItemUnion.motorizedStitchCamsParam = motorizedParamItem;
            }
            // YOYO Yarn weight variation
            else if ( static_cast< quint8 >( *( buffer + index + 4 + offset ) ) == 0xE2 )
            {
                // 从buffer + index + 4 + offset +6开始，到00结束，这是数据段
                int endIndex = index + 4 + offset + 6;
                while ( *( buffer + endIndex ) != 0x00 )
                    endIndex++;
                dataLen              = endIndex - ( index + 4 + offset + 6 );
                QByteArray  stepArr  = data->mid( index + 4 + offset + 6, dataLen );
                QString     str      = QString( stepArr );
                QStringList dataList = str.split( 0x20 );
                // 赋值
                YOYOYarnWeightParamItem yoyoParamItem;
                yoyoParamItem.motor     = dataList[ 0 ].toUShort();
                yoyoParamItem.position  = dataList[ 1 ].toUShort();
                yoyoParamItem.action    = *( buffer + index + 4 + offset + 6 + dataList[ 0 ].length() + 1 + dataList[ 1 ].length() + 1 );
                yoyoParamItem.variation = dataList[ 3 ].toUShort();

                item.type                          = 18;
                item.ItemUnion.yoyoYarnWeightParam = yoyoParamItem;
            }

            stepDataMap.insert( littleIndex, QSharedPointer< ChainDataItem >( new SokParser::ChainDataItem( item ) ) );
            offset += dataLen + 7;  // 数据长度加上9D 00 00 00 01
                                    // 00再加上尾部00，共7个，加1跳到下一条
        }

        chainMap.insert( stepIndex, stepDataMap );

        index = index + 4 + length;
    }

    return chainMap;
}

SokParser::GraduationMotorParamVector SokParser::getGraduationMotorParamVector( QByteArray* data )
{
    SokParser::GraduationMotorParamVector graduationMotorParamVector;
    if ( data->data() == Q_NULLPTR )
        return graduationMotorParamVector;

    int   index  = 0;
    char* buffer = data->data();
    // 搜索08 01 6A 00 28 00 01 00， 内容从此处开始计算
    const QByteArray searchFor = QByteArray::fromHex( "04 01 4D 00 28 00 01 00" );  // 要搜索的字节序列
    while ( index != -1 )
    {
        index = data->indexOf( searchFor,
                               index + 1 );  // 搜索字节序列，搜不到就自动跳出了
        GraduationMotorParamItem item;
        memset( item.blockName, 0, 21 );
        memcpy( item.blockName, buffer + index + 8,
                21 );  //名称从index+8开始共21个字节

        if ( item.blockName[ 0 ] == '\0' )
            break;

        // 读起始位置
        int i              = 0;
        item.stepStart     = 0;
        QByteArray byteArr = data->mid( index + 29 + i, 4 );
        QString    str     = QString( byteArr );
        item.stepStart     = str.toUShort();
        i                  = i + 4;  // 起始数据4个字节
        // 读结束位置
        item.stepEnd = 0;
        byteArr      = data->mid( index + 29 + i, 4 );
        str          = QString( byteArr );
        item.stepEnd = str.toUShort();
        i            = i + 4;  // 结束位置4个字节

        item.graduationId                = *( buffer + index + 29 + i );
        i                                = i + 3;  // Id后面2个0，加上本身
        item.stopForMovementRaisingMotor = *( buffer + index + 29 + i );
        i                                = i + 2;  // 1个0，加本身
        item.degreeWhenMotorBeginMove    = 0;
        byteArr                          = data->mid( index + 29 + i, 4 );
        str                              = QString( byteArr );
        item.degreeWhenMotorBeginMove    = str.toUShort();
        i                                = i + 4;  // 结束位置4个字节
        i                                = i + 6;  // 跳过 94 00 D4 00 01 00
        item.prohibitsValueChange        = ( *( buffer + index + 29 + i ) == '-' ? false : true );
        i                                = i + 6;  // 跳过若干00
        // 接下来是各个size的数据，基本上按照5 5 4 2 4 2 4组成
        for ( int size = 0; size < 8; size++ )
        {
            byteArr                          = data->mid( index + 29 + i, 5 );
            str                              = QString( byteArr );
            item.cylinderStart[ size ]       = str.toUShort();
            i                                = i + 5;
            byteArr                          = data->mid( index + 29 + i, 5 );
            str                              = QString( byteArr );
            item.cylinderEnd[ size ]         = str.toUShort();
            i                                = i + 5;
            byteArr                          = data->mid( index + 29 + i, 4 );
            str                              = QString( byteArr );
            item.startWidth[ size ]          = str.toUShort();
            i                                = i + 4;
            byteArr                          = data->mid( index + 29 + i, 2 );
            str                              = QString( byteArr );
            item.startMeasureQuarter[ size ] = str.toUShort();
            i                                = i + 2;
            byteArr                          = data->mid( index + 29 + i, 4 );
            str                              = QString( byteArr );
            item.endWidth[ size ]            = str.toUShort();
            i                                = i + 4;
            byteArr                          = data->mid( index + 29 + i, 2 );
            str                              = QString( byteArr );
            item.endMeasureQuarter[ size ]   = str.toUShort();
            i                                = i + 2 + 4;
        }

        graduationMotorParamVector.append( QSharedPointer< GraduationMotorParamItem >( new GraduationMotorParamItem( item ) ) );
    }
    return graduationMotorParamVector;
}

SokParser::SinkerMotorParamVector SokParser::getSinkerMotorParamVector( QByteArray* data )
{
    SokParser::SinkerMotorParamVector sinkMotorParamVector;
    if ( data->data() == Q_NULLPTR )
        return sinkMotorParamVector;

    int   index  = 0;
    char* buffer = data->data();
    // 搜索08 01 6A 00 28 00 01 00， 内容从此处开始计算
    const QByteArray searchFor = QByteArray::fromHex( "08 01 6A 00 28 00 01 00" );  // 要搜索的字节序列
    while ( index != -1 )
    {
        index = data->indexOf( searchFor,
                               index + 1 );  // 搜索字节序列，搜不到就自动跳出了
        SinkerMotorParamItem item;
        memset( item.blockName, 0, 21 );
        memcpy( item.blockName, buffer + index + 8,
                21 );  //名称从index+8开始共21个字节

        if ( item.blockName[ 0 ] == '\0' )
            break;

        // 读起始位置
        int i              = 0;
        item.stepStart     = 0;
        QByteArray byteArr = data->mid( index + 29 + i, 4 );
        QString    str     = QString( byteArr );
        item.stepStart     = str.toUShort();
        i                  = i + 4;  // 起始数据4个字节
        // 读结束位置
        item.stepEnd = 0;
        byteArr      = data->mid( index + 29 + i, 4 );
        str          = QString( byteArr );
        item.stepEnd = str.toUShort();
        i            = i + 4;  // 结束位置4个字节

        item.sinkerId                    = *( buffer + index + 29 + i );
        i                                = i + 3;  // Id后面2个0，加上本身
        item.stopForMovementRaisingMotor = *( buffer + index + 29 + i );
        i                                = i + 2;  // 1个0，加本身
        item.degreeWhenMotorBeginMove    = 0;
        byteArr                          = data->mid( index + 29 + i, 4 );
        str                              = QString( byteArr );
        item.degreeWhenMotorBeginMove    = str.toUShort();
        i                                = i + 4;  // 结束位置4个字节
        i                                = i + 6;  // 跳过 91 00 D8 00 01 00
        item.prohibitsAngularValueChange = ( *( buffer + index + 29 + i ) == '-' ? false : true );
        i                                = i + 2;
        item.prohibitsSinkerValueChange  = ( *( buffer + index + 29 + i ) == '-' ? false : true );
        i                                = i + 4;  // 跳过最后一个 00 2D 00
        // 接下来是各个size的数据，基本上按照5 5 4 4 8组成，前两个是Angular
        // St/End，后两个是Sinker st/end ,8是00个数
        for ( int size = 0; size < 8; size++ )
        {
            byteArr                   = data->mid( index + 29 + i, 5 );
            str                       = QString( byteArr );
            item.angularStart[ size ] = str.toUShort();
            i                         = i + 5;
            byteArr                   = data->mid( index + 29 + i, 5 );
            str                       = QString( byteArr );
            item.angularEnd[ size ]   = str.toUShort();
            i                         = i + 5;
            byteArr                   = data->mid( index + 29 + i, 4 );
            str                       = QString( byteArr );
            item.sinkerStart[ size ]  = str.toUShort();
            i                         = i + 4;
            byteArr                   = data->mid( index + 29 + i, 4 );
            str                       = QString( byteArr );
            item.sinkerEnd[ size ]    = str.toUShort();
            i                         = i + 4 + 8;
        }

        sinkMotorParamVector.append( QSharedPointer< SinkerMotorParamItem >( new SinkerMotorParamItem( item ) ) );
    }
    return sinkMotorParamVector;
}

SokParser::ElasticMotorParamVector SokParser::getElasticMotorParamVector( QByteArray* data )
{
    SokParser::ElasticMotorParamVector elasticMotorParamVector;
    if ( data->data() == Q_NULLPTR )
        return elasticMotorParamVector;

    int   index  = 0;
    char* buffer = data->data();
    // 搜索08 01 6A 00 28 00 01 00， 内容从此处开始计算
    const QByteArray searchFor = QByteArray::fromHex( "71 01 12 00 1F 00 01 00" );  // 要搜索的字节序列
    while ( index != -1 )
    {
        index = data->indexOf( searchFor,
                               index + 1 );  // 搜索字节序列，搜不到就自动跳出了
        ElasticMotorParamItem item;
        memset( item.blockName, 0, 21 );
        memcpy( item.blockName, buffer + index + 8,
                21 );  //名称从index+8开始共21个字节

        if ( item.blockName[ 0 ] == '\0' )
            break;

        // 读起始位置
        int i              = 0;
        item.stepStart     = 0;
        QByteArray byteArr = data->mid( index + 29 + i, 4 );
        QString    str     = QString( byteArr );
        item.stepStart     = str.toUShort();
        i                  = i + 4;  // 起始数据4个字节
        // 读结束位置
        item.stepEnd = 0;
        byteArr      = data->mid( index + 29 + i, 4 );
        str          = QString( byteArr );
        item.stepEnd = str.toUShort();
        i            = i + 4;  // 结束位置4个字节

        i                            = i + 6;  //跳过93 00 4A 01 01 00
        item.prohibitsValueChange1   = ( *( buffer + index + 29 + i ) == '-' ? false : true );
        i                            = i + 2;
        item.prohibitsValueChange2   = ( *( buffer + index + 29 + i ) == '-' ? false : true );
        i                            = i + 4;  // 中间有个2D不详
        item.prohibitsSawValueChange = ( *( buffer + index + 29 + i ) == '-' ? false : true );
        i                            = i + 2;

        // 接下来是各个size的数据，基本上按照5 5 5 5 5 5 5
        // 5组成，第3和第4个5废弃掉了，要跳过
        for ( int size = 0; size < 8; size++ )
        {
            byteArr                    = data->mid( index + 29 + i, 5 );
            str                        = QString( byteArr );
            item.elasticStart1[ size ] = str.toUShort();
            i                          = i + 5;
            byteArr                    = data->mid( index + 29 + i, 5 );
            str                        = QString( byteArr );
            item.elasticEnd1[ size ]   = str.toUShort();
            i                          = i + 5;
            byteArr                    = data->mid( index + 29 + i, 5 );
            str                        = QString( byteArr );
            item.elasticStart2[ size ] = str.toUShort();
            i                          = i + 5;
            byteArr                    = data->mid( index + 29 + i, 5 );
            str                        = QString( byteArr );
            item.elasticEnd2[ size ]   = str.toUShort();
            i                          = i + 5 + 10;
            byteArr                    = data->mid( index + 29 + i, 5 );
            str                        = QString( byteArr );
            item.sawStart[ size ]      = str.toUShort();
            i                          = i + 5;
            byteArr                    = data->mid( index + 29 + i, 5 );
            str                        = QString( byteArr );
            item.sawEnd[ size ]        = str.toUShort();
            i                          = i + 5;
        }

        elasticMotorParamVector.append( QSharedPointer< ElasticMotorParamItem >( new ElasticMotorParamItem( item ) ) );
    }
    return elasticMotorParamVector;
}

SokParser::ModuleDataVector SokParser::getModuleDataVector( QByteArray* data )
{
    SokParser::ModuleDataVector moduleDataVector;
    if ( data->data() == Q_NULLPTR )
        return moduleDataVector;

    int   index  = 0;
    char* buffer = data->data();
    // 搜索L.START， 内容从此处开始计算
    const QByteArray searchFor = QByteArray::fromHex( "4C 00 53 54 41 52 54" );  // 要搜索的字节序列
    while ( index != -1 )
    {
        index = data->indexOf( searchFor,
                               index + 1 );  // 搜索字节序列，搜不到就自动跳出了
        ModuleDataItem item;
        memset( item.name, 0, 38 );
        memcpy( item.name, buffer + index + 11,
                38 );  //名称从index+8开始共21个字节

        if ( item.name[ 0 ] == '\0' )
            break;

        item.startStepIndex = static_cast< quint8 >( *( buffer + index + 52 ) ) + static_cast< quint8 >( *( buffer + index + 53 ) ) * 256;
        item.endStepIndex   = static_cast< quint8 >( *( buffer + index + 54 ) ) + static_cast< quint8 >( *( buffer + index + 55 ) ) * 256;

        moduleDataVector.append( QSharedPointer< ModuleDataItem >( new ModuleDataItem( item ) ) );
    }
    return moduleDataVector;
}

SokParser::DisCfgMap SokParser::getDisCfgMap( QString fileName, quint8 type )
{
    SokParser::DisCfgMap disCfgMap;

    QString typeDIR = "";
    if ( type == 0 )
        typeDIR = "Dis/";
    else if ( type == 1 )
        typeDIR = "Disover/";
    else if ( type == 2 )
        typeDIR = "Distr/";
    else if ( type == 3 )
        typeDIR = "DisTal/";
    else if ( type == 4 )
        typeDIR = "DisTrMot/";
    else
    {
        qDebug() << "Type Not Correct!\n";
        return disCfgMap;
    }
    QString fullName = CFG_DIR + typeDIR + fileName + ".cfg";
    QFile   file( fullName );
    if ( !file.exists() )
    {
        qDebug() << fullName << "Not Exist!\n";
        return disCfgMap;
    }

    QSettings* configIniRead = new QSettings( fullName, QSettings::IniFormat );
    for ( int i = 1; i <= 44; i++ )
    {
        quint16 drumValue = 0;
        for ( int j = 0; j < 9; j++ )
        {
            QString section = QString( "Color_%1/Drum_%2" ).arg( QString::number( i, 10 ).rightJustified( 3, '0' ) ).arg( QString::number( j, 10 ).rightJustified( 3, '0' ) );
            //            qDebug() << section;
            QStringList valueList = configIniRead->value( section ).toStringList();
            // 第2列是第几个选针器，值是1~9，需要减1.第4列是是否为down，1就是up，0就是down
            if ( valueList.size() >= 4 )
            {
                quint16 drumIdx = valueList[ 1 ].toUShort() - 1;
                quint16 down    = valueList[ 3 ].toUShort();
                if ( down == 1 )
                    drumValue |= 0x01 << drumIdx;
                disCfgMap.insert( i, drumValue );
            }
        }
        //        qDebug() << drumValue;
    }
    for ( int i = 45; i <= 99; i++ )
    {
        quint16 fingerValue = 0;
        for ( int j = 0; j < 3; j++ )
        {
            QString section = QString( "Color_%1/Finger_%2" ).arg( QString::number( i, 10 ).rightJustified( 3, '0' ) ).arg( QString::number( j, 10 ).rightJustified( 3, '0' ) );
            //            qDebug() << section;
            QStringList valueList = configIniRead->value( section ).toStringList();
            // 第3列是第几个选针器，值是0~2，第4列是是否为down，1就是up，0就是down
            if ( valueList.size() >= 4 )
            {
                quint16 idx  = valueList[ 2 ].toUShort();
                quint16 down = valueList[ 3 ].toUShort();
                if ( down == 1 )
                    fingerValue |= 0x01 << idx;
                disCfgMap.insert( i, fingerValue );
            }
        }
        //        qDebug() << fingerValue;
    }

    //读入入完成后删除指针
    delete configIniRead;

    return disCfgMap;
}

QSharedPointer< SokParser::DisData > SokParser::getDisData( QString fileName )
{
    QSharedPointer< SokParser::DisData > db = QSharedPointer< SokParser::DisData >( new DisData );
    db->width                               = 0;
    db->height                              = 0;

    QFile file( DIS_DIR + fileName + ".DIS" );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "Open Dis File Failed!\n" << DIS_DIR + fileName + ".DIS";
        return db;
    }

    QByteArray data = file.readAll();
    if ( data.isEmpty() )
    {
        qDebug() << "File is empty!\n";
        return db;
    }

    long  fileSize = data.size();
    char* buffer   = data.data();
    db->width      = static_cast< quint8 >( *( buffer + fileSize / 2 ) ) + static_cast< quint8 >( *( buffer + fileSize / 2 + 1 ) ) * 256;
    db->height     = static_cast< quint8 >( *( buffer + fileSize / 2 + 2 ) ) + static_cast< quint8 >( *( buffer + fileSize / 2 + 3 ) ) * 256;

    db->drumData       = data.mid( 0x520, db->width * db->height );
    db->yarnfingerData = data.mid( fileSize / 2 + 0x520 );
    return db;
}

QString SokParser::getSlzCfg( QString fileName )
{
    QFile file( SLZ_DIR + fileName + ".SLZ" );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << SLZ_DIR + fileName + ".SLZ"
                 << "Open SLz Cfg File Failed!\n";
        return "";
    }

    QTextStream in( &file );
    QString     line;
    QString     result = "";

    while ( !in.atEnd() )
    {
        line = in.readLine();  // 读取一行
        if ( !line.isEmpty() )
        {
            // 如果行不是空的，则打印出来
            if ( line.startsWith( "#" ) )
                continue;
            if ( line.length() < 6 )
                continue;
            //            qDebug() << line;
            QStringList list = line.split( 0x20 );
            // 如果List[3]不为空且List[2]为"3600"，则将其append到result，append后补一个'\n'
            if ( list.size() > 3 && !list[ 3 ].isEmpty() && list.size() > 2 && list[ 2 ] == "3600" )
            {
                result += list[ 3 ] + '\n';
            }
        }
    }

    file.close();
    return result;
}

QString SokParser::getTasCfg( QString fileName )
{
    QFile file( TAS_DIR + fileName + ".tas" );
    // 不区分大小写打开文件
    QString lowerFileName = fileName.toLower();
    QString upperFileName = fileName.toUpper();
    if (!file.open(QIODevice::ReadOnly) &&
        !QFile(TAS_DIR + lowerFileName + ".tas").open(QIODevice::ReadOnly) &&
        !QFile(TAS_DIR + upperFileName + ".tas").open(QIODevice::ReadOnly))
    {
        qDebug() << "Open Tas File Failed!\n" << TAS_DIR + fileName + ".tas";
        return "";
    }

    QTextStream in( &file );
    QString     line;
    QString     result = "";

    while ( !in.atEnd() )
    {
        line = in.readLine();  // 读取一行
        if ( !line.isEmpty() )
        {
            // 如果行不是空的，则打印出来
            if ( line.startsWith( "#" ) )
                continue;
            if ( line.length() < 6 )
                continue;
            //            qDebug() << line;
            QStringList list = line.split( 0x20 );
            // 如果List[3]不为空且List[2]为"3600"，则将其append到result，append后补一个'\n'
            if ( list.size() > 3 && !list[ 3 ].isEmpty() && list.size() > 2 && list[ 2 ] == "3600" )
            {
                result += list[ 3 ] + '\n';
            }
        }
    }

    file.close();
    return result;
}
