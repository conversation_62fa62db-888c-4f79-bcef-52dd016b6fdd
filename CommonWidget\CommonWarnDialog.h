#ifndef COMMONWARNDIALOG_H
#define COMMONWARNDIALOG_H

#include <QScreen>
#include <QWidget>
#include "Config/readWarnConfig.h"

// 定义报警信息结构体
struct WarnInfoItem {
    int type_id;
    int id;
    
    // 重载相等运算符用于比较
    bool operator==(const WarnInfoItem& other) const {
        return type_id == other.type_id && id == other.id;
    }
};

namespace Ui
{
class CommonWarnDialog;
}

class CommonWarnDialog : public QWidget
{
    Q_OBJECT

public:
    explicit CommonWarnDialog(ReadWarnConfig* warnConfig, QWidget* parent = nullptr);
    ~CommonWarnDialog();
    
    // 刷新报警信息的公共接口
    void refreshWarnInfo(int type_id, int id, int status);

private:
    Ui::CommonWarnDialog* ui;
    QList<WarnInfoItem> warnList;  // 报警列表
    int currentIndex = -1;     // 当前显示的报警索引
    ReadWarnConfig* _warnConfig;  // 添加配置文件读取器指针
    
    // 显示报警详情的私有函数
    void showWarnDetail();
    // 更新计数显示
    void updateCountLabel();
};

#endif  // COMMONWARNDIALOG_H
