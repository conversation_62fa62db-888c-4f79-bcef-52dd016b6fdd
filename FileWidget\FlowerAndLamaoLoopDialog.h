#ifndef FLOWERANDLAMAOLOOPDIALOG_H
#define FLOWERANDLAMAOLOOPDIALOG_H

#include "CommonWidget/numberinputform.h"
#include "FATParser.h"
#include <QDebug>
#include <QDialog>
#include <QMessageBox>
#include <QTableWidget>
#include <cstring>

namespace Ui
{
class FlowerAndLamaoLoopDialog;
}

class FlowerAndLamaoLoopDialog : public QDialog
{
    Q_OBJECT

public:
    explicit FlowerAndLamaoLoopDialog( QWidget* parent = nullptr, FATParser::PatternExtraData* patternExtra = nullptr );
    ~FlowerAndLamaoLoopDialog();

private:
    Ui::FlowerAndLamaoLoopDialog* ui;
    FATParser::PatternExtraData*  patternExtraData;
    NumberInputForm*              numberInputFrm = nullptr;
    FATParser::FLowerAndLaMaoLoop flowerData[ 10 ];
    FATParser::FLowerAndLaMaoLoop laomaoData[ 10 ];
    int                           tableEditRowIndex = 0;
    int                           tableEditColIndex = 0;

    void switchData( int type );
    int  getDataLength( int type );

private slots:
    void onRadioButtonClicked();
    void onTableItemClicked( QTableWidgetItem* item );
    void onNumberInputFormFinished( QString value );
};

#endif  // FLOWERANDLAMAOLOOPDIALOG_H
