#ifndef READRESETCONFIG_H
#define READRESETCONFIG_H

#include <QDebug>
#include <QFile>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMessageBox>
#include <QString>
#include <QVector>

class ReadResetConfig
{
public:
    ReadResetConfig();
    ~ReadResetConfig();

    // 定义复位类型枚举
    enum ResetType
    {
        SOCK_RESET   = 0,  // 快速复位
        GATHER_RESET = 1   // 扎口复位
    };

#pragma pack( 1 )
    struct SockResetActionStruct
    {
        // 删除step字段
        int position;
        int type;
        int id;
        int value;
    };
#pragma pack()

    // 解析ResetConfig.json文件
    bool parseResetConfig( const QString& fileAddr );

    // 保存ResetConfig.json文件
    bool saveConfig();
    bool saveResetConfig( const QString& fileAddr );

    // 获取解析后的数据
    QVector< QVector< SockResetActionStruct > >& getResetList( ResetType type = SOCK_RESET )
    {
        return type == SOCK_RESET ? sockResetList : gatherResetList;
    }

    // 添加一个新的动作到指定圈索引
    bool addAction( int circleIndex, const SockResetActionStruct& action, ResetType type = SOCK_RESET );

    // 删除指定圈索引的指定指令索引
    bool removeAction( int circleIndex, int instructionIndex, ResetType type = SOCK_RESET );

    // 在指定位置插入新圈
    void insertCircle( int index, ResetType type = SOCK_RESET );

    // 删除指定位置的圈
    void deleteCircle( int index, ResetType type = SOCK_RESET );

private:
    QVector< QVector< SockResetActionStruct > > sockResetList;
    QVector< QVector< SockResetActionStruct > > gatherResetList;
    QString                                     configFileAddr;
};

#endif  // READRESETCONFIG_H
