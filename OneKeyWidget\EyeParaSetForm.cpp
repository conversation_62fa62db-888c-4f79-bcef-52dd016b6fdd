#include "EyeParaSetForm.h"
#include "ui_EyeParaSetForm.h"

EyeParaSetForm::EyeParaSetForm( QWidget* parent, quint8 type, quint8 index, quint16 params, bool enable, bool status )
    : QWidget( parent ), type( type ), index( index ), params( params ), enable( enable ), status( status ), ui( new Ui::EyeParaSetForm )
{
    ui->setupUi( this );

    ui->lbl_index->setText( QString( "%1%2" ).arg( "#" ).arg( QString::number( index ).rightJustified( 2, '0' ) ) );
    ui->le_params->setText( QString::number( params ) );

    // 根据status设置颜色块显示状态
    if ( status )
    {
        ui->cb_status->setColor( QColor( 46, 204, 113 ) );  // 更鲜艳的绿色
        ui->cb_status->setVisible( true );
    }
    else
    {
        ui->cb_status->setVisible( false );
    }

    // 根据enable状态设置控件样式和行为
    if ( !enable )
    {
        // 设置背景色为灰色
        ui->le_params->setStyleSheet( "background-color: #e0e0e0;"
                                      "color: #888888;"
                                      "border: 1px solid #c0c0c0;"
                                      "border-radius: 4px;"
                                      "padding: 2px;"
                                      "font-size: 8pt;" );
    }
    else
    {
        // 启用状态下连接鼠标点击事件
        connect( ui->le_params, &MyLineEdit::mouseRelease, this, [ & ]() {
            NumberInputForm* form = new NumberInputForm( this, "请输入数值", 1, 10 );
            form->setAttribute( Qt::WA_DeleteOnClose );
            form->show();
            // 数值修改完毕触发信号量
            connect( form, &NumberInputForm::InputFinished, this, [ & ]( QString text ) {
                this->params = text.toUInt();
                ui->le_params->setText( text );
                emit onParamsEdited( this->type, this->index, this->params );
            } );
        } );
    }
}

// 新增setStatus函数实现
void EyeParaSetForm::setStatus( bool status )
{
    // 如果状态没有变化，直接返回
    if ( this->status == status )
        return;

    this->status = status;

    if ( status )
    {
        // 状态为true时，设置颜色块为绿色并显示
        ui->cb_status->setColor( QColor( 46, 204, 113 ) );  // 更鲜艳的绿色
        ui->cb_status->setVisible( true );
    }
    else
    {
        // 状态为false时，隐藏颜色块
        ui->cb_status->setVisible( false );
    }
}

// 新增setEnable函数实现
void EyeParaSetForm::setEnable( bool enable )
{
    // 如果状态没有变化，直接返回
    if ( this->enable == enable )
        return;

    this->enable = enable;

    if ( !enable )
    {
        // 禁用状态：设置背景色为灰色
        ui->le_params->setStyleSheet( "background-color: #e0e0e0;"
                                      "color: #888888;"
                                      "border: 1px solid #c0c0c0;"
                                      "border-radius: 4px;"
                                      "padding: 2px;"
                                      "font-size: 8pt;" );

        // 断开所有之前的连接
        disconnect( ui->le_params, &MyLineEdit::mouseRelease, nullptr, nullptr );
    }
    else
    {
        // 启用状态：恢复默认样式
        ui->le_params->setStyleSheet( "background-color: white;"
                                      "color: #333333;"
                                      "border: 1px solid #c0c0c0;"
                                      "border-radius: 4px;"
                                      "padding: 2px;"
                                      "font-size: 8pt;" );

        // 重新连接鼠标点击事件
        connect( ui->le_params, &MyLineEdit::mouseRelease, this, [ & ]() {
            NumberInputForm* form = new NumberInputForm( this, "请输入数值", 1, 10 );
            form->setAttribute( Qt::WA_DeleteOnClose );
            form->show();
            // 数值修改完毕触发信号量
            connect( form, &NumberInputForm::InputFinished, this, [ & ]( QString text ) {
                this->params = text.toUInt();
                ui->le_params->setText( text );
                emit onParamsEdited( this->type, this->index, this->params );
            } );
        } );
    }
}

// 新增setParams函数实现
void EyeParaSetForm::setParams( quint16 params )
{
    // 更新参数值
    this->params = params;

    // 更新显示到控件
    ui->le_params->setText( QString::number( params ) );
}

EyeParaSetForm::~EyeParaSetForm()
{
    delete ui;
}
