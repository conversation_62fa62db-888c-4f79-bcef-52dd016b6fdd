﻿#ifndef PARAMETERS_H
#define PARAMETERS_H

#include <QString>

const QString SERIAL_PORT  = "/dev/ttyAS2";
const QString MAIN_CFG_DIR = "/opt/YTJ_Display/bin/MainCfg/";
const QString FAT_DIR      = "/opt/YTJ_Display/bin/FAT/";
const QString SOK_DIR      = "/opt/YTJ_Display/bin/SOK/";
const QString DIS_DIR      = "/opt/YTJ_Display/bin/Dis/";
const QString SLZ_DIR      = "/opt/YTJ_Display/bin/Slz/";
const QString TAS_DIR      = "/opt/YTJ_Display/bin/Tas/";
const QString CFG_DIR      = "/opt/YTJ_Display/bin/Cfg/";
const QString CFT_DIR      = "/opt/YTJ_Display/bin/Cft/";
// const QString MAIN_CFG_DIR = "./MainCfg/";
// const QString FAT_DIR    = "./FAT/";
// const QString SOK_DIR = "./SOK/";
// const QString DIS_DIR = "./Dis/";
// const QString SLZ_DIR = "./Slz/";
// const QString TAS_DIR = "./Tas/";
// const QString CFG_DIR = "./Cfg/";
// const QString CFT_DIR      = "./Cft/";

const int COM_MAX_RETRIES = 3;
const int COMM_INTERVAL   = 300;  // 300毫秒

#endif  // PARAMETERS_H
