#include <QApplication>

#include "Util/logmanager.h"
#include "Widget/widget.h"

// 消息处理函数
void messageOutput( QtMsgType type, const QMessageLogContext& context, const QString& msg )
{
    LogManager::getInstance()->messageHandler( type, context, msg );
}

int main( int argc, char* argv[] )
{
    QApplication a( argc, argv );

    // 初始化日志系统
    //    LogManager::getInstance()->initLogFile();
    //    qInstallMessageHandler( messageOutput );

    Widget w;
    w.show();

    return a.exec();
}
