#ifndef EYEPARASETFORM_H
#define EYEPARASETFORM_H

#include "CommonWidget/numberinputform.h"
#include "FileWidget/ColorBlockWidget.h"
#include <QWidget>

namespace Ui
{
class EyeParaSetForm;
}

class EyeParaSetForm : public QWidget
{
    Q_OBJECT

public:
    explicit EyeParaSetForm( QWidget* parent = nullptr, quint8 type = 0, quint8 index = 0, quint16 params = 0, bool enable = true, bool status = false );
    ~EyeParaSetForm();

    quint8 getType()
    {
        return this->type;
    }
    quint8 getIndex()
    {
        return this->index;
    }
    quint16 getParams()
    {
        return this->params;
    }
    bool isEnabled()
    {
        return this->enable;
    }
    bool getStatus()
    {
        return this->status;
    }

    // 新增setEnable函数
    void setEnable( bool enable );
    
    // 新增setStatus函数
    void setStatus( bool status );
    
    // 新增setParams函数
    void setParams( quint16 params );
    
signals:
    void onParamsEdited( quint8 type, quint8 index, quint16 params );

private:
    Ui::EyeParaSetForm* ui;
    quint8              type;
    quint8              index;
    quint16             params;
    bool                enable;  // 新增enable字段
    bool                status;  // 新增status字段
};

#endif  // EYEPARASETFORM_H
