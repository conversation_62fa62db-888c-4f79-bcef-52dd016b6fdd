#ifndef USBFORM_H
#define USBFORM_H

#include "Common/parameters.h"
#include "CommonWidget/mylineedit.h"
#include <CommonWidget/comboform.h>
#include <QButtonGroup>
#include <QFileSystemModel>
#include <QLabel>
#include <QMessageBox>
#include <QStandardItemModel>
#include <QWidget>

namespace Ui
{
class UsbForm;
}

class UsbForm : public QWidget
{
    Q_OBJECT

public:
    explicit UsbForm( QWidget* parent = nullptr );
    ~UsbForm();

private:
    Ui::UsbForm* ui;

    // 菜单页面
    QButtonGroup* menuBtnGroup;  //菜单按键组

    ComboForm* machineTypeComboForm    = nullptr;
    ComboForm* fileTypeComboForm       = nullptr;
    int        currentMachineTypeIndex = 0;      // 保存当前选择的机型索引
    int        currentFileTypeIndex    = 0;      // 保存当前选择的文件类型索引
    bool       isUsbAdminPageInited    = false;  // USB管理页面是否已初始化

    QFileSystemModel* usbFileModel   = nullptr;
    QFileSystemModel* localFileModel = nullptr;  // 本地文件系统模型
    QString           currentUsbPath;            // 当前U盘路径
    QString           currentUsbFile;            // 当前选中的U盘文件
    QString           currentLocalPath;          // 当前本地文件夹路径
    QString           currentLocalFile;          // 当前选中的本地文件
    QLabel*           noUsbTipLabel = nullptr;   // 无U盘提示标签

    QString checkUSBDirExists();                  // 检查U盘是否存在并返回路径
    void    initUsbFileView();                    // 初始化U盘文件视图
    void    refreshUsbFileView();                 // 刷新U盘文件视图
    void    initLocalFileView();                  // 初始化本地文件视图
    void    updateLocalFileView( int fileType );  // 更新本地文件视图

    void InitUsbAdminPage();  // USB管理页面初始化函数

signals:
    void UsbFormToMainWinToShowSignal();  //向主界面发送的显示主界面信号

private slots:
    void onUsbHomeBtnClicked();            //测试界面返回 主菜单键按下槽函数
    void onMenuBtnGroupClicked( int id );  //菜单按键组按下槽函数
};

#endif  // USBFORM_H
