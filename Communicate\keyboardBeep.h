#ifndef KEYBOARDBEEP_H
#define KEYBOARDBEEP_H

#include <QObject>
#include <QFile>
#include <QDebug>
#include <QThread>

// 定义蜂鸣器控制路径
#define BEEP_BRIGHTNESS_PATH "/sys/class/leds/user-led0/brightness"

class keyboardBeep : public QObject
{
    Q_OBJECT
    
public:
    explicit keyboardBeep(QObject *parent = nullptr);
    ~keyboardBeep();
    
    // 设置蜂鸣器状态
    bool setBeep(bool on);
    
    // 蜂鸣器短鸣一声
    bool beepOnce(int durationMs = 100);
    
    // 蜂鸣器按照指定模式鸣叫
    bool beepPattern(int onTimeMs, int offTimeMs, int count);
    
private:
    // 写入数据到文件
    bool writeToFile(const QString &path, const QString &data);
};

#endif // KEYBOARDBEEP_H
