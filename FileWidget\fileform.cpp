﻿#include "fileform.h"
#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

FileForm::FileForm( QWidget* parent ) : QWidget( parent ), ui( new Ui::FileForm )
{
    ui->setupUi( this );

    /* 返回主菜单键连接  */
    connect( ui->FileFormHome_btn, SIGNAL( clicked() ), this, SLOT( onFileHomeBtnClicked() ) );

    headRedirectBtnGroup = new QButtonGroup( this );
    headRedirectBtnGroup->addButton( ui->FileForm_pbtn_pattern, 1 );
    headRedirectBtnGroup->addButton( ui->FileForm_pbtn_chain, 2 );
    headRedirectBtnGroup->addButton( ui->FileForm_pbtn_fengtou, 3 );
    connect( headRedirectBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onHeaderRedirectMenuBtnGroupClicked( int ) ) );

    // init
    ui->File_stackedWidget->setCurrentIndex( 0 );
    ui->File_Tiltle_label->setText( "文件及生产/文件管理" );
    initFileAdminPage();

    ui->FileForm_pbtn_pattern->setVisible( false );
    ui->FileForm_pbtn_chain->setVisible( false );
    ui->FileForm_pbtn_fengtou->setVisible( false );
}

FileForm::~FileForm()
{
    delete fatDataMain;
    delete ui;
}

/* 测试界面返回 主菜单键按下槽函数 */
void FileForm::onFileHomeBtnClicked()
{
    if ( ui->File_stackedWidget->currentIndex() > 0 )
    {
        ui->File_stackedWidget->setCurrentIndex( 0 );
        ui->File_Tiltle_label->setText( "文件及生产/文件管理" );
        // 刷新文件
        if ( isFileAdminPageInited )
        {
            this->refreshFATFileList( FAT_DIR );
            this->initFATFileTableView();
        }
    }
    else
    {
        this->close();
        emit FileFormToMainWinToShowSignal();
    }
}

void FileForm::onHeaderRedirectMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            ui->File_Tiltle_label->setText( "文件及生产/花型" );
            this->ui->File_stackedWidget->setCurrentIndex( 5 );
            this->initPatternPage();
            break;
        case 2:
            ui->File_Tiltle_label->setText( "文件及生产/链条" );
            this->ui->File_stackedWidget->setCurrentIndex( 1 );
            this->initChainDataPage();
            break;
        case 3:
            ui->File_Tiltle_label->setText( "文件及生产/缝头" );
            this->ui->File_stackedWidget->setCurrentIndex( 2 );
            this->initFengtouPage();
            break;
    }
}

void FileForm::saveDataToMWWFile()
{
    QFileInfo fileInfo( this->currentFATFile );

    QFile file( FAT_DIR + fileInfo.baseName() + ".MWW" );
    if ( !file.open( QIODevice::WriteOnly ) )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "文件保存失败" ) );
        return;
    }

    QDataStream out( &file );
    out.setByteOrder( QDataStream::LittleEndian );
    // 保存 NeedleValveDataBase
    out << ( this->fatDataMain->NeedleActionDB->needle_cnt );
    out << ( this->fatDataMain->NeedleActionDB->action_cnt );
    for ( int i = 0; i < 32; i++ )
        for ( int j = 0; j < 25; j++ )
            out << this->fatDataMain->NeedleActionDB->Needle_data[ i ][ j ];
    for ( int i = 0; i < 100; i++ )
    {
        out << this->fatDataMain->NeedleActionDB->action_data[ i ].valid_Valve_cnt;
        for ( int j = 0; j < 50; j++ )
        {
            FATParser::ValveData* val = &this->fatDataMain->NeedleActionDB->action_data[ i ].Valve[ j ];
            quint16               fulldata( ( val->Valve_pos ) | ( val->sign << 11 ) | ( val->Valve_state << 12 ) );
            out << val->Valve_num << fulldata << val->null_value;
        }
        for ( int j = 0; j < 16; j++ )
        {
            FATParser::GudingNeedleIndex* gu = &this->fatDataMain->NeedleActionDB->action_data[ i ].Needle_index[ j ];
            out << gu->Needle_index << gu->state;
        }
    }

    // 保存 DensityData
    out << fatDataMain->DentisyData->needle_cnt;
    for ( int i = 0; i < 6; i++ )
        for ( int j = 0; j < 24; j++ )
            out << this->fatDataMain->DentisyData->Density[ i ][ j ];

    // 保存 MachineStepVector 的大小
    out << fatDataMain->StepVector.size();
    for ( const auto& step : fatDataMain->StepVector )
    {
        out << step->main_param.step_name << step->main_param.chicun << step->main_param.speed;
        quint16 fulldata1( ( step->main_param.xiangjin.state ) | ( step->main_param.xiangjin.end_value << 1 ) );
        out << step->main_param.xiangjin.start_value << fulldata1;
        quint8 fulldata2( ( step->main_param.zhentong_Density.state ) | ( step->main_param.zhentong_Density.end_value << 1 ) );
        out << step->main_param.zhentong_Density.start_value << fulldata2;
        quint8 fulldata3( ( step->main_param.chenjiang_Density.state ) | ( step->main_param.chenjiang_Density.end_value << 1 ) );
        out << step->main_param.chenjiang_Density.start_value << fulldata3;
        // main_param.null1
        out << step->main_param.null1.start_value << step->main_param.null1.end_value;
        quint8 fulldata4( ( step->main_param.shengke_Density.state ) | ( step->main_param.shengke_Density.end_value << 1 ) );
        out << step->main_param.shengke_Density.start_value << fulldata4;
        quint8 fulldata5( ( step->main_param.zuo_lengjiao.state ) | ( step->main_param.zuo_lengjiao.end_value << 1 ) );
        out << step->main_param.zuo_lengjiao.start_value << fulldata5;
        quint8 fulldata6( ( step->main_param.you_lengjiao.state ) | ( step->main_param.you_lengjiao.end_value << 1 ) );
        out << step->main_param.you_lengjiao.start_value << fulldata6;
        // Skip main_param.null_value
        for ( quint8 nullv : step->main_param.null_value )
        {
            out << nullv;
        }
        quint16 fulldata7( ( step->main_param.ktf1.state ) | ( step->main_param.ktf1.end_value << 1 ) );
        out << step->main_param.ktf1.start_value << fulldata7;
        quint16 fulldata8( ( step->main_param.ktf2.state ) | ( step->main_param.ktf2.end_value << 1 ) );
        out << step->main_param.ktf2.start_value << fulldata8;
        // Skip main_param.null_value1
        for ( quint8 nullv : step->main_param.null_value1 )
        {
            out << nullv;
        }
        out << step->main_param.huaxing_shezhi << step->main_param.lamao_shezhi << step->main_param.dagen_zhongxin << step->main_param.dagen_pianyi << step->main_param.fujia;
        // null_value2
        for ( quint8 nullv : step->main_param.null_value2 )
        {
            out << nullv;
        }

        out << step->action_cnt;

        FATParser::CYActionStruct* cy_ptr = step->cy_action_msg;
        for ( int i = 0; i < step->action_cnt; i++ )
        {
            out << ( cy_ptr + i )->cy_num << ( cy_ptr + i )->cy_circle_cnt << ( cy_ptr + i )->cy_state;
            for ( int j = 0; j < CmdCntPerCYAction; j++ )
            {
                out << ( cy_ptr + i )->cmd_msg[ j ].cmd_num;
                quint16 fulldatax( ( ( cy_ptr + i )->cmd_msg[ j ].cmd_param ) | ( ( cy_ptr + i )->cmd_msg[ j ].sign << 12 ) );
                out << fulldatax << ( cy_ptr + i )->cmd_msg[ j ].cmd_state;
            }
            out << ( cy_ptr + i )->cmd_cnt;
        }
    }

    // 保存 PatternData
    out << fatDataMain->PatternData->width << fatDataMain->PatternData->height;
    for ( int i = 0; i < 1000; i++ )
    {
        for ( int j = 0; j < 200; j++ )
        {
            FATParser::PatternUnitData* d = &fatDataMain->PatternData->pattern_data[ i ][ j ];
            quint8                      fulldata( ( d->xiangjin << 5 ) | ( d->la_mao << 4 ) | ( d->wa_gen_lamao << 3 ) | ( d->dasong_sanjiao << 2 ) | ( d->null ) );
            out << d->tian_sha << d->speed << d->zhu_suo << fulldata;
        }
    }

    // 保存 FengtouStepVector 的大小
    out << fatDataMain->FengtouVector.size();
    for ( const auto& fengtou : fatDataMain->FengtouVector )
    {
        // Skip start[ 126 ]
        out << fengtou->step_cnt;
        for ( int i = 0; i < fengtou->step_cnt; i++ )
        {
            // Skip step_start[ 64 ]
            for ( quint8 nullv : fengtou->step->step_start )
            {
                out << nullv;
            }
            out << ( fengtou->step + i )->baojing_yanshi << ( fengtou->step + i )->dongzuo_yanshi << ( fengtou->step + i )->dongzuo_zhiling << ( fengtou->step + i )->fuwei_dongzuo;
            // Skip null1[8]
            for ( quint8 nullv : fengtou->step->null1 )
            {
                out << nullv;
            }
            out << ( fengtou->step + i )->Valve_cnt;
            for ( int j = 0; j < 12; j++ )
            {
                FATParser::FengtouSignalValveStruct* s = &( fengtou->step + i )->Valve_array[ j ];
                out << s->num << s->type << s->state;
            }
            out << ( fengtou->step + i )->motor_cnt;
            for ( int j = 0; j < 6; j++ )
            {
                FATParser::FengtouMotorStruct* m = &( fengtou->step + i )->motor_array[ j ];
                quint8                         fulldata( ( m->num ) | ( m->pattern << 5 ) );
                out << fulldata << m->val << m->null;
            }
            // Skip null2[92]
            for ( quint8 nullv : fengtou->step->null2 )
            {
                out << nullv;
            }
        }
    }

    // 保存 PatternExtraData
    for ( int i = 0; i < 10; i++ )
    {
        FATParser::FLowerAndLaMaoLoop* l = &fatDataMain->PatternExtraData->flower_loop[ i ];
        out << l->loop_start << l->loop_end << l->loop_num;
    }
    for ( int i = 0; i < 10; i++ )
    {
        FATParser::FLowerAndLaMaoLoop* l = &fatDataMain->PatternExtraData->lamao_loop[ i ];
        out << l->loop_start << l->loop_end << l->loop_num;
    }
    for ( int i = 0; i < 7; i++ )
    {
        FATParser::Zhusuo* l = &fatDataMain->PatternExtraData->zhusuo[ i ];
        quint8             fulldata1( ( l->R8 << 7 ) | ( l->R7 << 6 ) | ( l->R6 << 5 ) | ( l->R5 << 4 ) | ( l->R4 << 3 ) | ( l->R3 << 2 ) | ( l->R2 << 1 ) | ( l->R1 ) );
        quint8             fulldata2( ( l->RM ) | ( l->null << 1 ) );
        out << fulldata1 << fulldata2;
    }
    out << fatDataMain->PatternExtraData->zhusuo_num_cfg.advance_quantity << fatDataMain->PatternExtraData->zhusuo_num_cfg.overlap_quantity;
    for ( int i = 0; i < 4; i++ )
    {
        FATParser::Zhusuo2* l = &fatDataMain->PatternExtraData->zhusuo2[ i ];
        quint8              fulldata1( ( l->R6 << 7 ) | ( l->R5 << 6 ) | ( l->R4 << 5 ) | ( l->R3 << 4 ) | ( l->R2 << 3 ) | ( l->R1 << 2 ) | ( l->RM << 1 ) | ( l->F1 ) );
        quint8              fulldata2( ( l->R7 ) | ( l->R8 << 1 ) | ( l->null << 2 ) );
        out << fulldata1 << fulldata2;
    }

    file.close();
}

void FileForm::restoreFromMWWFile( QString filename )
{
    QFile file( FAT_DIR + filename );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "文件打开失败" ) );
        return;
    }

    if ( this->fatDataMain == nullptr )
        this->fatDataMain = new FATParser::FATDataMain();

    QByteArray data   = file.readAll();
    char*      buffer = data.data();

    // 保存 NeedleValveDataBase
    QSharedPointer< FATParser::NeedleValveDataBase > db = QSharedPointer< FATParser::NeedleValveDataBase >( new FATParser::NeedleValveDataBase );
    /*选针个数*/
    memcpy( &db->needle_cnt, buffer, 2 );
    /*动作个数*/
    memcpy( &db->action_cnt, buffer + 0x02, 2 );
    memcpy( ( char* )&db->Needle_data[ 0 ][ 0 ], buffer + 0x04, 32 * 25 );
    memcpy( &db->action_data[ 0 ], buffer + 0x04 + 32 * 25, sizeof( FATParser::ActionStruct ) * 100 );
    this->fatDataMain->NeedleActionDB = db;

    // 保存 DensityData
    quint64                                  offset  = 0x04 + 32 * 25 + sizeof( FATParser::ActionStruct ) * 100;
    QSharedPointer< FATParser::DensityData > Density = QSharedPointer< FATParser::DensityData >( new FATParser::DensityData );
    memcpy( &Density->needle_cnt, buffer + offset, 4 );
    offset += 4;
    memcpy( ( quint8* )&Density->Density[ 0 ][ 0 ], buffer + offset, 6 * 24 * 2 );
    offset += 6 * 24 * 2;
    this->fatDataMain->DentisyData = Density;

    // 保存 MachineStepVector 的大小
    int vectorSize;
    memcpy( &vectorSize, buffer + offset, sizeof( int ) );
    offset += sizeof( int );
    FATParser::MachineStepVector step_vec;
    for ( int i = 0; i < vectorSize; i++ )
    {
        QSharedPointer< FATParser::SockMachineStepMsg > step = QSharedPointer< FATParser::SockMachineStepMsg >( new FATParser::SockMachineStepMsg );
        /*获取主程序参数*/
        memcpy( ( char* )&step->main_param, buffer + offset, sizeof( FATParser::MainProgamParam ) );
        offset += sizeof( FATParser::MainProgamParam );
        /*获取动作数量*/
        memcpy( &step->action_cnt, buffer + offset, 2 );
        offset += 2;
        /*申请动作信息数据内存*/
        step->cy_action_msg = new FATParser::CYActionStruct[ step->action_cnt ];
        /*拷贝动作信息数据*/
        memcpy( ( char* )step->cy_action_msg, buffer + offset, ( step->action_cnt * sizeof( FATParser::CYActionStruct ) ) );
        /*计算此次步骤的偏移量*/
        offset += ( step->action_cnt * sizeof( FATParser::CYActionStruct ) );
        /*将步骤加到向量中*/
        step_vec.append( step );
    }
    this->fatDataMain->StepVector = step_vec;

    // 保存 PatternData
    QSharedPointer< FATParser::PatternData > pattern_data = QSharedPointer< FATParser::PatternData >( new FATParser::PatternData );
    memcpy( &pattern_data->width, buffer + offset, 4 );
    offset += 4;
    memcpy( &pattern_data->height, buffer + offset, 4 );
    offset += 4;
    memcpy( ( char* )&pattern_data->pattern_data[ 0 ][ 0 ], buffer + offset, 1000 * 200 * 4 );
    offset += 1000 * 200 * 4;
    fatDataMain->PatternData = pattern_data;

    // 保存 FengtouStepVector 的大小
    int fengtourSize;
    memcpy( &fengtourSize, buffer + offset, sizeof( int ) );
    offset += sizeof( int );
    FATParser::FengtouStepVector vec;
    for ( int i = 0; i < fengtourSize; i++ )
    {
        QSharedPointer< FATParser::FengtouStruct > step = QSharedPointer< FATParser::FengtouStruct >( new FATParser::FengtouStruct );
        // skip   quint8             start[ 126 ]
        //        offset += 126;
        /*计算该缝头程序有多少step*/
        memcpy( &step->step_cnt, buffer + offset, 2 );
        offset += 2;
        /*申请内存*/
        step->step = new FATParser::FengtouStepStruct[ step->step_cnt ];
        memcpy( ( char* )( step->step ), buffer + offset, step->step_cnt * sizeof( FATParser::FengtouStepStruct ) );
        offset += step->step_cnt * sizeof( FATParser::FengtouStepStruct );
        vec.append( step );
    }
    fatDataMain->FengtouVector = vec;

    // 保存 PatternExtraData
    QSharedPointer< FATParser::PatternExtraData > patternExtra_data = QSharedPointer< FATParser::PatternExtraData >( new FATParser::PatternExtraData );
    // 花型循环数据
    memcpy( ( char* )&patternExtra_data->flower_loop, buffer + offset, 10 * sizeof( FATParser::FLowerAndLaMaoLoop ) );
    offset += 10 * sizeof( FATParser::FLowerAndLaMaoLoop );
    // 拉毛循环数据
    memcpy( ( char* )&patternExtra_data->lamao_loop, buffer + offset, 10 * sizeof( FATParser::FLowerAndLaMaoLoop ) );
    offset += 10 * sizeof( FATParser::FLowerAndLaMaoLoop );
    // 主梭配置数据
    memcpy( ( char* )&patternExtra_data->zhusuo, buffer + offset, 7 * sizeof( FATParser::Zhusuo ) );
    offset += 7 * sizeof( FATParser::Zhusuo );
    // 主梭提前量、重叠量
    memcpy( ( char* )&patternExtra_data->zhusuo_num_cfg, buffer + offset, sizeof( FATParser::ZhusoNumCfg ) );
    offset += sizeof( FATParser::ZhusoNumCfg );
    // 主梭2的位置
    memcpy( ( char* )&patternExtra_data->zhusuo2, buffer + offset, 4 * sizeof( FATParser::Zhusuo2 ) );
    fatDataMain->PatternExtraData = patternExtra_data;

    file.close();
}
