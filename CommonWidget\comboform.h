﻿#ifndef COMBOFORM_H
#define COMBOFORM_H

#include "Config/readlinkconfig.h"
#include <QPushButton>
#include <QWidget>

namespace Ui
{
class ComboForm;
}

class ComboForm : public QWidget
{
    Q_OBJECT

public:
    explicit ComboForm( QWidget* parent = nullptr, QMap< int, QString >* list = nullptr, int CountPerLine = 5 );
    ~ComboForm();

signals:
    void itemSelected( int itemKey );
    void finished();

public slots:
    void onPushButtonClicked();

protected:
    void closeEvent( QCloseEvent* event ) override;

private:
    Ui::ComboForm* ui;
};

#endif  // COMBOFORM_H
