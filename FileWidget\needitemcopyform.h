﻿#ifndef NEEDITEMCOPYFORM_H
#define NEEDITEMCOPYFORM_H

#include "CommonWidget/mylineedit.h"
#include <QWidget>

namespace Ui
{
class NeedItemCopyForm;
}

class NeedItemCopyForm : public QWidget
{
    Q_OBJECT

public:
    explicit NeedItemCopyForm( quint8* data, QWidget* parent = nullptr );
    ~NeedItemCopyForm();
    void setData( quint8* data );

private:
    Ui::NeedItemCopyForm* ui;
    MyLineEdit*           currentLineEdit        = nullptr;
    bool                  firsTimeToEditLineEdit = false;
    quint8*               _srcData               = nullptr;  // 保存数据原始指针，保存时会更新
    quint8                _data[ 8 ];
    void                  showData();
    void                  onBtnClicked();
private slots:
    void onLineEditClicked();
    void onLineEditFinished();
signals:
    void finished();

public slots:
    void onPushButtonClicked();

protected:
    void closeEvent( QCloseEvent* event ) override;
};

#endif  // NEEDITEMCOPYFORM_H
