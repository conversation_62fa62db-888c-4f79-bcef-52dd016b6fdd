#include "ReadOnekeyConfig.h"

ReadOnekeyConfig::ReadOnekeyConfig() {}

ReadOnekeyConfig::~ReadOnekeyConfig() {}

void ReadOnekeyConfig::parseConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        // 文件打开失败
        qDebug() << "OneKey Json Open Failed!";
        QMessageBox::warning( nullptr, "错误提示", "OneKeyConfig.json文件不存在" );
        return;
    }
    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        // Json文档无效
        qDebug() << "Json文档无效!";
        return;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObject = jsonDocument.object();

    // 获取ktfCfg数组
    QJsonArray ktfArray = jsonObject[ "KTF" ].toArray();
    // 遍历ktfCfg数组
    for ( int i = 0; i < ktfArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = ktfArray.at( i ).toObject();

        int        key = step[ "id" ].toInt();
        KtfCfgItem item;
        item.id      = step[ "id" ].toInt();
        item.name    = step[ "name" ].toString();
        item.enable  = step[ "enable" ].toInt();
        item.install = step[ "install" ].toInt();
        item.dir     = step[ "dir" ].toInt();

        // 对key和value进行处理
        this->_ktfList.insert( key, item );
    }

    file.close();  // 关闭文件
}

int ReadOnekeyConfig::saveConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::WriteOnly ) )
    {
        // 文件打开失败
        qDebug() << "Test Config Json文件打开失败!";
        return -1;
    }

    QJsonObject jsonObjectMain;

    QJsonArray ktfArray;
    // 遍历ktfCfg数组
    for ( QMap< int, KtfCfgItem >::iterator it = this->_ktfList.begin(); it != this->_ktfList.end(); ++it )
    {
        // 获取数组元素
        KtfCfgItem& item = it.value();
        QJsonObject step;
        step.insert( "id", it.key() );
        step.insert( "name", item.name );
        step.insert( "enable", item.enable );
        step.insert( "install", item.install );
        step.insert( "dir", item.dir );
        ktfArray.append( step );
    }
    jsonObjectMain.insert( "KTF", ktfArray );

    QJsonDocument jsonDocument( jsonObjectMain );
    try
    {
        file.write( jsonDocument.toJson() );
        file.close();
        qDebug() << "JSON 文件写入成功";
        file.close();  // 关闭文件

        return 1;
    }
    catch ( const std::exception& e )
    {
        file.close();  // 关闭文件
        return -1;
    }
}
