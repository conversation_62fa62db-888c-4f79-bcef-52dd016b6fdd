#ifndef SOKPARSER_H
#define SOKPARSER_H

#include "Common/parameters.h"
#include <QDebug>
#include <QFile>
#include <QMap>
#include <QSettings>
#include <QSharedPointer>
#include <QString>
#include <QVector>

class SokParser
{
public:
    SokParser();

#pragma pack( 1 )
    struct YOYOParamItem
    {
        quint8 yarnGrams;
        quint8 tenthsOfAGram;
    };

    struct HeadParam
    {
        quint16       needleNum;
        quint8        jackButtsDiagonals;
        quint8        oilProgram;
        quint8        diameter;
        quint16       dialMotorPos[ 16 ];
        quint16       patternStartPos;
        quint8        stitchCamGauge;
        quint8        sizeToActiveMachine;
        quint8        revValveRegulation;
        YOYOParamItem yoyoParamReset[ 8 ];
        YOYOParamItem yoyoParamZero[ 8 ];
        quint8        yarnForYOYOMotors[ 9 ];  // 第0个不显示，作用不详
    };

    struct ClosedToeSizeParamItem
    {
        quint8  tiltingDeviceDownDelay;
        quint16 turningDeviceInclination;
        quint16 turningDeviceTubeOutlet;
        quint8  tiltingDeviceInclinationStage;
        quint8  airBlastEnabled;
    };

    struct ClosedToeParam
    {
        quint8                 stitchingType;
        quint8                 numOfPinAtStartPerSeaming3;
        quint16                stitchingSpeed;
        quint8                 suctionType;
        quint8                 pickUpDevice;
        quint8                 linkTransfer;
        quint8                 OnboardDataModification;
        quint8                 revSockEjection;
        quint8                 sockPusherSpeed;
        quint16                suction[ 7 ];
        ClosedToeSizeParamItem sizeParam[ 8 ];
    };

    struct ChainSpeedParamItem
    {
        quint16 speed;
        quint16 position;
    };

    struct EconomizationParamItem
    {
        quint8 economizations[ 8 ];
        quint8 forStep;
    };

    struct SpecialFunctionParamItem
    {
        quint16 specialFunctionId;
        quint16 position;
        quint8  status;
    };

    struct CamParamItem
    {
        quint16 camId;
        quint16 position;
        quint8  status;
    };

    // fingerId由fingerId和feed换算过来，即feed为‘1’，就是fingerId，feed为‘e'就是256+fingerId
    struct FingerParamItem
    {
        quint16 fingerId;
        // char    feed;
        quint16 position;
        quint8  status;
    };

    struct ControlValeParamItem
    {
        quint8  movement;
        quint16 step;
        quint16 position;
    };

    struct PatternParamItem
    {
        char   patternName[ 8 ][ 30 ];
        char   codifyCode[ 30 ];
        char   adjustmentCode;
        quint8 adjustmentNeedleNum;
        char   alternationCode;
        quint8 status;
    };

    struct ElasticPatternParamItem
    {
        char   patternName[ 8 ][ 30 ];
        char   codifyCode[ 30 ];
        char   adjustmentCode;
        quint8 adjustmentNeedleNum;
        char   alternationCode;
        quint8 status;
    };

    struct SuperposedPatternParamItem
    {
        char   patternName[ 8 ][ 30 ];
        char   codifyCode[ 30 ];
        char   mirroringCode;
        quint8 needlesStart;
        quint8 widthNeedles;
        quint8 status;
    };

    struct DuckingStitchParamItem
    {
        char   patternName[ 8 ][ 30 ];
        char   codifyCode[ 30 ];
        char   adjustmentCode;
        quint8 adjustmentNeedleNum;
        quint8 status;
    };

    struct HeelPatternParamItem
    {
        char   patternName[ 30 ];
        char   codifyCode[ 30 ];
        char   heelCode;
        quint8 status;
    };

    struct SelectionParamItem
    {
        char    name[ 30 ];
        quint8  drum;
        char    type;
        quint16 position;
        quint8  status;
    };

    struct GussetParamItem
    {
        char    name[ 30 ];
        quint8  drum;
        quint16 position;
        quint16 quantity;
        quint8  piority;
        quint8  status;
    };

    struct RaisingDialMotorParamItem
    {
        quint16 position;
        quint8  motorId;
        char    movementCode;
        quint16 motorSteps;
    };

    struct MotorizedStitchCamsParamItem
    {
        char   patternName[ 8 ][ 30 ];
        char   codifyCode[ 30 ];
        quint8 status;
    };

    struct YOYOYarnWeightParamItem
    {
        quint8  motor;
        char    action;
        quint8  variation;
        quint16 position;
    };

    struct GraduationMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 stepStart;
        quint16 stepEnd;
        char    graduationId;
        char    stopForMovementRaisingMotor;
        quint16 degreeWhenMotorBeginMove;
        bool    prohibitsValueChange;
        quint16 cylinderStart[ 8 ];
        quint16 cylinderEnd[ 8 ];
        quint16 startWidth[ 8 ];
        quint16 startMeasureQuarter[ 8 ];
        quint16 endWidth[ 8 ];
        quint16 endMeasureQuarter[ 8 ];
    };

    struct ElasticMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 stepStart;
        quint16 stepEnd;
        bool    prohibitsValueChange1;
        quint16 elasticStart1[ 8 ];
        quint16 elasticEnd1[ 8 ];
        bool    prohibitsValueChange2;
        quint16 elasticStart2[ 8 ];
        quint16 elasticEnd2[ 8 ];
        bool    prohibitsSawValueChange;
        quint16 sawStart[ 8 ];
        quint16 sawEnd[ 8 ];
    };

    struct SinkerMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 stepStart;
        quint16 stepEnd;
        char    sinkerId;
        char    stopForMovementRaisingMotor;
        quint16 degreeWhenMotorBeginMove;
        bool    prohibitsAngularValueChange;
        quint16 angularStart[ 8 ];
        quint16 angularEnd[ 8 ];
        bool    prohibitsSinkerValueChange;
        quint16 sinkerStart[ 8 ];
        quint16 sinkerEnd[ 8 ];
    };

    union ChainDataItemUnion
    {
        char                         stepDescription[ 60 ];
        ChainSpeedParamItem          chainSpeedParam;
        EconomizationParamItem       economizationParam;
        quint16                      functionPram;
        quint16                      heelSizeVaration;
        SpecialFunctionParamItem     specialFunctionParam;
        CamParamItem                 camParam;
        FingerParamItem              fingerParam;
        ControlValeParamItem         controlValeParam;
        PatternParamItem             patternParam;
        ElasticPatternParamItem      elasticPatternParam;
        SuperposedPatternParamItem   superposedPatternParam;
        DuckingStitchParamItem       duckingStitchParam;
        HeelPatternParamItem         heelPatternParam;
        SelectionParamItem           selectionParam;
        GussetParamItem              gussetParam;
        RaisingDialMotorParamItem    raisingDialMotorParam;
        MotorizedStitchCamsParamItem motorizedStitchCamsParam;
        YOYOYarnWeightParamItem      yoyoYarnWeightParam;
    };

    struct ChainDataItem
    {
        quint8             type;
        ChainDataItemUnion ItemUnion;
    };

    struct ModuleDataItem
    {
        char    name[ 38 ];
        quint16 startStepIndex;
        quint16 endStepIndex;
    };

    typedef QMap< int, QSharedPointer< ChainDataItem > > StepDataMap;
    typedef QMap< int, StepDataMap >                     ChainDataMap;

    typedef QVector< QSharedPointer< GraduationMotorParamItem > > GraduationMotorParamVector;
    typedef QVector< QSharedPointer< ElasticMotorParamItem > >    ElasticMotorParamVector;
    typedef QVector< QSharedPointer< SinkerMotorParamItem > >     SinkerMotorParamVector;

    typedef QVector< QSharedPointer< ModuleDataItem > > ModuleDataVector;

    // 用于保存从sok文件中解析出来的数据
    struct SokFileData
    {
        QSharedPointer< HeadParam >      headParam;
        QSharedPointer< ClosedToeParam > closedToeParam;
        ChainDataMap                     chainData;
        GraduationMotorParamVector       graduationMotorParam;
        ElasticMotorParamVector          elasticMotorParam;
        SinkerMotorParamVector           sinkerMotorParam;
        ModuleDataVector                 moduleDataVector;
    };

    typedef QMap< int, quint16 > DisCfgMap;

    struct DisData
    {
        quint16    width;
        quint16    height;
        QByteArray drumData;
        QByteArray yarnfingerData;
    };

#pragma pack()

    /**
     *@brief  解析出sok所有数据
     *@param  QString & path 文件路径
     *@return int
     *<AUTHOR>
     *@date   2024/11/09
     */
    int Parser( QString fat_path, SokFileData* sok_main );
    /**
     * @brief getDisCfgMap 从cfg文件读取Dis颜色配置
     * @param fileName 传入的文件名，通常不包含扩展名
     * @param type 类型，Dis 0 Disover 1
     * @return Dis颜色配置
     */
    DisCfgMap getDisCfgMap( QString fileName, quint8 type );

    /**
     * @brief getDisData 从DIS文件中读取Dis数据
     * @param fileName 文件名
     * @return Dis数据，包含宽度、高度，选针和梭口四类数据。
     */
    QSharedPointer< DisData > getDisData( QString fileName );

    /**
     * @brief getSlzCfg 从slz文件中读取选针数据
     * @param fileName slz文件名，不含扩展名
     * @return slz选针数据
     */
    QString getSlzCfg( QString fileName );

    /**
     * @brief getTasCfg 从tas文件中读取选针数据
     * @param fileName tas文件名，不含扩展名
     * @return tas数据
     */
    QString getTasCfg( QString fileName );

    /**
     * @brief getGraduationMotorParamVector 获取Graduation电机密度数据
     * @param data 文件内容指针
     * @return Graduation电机密度数据
     */
    GraduationMotorParamVector getGraduationMotorParamVector( QByteArray* data );

    /**
     * @brief getElasticMotorParamVector 获取橡筋电机密度数据
     * @param data 文件内容指针
     * @return 橡筋电机密度数据
     */
    ElasticMotorParamVector getElasticMotorParamVector( QByteArray* data );

    /**
     * @brief getSinkerMotorParamVector 获取生克电机密度
     * @param data 文件内容指针
     * @return 生克电机密度
     */
    SinkerMotorParamVector getSinkerMotorParamVector( QByteArray* data );

    /**
     * @brief GetChainData 获取链条数据
     * @param data 文件内容指针
     * @param fileSaveType 文件保存类型 0-顺序 1-逆序
     * @return 链条数据
     */
    ChainDataMap GetChainData( QByteArray* data, quint8 fileSaveType, int typeIndex );

    /**
     * @brief getModuleDataVector 获取模块数组
     * @param data 文件内容指针
     * @return 模块数据
     */
    ModuleDataVector getModuleDataVector( QByteArray* data );

private:
    /**
     * @brief 获取头部参数
     * @param data 文件内容指针
     * @return 头部参数
     */
    QSharedPointer< HeadParam > GetHeadParam( QByteArray* data );

    /**
     * @brief 获取缝头参数
     * @param data 文件内容指针
     * @return 缝头参数
     */
    QSharedPointer< ClosedToeParam > GetClosedToeParam( QByteArray* data );
};

#endif  // SOKPARSER_H
