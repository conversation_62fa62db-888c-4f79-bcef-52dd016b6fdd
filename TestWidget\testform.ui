<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TestForm</class>
 <widget class="QWidget" name="TestForm">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QStackedWidget" name="Test_stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>50</y>
     <width>1024</width>
     <height>551</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="Menu_page">
    <widget class="QWidget" name="layoutWidget">
     <property name="geometry">
      <rect>
       <x>50</x>
       <y>55</y>
       <width>931</width>
       <height>441</height>
      </rect>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_8">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <widget class="QPushButton" name="Test_valve_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
	background-color: rgb(193, 193, 193);
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(://m-airvalve.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed {
	background-color: #c1c1c1;
}
</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_12">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>气阀</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QPushButton" name="Test_select_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/select.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_13">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>选针器</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="QPushButton" name="Test_serve_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/ServoMotor.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_10">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>伺服
电机</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <item>
           <widget class="QPushButton" name="Test_step_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/Stepmotor.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_9">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>织袜
步进</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <item>
           <widget class="QPushButton" name="Test_fengtou_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/fengtou.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_14">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>缝头</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_9">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_9">
          <item>
           <widget class="QPushButton" name="Test_keyboard_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/keyboard.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_16">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>按键</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_7">
          <item>
           <widget class="QPushButton" name="Test_function_btn">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/setting (1).png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_15">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
              <stylestrategy>PreferDefault</stylestrategy>
             </font>
            </property>
            <property name="text">
             <string>功能</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QWidget" name="ValveTest_page">
    <widget class="QWidget" name="OneValveTest_page" native="true">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>1021</width>
       <height>491</height>
      </rect>
     </property>
     <widget class="QTabWidget" name="tabWidget_Valve">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>0</y>
        <width>1001</width>
        <height>491</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QTabBar::tab{
	height: 50px;
	width:140px;
	border-radius: 5px 5px 0 0;
	margin-right: 2px;
	padding: 5px;
	background-color: #f0f0f0;
	border: 1px solid #d0d0d0;
	border-bottom: none;
	font-size: 9pt;
}
QTabBar::tab:hover {
	background-color: #e0e0e0;
}
QTabBar::tab:selected {
	background-color: #2980b9;
	color: white;
	font-weight: bold;
	border: 1px solid #2980b9;
	border-bottom: none;
}
QTabWidget::pane {
	border: 1px solid #c0c0c0;
	background-color: #f8f8f8;
	border-radius: 0 0 5px 5px;
}
QTabWidget::tab-bar {
	left: 5px;
}</string>
      </property>
      <property name="currentIndex">
       <number>3</number>
      </property>
      <widget class="QWidget" name="tab_specialFun">
       <attribute name="title">
        <string>特殊功能</string>
       </attribute>
      </widget>
      <widget class="QWidget" name="tab_cam">
       <attribute name="title">
        <string>三角</string>
       </attribute>
      </widget>
      <widget class="QWidget" name="tab_yarnFinger">
       <attribute name="title">
        <string>纱嘴</string>
       </attribute>
      </widget>
      <widget class="QWidget" name="tab_yarn2">
       <attribute name="title">
        <string>副梭</string>
       </attribute>
      </widget>
      <widget class="QWidget" name="tab_extend">
       <attribute name="title">
        <string>扩展</string>
       </attribute>
      </widget>
     </widget>
    </widget>
    <widget class="QPushButton" name="ValveTest_OverTurn_btn">
     <property name="geometry">
      <rect>
       <x>150</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
    font-size: 9pt;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string> 反转</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ValveTest_Auto_btn">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
    font-size: 9pt;
}</string>
     </property>
     <property name="text">
      <string>自动</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ValveTest_Clear_btn">
     <property name="geometry">
      <rect>
       <x>260</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
    font-size: 9pt;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string> 清除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ValveTest_Continue_btn">
     <property name="geometry">
      <rect>
       <x>370</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
    font-size: 9pt;
}</string>
     </property>
     <property name="text">
      <string>连续</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_valveFreq">
     <property name="geometry">
      <rect>
       <x>700</x>
       <y>500</y>
       <width>131</width>
       <height>41</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit {
    background-color: white;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
    font-size: 9pt;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>505</y>
       <width>61</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #2980b9;
    color: white;
    border-radius: 5px;
    padding: 2px;
    font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>频率</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="ValveTest_FreqDec_btn">
     <property name="geometry">
      <rect>
       <x>930</x>
       <y>500</y>
       <width>81</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    border: 1px solid #27ae60;
    border-radius: 5px;
    padding: 5px;
    color: white;
    font-size: 9pt;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #27ae60;
    border: 1px solid #229954;
}
QPushButton:pressed {
    background-color: #229954;
    border: 1px solid #1e8449;
}</string>
     </property>
     <property name="text">
      <string>频率-</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ValveTest_FreqPlus_btn">
     <property name="geometry">
      <rect>
       <x>840</x>
       <y>500</y>
       <width>81</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    border: 1px solid #27ae60;
    border-radius: 5px;
    padding: 5px;
    color: white;
    font-size: 9pt;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #27ae60;
    border: 1px solid #229954;
}
QPushButton:pressed {
    background-color: #229954;
    border: 1px solid #1e8449;
}</string>
     </property>
     <property name="text">
      <string>频率+</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_valPos">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>506</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: white;
    color: #333333;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 2px;
    font-size: 9pt;
}</string>
     </property>
     <property name="text">
      <string>Position</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="SelectTest_page">
    <widget class="QFrame" name="frame">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>0</y>
       <width>411</width>
       <height>471</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame {
    background-color: #f5f5f5;
    border-radius: 10px;
    border: 1px solid #d0d0d0;
}

QCheckBox {
    background-color: #ffffff;
    width: 70px;
    height: 40px;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QCheckBox:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QCheckBox::indicator:unchecked {
    border-image: url(:/checkbox.png);
    Width: 30px;
    Height: 30px;
}

QCheckBox::indicator:checked {
    border-image: url(:/checkbox_checked.png);
    Width: 30px;
    Height: 30px;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>11</y>
        <width>371</width>
        <height>451</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <widget class="QCheckBox" name="chx_Selector1">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器1</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QCheckBox" name="chx_Selector2">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器2</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QCheckBox" name="chx_Selector3">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器3</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QCheckBox" name="chx_Selector4">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器4</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QCheckBox" name="chx_Selector5">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器5</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QCheckBox" name="chx_Selector6">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器6</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QCheckBox" name="chx_Selector7">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器7</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QCheckBox" name="chx_Selector8">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器8</string>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QCheckBox" name="chx_Selector9">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器9</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QCheckBox" name="chx_Selector10">
         <property name="styleSheet">
          <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
         </property>
         <property name="text">
          <string>选针器10</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QFrame" name="frame_2">
     <property name="geometry">
      <rect>
       <x>440</x>
       <y>0</y>
       <width>331</width>
       <height>471</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame {
    background-color: #f5f5f5;
    border-radius: 10px;
    border: 1px solid #d0d0d0;
}

QCheckBox{
    background-color: #ffffff;
    width: 70px;
    height: 40px;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QCheckBox:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QCheckBox::indicator:unchecked{
    border-image: url(:/checkbox.png);
    Width: 30px;
    Height: 30px;
}

QCheckBox::indicator:checked{
    border-image: url(:/checkbox_checked.png);
    Width: 30px;
    Height: 30px;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>11</y>
        <width>291</width>
        <height>451</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QRadioButton" name="rb_knife1">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片1</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife2">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片2</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife3">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片3</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife4">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片4</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife5">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片5</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife6">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片6</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife7">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片7</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="rb_knife8">
         <property name="styleSheet">
          <string notr="true">QRadioButton {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
    font-size: 8pt;
}

QRadioButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}

QRadioButton::indicator:unchecked{
    border-image: url(:/radio_button);
    Width: 30px;
    Height: 30px;
}

QRadioButton::indicator:checked{
    border-image: url(:/radio-button-checked.png);
    Width: 30px;
    Height: 30px;
}</string>
         </property>
         <property name="text">
          <string>刀片8</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QPushButton" name="SelectTest_1x1_btn">
     <property name="geometry">
      <rect>
       <x>190</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}</string>
     </property>
     <property name="text">
      <string>1X1 A/B</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_SignalKnife_btn">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}</string>
     </property>
     <property name="text">
      <string>单刀测试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_Auto_btn">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}
</string>
     </property>
     <property name="text">
      <string>自动测试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_AllUpDown_btn">
     <property name="geometry">
      <rect>
       <x>310</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}</string>
     </property>
     <property name="text">
      <string> 全上/全下</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_Old_btn">
     <property name="geometry">
      <rect>
       <x>670</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}</string>
     </property>
     <property name="text">
      <string>老化测试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_KnifeCheck_btn">
     <property name="geometry">
      <rect>
       <x>790</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}</string>
     </property>
     <property name="text">
      <string>刀片检测</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_FreqPlus_btn">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>240</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #27ae60;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #16a085;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>频率+</string>
     </property>
    </widget>
    <widget class="QLabel" name="le_selectorFreq">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>160</y>
       <width>111</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(255, 255, 255);</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_11">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>120</y>
       <width>111</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">/*background-color: rgb(255, 255, 255);*/
background-color: rgb(5, 155, 255);</string>
     </property>
     <property name="text">
      <string>频率设定</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_FreqDec_btn">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>300</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #27ae60;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #16a085;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>频率-</string>
     </property>
    </widget>
    <widget class="QPushButton" name="SelectTest_SignalLoop_btn">
     <property name="geometry">
      <rect>
       <x>430</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}</string>
     </property>
     <property name="text">
      <string>单刀循环</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="ServoTest_page">
    <widget class="QLabel" name="label_232">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>50</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>速度设定</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="ServoTest_ServoSpeedSet_LE">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>50</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_233">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>250</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>编码器</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_Code_label">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>250</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_235">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>绝对角度</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_AbAngle_label">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_Circle_label">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>390</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_238">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>390</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>圈计数</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_239">
     <property name="geometry">
      <rect>
       <x>540</x>
       <y>50</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>方向设定</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_DirSet_label">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>50</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string> 停止</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_Status_label">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>110</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>关</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_241">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>110</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>伺服状态</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_Elastic_label">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>110</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>关</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_244">
     <property name="geometry">
      <rect>
       <x>540</x>
       <y>110</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>橡筋使能</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_ActSpeed_label">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>250</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_246">
     <property name="geometry">
      <rect>
       <x>540</x>
       <y>250</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>实际速度</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_AbNeedle_label">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_250">
     <property name="geometry">
      <rect>
       <x>540</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>绝对针位</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_Zero_label">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>390</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="ServoTest_ZeroPos_label">
     <property name="geometry">
      <rect>
       <x>540</x>
       <y>390</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
      background-color: #3498db;
      color: white;
      border-radius: 5px;
      padding: 2px;
      font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>零位位置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_2">
     <property name="geometry">
      <rect>
       <x>450</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>默认低速</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_4">
     <property name="geometry">
      <rect>
       <x>210</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>橡筋同步</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_3">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>200</y>
       <width>101</width>
       <height>101</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    border-radius:50px;					    /* 按钮边框的圆角设置 */
	border-image: url(:/run1.png);
	background-color: rgb(255, 255, 255);
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_7">
     <property name="geometry">
      <rect>
       <x>570</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>伺服减速</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_6">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>伺服状态</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_5">
     <property name="geometry">
      <rect>
       <x>330</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>零位校准</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_1">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>切换方向</string>
     </property>
    </widget>
    <widget class="QPushButton" name="ServoTest_btn_8">
     <property name="geometry">
      <rect>
       <x>690</x>
       <y>490</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>伺服加速</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="StepTest_page">
    <widget class="QPushButton" name="StepTest_MotorCW_btn">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>电机正转</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StepTest_MotorCCW_btn">
     <property name="geometry">
      <rect>
       <x>210</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>电机反转</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StepTest_MotorGoZero_btn">
     <property name="geometry">
      <rect>
       <x>330</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>电机归零</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StepTest_MoveBackAndForth_btn">
     <property name="geometry">
      <rect>
       <x>450</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>往复运动</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StepTest_MotorStart_btn">
     <property name="geometry">
      <rect>
       <x>570</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>电机启动</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StepTest_MotorStop_btn">
     <property name="geometry">
      <rect>
       <x>690</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>电机停止</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StepTest_SetZero_btn">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>460</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}
QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string>电机零位</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_6">
     <property name="geometry">
      <rect>
       <x>600</x>
       <y>80</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_211">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>200</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>沉降电机</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_7">
     <property name="geometry">
      <rect>
       <x>600</x>
       <y>140</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_9_2">
     <property name="geometry">
      <rect>
       <x>859</x>
       <y>260</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_8_2">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>200</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_8">
     <property name="geometry">
      <rect>
       <x>601</x>
       <y>200</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_7_2">
     <property name="geometry">
      <rect>
       <x>859</x>
       <y>140</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_4">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>260</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_4_2">
     <property name="geometry">
      <rect>
       <x>359</x>
       <y>260</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_58">
     <property name="geometry">
      <rect>
       <x>8</x>
       <y>80</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>剪刀盘</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_45">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>20</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>120</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string> 输出值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_8_1">
     <property name="geometry">
      <rect>
       <x>731</x>
       <y>200</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_9_1">
     <property name="geometry">
      <rect>
       <x>730</x>
       <y>260</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_7_1">
     <property name="geometry">
      <rect>
       <x>730</x>
       <y>140</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_4_1">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>260</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_227">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>20</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>120</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>编码器值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_49">
     <property name="geometry">
      <rect>
       <x>9</x>
       <y>260</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>针筒密度</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_9">
     <property name="geometry">
      <rect>
       <x>600</x>
       <y>260</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_52">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>80</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>织袜风门</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_214">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>140</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>生克电机</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_5">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_55">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>320</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>机头升降</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_46">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>260</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>橡筋转速1</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_226">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>20</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>120</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>当前值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_1">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>80</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_6_2">
     <property name="geometry">
      <rect>
       <x>859</x>
       <y>80</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_6_1">
     <property name="geometry">
      <rect>
       <x>730</x>
       <y>80</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_254">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>140</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>右棱角</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_255">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>200</y>
       <width>90</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>左棱角</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_2_1">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>140</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_2_2">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>140</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_3_2">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>200</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_2">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>140</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_StepTest_3">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>200</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
       background-color: white;
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_3_1">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>200</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_266">
     <property name="geometry">
      <rect>
       <x>730</x>
       <y>20</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>当前值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_267">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>20</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>编码器值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_47">
     <property name="geometry">
      <rect>
       <x>600</x>
       <y>20</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string> 输出值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_4_1" native="true">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>260</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_7_1" native="true">
     <property name="geometry">
      <rect>
       <x>980</x>
       <y>140</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_6_1" native="true">
     <property name="geometry">
      <rect>
       <x>980</x>
       <y>80</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_8_1" native="true">
     <property name="geometry">
      <rect>
       <x>980</x>
       <y>200</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_2_1" native="true">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>140</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_3_1" native="true">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>200</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_1_1">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>80</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_1_2">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>80</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_5_1">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_StepTest_5_2">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>320</y>
       <width>120</width>
       <height>40</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
       background-color: rgb(209, 209, 209);
       color: #333333;
       border: 1px solid #dddddd;
       border-radius: 5px;
       padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_StepTest_5_1" native="true">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>320</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="FengtouTest_page">
    <widget class="ColorBlockWidget" name="cb_FTTest_2_2" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>85</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_1_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>35</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_245">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>145</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>缝头电机</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_8_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>280</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_7_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>245</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_5_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>175</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_268">
     <property name="geometry">
      <rect>
       <x>236</x>
       <y>0</y>
       <width>130</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>当前值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_2_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>70</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_251">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>180</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>缝齿电机</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_4">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>140</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_6_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>210</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_269">
     <property name="geometry">
      <rect>
       <x>367</x>
       <y>0</y>
       <width>130</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>编码器值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_4_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>140</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_3_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>105</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_6">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>210</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_1_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>35</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_3_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>105</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_3_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>105</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_3">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>105</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_247">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>110</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>摆臂上下</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_8">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>280</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_1_2" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>50</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_2_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>70</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_252">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>40</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>抓手上下</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_231">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>75</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>摆臂左右</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_2_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>70</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_5_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>175</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_2">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>70</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_5_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>175</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_253">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>215</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>翻盖电机</string>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_8_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>280</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_262">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>250</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>推杆电机</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_1">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>35</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_4_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>140</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_7_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>245</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_6_2" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>225</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_1_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>35</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_4_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>140</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_7">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>245</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_263">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>285</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>袜筒上下</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_5">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>175</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_8_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>280</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_7_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>245</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_48">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>0</y>
       <width>130</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string> 输出值</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_6_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>210</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_6_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>210</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QFrame" name="frm_FtValveButtonList">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>20</y>
       <width>481</width>
       <height>321</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_MoveBackAndForth_btn">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>440</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>往复运动</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_MotorCCW_btn">
     <property name="geometry">
      <rect>
       <x>140</x>
       <y>440</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机反转</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_MotorGoZero_btn">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>440</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机归零</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_MotorStart_btn">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>490</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机启动</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_MotorCW_btn">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>440</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机正转</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_SetZero_btn">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>490</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机零位</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_MotorStop_btn">
     <property name="geometry">
      <rect>
       <x>140</x>
       <y>490</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机停止</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_1">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>350</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>压盘上下</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_2">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>350</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>盘勾收放</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_3">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>350</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>剪刀架升降</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_5">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>400</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>抓臂开合</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_6">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>400</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>反袜管进出</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_7">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>400</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>反袜筒上下</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_4">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>350</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>顶盘上下</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_8">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>400</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>盘齿转臂进出</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_SelfCheck_btn">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>490</y>
       <width>110</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机自检</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_10">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>450</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>缝头机进退</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_9">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>450</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>盘齿锁头进退</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_11">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>450</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>压环上下</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_12">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>450</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>剪刀进退</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_14">
     <property name="geometry">
      <rect>
       <x>660</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>直筒锁进出</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_16">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>模拟动作</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_13">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>吸风筒升降</string>
     </property>
    </widget>
    <widget class="QPushButton" name="FTTest_Action_15">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>500</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>锁头进出</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_9_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>315</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_9_2">
     <property name="geometry">
      <rect>
       <x>379</x>
       <y>315</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_217">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>320</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>95</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>缝头风门</string>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_9_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>315</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_9">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>315</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_264">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>355</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>袜筒左右</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_10">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>350</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_10_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>350</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_10_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>350</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_FTTest_11">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>385</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>127</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit:focus{
	border: 1px solid red;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_265">
     <property name="geometry">
      <rect>
       <x>26</x>
       <y>390</y>
       <width>90</width>
       <height>20</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>90</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>袜筒升降</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_11_2">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>385</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_FTTest_11_1">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>385</y>
       <width>120</width>
       <height>30</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>130</width>
       <height>30</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(200, 200, 200);</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_10_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>350</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
    <widget class="ColorBlockWidget" name="cb_FTTest_11_1" native="true">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>385</y>
       <width>16</width>
       <height>16</height>
      </rect>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_function">
    <widget class="QFrame" name="frame_function">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>20</y>
       <width>1001</width>
       <height>521</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame {
    background-color: #f5f5f5;
    border-radius: 10px;
    border: 1px solid #d0d0d0;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_servStatus">
    <widget class="QGroupBox" name="gBox_servoParaDown">
     <property name="geometry">
      <rect>
       <x>520</x>
       <y>10</y>
       <width>491</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 8px;
    margin-top: 20px;
    padding: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
}</string>
     </property>
     <property name="title">
      <string>参数下发</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_servoParaSendAll">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>500</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    border: 1px solid #27ae60;
    border-radius: 5px;
    padding: 5px;
    color: white;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #27ae60;
    border: 1px solid #229954;
}
QPushButton:pressed {
    background-color: #229954;
    border: 1px solid #1e8449;
}</string>
     </property>
     <property name="text">
      <string>一键下发</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gBox_servoParaUp">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>10</y>
       <width>491</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 8px;
    margin-top: 20px;
    padding: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
}</string>
     </property>
     <property name="title">
      <string>状态上报</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_servoParaClose">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>500</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #e74c3c;
    border: 1px solid #c0392b;
    border-radius: 5px;
    padding: 5px;
    color: white;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #c0392b;
    border: 1px solid #a93226;
}
QPushButton:pressed {
    background-color: #a93226;
    border: 1px solid #922b21;
}</string>
     </property>
     <property name="text">
      <string>关闭</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_servoParaSave">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>500</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>保存参数</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_keyboard">
    <widget class="QGroupBox" name="gbox_keyboard">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>40</y>
       <width>451</width>
       <height>451</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 8px;
    margin-top: 20px;
    padding: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
}</string>
     </property>
     <property name="title">
      <string>按键</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_led">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>40</y>
       <width>451</width>
       <height>451</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 8px;
    margin-top: 20px;
    padding: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
}</string>
     </property>
     <property name="title">
      <string>LED</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QPushButton" name="TestFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>3</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="Test_Title_label">
   <property name="geometry">
    <rect>
     <x>419</x>
     <y>5</y>
     <width>191</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>机器手动</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
  <customwidget>
   <class>ColorBlockWidget</class>
   <extends>QWidget</extends>
   <header>FileWidget/ColorBlockWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
