#ifndef READCFG_H
#define READCFG_H

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMap>
#include <QMessageBox>

class ReadCfg
{
public:
    ReadCfg();
    ~ReadCfg();
#pragma pack( 1 )
    struct ValveStruct
    {
        int     key;
        int     mappedId;
        QString position;
        QString value;
    };

    // 修改FunctionStruct结构体，添加Action结构
    struct ActionStruct
    {
        int     step;
        quint8  type;
        quint8  id;
        quint16 delay;
        quint16 position;
        quint8  value;
    };

    struct FunctionStruct
    {
        int                     key;
        QString                 value;
        QVector< ActionStruct > actions;
    };

    struct SockMotorStruct
    {
        int     id;
        QString name;
    };
#pragma pack()
    void parseCfgConfig( QString fileAddr );

    QMap< int, ValveStruct >* getSpecialFunList()
    {
        return &_specialFunList;
    }
    QMap< int, ValveStruct >* getCamList()
    {
        return &_camList;
    }
    QMap< int, ValveStruct >* getYarnFingerList()
    {
        return &_yarnFingerList;
    }
    QMap< int, QString >* getSockInputList()
    {
        return &_sockInputList;
    }
    QMap< int, QString >* getSeamHeadInputList()
    {
        return &_seamHeadInputList;
    }
    QMap< int, QString >* getMotorZeroInputList()
    {
        return &_motorZeroInputList;
    }
    QMap< int, ValveStruct >* getYarn2List()
    {
        return &_yarn2List;
    }
    QMap< int, ValveStruct >* getExtendList()
    {
        return &_extendList;
    }
    QMap< int, FunctionStruct >* getFunctionList()
    {
        return &_functionList;
    }

    QMap< int, SockMotorStruct >* getSockMotorList()
    {
        return &_sockMotorList;
    }

private:
    QMap< int, ValveStruct >    _specialFunList;
    QMap< int, ValveStruct >    _camList;
    QMap< int, ValveStruct >    _yarnFingerList;
    QMap< int, QString >        _sockInputList;
    QMap< int, QString >        _seamHeadInputList;
    QMap< int, QString >        _motorZeroInputList;
    QMap< int, ValveStruct >    _yarn2List;
    QMap< int, ValveStruct >    _extendList;
    QMap< int, FunctionStruct > _functionList;

    QMap< int, SockMotorStruct > _sockMotorList;
};

#endif  // READCFG_H
