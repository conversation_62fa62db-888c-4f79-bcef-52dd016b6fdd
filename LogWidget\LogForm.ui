<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LogForm</class>
 <widget class="QWidget" name="LogForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QPushButton" name="LogFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>8</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="Log_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>419</x>
     <y>10</y>
     <width>191</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
   </property>
   <property name="text">
    <string>日志与帮助</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>60</y>
     <width>1021</width>
     <height>541</height>
    </rect>
   </property>
   <property name="currentIndex">
    <number>2</number>
   </property>
   <widget class="QWidget" name="page">
    <widget class="QLabel" name="label_10">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>270</y>
       <width>150</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>帮助</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_14">
     <property name="geometry">
      <rect>
       <x>170</x>
       <y>269</y>
       <width>150</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>报警日志</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="Log_warn_btn">
     <property name="geometry">
      <rect>
       <x>200</x>
       <y>150</y>
       <width>100</width>
       <height>100</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>100</width>
       <height>100</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/MPIS-AlarmTps.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QPushButton" name="Log_help_btn">
     <property name="geometry">
      <rect>
       <x>680</x>
       <y>150</y>
       <width>100</width>
       <height>100</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>100</width>
       <height>100</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/help.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_15">
     <property name="geometry">
      <rect>
       <x>420</x>
       <y>269</y>
       <width>150</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>网络设置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="Log_network_btn">
     <property name="geometry">
      <rect>
       <x>450</x>
       <y>150</y>
       <width>100</width>
       <height>100</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>100</width>
       <height>100</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/network_on.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_2">
    <widget class="QTableWidget" name="tableView_warnLog">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>40</y>
       <width>1001</width>
       <height>491</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    border: 1px solid #dddddd;
    border-radius: 5px;
    gridline-color: #e0e0e0;
    selection-background-color: #3498db;
    selection-color: white;
}

QHeaderView::section {
    background-color: #f0f0f0;
    border: 1px solid #dddddd;
    padding: 4px;
    font-weight: bold;
    color: #333333;
}</string>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_listSame">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>10</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>选择相同</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_3">
    <widget class="QPushButton" name="pbtn_net_setParam">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>480</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>设置参数</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_net_test">
     <property name="geometry">
      <rect>
       <x>141</x>
       <y>480</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>通信测试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_net_refresh">
     <property name="geometry">
      <rect>
       <x>260</x>
       <y>480</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>模块刷新</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_net_importParam">
     <property name="geometry">
      <rect>
       <x>620</x>
       <y>480</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>导入参数</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_net_upgrade">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>480</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>模块升级</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_net_exportParam">
     <property name="geometry">
      <rect>
       <x>740</x>
       <y>480</y>
       <width>120</width>
       <height>50</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>导出参数</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="groupBox">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>10</y>
       <width>961</width>
       <height>461</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QGroupBox {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 8px;
    margin-top: 20px;
    padding: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
}</string>
     </property>
     <property name="title">
      <string>网络参数</string>
     </property>
     <widget class="QLabel" name="label">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>40</y>
        <width>121</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
      </property>
      <property name="text">
       <string>网络使能</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_enable">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>40</y>
        <width>141</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">MyLineEdit {
    background-color: white;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 2px;
    color: #333333;
}
MyLineEdit:focus {
    border: 1px solid #3498db;
    background-color: #e8f4fc;
}</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_2">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>90</y>
        <width>121</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>机器编号</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>140</y>
        <width>121</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>机器IP地址</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_4">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>190</y>
        <width>121</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>子网掩码</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_5">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>240</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>默认网关</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_6">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>290</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>服务器IP地址</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_7">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>340</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>服务器端口</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_8">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>390</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>MAC地址</string>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_machineId">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>90</y>
        <width>141</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_machineIP1">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>140</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_machineIP2">
      <property name="geometry">
       <rect>
        <x>240</x>
        <y>140</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_machineIP3">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>140</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_machineIP4">
      <property name="geometry">
       <rect>
        <x>400</x>
        <y>140</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_9">
      <property name="geometry">
       <rect>
        <x>222</x>
        <y>140</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_11">
      <property name="geometry">
       <rect>
        <x>300</x>
        <y>140</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_12">
      <property name="geometry">
       <rect>
        <x>380</x>
        <y>140</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_13">
      <property name="geometry">
       <rect>
        <x>222</x>
        <y>190</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_mask3">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>190</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_mask1">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>190</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_16">
      <property name="geometry">
       <rect>
        <x>300</x>
        <y>190</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_17">
      <property name="geometry">
       <rect>
        <x>380</x>
        <y>190</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_mask2">
      <property name="geometry">
       <rect>
        <x>240</x>
        <y>190</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_mask4">
      <property name="geometry">
       <rect>
        <x>400</x>
        <y>190</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_18">
      <property name="geometry">
       <rect>
        <x>222</x>
        <y>240</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_gate3">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>240</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_gate1">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>240</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_19">
      <property name="geometry">
       <rect>
        <x>300</x>
        <y>240</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_20">
      <property name="geometry">
       <rect>
        <x>380</x>
        <y>240</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_gate2">
      <property name="geometry">
       <rect>
        <x>240</x>
        <y>240</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_gate4">
      <property name="geometry">
       <rect>
        <x>400</x>
        <y>240</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_21">
      <property name="geometry">
       <rect>
        <x>222</x>
        <y>290</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_servIP3">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>290</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_servIP1">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>290</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_22">
      <property name="geometry">
       <rect>
        <x>300</x>
        <y>290</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_23">
      <property name="geometry">
       <rect>
        <x>380</x>
        <y>290</y>
        <width>20</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="text">
       <string>.</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_servIP2">
      <property name="geometry">
       <rect>
        <x>240</x>
        <y>290</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_servIP4">
      <property name="geometry">
       <rect>
        <x>400</x>
        <y>290</y>
        <width>61</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_servPort">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>340</y>
        <width>141</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_mac">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>390</y>
        <width>301</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QLabel" name="label_24">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>40</y>
        <width>151</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>网络协议</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_25">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>90</y>
        <width>151</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>模块版本</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_26">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>140</y>
        <width>151</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>测试发送数据包</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_27">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>190</y>
        <width>151</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>测试接收数据包</string>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_protocol">
      <property name="geometry">
       <rect>
        <x>660</x>
        <y>40</y>
        <width>271</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_version">
      <property name="geometry">
       <rect>
        <x>660</x>
        <y>90</y>
        <width>271</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_sendPkg">
      <property name="geometry">
       <rect>
        <x>660</x>
        <y>140</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_sendTime">
      <property name="geometry">
       <rect>
        <x>800</x>
        <y>140</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_recvPkg">
      <property name="geometry">
       <rect>
        <x>660</x>
        <y>190</y>
        <width>131</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
     </widget>
     <widget class="QLabel" name="label_28">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>340</y>
        <width>151</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>工厂名称</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_29">
      <property name="geometry">
       <rect>
        <x>500</x>
        <y>390</y>
        <width>151</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>机器编号(新)</string>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_factoryName">
      <property name="geometry">
       <rect>
        <x>660</x>
        <y>340</y>
        <width>211</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_net_machineIdN">
      <property name="geometry">
       <rect>
        <x>660</x>
        <y>390</y>
        <width>211</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QPushButton" name="pbtn_editFactoryName">
      <property name="geometry">
       <rect>
        <x>880</x>
        <y>340</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string>编辑</string>
      </property>
     </widget>
     <widget class="QPushButton" name="pbtn_editMachineId">
      <property name="geometry">
       <rect>
        <x>880</x>
        <y>390</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string>编辑</string>
      </property>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_4"/>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
