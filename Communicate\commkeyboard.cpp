// 首先需要在文件顶部包含keyboardBeep.h头文件
#include "commkeyboard.h"
#include "keyboardBeep.h"

CommKeyboard::CommKeyboard(QObject *parent) : QObject(parent), m_serialPort(nullptr), m_testTimer(nullptr), m_testRetryCount(0)
{
    m_serialPort = new QSerialPort(this);
    initSerialPort();
    
    // 初始化蜂鸣器
    m_beep = new keyboardBeep(this);
    
    // 初始化测试定时器
    m_testTimer = new QTimer(this);
    m_testTimer->setSingleShot(true);
    connect(m_testTimer, &QTimer::timeout, this, &CommKeyboard::onTestTimeout);
}

CommKeyboard::~CommKeyboard()
{
    closeSerialPort();
    if (m_serialPort) {
        delete m_serialPort;
        m_serialPort = nullptr;
    }
    
    // 释放蜂鸣器资源
    if (m_beep) {
        delete m_beep;
        m_beep = nullptr;
    }
    
    // 释放定时器资源
    if (m_testTimer) {
        m_testTimer->stop();
        delete m_testTimer;
        m_testTimer = nullptr;
    }
}

bool CommKeyboard::initSerialPort()
{
    // 如果串口已经打开，先关闭
    if (m_serialPort->isOpen()) {
        m_serialPort->clear();
        m_serialPort->close();
    }
    
    // 设置串口名称，使用宏定义
    m_serialPort->setPortName(KEYBOARD_SERIAL_PORT);
    
    // 尝试以读写方式打开串口
    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        qDebug() << KEYBOARD_SERIAL_PORT << " 串口打开失败!";
        return false;
    }
    
    // 设置串口参数
    m_serialPort->setBaudRate(QSerialPort::Baud115200);  // 波特率115200
    m_serialPort->setDataBits(QSerialPort::Data8);       // 数据位8位
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl); // 无流控制
    m_serialPort->setParity(QSerialPort::NoParity);      // 无校验位
    m_serialPort->setStopBits(QSerialPort::OneStop);     // 一位停止位
    
    // 连接信号槽，当有数据可读时触发onDataReceived槽函数
    connect(m_serialPort, &QSerialPort::readyRead, this, &CommKeyboard::onDataReceived);
    
    qDebug() << KEYBOARD_SERIAL_PORT << " 串口初始化成功";
    return true;
}

void CommKeyboard::closeSerialPort()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->clear();
        m_serialPort->close();
        qDebug() << KEYBOARD_SERIAL_PORT << " 串口已关闭";
    }
}

bool CommKeyboard::sendData(const QByteArray &data)
{
    if (!m_serialPort || !m_serialPort->isOpen()) {
        qDebug() << "串口未打开，无法发送数据";
        return false;
    }
    
    // 发送数据
    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1) {
        qDebug() << "发送数据失败:" << m_serialPort->errorString();
        return false;
    } else if (bytesWritten != data.size()) {
        qDebug() << "发送数据不完整";
        return false;
    }
    
    // 等待数据发送完成
    if (!m_serialPort->waitForBytesWritten(WRITE_TIMEOUT)) {
        qDebug() << "发送数据超时:" << m_serialPort->errorString();
        return false;
    }
    
    return true;
}

bool CommKeyboard::updateLEDStatus(int ledNumber, bool isOn)
{
    // 检查LED编号是否有效（1-8）
    if (ledNumber < 1 || ledNumber > 8) {
        qDebug() << "无效的LED编号:" << ledNumber;
        return false;
    }
    
    // 构建LED控制帧
    // 帧格式：'L' + LED编号(1-8) + LED状态(0/1) + '\n'
    QByteArray frame;
    frame.append('L');
    frame.append('0' + ledNumber); // 将数字转换为ASCII字符
    frame.append(isOn ? '1' : '0');
    frame.append('\n');
    
    // 发送控制帧
    bool result = sendData(frame);
    if (result) {
        qDebug() << "发送LED控制帧成功: LED" << ledNumber << (isOn ? "开启" : "关闭");
    } else {
        qDebug() << "发送LED控制帧失败: LED" << ledNumber;
    }
    
    return result;
}

void CommKeyboard::onDataReceived()
{
    // 读取所有可用数据
    QByteArray data = m_serialPort->readAll();
    if (data.isEmpty()) {
        return;
    }
    
    // 将新数据添加到缓冲区
    m_buffer.append(data);
    
    // 解析缓冲区中的数据
    parseReceivedData();
}

bool CommKeyboard::sendTestFrame()
{
    // 构建测试帧
    QByteArray testFrame = "TEST\n";
    
    // 重置重试计数
    m_testRetryCount = 0;
    
    // 发送测试帧
    bool result = sendData(testFrame);
    if (result) {
        qDebug() << "发送键盘测试帧成功";
        // 启动超时定时器
        m_testTimer->start(TEST_TIMEOUT);
    } else {
        qDebug() << "发送键盘测试帧失败";
        // 直接报告测试失败
        emit keyboardTestResult(false);
    }
    
    return result;
}

void CommKeyboard::onTestTimeout()
{
    m_testRetryCount++;
    
    if (m_testRetryCount < MAX_TEST_RETRIES) {
        // 未达到最大重试次数，重新发送测试帧
        qDebug() << "键盘测试超时，重试第" << m_testRetryCount << "次";
        QByteArray testFrame = "TEST\n";
        if (sendData(testFrame)) {
            // 重新启动定时器
            m_testTimer->start(TEST_TIMEOUT);
        } else {
            // 发送失败，直接报告测试失败
            emit keyboardTestResult(false);
        }
    } else {
        // 达到最大重试次数，报告测试失败
        qDebug() << "键盘测试失败，已重试" << m_testRetryCount << "次";
        emit keyboardTestResult(false);
    }
}

void CommKeyboard::parseReceivedData()
{
    // 循环处理缓冲区中的所有完整数据包
    while (m_buffer.size() >= 4) {
        // 检查是否为单键数据格式：第1字节为'K'，第4字节为'\n'
        if (m_buffer.at(0) == 'K' && m_buffer.at(3) == '\n') {
            // 解析键值
            int tens = m_buffer.at(1) - '0';
            int units = m_buffer.at(2) - '0';
            
            // 计算键值
            int keyValue = tens * 10 + units;
            
            // 打印键值
            qDebug() << "接收到按键键值:" << keyValue;
            
            // 蜂鸣器短鸣一声
            if (m_beep) {
                m_beep->beepOnce(BEEP_DURATION);
            }
            
            // 发出按键信号
            emit keyPressed(keyValue);
            
            // 从缓冲区中移除已处理的数据包
            m_buffer.remove(0, 4);
        } 
        // 检查是否为组合键数据格式：第1字节为'C'，第4字节为'+'，第7字节为'\n'
        else if (m_buffer.size() >= 7 && m_buffer.at(0) == 'C' && m_buffer.at(3) == '+' && m_buffer.at(6) == '\n') {
            // 解析第一个键值
            int tens1 = m_buffer.at(1) - '0';
            int units1 = m_buffer.at(2) - '0';
            int keyValue1 = tens1 * 10 + units1;
            
            // 解析第二个键值
            int tens2 = m_buffer.at(4) - '0';
            int units2 = m_buffer.at(5) - '0';
            int keyValue2 = tens2 * 10 + units2;
            
            // 打印组合键值
            qDebug() << "接收到组合键键值:" << keyValue1 << "+" << keyValue2;
            
            // 蜂鸣器短鸣两声
            if (m_beep) {
                m_beep->beepPattern(BEEP_DURATION, BEEP_DURATION, 2);
            }
            
            // 发出组合键信号
            emit combinationKeyPressed(keyValue1, keyValue2);
            
            // 从缓冲区中移除已处理的数据包
            m_buffer.remove(0, 7);
        }
        // 检查是否为测试响应帧：'TESTR\n'
        else if (m_buffer.size() >= 6 && m_buffer.startsWith("TESTR\n")) {
            qDebug() << "接收到键盘测试响应，测试成功";
            
            // 停止定时器
            m_testTimer->stop();
            
            // 发出键盘测试成功信号
            emit keyboardTestResult(true);
            
            // 从缓冲区中移除已处理的数据包
            m_buffer.remove(0, 6);
        } else {
            // 如果数据格式不符合要求，移除第一个字节，继续检查
            m_buffer.remove(0, 1);
        }
    }
}
