﻿#ifndef READLINKCONFIG_H
#define READLINKCONFIG_H

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMessageBox>

class ReadLinkConfig
{
public:
    ReadLinkConfig();
    ~ReadLinkConfig();
    void parseLinkConfig( QString fileAddr );

    QMap< int, QString >* getStepNameList()
    {
        return &_stepNameList;
    }
    QMap< int, QString >* getCYStateList()
    {
        return &_CYStateList;
    }
    QMap< int, QString >* getSingalMapList()
    {
        return &_signalMapList;
    }
    QMap< int, QString >* getMotorMapList()
    {
        return &_motorMapList;
    }
    QMap< int, QString >* getFTCommandList()
    {
        return &_FTCommandList;
    }
    QMap< int, QString >* getAirValveList()
    {
        return &_airvalveList;
    }
    QMap< int, QString >* getFengtouList()
    {
        return &_fengtouList;
    }
    QMap< int, QString >* getFengtouCommandList()
    {
        return &_fengtouCommandList;
    }
    QMap< int, QString >* getFengtouResetList()
    {
        return &_fengtouResetList;
    }
    QMap< int, QString >* getMotorPatternList()
    {
        return &_motorPatternList;
    }
    QMap< int, QString >* getFengtouVSTypeList()
    {
        return &_fengtouVSTypeList;
    }
    QMap< int, QString >* getFengtouVStateList()
    {
        return &_fengtouVStateList;
    }
    QMap< int, QString >* getFengtouSStateList()
    {
        return &_fengtouSStateList;
    }
    QMap< int, QString >* getNeedleNameList()
    {
        return &_needleNameList;
    }
    QMap< int, QString >* getNeedleStateList()
    {
        return &_needleStateList;
    }
    QMap< int, QString >* getTianshaColorList()
    {
        return &_tianshaColorList;
    }
    QMap< int, QString >* getZhusuoColorList()
    {
        return &_zhusuoColorList;
    }
    QMap< int, QString >* getSuduColorList()
    {
        return &_suduColorList;
    }
    QMap< int, QString >* getXiangjinColorList()
    {
        return &_xiangjinColorList;
    }
    QMap< int, QString >* getLamaoColorList()
    {
        return &_lamaoColorList;
    }
    QMap< int, QString >* getWagenLamaoColorList()
    {
        return &_wagenlamaoColorList;
    }
    QMap< int, QString >* getDasongSanjiaoColorList()
    {
        return &_dasongsanjiaoColorList;
    }

private:
    QMap< int, QString > _stepNameList;
    QMap< int, QString > _CYStateList;
    QMap< int, QString > _signalMapList;
    QMap< int, QString > _motorMapList;
    QMap< int, QString > _FTCommandList;
    QMap< int, QString > _airvalveList;
    QMap< int, QString > _fengtouList;
    QMap< int, QString > _fengtouCommandList;
    QMap< int, QString > _fengtouResetList;
    QMap< int, QString > _motorPatternList;
    QMap< int, QString > _fengtouVSTypeList;
    QMap< int, QString > _fengtouVStateList;
    QMap< int, QString > _fengtouSStateList;
    QMap< int, QString > _needleNameList;
    QMap< int, QString > _needleStateList;
    QMap< int, QString > _tianshaColorList;
    QMap< int, QString > _zhusuoColorList;
    QMap< int, QString > _suduColorList;
    QMap< int, QString > _xiangjinColorList;
    QMap< int, QString > _lamaoColorList;
    QMap< int, QString > _wagenlamaoColorList;
    QMap< int, QString > _dasongsanjiaoColorList;
};

#endif  // READLINKCONFIG_H
