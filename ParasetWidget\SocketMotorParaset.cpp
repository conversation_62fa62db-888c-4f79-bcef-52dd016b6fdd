#include "Config/readmachinefileconfig.h"
#include "parasetform.h"
#include "ui_parasetform.h"
#include <QDebug>
#include <QLayout>
#include <QWidget>

void ParaSetForm::initSocketMotorParasetTab()
{
    // 只有1个面板
    QVBoxLayout* mainLayout    = new QVBoxLayout( this->ui->SocketMotorPara_tab );
    int          totalElements = this->readMachineFileConfig->getSocketMotorCfgList()->count();

    // 先插入顶部一行Label
    QHBoxLayout* hboxLayer = new QHBoxLayout();
    mainLayout->addLayout( hboxLayer );

    mainLayout->setMargin( 0 );
    mainLayout->setSpacing( 10 );

    hboxLayer->setMargin( 2 );
    hboxLayer->setSpacing( 3 );
    QLabel* label1  = new QLabel( " " );
    QLabel* label2  = new QLabel( "工作电流" );
    QLabel* label3  = new QLabel( "锁定电流" );
    QLabel* label4  = new QLabel( "工作频率" );
    QLabel* label5  = new QLabel( "复位频率" );
    QLabel* label6  = new QLabel( "工作量程" );
    QLabel* label7  = new QLabel( "方向" );
    QLabel* label8  = new QLabel( "零位" );
    QLabel* label9  = new QLabel( "检测" );
    QLabel* label10 = new QLabel( "使能" );
    label1->setStyleSheet( "font-size: 8pt;" );
    label2->setStyleSheet( "font-size: 8pt;" );
    label3->setStyleSheet( "font-size: 8pt;" );
    label4->setStyleSheet( "font-size: 8pt;" );
    label5->setStyleSheet( "font-size: 8pt;" );
    label6->setStyleSheet( "font-size: 8pt;" );
    label7->setStyleSheet( "font-size: 8pt;" );
    label8->setStyleSheet( "font-size: 8pt;" );
    label9->setStyleSheet( "font-size: 8pt;" );
    label10->setStyleSheet( "font-size: 8pt;" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    label5->setAlignment( Qt::AlignHCenter );
    label6->setAlignment( Qt::AlignHCenter );
    label7->setAlignment( Qt::AlignHCenter );
    label8->setAlignment( Qt::AlignHCenter );
    label9->setAlignment( Qt::AlignHCenter );
    label10->setAlignment( Qt::AlignHCenter );
    hboxLayer->addWidget( label1 );
    hboxLayer->addWidget( label2 );
    hboxLayer->addWidget( label3 );
    hboxLayer->addWidget( label4 );
    hboxLayer->addWidget( label5 );
    hboxLayer->addWidget( label6 );
    hboxLayer->addWidget( label7 );
    hboxLayer->addWidget( label8 );
    hboxLayer->addWidget( label9 );
    hboxLayer->addWidget( label10 );
    hboxLayer->setStretch( 0, 2 );
    hboxLayer->setStretch( 1, 2 );
    hboxLayer->setStretch( 2, 2 );
    hboxLayer->setStretch( 3, 2 );
    hboxLayer->setStretch( 4, 2 );
    hboxLayer->setStretch( 5, 2 );
    hboxLayer->setStretch( 6, 1 );
    hboxLayer->setStretch( 7, 1 );
    hboxLayer->setStretch( 8, 1 );
    hboxLayer->setStretch( 9, 1 );

    for ( int i = 0; i < 9; i++ )
    {
        QHBoxLayout* hboxLayer = new QHBoxLayout();
        mainLayout->addLayout( hboxLayer );
        hboxLayer->setMargin( 2 );
        hboxLayer->setSpacing( 3 );

        if ( i < totalElements )
        {
            ReadMachineFileConfig::StepperMotorConfigItem item  = this->readMachineFileConfig->getSocketMotorCfgList()->value( i + 1 );
            QLabel*                                       label = new QLabel( QString( "%1.%2" ).arg( i + 1 ).arg( item.title ) );
            label->setStyleSheet( "font-size: 8pt;min-width:100px;" );
            hboxLayer->addWidget( label );
            hboxLayer->setStretch( 0, 2 );
            for ( int j = 0; j < item.data.count(); j++ )
            {
                ReadMachineFileConfig::StepperMotorConfigDataItem dataItem = item.data.at( j );
                MyLineEdit*                                       lineEdit = new MyLineEdit();
                lineEdit->setAlignment( Qt::AlignHCenter );
                lineEdit->setProperty( "row_index", i + 1 );
                lineEdit->setProperty( "col_index", j );
                lineEdit->setStyleSheet( "font-size: 8pt;" );
                if ( dataItem.editable )
                {
                    connect( lineEdit, &MyLineEdit::mouseRelease, this, &ParaSetForm::onSocketMotorParasetEditClicked );
                }
                if ( dataItem.unit == "enum" )
                {
                    lineEdit->setText( dataItem.candidate[ dataItem.value ] );
                }
                else if ( dataItem.unit == "/" )
                {
                    lineEdit->setText( "/" );
                }
                else
                {
                    lineEdit->setText( QString( "%1%2" ).arg( dataItem.value ).arg( dataItem.unit ) );
                }

                hboxLayer->addWidget( lineEdit );
                if ( j > 4 )
                {
                    hboxLayer->setStretch( j + 1, 1 );
                }
                else
                {
                    hboxLayer->setStretch( j + 1, 2 );
                }
            }
        }
        else
        {
            QLabel* label = new QLabel( QString( "%1.暂无选项" ).arg( i + 1 ) );
            label->setStyleSheet( "font-size: 8pt;min-width:100px;" );
            hboxLayer->addWidget( label );
            hboxLayer->setStretch( 0, 2 );
            for ( int j = 0; j < 9; j++ )
            {
                MyLineEdit* lineEdit = new MyLineEdit();
                lineEdit->setAlignment( Qt::AlignHCenter );
                lineEdit->setStyleSheet( "font-size: 8pt;" );
                hboxLayer->addWidget( lineEdit );
                if ( j > 4 )
                {
                    hboxLayer->setStretch( j + 1, 1 );
                }
                else
                {
                    hboxLayer->setStretch( j + 1, 2 );
                }
            }
        }
    }
}

void ParaSetForm::onSocketMotorParasetEditClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    QVariant variant                = senderLineEdit->property( "row_index" );
    if ( variant.isValid() )
    {
        int                                           index = variant.toInt();
        ReadMachineFileConfig::StepperMotorConfigItem item  = this->readMachineFileConfig->getSocketMotorCfgList()->value( index );

        QVariant variantx = senderLineEdit->property( "col_index" );
        if ( variantx.isValid() )
        {
            int                                               col_index = variantx.toInt();
            ReadMachineFileConfig::StepperMotorConfigDataItem dataItem  = item.data.at( col_index );
            // 枚举类型跳出Combo选择框
            if ( dataItem.unit == "enum" )
            {
                // 生成Combo选择框所需的key-value对
                QMap< int, QString > list;
                for ( int i = 0; i < dataItem.candidate.length(); i++ )
                {
                    list.insert( i, dataItem.candidate[ i ] );
                }
                if ( this->comboFrm == nullptr )
                {
                    this->comboFrm = new ComboForm( nullptr, &list );
                    this->comboFrm->setAttribute( Qt::WA_DeleteOnClose );
                    connect( this->comboFrm, &QObject::destroyed, this, [this]() { this->comboFrm = nullptr; } );
                }
                connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                    //                qDebug() << id;
                    QVariant variant = this->currentSelectedMyLineEdit->property( "row_index" );
                    if ( variant.isValid() )
                    {
                        int                                            index = variant.toInt();
                        ReadMachineFileConfig::StepperMotorConfigItem& item  = ( *this->readMachineFileConfig->getSocketMotorCfgList() )[ index ];

                        QVariant variantx = this->currentSelectedMyLineEdit->property( "col_index" );
                        if ( variantx.isValid() )
                        {
                            int                                                col_index = variantx.toInt();
                            ReadMachineFileConfig::StepperMotorConfigDataItem& dataItem  = item.data[ col_index ];
                            dataItem.value                                               = id;
                            this->currentSelectedMyLineEdit->setText( dataItem.candidate[ id ] );
                        }
                    }
                    this->comboFrm->close();
                } );
                this->comboFrm->show();
            }
            else
            {
                if ( this->floatInputFrm == nullptr )
                {
                    this->floatInputFrm = new FloatInputForm();
                    this->floatInputFrm->setAttribute( Qt::WA_DeleteOnClose );
                    connect( this->floatInputFrm, &FloatInputForm::InputFinished, this, &ParaSetForm::onSocketMotorFloatInputFormFinished );
                    connect( this->floatInputFrm, &QObject::destroyed, this, [this]() { this->floatInputFrm = nullptr; } );
                }
                this->floatInputFrm->show();
            }
        }
    }
}

void ParaSetForm::onSocketMotorFloatInputFormFinished( float value )
{
    if ( this->currentSelectedMyLineEdit != nullptr )
    {
        QVariant variant = this->currentSelectedMyLineEdit->property( "row_index" );
        if ( variant.isValid() )
        {
            int                                            index    = variant.toInt();
            ReadMachineFileConfig::StepperMotorConfigItem& item     = ( *this->readMachineFileConfig->getSocketMotorCfgList() )[ index ];
            QVariant                                       variantx = this->currentSelectedMyLineEdit->property( "col_index" );
            if ( variantx.isValid() )
            {
                int                                                col_index = variantx.toInt();
                ReadMachineFileConfig::StepperMotorConfigDataItem& dataItem  = item.data[ col_index ];
                dataItem.value                                               = value;
                this->currentSelectedMyLineEdit->setText( QString( "%1%2" ).arg( value ).arg( dataItem.unit ) );
            }
        }
    }
}
