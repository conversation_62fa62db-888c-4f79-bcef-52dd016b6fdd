
#include "test_steptest.h"
#include "testform.h"
#include "ui_testform.h"
#include <QDebug>
#include <QList>
#include <QPushButton>

/* 创建伺服电机测试相关初始化 */
void TestForm::ServoTestInit()
{
    if ( isServTestPageInit == false )
    {
        /* 创建控制按键按键组 */
        this->CreateServoTestCtrlBtnGroup();

        /* 创建LineEdit点击槽函数 */
        connect( ui->ServoTest_ServoSpeedSet_LE, &MyLineEdit::mouseDoubleClick, this, &TestForm::onServoTestSpeedLineEditClicked );

        //将初始参数设置为20
        this->refreshServoSpeed( 30 );
        this->refreshServoStatus( 0 );
        this->refreshServoElastic( 0 );
        this->refreshServoDirection( 0 );

        isServTestPageInit = true;
    }
}

/* 参数初始化 */
void TestForm::refreshServoSpeed( quint16 speed )
{
    this->servoSpeedSetValue = speed;  //将初始参数设置为0
    ui->ServoTest_ServoSpeedSet_LE->setText( QString::number( this->servoSpeedSetValue, 10 ) );
}

void TestForm::refreshServoDirection( quint8 dir )
{
    this->servoDirectionValue = dir;
    ui->ServoTest_DirSet_label->setText( dir == 1 ? "正向" : "反向" );
}

void TestForm::refreshServoElastic( quint8 elastic )
{
    this->servoElasticValue = elastic;
    ui->ServoTest_Elastic_label->setText( elastic == 0 ? "不同步" : "同步" );
}

void TestForm::refreshServoStatus( quint8 status )
{
    this->servoStatusValue = status;
    ui->ServoTest_Status_label->setText( status == 0 ? "停止" : "运行" );
    if ( status == 1 )
    {
        this->ui->ServoTest_btn_3->setStyleSheet( QString( "QPushButton {"
                                                           "outline: none;"
                                                           "border-radius: 50px;"
                                                           "border-image: url(:/stop.png);"
                                                           "color: white;"
                                                           "font-weight: bold;"
                                                           "}" ) );
        this->ui->TestFormHome_btn->setEnabled( false );
    }
    else
    {
        this->ui->ServoTest_btn_3->setStyleSheet( QString( "QPushButton {"
                                                           "outline: none;"
                                                           "border-radius: 50px;"
                                                           "border-image: url(:/run1.png);"
                                                           "color: white;"
                                                           "font-weight: bold;"
                                                           "}" ) );
        this->ui->TestFormHome_btn->setEnabled( true );
    }
}

/* 创建伺服电机测试控制按键组 */
void TestForm::CreateServoTestCtrlBtnGroup()
{
    ServoTestCtrlBtnGroup = new QButtonGroup( this );

    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_1, 0 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_2, 1 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_3, 2 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_4, 3 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_5, 4 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_6, 5 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_7, 6 );
    ServoTestCtrlBtnGroup->addButton( ui->ServoTest_btn_8, 7 );

    connect( this->ServoTestCtrlBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onServoTestCtrlBtnGroupClicked( int ) ) );
}

/* 伺服电机测试界面下键盘按下槽函数 */
void TestForm::onServoTestSpeedLineEditFinished( QString text )
{
    ui->ServoTest_ServoSpeedSet_LE->setText( text );
    refreshServoSpeed( text.toUInt() );
    if ( this->servoStatusValue == 1 )
        sendServoTestCommond();
}

/* 伺服电机转速设置LineEdit点击槽函数 */
void TestForm::onServoTestSpeedLineEditClicked()
{
    if ( this->numFrm == nullptr )
    {
        this->numFrm = new NumberInputForm( this, "请输入数值", 30, 300 );
        this->numFrm->setAttribute( Qt::WA_ShowModal, true );
        this->numFrm->setAttribute( Qt::WA_DeleteOnClose );
        connect( this->numFrm, &NumberInputForm::InputFinished, this, &TestForm::onServoTestSpeedLineEditFinished );
        connect( this->numFrm, &QObject::destroyed, this, [this]() {
            disconnect( this->numFrm, &NumberInputForm::InputFinished, this, &TestForm::onServoTestSpeedLineEditFinished );
            this->numFrm = nullptr;
        } );
    }
    this->numFrm->show();
}

/* 伺服电机控制按键点击槽函数 */

void TestForm::onServoTestCtrlBtnGroupClicked( int id )
{
    if ( this->servoStatusValue == 1 && id != 1 && id != 2 && id != 6 && id != 7 )
    {
        QMessageBox::warning( nullptr, "错误提示", "运行状态不允许改变参数" );
        return;
    }

    switch ( id )
    {
        case 0:
            if ( this->servoDirectionValue == 1 )
                refreshServoDirection( 0 );
            else
                refreshServoDirection( 1 );
            break;
        case 1:
            refreshServoSpeed( 60 );
            if ( this->servoStatusValue == 1 )
                sendServoTestCommond();
            break;
        case 2:
            if ( this->servoStatusValue == 1 )
                refreshServoStatus( 0 );
            else
                refreshServoStatus( 1 );
            sendServoTestCommond();
            break;
        case 3:
            if ( this->servoElasticValue == 0 )
                refreshServoElastic( 1 );
            else
                refreshServoElastic( 0 );
            break;
        case 4:
            // 关闭Dialog时同时清空指针，避免指针引用刷新函数
            if ( servZeroDialog == nullptr )
            {
                servZeroDialog = new ServZeroDialog( this, comm, mainData );
                servZeroDialog->setAttribute( Qt::WA_ShowModal, true );
                servZeroDialog->setAttribute( Qt::WA_DeleteOnClose );
                connect( servZeroDialog, &QObject::destroyed, this, [this]() { servZeroDialog = nullptr; } );
            }
            servZeroDialog->show();
            break;
        case 5:
            ui->Test_stackedWidget->setCurrentIndex( 7 );
            initServoParaPage();
            break;
        case 6:
            if ( this->servoSpeedSetValue > 30 )
            {
                refreshServoSpeed( this->servoSpeedSetValue - 1 );
                if ( this->servoStatusValue == 1 )
                    sendServoTestCommond();
            }
            break;
        case 7:
            refreshServoSpeed( this->servoSpeedSetValue + 1 );
            if ( this->servoStatusValue == 1 )
                sendServoTestCommond();
            break;
    }
}

void TestForm::sendServoTestCommond()
{
    quint8 data[ 6 ] = { 0 };
    data[ 0 ]        = 0x02;
    data[ 1 ]        = this->servoStatusValue;
    memcpy( &data[ 2 ], &this->servoSpeedSetValue, 2 );
    data[ 4 ] = this->servoDirectionValue;
    data[ 5 ] = this->servoElasticValue;
    comm->pushDataTobuffer( 0x09, data, 6 );
}

void TestForm::refreshServoParams( quint16 codeValue, quint16 speedValue, quint16 circleValue, quint16 abNeedleValue, quint16 zeroValue, float abAngelValue )
{
    ui->ServoTest_Code_label->setText( QString::number( codeValue ) );
    ui->ServoTest_ActSpeed_label->setText( QString::number( speedValue ) );
    ui->ServoTest_Circle_label->setText( QString::number( circleValue ) );
    ui->ServoTest_AbNeedle_label->setText( QString::number( abNeedleValue ) );
    ui->ServoTest_Zero_label->setText( QString::number( zeroValue ) );
    ui->ServoTest_AbAngle_label->setText( QString::number( abAngelValue, 'f', 1 ) );
}
