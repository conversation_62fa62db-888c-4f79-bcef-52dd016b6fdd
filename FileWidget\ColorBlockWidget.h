#ifndef COLORBLOCKWIDGET_H
#define COLORBLOCKWIDGET_H

#include <QDebug>
#include <QWidget>
#include <qpainter.h>

class ColorBlockWidget : public QWidget
{
    Q_OBJECT
public:
    ColorBlockWidget( QWidget* parent = nullptr, QColor color = Qt::red, QString text = "" ) : QWidget( parent )
    {
        m_color = color;
        m_text  = text;
        //        setFixedSize( 25, 25 );
    }
    QSize sizeHint() const override
    {
        return { 25, 25 };
    }

    void setColor( QColor color )
    {
        m_color = color;
        this->update();
    }

private:
    QColor  m_color;
    QString m_text;

protected:
    void paintEvent( QPaintEvent* ) override
    {
        QPainter p{ this };

        // 绘制边框
        p.setPen( Qt::black );
        p.drawRect( 0, 0, width() - 1, height() - 1 );

        // 绘制填充色块
        p.fillRect( 1, 1, width() - 2, height() - 2, m_color );
        p.drawText( 0, 0, width(), height(), Qt::AlignCenter, m_text );

        //        qDebug() << "ColorWidget" << width() << height();
    }
};

#endif  // COLORBLOCKWIDGET_H
