#include "UsbForm.h"
#include "ui_UsbForm.h"

UsbForm::UsbForm( QWidget* parent ) : QWidget( parent ), ui( new Ui::UsbForm )
{
    ui->setupUi( this );

    /* 返回主菜单键连接  */
    connect( ui->UsbFormHome_btn, SIGNAL( clicked() ), this, SLOT( onUsbHomeBtnClicked() ) );

    menuBtnGroup = new QButtonGroup( this );
    menuBtnGroup->addButton( ui->Usb_usb_btn, 1 );
    menuBtnGroup->addButton( ui->Usb_enable_btn, 2 );
    connect( menuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onMenuBtnGroupClicked( int ) ) );

    // init
    ui->stackedWidget->setCurrentIndex( 0 );
    ui->Usb_Tiltle_label->setText( "文件管理" );
    //    initFileAdminPage();
}

UsbForm::~UsbForm()
{
    if ( noUsbTipLabel )
    {
        delete noUsbTipLabel;
        noUsbTipLabel = nullptr;
    }
    delete ui;
}

/* 测试界面返回 主菜单键按下槽函数 */
void UsbForm::onUsbHomeBtnClicked()
{
    if ( ui->stackedWidget->currentIndex() > 0 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Usb_Tiltle_label->setText( "文件管理" );
    }
    else
    {
        this->close();
        emit UsbFormToMainWinToShowSignal();
    }
}

void UsbForm::onMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            ui->Usb_Tiltle_label->setText( "文件管理" );
            ui->stackedWidget->setCurrentIndex( 1 );
            InitUsbAdminPage();
            break;
        case 2:
            ui->Usb_Tiltle_label->setText( "文件启用" );
            ui->stackedWidget->setCurrentIndex( 2 );
            break;
    }
}
