
#include "testform.h"
#include "ui_testform.h"

void TestForm::initKeyBoardTest()
{
    if ( isKeyboardTestPageInit )
        return;
    // 初始化键盘布局
    initGboxKeyBoard();

    // 初始化LED布局
    initGboxLED();

    isKeyboardTestPageInit = true;
}

void TestForm::initGboxKeyBoard()
{

    // 清除现有布局
    if ( ui->gbox_keyboard->layout() )
    {
        delete ui->gbox_keyboard->layout();
    }

    // 创建网格布局
    QGridLayout* gridLayout = new QGridLayout( ui->gbox_keyboard );
    gridLayout->setSpacing( 15 );  // 增加间距
    gridLayout->setContentsMargins( 20, 25, 20, 20 );  // 增加内边距

    // 定义键盘布局
    QStringList keyNames = { "1", "2", "3", "A", "4", "5", "6", "B", "7", "8", "9", "C", "*", "0", "#", "D" };

    // 定义颜色样式
    QString normalStyle  = "QLabel { "
                          "background-color: #f8f8f8; "
                          "color: #333333; "
                          "border: 2px solid #dddddd; "
                          "border-radius: 10px; "
                          "font-size: 24px; "
                          "font-weight: bold; "
                          "}";
    QString pressedStyle = "QLabel { "
                          "background-color: #3498db; "
                          "color: white; "
                          "border: 2px solid #2980b9; "
                          "border-radius: 10px; "
                          "font-size: 24px; "
                          "font-weight: bold; "
                          "}";

    // 创建键盘标签
    int index = 0;
    for ( int row = 0; row < 4; row++ )
    {
        for ( int col = 0; col < 4; col++ )
        {
            QString keyName  = keyNames[ index++ ];
            QLabel* keyLabel = new QLabel( keyName, ui->gbox_keyboard );
            keyLabel->setAlignment( Qt::AlignCenter );
            keyLabel->setStyleSheet( normalStyle );
            keyLabel->setMinimumSize( 90, 90 );  // 增加按键大小
            keyLabel->setSizePolicy( QSizePolicy::Expanding, QSizePolicy::Expanding );

            // 添加到布局
            gridLayout->addWidget( keyLabel, row, col );

            // 保存到映射中
            keyLabels[ keyName ] = keyLabel;
        }
    }

    // 设置行列比例
    for ( int i = 0; i < 4; i++ )
    {
        gridLayout->setRowStretch( i, 1 );
        gridLayout->setColumnStretch( i, 1 );
    }

    // 应用布局
    ui->gbox_keyboard->setLayout( gridLayout );

    // 重置所有按键状态
    resetAllKeyStatus();
}

void TestForm::initGboxLED()
{
    // 清除现有布局
    if ( ui->gbox_led->layout() )
    {
        delete ui->gbox_led->layout();
    }

    // 创建网格布局
    QGridLayout* gridLayout = new QGridLayout( ui->gbox_led );
    gridLayout->setSpacing( 15 );  // 增加间距
    gridLayout->setContentsMargins( 20, 25, 20, 20 );  // 增加内边距

    // 定义LED名称
    QStringList ledNames;
    for ( int i = 1; i <= 8; i++ )
    {
        ledNames << QString( "LED%1" ).arg( i );
    }

    // 定义颜色样式
    QString offStyle = "QPushButton { "
                       "background-color: #f8f8f8; "
                       "color: #333333; "
                       "border: 2px solid #dddddd; "
                       "border-radius: 10px; "
                       "font-size: 18px; "
                       "font-weight: bold; "
                       "min-height: 60px; "
                       "}"
                       "QPushButton:hover { "
                       "background-color: #e8f4fc; "
                       "border: 2px solid #3498db; "
                       "color: #2980b9; "
                       "}";

    QString onStyle = "QPushButton { "
                      "background-color: #2ecc71; "
                      "color: white; "
                      "border: 2px solid #27ae60; "
                      "border-radius: 10px; "
                      "font-size: 18px; "
                      "font-weight: bold; "
                      "min-height: 60px; "
                      "}"
                      "QPushButton:hover { "
                      "background-color: #27ae60; "
                      "border: 2px solid #229954; "
                      "}";

    // 创建LED按钮
    int index = 0;
    for ( int row = 0; row < 2; row++ )
    {
        for ( int col = 0; col < 4; col++ )
        {
            QString      ledName   = ledNames[ index++ ];
            QPushButton* ledButton = new QPushButton( ledName, ui->gbox_led );
            ledButton->setStyleSheet( offStyle );
            ledButton->setCheckable( true );

            // 连接按钮点击信号
            connect( ledButton, &QPushButton::clicked, this, [ this, ledName, ledButton, onStyle, offStyle ]() {
                bool isChecked = ledButton->isChecked();
                ledButton->setStyleSheet( isChecked ? onStyle : offStyle );

                // 获取LED编号
                int ledNumber = ledName.mid(3).toInt();

                // 调用键盘通信对象的updateLEDStatus函数控制实际LED
                if (m_keyboard) {
                    m_keyboard->updateLEDStatus(ledNumber, isChecked);
                    qDebug() << ledName << (isChecked ? "开启" : "关闭") << "LED编号:" << ledNumber;
                } else {
                    qDebug() << "键盘通信对象未初始化，无法控制" << ledName;
                }
            } );

            // 添加到布局
            gridLayout->addWidget( ledButton, row, col );

            // 保存到映射中
            ledButtons[ ledName ] = ledButton;
        }
    }

    // 设置行列比例
    for ( int i = 0; i < 2; i++ )
    {
        gridLayout->setRowStretch( i, 1 );
    }

    for ( int i = 0; i < 4; i++ )
    {
        gridLayout->setColumnStretch( i, 1 );
    }

    // 应用布局
    ui->gbox_led->setLayout( gridLayout );
}

void TestForm::updateLEDStatus( const QString& ledName, bool isOn )
{
    if ( !ledButtons.contains( ledName ) )
        return;

    QString offStyle = "QPushButton { "
                       "background-color: #f8f8f8; "
                       "color: #333333; "
                       "border: 2px solid #dddddd; "
                       "border-radius: 10px; "
                       "font-size: 18px; "
                       "font-weight: bold; "
                       "min-height: 60px; "
                       "}"
                       "QPushButton:hover { "
                       "background-color: #e8f4fc; "
                       "border: 2px solid #3498db; "
                       "color: #2980b9; "
                       "}";

    QString onStyle = "QPushButton { "
                      "background-color: #2ecc71; "
                      "color: white; "
                      "border: 2px solid #27ae60; "
                      "border-radius: 10px; "
                      "font-size: 18px; "
                      "font-weight: bold; "
                      "min-height: 60px; "
                      "}"
                      "QPushButton:hover { "
                      "background-color: #27ae60; "
                      "border: 2px solid #229954; "
                      "}";

    QPushButton* button = ledButtons[ ledName ];
    button->setChecked( isOn );
    button->setStyleSheet( isOn ? onStyle : offStyle );
}

void TestForm::updateKeyStatus( int keyValue, bool pressed )
{
    if ( ui->Test_stackedWidget->currentIndex() != 8 )
        return;
    // 按键状态映射(1-15)
    // 1  2  3  A
    // 4  5  6  B
    // 7  8  9  C
    // *  0  #  D
    QString keyName;

    // 将键值转换为键名
    switch ( keyValue )
    {
        case 0:
            keyName = "1";
            break;
        case 1:
            keyName = "2";
            break;
        case 2:
            keyName = "3";
            break;
        case 3:
            keyName = "A";
            break;
        case 6:
            keyName = "4";
            break;
        case 7:
            keyName = "5";
            break;
        case 8:
            keyName = "6";
            break;
        case 9:
            keyName = "B";
            break;
        case 12:
            keyName = "7";
            break;
        case 13:
            keyName = "8";
            break;
        case 14:
            keyName = "9";
            break;
        case 15:
            keyName = "C";
            break;
        case 18:
            keyName = "*";
            break;
        case 19:
            keyName = "0";
            break;
        case 20:
            keyName = "#";
            break;
        case 21:
            keyName = "D";
            break;
        default:
            return;  // 无效键值
    }

    // 更新按键样式
    if ( keyLabels.contains( keyName ) )
    {
        QString normalStyle  = "QLabel { "
                              "background-color: #f8f8f8; "
                              "color: #333333; "
                              "border: 2px solid #dddddd; "
                              "border-radius: 10px; "
                              "font-size: 24px; "
                              "font-weight: bold; "
                              "}";
        QString pressedStyle = "QLabel { "
                              "background-color: #3498db; "
                              "color: white; "
                              "border: 2px solid #2980b9; "
                              "border-radius: 10px; "
                              "font-size: 24px; "
                              "font-weight: bold; "
                              "}";

        keyLabels[ keyName ]->setStyleSheet( pressed ? pressedStyle : normalStyle );
    }
}

void TestForm::resetAllKeyStatus()
{
    QString normalStyle  = "QLabel { "
                          "background-color: #f8f8f8; "
                          "color: #333333; "
                          "border: 2px solid #dddddd; "
                          "border-radius: 10px; "
                          "font-size: 24px; "
                          "font-weight: bold; "
                          "}";

    // 重置所有按键状态
    for ( auto it = keyLabels.begin(); it != keyLabels.end(); ++it )
    {
        it.value()->setStyleSheet( normalStyle );
    }
}
