#ifndef WAVEMODEFORM_H
#define WAVEMODEFORM_H

#include <QWidget>

namespace Ui {
class WaveModeForm;
}

class WaveModeForm : public QWidget
{
    Q_OBJECT

public:
    explicit WaveModeForm(QWidget *parent = nullptr);
    ~WaveModeForm();

    // 定义编织模式枚举
    enum WaveMode {
        ContinueMode = 0,    // 断电续织
        RestartMode = 1      // 重新编织
    };

signals:
    // 定义信号，返回选择的模式
    void waveModeSelected(int mode);

private slots:
    // 按钮点击槽函数
    void onContinueButtonClicked();
    void onRestartButtonClicked();

private:
    Ui::WaveModeForm *ui;
};

#endif // WAVEMODEFORM_H
