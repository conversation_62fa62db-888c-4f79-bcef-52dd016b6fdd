﻿#ifndef FILESELECTFORM_H
#define FILESELECTFORM_H

#include "Common/parameters.h"
#include <QDebug>
#include <QDir>
#include <QFrame>
#include <QLabel>
#include <QPushButton>
#include <QTextCodec>
#include <QVBoxLayout>
#include <QWidget>

namespace Ui
{
class FileSelectForm;
}

class FileSelectForm : public QWidget
{
    Q_OBJECT

public:
    explicit FileSelectForm( QWidget* parent = nullptr, QString dir = FAT_DIR, quint8 filter = 0 );
    ~FileSelectForm();

    void show();  // 重写show函数，自动刷新文件列表

signals:
    void itemSelected( QString name );
    void finished();

public slots:
    void onPushButtonClicked();

protected:
    void closeEvent( QCloseEvent* event ) override;

private:
    Ui::FileSelectForm* ui;
    QString             addr = "";
    quint8              fileFilter = 0;   // 文件过滤器类型

    // 分页相关
    static const int ITEMS_PER_PAGE = 8; // 每页显示的文件数量
    int currentPage = 0;                  // 当前页码
    int totalPages = 0;                   // 总页数
    QFileInfoList fileInfoList;           // 文件信息列表
    QWidget* fileListContainer = nullptr; // 文件列表容器
    QLabel* pageInfoLabel = nullptr;      // 页码信息标签

    // 分页相关方法
    void refreshFileList();               // 刷新文件夹中的文件列表
    void updateFileList();                // 更新文件列表显示
    void updatePageInfo();                // 更新页码信息
    void onPrevPageClicked();            // 上一页按钮点击
    void onNextPageClicked();            // 下一页按钮点击
};

#endif  // FILESELECTFORM_H
