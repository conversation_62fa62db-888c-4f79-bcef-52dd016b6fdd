﻿#include "readlinkconfig.h"
#include <QDebug>
#include <QFile>

ReadLinkConfig::ReadLinkConfig() {}

ReadLinkConfig::~ReadLinkConfig() {}

void ReadLinkConfig::parseLinkConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        // 文件打开失败
        qDebug() << "Linkdata Json  Open Failed!";
        QMessageBox::warning( nullptr, "错误提示", "link-data.json文件不存在" );
        return;
    }
    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        // Json文档无效
        qDebug() << "Linkdata Json is inValid!";
        return;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObject = jsonDocument.object();

    // 获取stepNames数组
    QJsonArray stepNamesArray = jsonObject[ "stepNames" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < stepNamesArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = stepNamesArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_stepNameList.insert( key, value );
    }

    // 获取CYState数组
    QJsonArray CYStateArray = jsonObject[ "CYState" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < CYStateArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = CYStateArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_CYStateList.insert( key, value );
    }

    // 获取SignalMap数组
    QJsonArray signalMapArray = jsonObject[ "SignalMap" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < signalMapArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = signalMapArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_signalMapList.insert( key, value );
    }

    // 获取MotorMap数组
    QJsonArray motorMapArray = jsonObject[ "MotorMap" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < motorMapArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = motorMapArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_motorMapList.insert( key, value );
    }

    // 获取FTCommands数组
    QJsonArray FTCommandsArray = jsonObject[ "FTCommands" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < FTCommandsArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = FTCommandsArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_FTCommandList.insert( key, value );
    }

    // 获取airvalve数组
    QJsonArray airvalveArray = jsonObject[ "airvalve" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < airvalveArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = airvalveArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_airvalveList.insert( key, value );
    }

    // 获取fengtou数组
    QJsonArray fengtouArray = jsonObject[ "FengtouStep" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < fengtouArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_fengtouList.insert( key, value );
    }

    // 获取fengtouCommand数组
    QJsonArray fengtouCommandArray = jsonObject[ "FengtouCommand" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < fengtouCommandArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouCommandArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_fengtouCommandList.insert( key, value );
    }

    // 获取fengtouReset数组
    QJsonArray fengtouResetArray = jsonObject[ "FengtouReset" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < fengtouResetArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouResetArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_fengtouResetList.insert( key, value );
    }

    // 获取motorPattern数组
    QJsonArray motorPatternArray = jsonObject[ "MotorPattern" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < motorPatternArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = motorPatternArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_motorPatternList.insert( key, value );
    }

    // 获取fengtouVSType数组
    QJsonArray fengtouVSTypeArray = jsonObject[ "FengtouVSType" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < fengtouVSTypeArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouVSTypeArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_fengtouVSTypeList.insert( key, value );
    }

    // 获取fengtouVState数组
    QJsonArray fengtouVStateArray = jsonObject[ "FengtouVState" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < fengtouVStateArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouVStateArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_fengtouVStateList.insert( key, value );
    }

    // 获取fengtouSState数组
    QJsonArray fengtouSStateArray = jsonObject[ "FengtouSState" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < fengtouSStateArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouSStateArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_fengtouSStateList.insert( key, value );
    }

    // 获取needleName数组
    QJsonArray needleNameArray = jsonObject[ "NeedleName" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < needleNameArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = needleNameArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_needleNameList.insert( key, value );
    }

    // 获取needleName数组
    QJsonArray needleStateArray = jsonObject[ "NeedleState" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < needleStateArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = needleStateArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_needleStateList.insert( key, value );
    }

    // 获取tianshaColor数组
    QJsonArray tianshaColorArray = jsonObject[ "PatternTianshaColor" ].toArray();
    for ( int i = 0; i < tianshaColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = tianshaColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_tianshaColorList.insert( key, value );
    }

    // 获取zhusuoColor数组
    QJsonArray zhusuoColorArray = jsonObject[ "PatternZhusuoColor" ].toArray();
    for ( int i = 0; i < zhusuoColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = zhusuoColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_zhusuoColorList.insert( key, value );
    }

    // 获取suduColor数组
    QJsonArray suduColorArray = jsonObject[ "PatternSuduColor" ].toArray();
    for ( int i = 0; i < suduColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = suduColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_suduColorList.insert( key, value );
    }

    // 获取xiangjinColor数组
    QJsonArray xiangjinColorArray = jsonObject[ "PatternXiangjinColor" ].toArray();
    for ( int i = 0; i < xiangjinColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = xiangjinColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_xiangjinColorList.insert( key, value );
    }

    // 获取lamaoColor数组
    QJsonArray lamaoColorArray = jsonObject[ "PatternLamaoColor" ].toArray();
    for ( int i = 0; i < lamaoColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = lamaoColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_lamaoColorList.insert( key, value );
    }

    // 获取wagenlamaoColor数组
    QJsonArray wagenlamaoColorArray = jsonObject[ "PatternWagenLamaoColor" ].toArray();
    for ( int i = 0; i < wagenlamaoColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = wagenlamaoColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_wagenlamaoColorList.insert( key, value );
    }

    // 获取dasongsanjiaoColor数组
    QJsonArray dasongsanjiaoColorArray = jsonObject[ "PatternDasongSanjiaoColor" ].toArray();
    for ( int i = 0; i < dasongsanjiaoColorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = dasongsanjiaoColorArray.at( i ).toObject();

        // 获取key和value属性
        int     key   = step[ "key" ].toInt();
        QString value = step[ "value" ].toString();

        // 对key和value进行处理
        this->_dasongsanjiaoColorList.insert( key, value );
    }

    file.close();  // 关闭文件
}
