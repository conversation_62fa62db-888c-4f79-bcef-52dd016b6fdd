{"basic": [{"id": 1, "title": "机器类型", "value": 1, "unit": "enum", "editable": true, "candidate": ["G615", "G616"]}, {"id": 2, "title": "最大针数", "value": 200, "unit": "", "editable": true}, {"id": 3, "title": "机器零位", "value": 30, "unit": "", "editable": true}, {"id": 4, "title": "吸风自动控制", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 5, "title": "袜跟左位置", "value": 0, "unit": "°", "editable": true}, {"id": 6, "title": "袜跟右位置", "value": 0, "unit": "°", "editable": true}, {"id": 7, "title": "袜跟快速使能", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 8, "title": "袜跟减速提前", "value": 0, "unit": "°", "editable": true}, {"id": 9, "title": "袜跟减速自适应", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 10, "title": "1C-抄针正转位置", "value": 0, "unit": "°", "editable": true}, {"id": 11, "title": "袜跟结束回头角度", "value": 76, "unit": "°", "editable": true}, {"id": 12, "title": "电眼使能", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 13, "title": "KTF初始值", "value": 0, "unit": "cN", "editable": true}, {"id": 14, "title": "KTF步进值", "value": 0, "unit": "cN", "editable": true}, {"id": 15, "title": "断电续织", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}], "needle": [{"id": 1, "title": "选针器极性", "value": 0, "unit": "enum", "candidate": ["正向", "反向"], "editable": true}, {"id": 2, "title": "选针器排列", "value": 0, "unit": "enum", "candidate": ["正向+", "反向+", "正向-", "反向-"], "editable": true}, {"id": 3, "title": "选针器段数", "value": 8, "unit": "", "editable": true}, {"id": 4, "title": "提花针第1排起始", "value": 8, "unit": "", "editable": true}, {"id": 5, "title": "提花针第1排结束", "value": 8, "unit": "", "editable": true}, {"id": 6, "title": "提花针第1排组数", "value": 8, "unit": "", "editable": true}, {"id": 7, "title": "提花针第2排起始", "value": 8, "unit": "", "editable": true}, {"id": 8, "title": "提花针第2排结束", "value": 8, "unit": "", "editable": true}, {"id": 9, "title": "提花针第2排组数", "value": 8, "unit": "", "editable": true}, {"id": 10, "title": "提花针组数", "value": 8, "unit": "", "editable": true}, {"id": 11, "title": "选针器提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 12, "title": "拉毛极性", "value": 0, "unit": "enum", "candidate": ["正向", "反向"], "editable": true}, {"id": 13, "title": "拉毛排列", "value": 0, "unit": "enum", "candidate": ["正向+", "反向+", "正向-", "反向-"], "editable": true}, {"id": 14, "title": "拉毛段数", "value": 8, "unit": "", "editable": true}, {"id": 15, "title": "橡筋选针位置", "value": 18, "unit": "°", "editable": true}, {"id": 16, "title": "集圈选针位置", "value": 79, "unit": "°", "editable": true}, {"id": 17, "title": "1C位置", "value": 100, "unit": "°", "editable": true}, {"id": 18, "title": "2C位置", "value": 130, "unit": "°", "editable": true}, {"id": 19, "title": "3C位置", "value": 165, "unit": "°", "editable": true}, {"id": 20, "title": "4C位置", "value": 195, "unit": "°", "editable": true}, {"id": 21, "title": "5C位置", "value": 225, "unit": "°", "editable": true}, {"id": 22, "title": "6C位置", "value": 258, "unit": "°", "editable": true}, {"id": 23, "title": "1F位置", "value": 287, "unit": "°", "editable": true}, {"id": 24, "title": "拉毛正位置", "value": 212.9, "unit": "°", "editable": true}, {"id": 25, "title": "拉毛反位置", "value": 246.1, "unit": "°", "editable": true}, {"id": 26, "title": "橡筋提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 27, "title": "集圈提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 28, "title": "1C提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 29, "title": "2C提前角度", "value": 2.0, "unit": "°", "editable": true}, {"id": 30, "title": "3C提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 31, "title": "4C提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 32, "title": "5C提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 33, "title": "6C提前角度", "value": 4.5, "unit": "°", "editable": true}, {"id": 34, "title": "1F提前角度", "value": 4.5, "unit": "°", "editable": true}], "peripheral": [{"id": 1, "title": "主梭起始位置", "value": 20, "unit": "°", "editable": true}, {"id": 2, "title": "主梭提前量", "value": 10, "unit": "°", "editable": true}, {"id": 3, "title": "主梭间距", "value": 3, "unit": "°", "editable": true}, {"id": 4, "title": "半位延时角度", "value": 20, "unit": "°", "editable": true}, {"id": 5, "title": "主梭橡筋梭组数", "value": 1, "unit": "", "editable": true}, {"id": 6, "title": "主梭橡筋梭个数/组", "value": 1, "unit": "", "editable": true}, {"id": 7, "title": "主梭集圈梭组数", "value": 1, "unit": "", "editable": true}, {"id": 8, "title": "主梭集圈梭个数/组", "value": 1, "unit": "", "editable": true}, {"id": 9, "title": "副梭间距", "value": 45, "unit": "°", "editable": true}, {"id": 10, "title": "添纱重叠量", "value": 45, "unit": "°", "editable": true}, {"id": 11, "title": "橡筋副梭位置", "value": 18, "unit": "°", "editable": true}, {"id": 12, "title": "集圈副梭位置", "value": 79, "unit": "°", "editable": true}, {"id": 13, "title": "1C-添纱位置", "value": 140, "unit": "°", "editable": true}, {"id": 14, "title": "2C-添纱位置", "value": 170, "unit": "°", "editable": true}, {"id": 15, "title": "3C-添纱位置", "value": 200, "unit": "°", "editable": true}, {"id": 16, "title": "4C-添纱位置", "value": 230, "unit": "°", "editable": true}, {"id": 17, "title": "5C-添纱位置", "value": 260, "unit": "°", "editable": true}, {"id": 18, "title": "6C-添纱位置", "value": 278, "unit": "°", "editable": true}, {"id": 19, "title": "打松三角提前量", "value": 30, "unit": "°", "editable": true}, {"id": 20, "title": "出袜控制时间", "value": 2000, "unit": "ms", "editable": true}], "user": [{"id": 1, "title": "自动吸风", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 2, "title": "断电续织", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 3, "title": "实际速度（%）", "value": 100, "unit": "", "editable": true}, {"id": 4, "title": "袜跟加减速", "value": 0, "unit": "enum", "candidate": ["Lv.1", "Lv.2", "Lv.3", "Lv.4", "Lv.5"], "editable": true}, {"id": 5, "title": "屏保时间", "value": 10, "unit": "分钟", "editable": true}, {"id": 6, "title": "自动加油", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 7, "title": "加油模式", "value": 0, "unit": "enum", "candidate": ["时间", "产量", "圈数"], "editable": true}, {"id": 8, "title": "加油间隔", "value": 0, "unit": "enum", "candidate": ["时间", "产量", "圈数"], "editable": true}, {"id": 9, "title": "加油保持", "value": 2, "unit": "秒", "editable": true}, {"id": 10, "title": "移袜检测时间", "value": 1.0, "unit": "秒", "editable": true}, {"id": 11, "title": "添纱退出延迟", "value": 5, "unit": "°", "editable": true}, {"id": 12, "title": "开机预热", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 13, "title": "点动速度", "value": 0, "unit": "", "editable": true}, {"id": 14, "title": "慢速速度", "value": 80, "unit": "", "editable": true}, {"id": 15, "title": "中速速度", "value": 100, "unit": "", "editable": true}, {"id": 16, "title": "快速速度", "value": 300, "unit": "", "editable": true}, {"id": 17, "title": "剪刀盘速率", "value": 100, "unit": "%", "editable": true}, {"id": 18, "title": "选针补偿参数1", "value": 0, "unit": "", "editable": true}, {"id": 19, "title": "选针补偿参数2", "value": 0, "unit": "", "editable": true}, {"id": 20, "title": "密度参数跟随", "value": 0, "unit": "enum", "candidate": ["系统", "花型"], "editable": true}, {"id": 21, "title": "揿针动作跟随", "value": 0, "unit": "enum", "candidate": ["角度", "信号"], "editable": true}, {"id": 22, "title": "橡筋跟随低速", "value": 0, "unit": "", "editable": true}], "fengtou": [{"id": 1, "title": "缝齿盘校准模式", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 2, "title": "缝头停车减速提前", "value": 30, "unit": "", "editable": true}, {"id": 3, "title": "缝头电机齿轮数", "value": 10, "unit": "", "editable": true}, {"id": 4, "title": "缝头针筒齿轮数", "value": 10, "unit": "", "editable": true}, {"id": 5, "title": "缝头完毕每针宽度", "value": 10, "unit": "", "editable": true}, {"id": 6, "title": "缝头第1针0位置", "value": 10, "unit": "", "editable": true}, {"id": 7, "title": "摆臂上下接袜1位置", "value": 10, "unit": "", "editable": true}, {"id": 8, "title": "摆臂上下接袜2位置", "value": 10, "unit": "", "editable": true}, {"id": 9, "title": "摆臂上下接袜3位置", "value": 10, "unit": "", "editable": true}, {"id": 10, "title": "摆臂上下接袜4位置", "value": 10, "unit": "", "editable": true}, {"id": 11, "title": "摆臂上下移出5位置", "value": 10, "unit": "", "editable": true}, {"id": 12, "title": "摆臂左右等待6位置", "value": 10, "unit": "", "editable": true}, {"id": 13, "title": "抓手上下等待7位置", "value": 10, "unit": "", "editable": true}, {"id": 14, "title": "抓手上下翻袜8位置", "value": 10, "unit": "", "editable": true}, {"id": 15, "title": "抓手上下复位9位置", "value": 10, "unit": "", "editable": true}, {"id": 16, "title": "缝头停车补偿设置", "value": 10, "unit": "°", "editable": true}, {"id": 17, "title": "缝头停车误差容限", "value": 10, "unit": "°", "editable": true}, {"id": 18, "title": "摆臂左右套袜10位置", "value": 10, "unit": "", "editable": true}, {"id": 19, "title": "针筒销子误差容限", "value": 10, "unit": "°", "editable": true}, {"id": 20, "title": "缝头动作快速模式", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 21, "title": "缝头动作跟随", "value": 0, "unit": "enum", "candidate": ["花型", "内存"], "editable": true}], "other": [{"id": 1, "title": "移袜感应信号", "value": 0, "unit": "enum", "candidate": ["正向", "反向"], "editable": true}, {"id": 2, "title": "冷机时间设置", "value": 120, "unit": "分钟", "editable": true}, {"id": 3, "title": "热机持续时间", "value": 10, "unit": "分钟", "editable": true}, {"id": 4, "title": "冷机保护自动控制", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 5, "title": "快速复位完成停车", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 6, "title": "照明灯自动控制", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 7, "title": "橡筋-袜跟右2位置", "value": 0, "unit": "°", "editable": true}, {"id": 8, "title": "橡筋-剪线位置", "value": 0, "unit": "°", "editable": true}, {"id": 9, "title": "套刺翻转销退11位置", "value": 0, "unit": "", "editable": true}, {"id": 10, "title": "橡筋跟随花型退出延时", "value": 0, "unit": "°", "editable": true}, {"id": 11, "title": "橡筋速度跟随模式", "value": 0, "unit": "enum", "candidate": ["Lv.1", "Lv.2", "Lv.3", "Lv.4", "Lv.5"], "editable": true}, {"id": 12, "title": "抄刀选针器通电时间", "value": 20, "unit": "", "editable": true}, {"id": 13, "title": "袜跟换色M-D进提前", "value": 10, "unit": "°", "editable": true}, {"id": 14, "title": "袜跟换色M-D退滞后", "value": 10, "unit": "°", "editable": true}, {"id": 15, "title": "缝头功能控制", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 16, "title": "裸氨剪刀工作延时", "value": 10, "unit": "°", "editable": true}, {"id": 17, "title": "裸氨剪刀工作角度", "value": 10, "unit": "°", "editable": true}, {"id": 18, "title": "裸氨剪刀退出延时", "value": 10, "unit": "°", "editable": true}, {"id": 19, "title": "裸氨剪刀退出角度", "value": 10, "unit": "°", "editable": true}, {"id": 20, "title": "启动前零位检测", "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 21, "title": "织袜风门停车关闭", "value": 0, "unit": "秒", "editable": true}, {"id": 22, "title": "缝齿电机零位11位置", "value": 0, "unit": "", "editable": true}, {"id": 23, "title": "2F-5号主梭位置", "value": 0, "unit": "°", "editable": true}, {"id": 24, "title": "2F主梭提前量", "value": 0, "unit": "°", "editable": true}, {"id": 25, "title": "照明灯亮度", "value": 0, "unit": "%", "editable": true}, {"id": 26, "title": "移袜检测超时时间", "value": 0, "unit": "秒", "editable": true}, {"id": 27, "title": "机头升降移袜位置", "value": 0, "unit": "°", "editable": true}], "xiangjin": [{"id": 1, "title": "橡筋电机", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 2, "title": "橡筋梭1", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 3, "title": "橡筋剪刀1", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 4, "title": "橡筋输送1", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 5, "title": "橡筋梭2", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 6, "title": "橡筋剪刀2", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 7, "title": "橡筋输送2", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 8, "title": "剪刀设置1", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 9, "title": "橡筋梭半位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 10, "title": "剪线设置2", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 11, "title": "剪刀1跟随", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 12, "title": "跟随1设置", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 13, "title": "剪刀2跟随", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 14, "title": "跟随2设置", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 15, "title": "橡筋眉毛半位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 16, "title": "橡筋眉毛全位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 17, "title": "机头跟随", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 18, "title": "机头设置", "value1": 0, "value2": 110, "unit": "°", "editable": true}], "sanjiao": [{"id": 1, "title": "橡筋刀半位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 2, "title": "橡筋刀全位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 3, "title": "橡筋眉毛半位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 4, "title": "橡筋眉毛全位", "value1": 0, "value2": 110, "unit": "°", "editable": true}, {"id": 5, "title": "T1提花刀", "value1": 0, "value2": 110, "unit": "°", "editable": true}], "socketMotor": [{"id": 1, "title": "剪刀盘", "data": [{"id": 1, "value": 2.1, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K(启动)", "editable": true}, {"id": 4, "value": 2.0, "unit": "K(停止)", "editable": true}, {"id": 5, "value": null, "unit": "/", "editable": false}, {"id": 6, "value": 0, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": null, "unit": "/", "editable": false}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 2, "title": "右棱角", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 3, "title": "左棱角", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 4, "title": "针筒密度", "data": [{"id": 1, "value": 2.0, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K", "editable": true}, {"id": 4, "value": 1.0, "unit": "K", "editable": true}, {"id": 5, "value": 0, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": 0, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 5, "title": "机头升降", "data": [{"id": 1, "value": 2.1, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K", "editable": true}, {"id": 4, "value": 1.0, "unit": "K", "editable": true}, {"id": 5, "value": 400, "unit": "", "editable": true}, {"id": 6, "value": 0, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": 0, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 6, "title": "织袜风门", "data": [{"id": 1, "value": 2.1, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K", "editable": true}, {"id": 4, "value": 1.0, "unit": "K", "editable": true}, {"id": 5, "value": 400, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": 0, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 7, "title": "生克电机", "data": [{"id": 1, "value": 2.1, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K", "editable": true}, {"id": 4, "value": 2.0, "unit": "K", "editable": true}, {"id": 5, "value": 2000, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": 0, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 8, "title": "沉降电机", "data": [{"id": 1, "value": 2.1, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K", "editable": true}, {"id": 4, "value": 2.0, "unit": "K", "editable": true}, {"id": 5, "value": 2000, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": 0, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 9, "title": "橡筋电机", "data": [{"id": 1, "value": 2.0, "unit": "A", "editable": true}, {"id": 2, "value": 1.0, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K(启动)", "editable": true}, {"id": 4, "value": 2.0, "unit": "K(停止)", "editable": true}, {"id": 5, "value": null, "unit": "/", "editable": false}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": null, "unit": "/", "editable": false}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}], "fengtouMotor": [{"id": 1, "title": "袜筒左右", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 2, "title": "袜筒升降", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 3, "title": "抓手上下", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 4, "title": "摆臂左右", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 5, "title": "摆臂上下", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 6, "title": "缝头电机", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 7, "title": "缝齿电机", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 8, "title": "翻盖电机", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}], "otherMotor": [{"id": 1, "title": "推杆电机", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 2, "title": "袜筒上下", "data": [{"id": 1, "value": 5.0, "unit": "A", "editable": true}, {"id": 2, "value": 5.2, "unit": "A", "editable": true}, {"id": 3, "value": 5.2, "unit": "K", "editable": true}, {"id": 4, "value": 2.2, "unit": "K", "editable": true}, {"id": 5, "value": 2200, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 0, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 3, "title": "缝头风门", "data": [{"id": 1, "value": 2.1, "unit": "A", "editable": true}, {"id": 2, "value": 2.2, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K", "editable": true}, {"id": 4, "value": 2.0, "unit": "K", "editable": true}, {"id": 5, "value": 2000, "unit": "", "editable": true}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": 0, "unit": "enum", "candidate": ["L", "H"], "editable": true}, {"id": 8, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}, {"id": 4, "title": "橡筋电机2", "data": [{"id": 1, "value": 2.0, "unit": "A", "editable": true}, {"id": 2, "value": 1.0, "unit": "A", "editable": true}, {"id": 3, "value": 2.0, "unit": "K(启动)", "editable": true}, {"id": 4, "value": 2.0, "unit": "K(停止)", "editable": true}, {"id": 5, "value": null, "unit": "/", "editable": false}, {"id": 6, "value": 1, "unit": "enum", "candidate": ["反", "正"], "editable": true}, {"id": 7, "value": null, "unit": "/", "editable": false}, {"id": 8, "value": null, "unit": "/", "editable": false}, {"id": 9, "value": 1, "unit": "enum", "candidate": ["关", "开"], "editable": true}]}], "socketWarn": [{"id": 1, "title": "掉电信号", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 2, "title": "左揿针信号", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 3, "title": "右揿针信号", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 4, "title": "织袜落袜报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 5, "title": "左棱角半位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 6, "title": "超针刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 7, "title": "船袜三角1C221", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 8, "title": "左活络头", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 9, "title": "橡筋刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 10, "title": "压针刀1", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 11, "title": "右菱角半位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 12, "title": "退圈刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 13, "title": "缝头起针", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 14, "title": "T2 提花刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 15, "title": "右菱角全位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 16, "title": "T1 提花刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 17, "title": "右活络头", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 18, "title": "船袜三角3C223", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 19, "title": "左菱角全位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 20, "title": "脚底加固三角", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 21, "title": "船袜三角2C222", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 22, "title": "线架断线1", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 23, "title": "线架断线2", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 24, "title": "线架断线3", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 25, "title": "铜管升降", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 26, "title": "针简销子", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 27, "title": "橡筋张力1", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 28, "title": "橡筋张力2", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 29, "title": ":提花针针脚", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 30, "title": "摇手柄", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 31, "title": "针杆探测器", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 32, "title": "12V电源短路", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 33, "title": "袜跟张力", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 34, "title": "断针探测器", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 35, "title": "针舌探测器1", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 36, "title": "针舌探测器2", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 37, "title": "油压低", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 38, "title": "缺油报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 39, "title": "机头分离", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 40, "title": "哈弗盘到位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 41, "title": "圆盘剪刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 42, "title": "剪刀缠线", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 43, "title": "1号KTF报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 44, "title": "2号KTF报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 45, "title": "哈弗头离合", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 46, "title": "蝴湖蝶门门", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 47, "title": "移袜检测", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 48, "title": "3号KTF报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 49, "title": "气压报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 50, "title": "4号KTF报警", "enable": true, "sensitivity": 1, "direction": 0}], "fengtouWarn": [{"id": 1, "title": "翻袜管摆动销子", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 2, "title": "压盘下降上", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 3, "title": "压盘下降下", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 4, "title": "缝头剪刀", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 5, "title": "缝头线张力", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 6, "title": "套尺压环", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 7, "title": "翻袜管上升锁进", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 8, "title": "翻袜管上升锁退", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 9, "title": "抓手门感应", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 10, "title": "吸风筒上", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 11, "title": "吸风筒下", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 12, "title": "摆臂保护", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 13, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 14, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 15, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 16, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 17, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 18, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 19, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 20, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 21, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 22, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 23, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 24, "title": "缝头点动按钮", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 25, "title": "缝头停止按钮", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 26, "title": "缝头落袜报警", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 27, "title": "NC", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 28, "title": "翻袜筒感应", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 29, "title": "下筒压板下降", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 30, "title": "下简压板上升", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 31, "title": "探袜感应上", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 32, "title": "探袜感应下", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 33, "title": "挂刺放松", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 34, "title": "挂刺收紧", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 35, "title": "缝头机后退", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 36, "title": "缝头机前进", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 37, "title": "套刺感应", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 38, "title": "抓柄翻袜位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 39, "title": "抓柄上限位", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 40, "title": "上接翻袜管升", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 41, "title": "上接翻袜管降", "enable": true, "sensitivity": 1, "direction": 0}, {"id": 42, "title": "套刺翻转销子", "enable": true, "sensitivity": 1, "direction": 0}], "fastReset": [{"id": 1, "step_name": "快速复位", "chicun": 6, "speed": 80, "xiangjin": {"start_value": 1, "end_value": 7, "state": 0}, "ktf1": {"start_value": 1, "end_value": 7, "state": 0}, "ktf2": {"start_value": 1, "end_value": 7, "state": 0}, "zhentong_Density": {"start_value": 1, "end_value": 7, "state": 0}, "chenjiang_Density": {"start_value": 1, "end_value": 7, "state": 0}, "shengke_Density": {"start_value": 1, "end_value": 7, "state": 0}, "zuo_lengjiao": {"start_value": 1, "end_value": 7, "state": 0}, "you_lengjiao": {"start_value": 1, "end_value": 7, "state": 0}, "huaxing_shezhi": 0, "lamao_shezhi": 0, "dagen_zhongxin": 0, "dagen_pianyi": 0, "fujia": 0, "cy_action_msg": [{"cy_num": 1, "cy_circle_cnt": 20, "cy_state": 1, "cmd_msg": [{"cmd_num": 1, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 2, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 3, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 4, "cmd_param": 24, "cmd_state": 1}]}, {"cy_num": 2, "cy_circle_cnt": 20, "cy_state": 1, "cmd_msg": [{"cmd_num": 15, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 6, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 7, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 8, "cmd_param": 24, "cmd_state": 1}]}]}, {"id": 2, "step_name": "缝头测试", "chicun": 6, "speed": 81, "xiangjin": {"start_value": 1, "end_value": 7, "state": 0}, "ktf1": {"start_value": 1, "end_value": 7, "state": 0}, "ktf2": {"start_value": 1, "end_value": 7, "state": 0}, "zhentong_Density": {"start_value": 1, "end_value": 7, "state": 0}, "chenjiang_Density": {"start_value": 1, "end_value": 7, "state": 0}, "shengke_Density": {"start_value": 1, "end_value": 7, "state": 0}, "zuo_lengjiao": {"start_value": 1, "end_value": 7, "state": 0}, "you_lengjiao": {"start_value": 1, "end_value": 7, "state": 0}, "huaxing_shezhi": 0, "lamao_shezhi": 0, "dagen_zhongxin": 0, "dagen_pianyi": 0, "fujia": 0, "cy_action_msg": [{"cy_num": 1, "cy_circle_cnt": 20, "cy_state": 1, "cmd_msg": [{"cmd_num": 1, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 2, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 3, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 4, "cmd_param": 24, "cmd_state": 1}]}, {"cy_num": 2, "cy_circle_cnt": 20, "cy_state": 1, "cmd_msg": [{"cmd_num": 15, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 6, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 7, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 8, "cmd_param": 24, "cmd_state": 1}]}]}, {"id": 3, "step_name": "扎口复位", "chicun": 6, "speed": 82, "xiangjin": {"start_value": 1, "end_value": 7, "state": 0}, "ktf1": {"start_value": 1, "end_value": 7, "state": 0}, "ktf2": {"start_value": 1, "end_value": 7, "state": 0}, "zhentong_Density": {"start_value": 1, "end_value": 7, "state": 0}, "chenjiang_Density": {"start_value": 1, "end_value": 7, "state": 0}, "shengke_Density": {"start_value": 1, "end_value": 7, "state": 0}, "zuo_lengjiao": {"start_value": 1, "end_value": 7, "state": 0}, "you_lengjiao": {"start_value": 1, "end_value": 7, "state": 0}, "huaxing_shezhi": 0, "lamao_shezhi": 0, "dagen_zhongxin": 0, "dagen_pianyi": 0, "fujia": 0, "cy_action_msg": [{"cy_num": 1, "cy_circle_cnt": 20, "cy_state": 1, "cmd_msg": [{"cmd_num": 1, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 2, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 3, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 4, "cmd_param": 24, "cmd_state": 1}]}, {"cy_num": 2, "cy_circle_cnt": 20, "cy_state": 1, "cmd_msg": [{"cmd_num": 15, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 6, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 7, "cmd_param": 24, "cmd_state": 1}, {"cmd_num": 8, "cmd_param": 24, "cmd_state": 1}]}]}], "fengtouCal": [{"id": 0, "step": [{"id": 1, "baojing_yanshi": 200, "dongzuo_yanshi": 100, "dongzuo_zhiling": 2, "fuwei_dongzuo": 2, "Valve_array": [{"num": 4, "type": 1, "state": 0}, {"num": 5, "type": 0, "state": 0}, {"num": 7, "type": 0, "state": 0}, {"num": 8, "type": 1, "state": 0}, {"num": 1, "type": 0, "state": 0}, {"num": 2, "type": 1, "state": 0}], "motor_array": [{"num": 1, "pattern": 1, "val": 20}, {"num": 2, "pattern": 1, "val": 20}, {"num": 3, "pattern": 1, "val": 20}, {"num": 4, "pattern": 1, "val": 20}, {"num": 5, "pattern": 1, "val": 20}]}]}], "zeroSensor": [{"id": 1, "name": "针筒密度零位"}, {"id": 2, "name": "织袜风门零位"}, {"id": 3, "name": "机头升降零位"}, {"id": 4, "name": "沉降电机零位"}, {"id": 5, "name": "生克电机零位"}, {"id": 6, "name": "缝头风门零位"}, {"id": 7, "name": "袜筒左右零位"}, {"id": 8, "name": "袜筒升降零位"}, {"id": 9, "name": "抓手上下零位"}, {"id": 10, "name": "抓手上下限位"}, {"id": 11, "name": "摆臂左右零位"}, {"id": 12, "name": "摆臂左右限位"}, {"id": 13, "name": "摆臂上下零位"}, {"id": 14, "name": "缝头电机零位"}, {"id": 15, "name": "缝齿电机零位"}, {"id": 16, "name": "翻盖电机零位"}, {"id": 17, "name": "翻盖电机限位"}, {"id": 18, "name": "左棱角零位"}, {"id": 19, "name": "右棱角零位"}, {"id": 20, "name": "推杆电机零位"}, {"id": 21, "name": "袜筒上下零位"}], "position": [{"id": 1, "title": "左活络头位置", "value": 0, "unit": "°", "editable": true}, {"id": 2, "title": "右活络头位置", "value": 0, "unit": "°", "editable": true}, {"id": 3, "title": "左揿针位置", "value": 0, "unit": "°", "editable": true}, {"id": 4, "title": "右揿针位置", "value": 0, "unit": "°", "editable": true}, {"id": 5, "title": "左挑位置", "value": 140, "unit": "°", "editable": true}, {"id": 6, "title": "右挑位置", "value": 140, "unit": "°", "editable": true}, {"id": 7, "title": "正向毛刀位置", "value": 0, "unit": "°", "editable": true}, {"id": 8, "title": "反向毛刀位置", "value": 0, "unit": "°", "editable": true}, {"id": 9, "title": "1口打松三角位置", "value": 0, "unit": "°", "editable": true}, {"id": 10, "title": "2口打松三角位置", "value": 30, "unit": "°", "editable": true}, {"id": 11, "title": "哈夫半位进半位置", "value": 280, "unit": "°", "editable": true}, {"id": 12, "title": "哈夫半位进全位置", "value": 130, "unit": "°", "editable": true}, {"id": 13, "title": "哈夫半位出半位置", "value": 230, "unit": "°", "editable": true}, {"id": 14, "title": "哈夫半位出全位置", "value": 90, "unit": "°", "editable": true}, {"id": 15, "title": "哈夫半位进花位置", "value": 140, "unit": "°", "editable": true}, {"id": 16, "title": "T1检查位置", "value": 180, "unit": "°", "editable": true}, {"id": 17, "title": "T2圆位置", "value": 200, "unit": "°", "editable": true}, {"id": 18, "title": "左活络头半位", "value": 70, "unit": "°", "editable": true}, {"id": 19, "title": "左活络头全位", "value": 250, "unit": "°", "editable": true}, {"id": 20, "title": "右活络头半位", "value": 70, "unit": "°", "editable": true}, {"id": 21, "title": "右活络头全位", "value": 250, "unit": "°", "editable": true}, {"id": 22, "title": "退圈刀半位置", "value": 230, "unit": "°", "editable": true}, {"id": 23, "title": "退圈刀全位置", "value": 50, "unit": "°", "editable": true}, {"id": 24, "title": "左棱角半位置", "value": 270, "unit": "°", "editable": true}], "position2": [{"id": 1, "title": "左棱角全位置", "value": 180, "unit": "°", "editable": true}, {"id": 2, "title": "右棱角半位置", "value": 100, "unit": "°", "editable": true}, {"id": 3, "title": "右棱角全位置", "value": 280, "unit": "°", "editable": true}, {"id": 4, "title": "橡筋半位置", "value": 210, "unit": "°", "editable": true}, {"id": 5, "title": "橡筋全位置", "value": 30, "unit": "°", "editable": true}, {"id": 6, "title": "挑刀位置", "value": 260, "unit": "°", "editable": true}, {"id": 7, "title": "压刀位置", "value": 240, "unit": "°", "editable": true}, {"id": 8, "title": "右压选针器刀位置", "value": 240, "unit": "°", "editable": true}, {"id": 9, "title": "左压选针器刀位置", "value": 200, "unit": "°", "editable": true}, {"id": 10, "title": "脚低加固起针刀位置", "value": 170, "unit": "°", "editable": true}, {"id": 11, "title": "缝头转移起针刀位置", "value": 200, "unit": "°", "editable": true}, {"id": 12, "title": "船三角1位置", "value": 180, "unit": "°", "editable": true}, {"id": 13, "title": "船三角2位置", "value": 180, "unit": "°", "editable": true}, {"id": 14, "title": "船三角3位置", "value": 180, "unit": "°", "editable": true}], "Servo": [{"id": 1, "title": "过流保护阈值", "value": 30212, "unit": "mA", "editable": true}, {"id": 2, "title": "过压保护阈值", "value": 380, "unit": "V", "editable": true}, {"id": 3, "title": "欠压保护阈值", "value": 180, "unit": "V", "editable": true}, {"id": 4, "title": "速度环最大速度", "value": 500, "unit": "r/min", "editable": true}, {"id": 5, "title": "电机加减速时间", "value": 50, "unit": "ms", "editable": true}, {"id": 6, "title": "位置环最大速度", "value": 425, "unit": "r/min", "editable": true}, {"id": 7, "title": "Kp", "value": 10, "unit": "", "editable": true}, {"id": 8, "title": "<PERSON>", "value": 10, "unit": "", "editable": true}], "EEye": {"realCount": 8, "items": [{"id": 1, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 1, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 2, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 2, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 3, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 3, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 4, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 4, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 5, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 5, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 6, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 6, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 7, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 7, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 8, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 8, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 9, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 9, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 10, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 10, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 11, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 11, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 12, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 12, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 13, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 13, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 14, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 14, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 15, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 15, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 16, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 16, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 17, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 17, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 18, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 18, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 19, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 19, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 20, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 20, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 21, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 21, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 22, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 22, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 23, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 23, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 24, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 24, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 25, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 25, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 26, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 26, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 27, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 27, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 28, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 28, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 29, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 29, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 30, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 30, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 31, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 31, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 32, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 32, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 33, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 33, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 34, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 34, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 35, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 35, "disConThreshold": 0, "wrapThreshold": 0}, {"id": 36, "sensitivity": 0, "inductionDelay": 0, "stFreq": 0, "endFreq": 0, "mappedId": 36, "disConThreshold": 0, "wrapThreshold": 0}]}}