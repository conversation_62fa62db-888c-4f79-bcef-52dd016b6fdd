#include "SokBusiness.h"
#include "Communicate/circleDataProtocol.h"
#include <QDebug>
#include <QFile>
#include <QFileInfo>
#include <QTextCodec>
#include <algorithm>

SokBusiness::SokBusiness( MainWidgetData* mainData, CommOperations* commOps, QObject* parent ) : QObject( parent ), mainData( mainData ), m_commOperations( commOps )
{
    sokParser   = new SokParser();
    sokFileData = QSharedPointer< SokParser::SokFileData >( new SokParser::SokFileData() );
    craftParams = QSharedPointer< CraftParamForm::CraftParam >( new CraftParamForm::CraftParam() );
}

SokBusiness::~SokBusiness()
{
    if ( sokParser )
    {
        delete sokParser;
        sokParser = nullptr;
    }
}

void SokBusiness::setSokFile( const QString& sokFilePath )
{
    if ( sokFilePath != currentSokFile )
    {
        currentSokFile = sokFilePath;
        readAndParseSokFile();
    }
}

QString SokBusiness::getSokFile() const
{
    return currentSokFile;
}

void SokBusiness::setCraftFile( const QString& craftFilePath )
{
    if ( craftFilePath != currentCraftFile )
    {
        currentCraftFile = craftFilePath;
        readAndParseCftFile();
    }
}

QString SokBusiness::getCraftFile() const
{
    return currentCraftFile;
}

void SokBusiness::setCurrentSize( int size )
{
    if ( size >= 0 && size <= 7 )
    {
        currentSize = size;
    }
}

int SokBusiness::getCurrentSize() const
{
    return currentSize;
}

bool SokBusiness::readAndParseSokFile()
{
    QMutexLocker locker( &mutex );

    if ( currentSokFile.isEmpty() || !QFile::exists( currentSokFile ) )
    {
        qDebug() << "Sok file does not exist: " << currentSokFile;
        emit sokFileParseCompleted( false );
        return false;
    }

    // 清空之前的数据
    sokFileData->chainData.clear();
    sokFileData->moduleDataVector.clear();
    stepDataMap.clear();
    econoVector.clear();

    // 解析sok文件
    bool success = sokParser->Parser( currentSokFile, sokFileData.data() );
    if ( !success )
    {
        qDebug() << "Failed to parse sok file: " << currentSokFile;
        emit sokFileParseCompleted( false );
        return false;
    }

    // 处理步骤数据
    success = processStepData();
    caculateMotorParmBySok();

    emit sokFileParseCompleted( success );
    return success;
}

bool SokBusiness::readAndParseCftFile()
{
    QMutexLocker locker( &mutex );

    if ( currentCraftFile.isEmpty() || !QFile::exists( currentCraftFile ) )
    {
        qDebug() << "Craft file does not exist: " << currentCraftFile;
        emit craftFileParseCompleted( false );
        return false;
    }

    // 原有的 CFT 文件处理逻辑
    QFile file( currentCraftFile );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "工艺文件打开失败" ) );
        emit craftFileParseCompleted( false );
        return false;
    }

    // 清空craftParams
    craftParams->sinkerMotorParam.clear();
    craftParams->elasticMotorParam.clear();
    craftParams->graduationMotorParam.clear();
    craftParams->speedParam.clear();

    QDataStream in( &file );
    in.setByteOrder( QDataStream::LittleEndian );

    // 检查文件是否可读
    if ( in.status() != QDataStream::Ok )
    {
        qDebug() << "文件数据流错误";
        file.close();
        emit craftFileParseCompleted( false );
        return false;
    }

    try
    {
        // 读取craftParams.speedParam
        quint32 size;
        in >> size;
        if ( size > 10000 )
        {  // 防止异常大小导致内存问题
            qDebug() << "Speed参数数量异常: " << size;
            file.close();
            emit craftFileParseCompleted( false );
            return false;
        }

        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< CraftParamForm::SpeedParam > item = QSharedPointer< CraftParamForm::SpeedParam >( new CraftParamForm::SpeedParam() );
            in >> item->speed >> item->speed_new;
            craftParams->speedParam.append( item );
        }

        // 读取craftParams.graduationMotorParam
        in >> size;
        if ( size > 10000 )
        {
            qDebug() << "GraduationMotor参数数量异常: " << size;
            file.close();
            emit craftFileParseCompleted( false );
            return false;
        }

        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< CraftParamForm::GraduationMotorParamItem > item = QSharedPointer< CraftParamForm::GraduationMotorParamItem >( new CraftParamForm::GraduationMotorParamItem() );

            // 使用memset初始化字符数组
            memset( item->blockName, 0, sizeof( item->blockName ) );
            // 读取固定长度字符串，确保不超出数组边界
            in.readRawData( item->blockName, sizeof( item->blockName ) - 1 );

            in >> item->stepStart >> item->stepEnd;
            qint8 gradId;
            in >> gradId;
            item->graduationId = static_cast< char >( gradId );
            in >> item->prohibitsValueChange;
            for ( int j = 0; j < 8; j++ )
            {
                in >> item->cylinderStart[ j ] >> item->cylinderEnd[ j ];
            }
            craftParams->graduationMotorParam.append( item );
        }

        // 读取craftParams.elasticMotorParam
        in >> size;
        if ( size > 10000 )
        {
            qDebug() << "ElasticMotor参数数量异常: " << size;
            file.close();
            emit craftFileParseCompleted( false );
            return false;
        }

        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< CraftParamForm::ElasticMotorParamItem > item = QSharedPointer< CraftParamForm::ElasticMotorParamItem >( new CraftParamForm::ElasticMotorParamItem() );

            // 使用memset初始化字符数组
            memset( item->blockName, 0, sizeof( item->blockName ) );
            // 读取固定长度字符串，确保不超出数组边界
            in.readRawData( item->blockName, sizeof( item->blockName ) - 1 );

            in >> item->stepStart >> item->stepEnd >> item->prohibitsValueChange1 >> item->prohibitsValueChange2 >> item->prohibitsSawValueChange;
            for ( int j = 0; j < 8; j++ )
            {
                in >> item->elasticStart1[ j ] >> item->elasticEnd1[ j ] >> item->elasticStart2[ j ] >> item->elasticEnd2[ j ] >> item->sawStart[ j ] >> item->sawEnd[ j ];
            }
            craftParams->elasticMotorParam.append( item );
        }

        // 读取craftParams.sinkerMotorParam
        in >> size;
        if ( size > 10000 )
        {
            qDebug() << "SinkerMotor参数数量异常: " << size;
            file.close();
            emit craftFileParseCompleted( false );
            return false;
        }

        for ( quint32 i = 0; i < size; i++ )
        {
            QSharedPointer< CraftParamForm::SinkerMotorParamItem > item = QSharedPointer< CraftParamForm::SinkerMotorParamItem >( new CraftParamForm::SinkerMotorParamItem() );

            // 使用memset初始化字符数组
            memset( item->blockName, 0, sizeof( item->blockName ) );
            // 读取固定长度字符串，确保不超出数组边界
            in.readRawData( item->blockName, sizeof( item->blockName ) - 1 );

            in >> item->stepStart >> item->stepEnd;
            qint8 sinkId;
            in >> sinkId;
            item->sinkerId = static_cast< char >( sinkId );
            in >> item->prohibitsAngularValueChange >> item->prohibitsSinkerValueChange;
            for ( int j = 0; j < 8; j++ )
            {
                in >> item->angularStart[ j ] >> item->angularEnd[ j ] >> item->sinkerStart[ j ] >> item->sinkerEnd[ j ];
            }
            craftParams->sinkerMotorParam.append( item );
        }
    }
    catch ( ... )
    {
        qDebug() << "解析工艺文件时发生异常";
        file.close();
        emit craftFileParseCompleted( false );
        return false;
    }

    // 检查数据流状态
    if ( in.status() != QDataStream::Ok )
    {
        qDebug() << "文件读取过程中出现错误";
        file.close();
        emit craftFileParseCompleted( false );
        return false;
    }

    file.close();
    emit craftFileParseCompleted( true );
    return true;
}

void SokBusiness::calculateMotorParamsByCft()
{
    QMutexLocker locker( &mutex );

    // 如果没有解析出链条数据
    if ( stepDataMap.size() == 0 )
        return;

    // 修正速度
    // 遍历stepDataMap，查找并替换匹配的速度值
    QMap< int, QSharedPointer< CircleData > >::iterator it;
    for ( it = stepDataMap.begin(); it != stepDataMap.end(); ++it )
    {
        CircleData* stepItem = it.value().data();
        for ( int i = 0; i < craftParams->speedParam.size(); ++i )
        {
            CraftParamForm::SpeedParam* item = craftParams->speedParam.at( i ).data();
            if ( stepItem->main_motor.speed == item->speed )
            {
                stepItem->main_motor.speed = item->speed_new;
                break;
            }
        }
    }

    // 计算motor的数据
    for ( int i = 0; i < craftParams->graduationMotorParam.size(); ++i )
    {
        CraftParamForm::GraduationMotorParamItem* item = craftParams->graduationMotorParam.at( i ).data();
        // 遍历stepStart和stepEnd之间
        for ( int j = item->stepStart; j <= item->stepEnd; j++ )
        {
            // 计算cylinder
            quint16 cylinder = 0;
            if ( item->cylinderStart == item->cylinderEnd )
                cylinder = item->cylinderStart[ currentSize ];
            else
            {
                cylinder =
                    item->cylinderStart[ currentSize ] + ( j - item->stepStart ) * ( item->cylinderEnd[ currentSize ] - item->cylinderStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }

            // 如果stepMap中有元素，则赋值即可
            if ( stepDataMap.contains( j ) )
            {
                CircleData* stepItem            = stepDataMap.value( j ).data();
                stepItem->motor_data[ 3 ].value = cylinder;
            }
        }
    }
    // 计算Elastic Motor数据
    for ( int i = 0; i < craftParams->elasticMotorParam.size(); ++i )
    {
        CraftParamForm::ElasticMotorParamItem* item = craftParams->elasticMotorParam.at( i ).data();
        // 遍历stepStart和stepEnd之间
        for ( int j = item->stepStart; j <= item->stepEnd; j++ )
        {
            // 计算elastic1
            quint16 elastic1 = 0, elastic2 = 0, saw = 0;
            if ( item->elasticStart1 == item->elasticEnd1 )
                elastic1 = item->elasticStart1[ currentSize ];
            else
            {
                elastic1 =
                    item->elasticStart1[ currentSize ] + ( j - item->stepStart ) * ( item->elasticEnd1[ currentSize ] - item->elasticStart1[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            if ( item->elasticStart2 == item->elasticEnd2 )
                elastic2 = item->elasticStart2[ currentSize ];
            else
            {
                elastic2 =
                    item->elasticStart2[ currentSize ] + ( j - item->stepStart ) * ( item->elasticEnd2[ currentSize ] - item->elasticStart2[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            if ( item->sawStart == item->sawEnd )
                saw = item->sawStart[ currentSize ];
            else
            {
                saw = item->sawStart[ currentSize ] + ( j - item->stepStart ) * ( item->sawEnd[ currentSize ] - item->sawStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            // 如果stepMap中有元素，则赋值即可
            if ( stepDataMap.contains( j ) )
            {
                CircleData* stepItem            = stepDataMap.value( j ).data();
                stepItem->motor_data[ 4 ].value = elastic1;
                // stepItem->motor_data[ 1 ].value = elastic2;
                stepItem->motor_data[ 0 ].value = saw;
            }
        }
    }
    // 计算生克马达数据
    for ( int i = 0; i < craftParams->sinkerMotorParam.size(); ++i )
    {
        CraftParamForm::SinkerMotorParamItem* item = craftParams->sinkerMotorParam.at( i ).data();
        // 遍历stepStart和stepEnd之间
        for ( int j = item->stepStart; j <= item->stepEnd; j++ )
        {
            // 计算angular
            quint16 angular = 0, sinker = 0;
            if ( item->angularStart == item->angularEnd )
                angular = item->angularStart[ currentSize ];
            else
            {
                angular = item->angularStart[ currentSize ] + ( j - item->stepStart ) * ( item->angularEnd[ currentSize ] - item->angularStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            if ( item->sinkerStart == item->sinkerEnd )
                sinker = item->sinkerStart[ currentSize ];
            else
            {
                sinker = item->sinkerStart[ currentSize ] + ( j - item->stepStart ) * ( item->sinkerEnd[ currentSize ] - item->sinkerStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            // 如果stepMap中有元素，则赋值即可
            if ( stepDataMap.contains( j ) )
            {
                CircleData* stepItem            = stepDataMap.value( j ).data();
                stepItem->motor_data[ 8 ].value = angular;
                stepItem->motor_data[ 7 ].value = sinker;
            }
        }
    }
}

void SokBusiness::caculateMotorParmBySok()
{
    // 计算motor的数据
    for ( int i = 0; i < sokFileData->graduationMotorParam.size(); ++i )
    {
        SokParser::GraduationMotorParamItem* item = sokFileData->graduationMotorParam.at( i ).data();
        // 遍历stepStart和stepEnd之间
        for ( int j = item->stepStart; j <= item->stepEnd; j++ )
        {
            // 计算cylinder
            quint16 cylinder = 0;
            if ( item->cylinderStart == item->cylinderEnd )
                cylinder = item->cylinderStart[ currentSize ];
            else
            {
                cylinder =
                    item->cylinderStart[ currentSize ] + ( j - item->stepStart ) * ( item->cylinderEnd[ currentSize ] - item->cylinderStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }

            // 如果stepMap中有元素，则赋值即可
            if ( stepDataMap.contains( j ) )
            {
                CircleData* stepItem            = stepDataMap.value( j ).data();
                stepItem->motor_data[ 3 ].value = cylinder;
            }
        }
    }
    // 计算Elastic Motor数据
    for ( int i = 0; i < sokFileData->elasticMotorParam.size(); ++i )
    {
        SokParser::ElasticMotorParamItem* item = sokFileData->elasticMotorParam.at( i ).data();
        // 遍历stepStart和stepEnd之间
        for ( int j = item->stepStart; j <= item->stepEnd; j++ )
        {
            // 计算elastic1
            quint16 elastic1 = 0, elastic2 = 0, saw = 0;
            if ( item->elasticStart1 == item->elasticEnd1 )
                elastic1 = item->elasticStart1[ currentSize ];
            else
            {
                elastic1 =
                    item->elasticStart1[ currentSize ] + ( j - item->stepStart ) * ( item->elasticEnd1[ currentSize ] - item->elasticStart1[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            if ( item->elasticStart2 == item->elasticEnd2 )
                elastic2 = item->elasticStart2[ currentSize ];
            else
            {
                elastic2 =
                    item->elasticStart2[ currentSize ] + ( j - item->stepStart ) * ( item->elasticEnd2[ currentSize ] - item->elasticStart2[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            if ( item->sawStart == item->sawEnd )
                saw = item->sawStart[ currentSize ];
            else
            {
                saw = item->sawStart[ currentSize ] + ( j - item->stepStart ) * ( item->sawEnd[ currentSize ] - item->sawStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            // 如果stepMap中有元素，则赋值即可
            if ( stepDataMap.contains( j ) )
            {
                CircleData* stepItem            = stepDataMap.value( j ).data();
                stepItem->motor_data[ 4 ].value = elastic1;
                // stepItem->motor_data[ 1 ].value = elastic2;
                stepItem->motor_data[ 0 ].value = saw;
            }
        }
    }
    // 计算生克马达数据
    for ( int i = 0; i < sokFileData->sinkerMotorParam.size(); ++i )
    {
        SokParser::SinkerMotorParamItem* item = sokFileData->sinkerMotorParam.at( i ).data();
        // 遍历stepStart和stepEnd之间
        for ( int j = item->stepStart; j <= item->stepEnd; j++ )
        {
            // 计算angular
            quint16 angular = 0, sinker = 0;
            if ( item->angularStart == item->angularEnd )
                angular = item->angularStart[ currentSize ];
            else
            {
                angular = item->angularStart[ currentSize ] + ( j - item->stepStart ) * ( item->angularEnd[ currentSize ] - item->angularStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            if ( item->sinkerStart == item->sinkerEnd )
                sinker = item->sinkerStart[ currentSize ];
            else
            {
                sinker = item->sinkerStart[ currentSize ] + ( j - item->stepStart ) * ( item->sinkerEnd[ currentSize ] - item->sinkerStart[ currentSize ] ) / ( item->stepEnd - item->stepStart );
            }
            // 如果stepMap中有元素，则赋值即可
            if ( stepDataMap.contains( j ) )
            {
                CircleData* stepItem            = stepDataMap.value( j ).data();
                stepItem->motor_data[ 8 ].value = angular;
                stepItem->motor_data[ 7 ].value = sinker;
            }
        }
    }
}

bool SokBusiness::processStepData()
{
    // 重新加载先清空
    stepDataMap.clear();
    econoVector.clear();

    // 首先找出最大的索引，最小索引为0
    maxStepIndex = 0;
    for ( auto it = sokFileData->chainData.begin(); it != sokFileData->chainData.end(); ++it )
    {
        int key = it.key();
        if ( key > maxStepIndex )
        {
            maxStepIndex = key;
        }
    }

    // 创建局部变量
    SokParser::DisCfgMap                 currentCfgMap;
    QSharedPointer< SokParser::DisData > currentDisData;
    int                                  currentDisLine = 0;
    QVector< SlzDataItem >               slzVector;     // 用于临时计算slz数据
    QVector< GussetDataItem >            gussetVector;  // 用于临时计算gusset数据

    // 从最小索引到最大索引依次遍历
    for ( int key = 0; key <= maxStepIndex; ++key )
    {
        // 新建元素
        QSharedPointer< CircleData > stepItem( new CircleData() );
        memset( stepItem.data(), 0, sizeof( CircleData ) );
        stepItem->circle_id = key;

        // 用于保存当前的step中有没有花型环节
        bool   hasPatternStep = false;
        quint8 countOfYarn = 0, countOfYOYOYarn = 0;

        // 检查当前索引是否存在，存在则处理数据
        if ( sokFileData->chainData.contains( key ) )
        {
            const SokParser::StepDataMap& value = sokFileData->chainData.value( key );

            // 遍历 StepDataMap 的值
            for ( SokParser::StepDataMap::const_iterator subIt = value.begin(); subIt != value.end(); ++subIt )
            {
                SokParser::ChainDataItem* subValue = subIt.value().data();  // 获取子值

                // 根据类型处理不同的数据
                switch ( subValue->type )
                {
                    case 1:  // 主电机速度和位置
                        stepItem->main_motor.start_pos = subValue->ItemUnion.chainSpeedParam.position;
                        stepItem->main_motor.speed     = subValue->ItemUnion.chainSpeedParam.speed;
                        break;

                    case 2:  // Economization
                    {
                        EconomizationItem item;
                        item.step_index = key;
                        item.forStep    = subValue->ItemUnion.economizationParam.forStep;
                        for ( int i = 0; i < 8; i++ )
                        {
                            item.economizations[ i ] = subValue->ItemUnion.economizationParam.economizations[ i ];
                        }
                        econoVector.append( item );
                    }
                    break;

                    case 5:  // 特殊功能
                        if ( stepItem->valve_count < 80 )
                        {
                            stepItem->valve_actions[ stepItem->valve_count ].valve_id =
                                mainData->readCfg->getSpecialFunList()->value( subValue->ItemUnion.specialFunctionParam.specialFunctionId ).mappedId;
                            stepItem->valve_actions[ stepItem->valve_count ].angle       = subValue->ItemUnion.specialFunctionParam.position;
                            stepItem->valve_actions[ stepItem->valve_count ].valve_state = subValue->ItemUnion.specialFunctionParam.status;
                            stepItem->valve_count++;
                        }
                        break;

                    case 6:  // 三角
                        if ( stepItem->valve_count < 80 )
                        {
                            stepItem->valve_actions[ stepItem->valve_count ].valve_id    = mainData->readCfg->getCamList()->value( subValue->ItemUnion.camParam.camId ).mappedId;
                            stepItem->valve_actions[ stepItem->valve_count ].angle       = subValue->ItemUnion.camParam.position;
                            stepItem->valve_actions[ stepItem->valve_count ].valve_state = subValue->ItemUnion.camParam.status;
                            stepItem->valve_count++;
                        }
                        break;

                    case 7:  // 主梭
                        countOfYarn++;
                        if ( countOfYarn <= 13 )
                        {
                            stepItem->main_finger[ countOfYarn - 1 ].id = mainData->readCfg->getYarnFingerList()->value( subValue->ItemUnion.fingerParam.fingerId ).mappedId;
                            if ( subValue->ItemUnion.fingerParam.status == 1 )
                            {
                                stepItem->main_finger[ countOfYarn - 1 ].work_on_angle = subValue->ItemUnion.fingerParam.position;
                            }
                            else if ( subValue->ItemUnion.fingerParam.status == 0 )
                            {
                                stepItem->main_finger[ countOfYarn - 1 ].work_off_angle = subValue->ItemUnion.fingerParam.position;
                            }
                        }
                        break;

                    case 8:  // 控制阀
                        if ( subValue->ItemUnion.controlValeParam.movement == '0' )
                        {
                            stepItem->motor_data[ 6 ].position = 0;
                            stepItem->motor_data[ 6 ].value    = 0;
                        }
                        else if ( subValue->ItemUnion.controlValeParam.movement == '1' )
                        {
                            stepItem->motor_data[ 6 ].position = 0;
                            stepItem->motor_data[ 6 ].value    = 72;
                        }
                        else if ( subValue->ItemUnion.controlValeParam.movement == '2' )
                        {
                            stepItem->motor_data[ 6 ].position = 0;
                            stepItem->motor_data[ 6 ].value    = 288;
                        }
                        else if ( subValue->ItemUnion.controlValeParam.movement == 'L' )
                        {
                            stepItem->motor_data[ 6 ].position = 0;
                            stepItem->motor_data[ 6 ].value    = subValue->ItemUnion.controlValeParam.step;
                        }
                        else if ( subValue->ItemUnion.controlValeParam.movement == 'R' )
                        {
                            stepItem->motor_data[ 6 ].position = 0;
                            stepItem->motor_data[ 6 ].value    = ( 288 + subValue->ItemUnion.controlValeParam.step ) % 360;
                        }
                        break;

                    case 9:  // 花型数据
                        hasPatternStep = true;
                        // 不管是进是出，全部要清空
                        if ( currentCfgMap.size() > 0 )
                            currentCfgMap.clear();
                        currentDisData.reset();
                        currentDisLine = 0;

                        // status为1是进In，为0是out
                        if ( subValue->ItemUnion.patternParam.status )
                        {
                            // 读取cfg文件
                            QString cfgFileName( subValue->ItemUnion.patternParam.codifyCode );
                            if ( cfgFileName.isEmpty() )
                            {
                                qDebug() << "cfg File is empty";
                            }
                            else
                            {
                                currentCfgMap = sokParser->getDisCfgMap( cfgFileName, 0 );
                            }

                            // 读取dis文件
                            QString disFileName( subValue->ItemUnion.patternParam.patternName[ currentSize ] );
                            if ( disFileName.isEmpty() )
                            {
                                qDebug() << "dis File is empty";
                            }
                            else
                            {
                                currentDisData = sokParser->getDisData( disFileName );
                            }

                            // 判断宽度是否与针数一致
                            if ( currentDisData && currentDisData->width < sokFileData->headParam->needleNum )
                            {
                                qDebug() << "dis width lower than needleNum";
                            }

                            // 生成第一行数据
                            if ( currentDisData )
                            {
                                for ( quint16 i = 0; i < sokFileData->headParam->needleNum; i++ )
                                {
                                    // 如果宽度小于针数，则要循环取余。大于时取余操作实际无用。
                                    stepItem->needle_data[ i ].selector = currentCfgMap.value( currentDisData->drumData[ i % currentDisData->width ] );
                                    // stepItem->needle_data[ i ].nozzle   = static_cast< quint8 >( currentDisData->yarnfingerData[ i % currentDisData->width ] ) > 0
                                    //                                         ? currentCfgMap.value( currentDisData->yarnfingerData[ i % currentDisData->width ] )
                                    //                                         : 0;
                                }
                                currentDisLine++;
                            }

                            // 将currentDisData->yarnfingerData数据中的前6个放入fu_suo中
                            for ( int i = 0; i < 6; i++ )
                            {
                                stepItem->fu_suo[ i ] = currentCfgMap.value( i );
                            }
                        }
                        break;

                    case 10:  // 橡筋花型数据
                        hasPatternStep = true;
                        // 不管是进是出，全部要清空
                        if ( currentCfgMap.size() > 0 )
                            currentCfgMap.clear();
                        currentDisData.reset();
                        currentDisLine = 0;
                        // status为1是进In，为0是out
                        if ( subValue->ItemUnion.elasticPatternParam.status )
                        {
                            // 读取cfg文件
                            QString cfgFileName( subValue->ItemUnion.elasticPatternParam.codifyCode );
                            if ( cfgFileName == "" )
                            {
                                qDebug() << "cfg File is empty";
                            }
                            else
                            {
                                currentCfgMap = sokParser->getDisCfgMap( cfgFileName, 0 );
                            }
                            // 读取dis文件
                            QString disFileName( subValue->ItemUnion.elasticPatternParam.patternName[ currentSize ] );
                            if ( disFileName == "" )
                            {
                                qDebug() << "dis File is empty";
                            }
                            else
                            {
                                currentDisData = sokParser->getDisData( disFileName );
                            }
                            // 判断宽度是否与针数一致
                            if ( currentDisData->width < sokFileData->headParam->needleNum )
                            {
                                qDebug() << "dis width lower than needleNum";
                            }
                            // 生成第一行数据
                            for ( quint16 i = 0; i < sokFileData->headParam->needleNum; i++ )
                            {
                                // 如果宽度小于针数，则要循环取余。大于时取余操作实际无用。
                                stepItem->needle_data[ i ].selector = currentCfgMap.value( currentDisData->drumData[ i % currentDisData->width ] );
                                // stepItem->needle_data[ i ].nozzle   = static_cast< quint8 >( currentDisData->yarnfingerData[ i % currentDisData->width ] ) > 0
                                //                                         ? currentCfgMap.value( currentDisData->yarnfingerData[ i % currentDisData->width ] )
                                //                                         : 0;
                            }
                            currentDisLine++;
                        }
                        break;

                    case 16:  // 提升盘电机
                        stepItem->motor_data[ 5 ].position = subValue->ItemUnion.raisingDialMotorParam.position;
                        if ( subValue->ItemUnion.raisingDialMotorParam.movementCode == 'T' )
                        {
                            stepItem->motor_data[ 5 ].value = sokFileData->headParam->dialMotorPos[ subValue->ItemUnion.raisingDialMotorParam.motorSteps ];
                        }
                        else if ( subValue->ItemUnion.raisingDialMotorParam.movementCode == 'A' )
                        {
                            stepItem->motor_data[ 5 ].value = subValue->ItemUnion.raisingDialMotorParam.motorSteps;
                        }
                        break;
                    case 11:
                        break;
                    case 12:
                        break;
                    case 13:
                        break;
                    case 17:
                        break;
                    case 18:  // YOYO纱线
                        countOfYOYOYarn++;
                        stepItem->yoyo_yarn_count = countOfYOYOYarn;
                        if ( countOfYOYOYarn <= 8 )
                        {
                            stepItem->yoyo_yarn[ countOfYOYOYarn - 1 ].motor     = subValue->ItemUnion.yoyoYarnWeightParam.motor;
                            stepItem->yoyo_yarn[ countOfYOYOYarn - 1 ].action    = subValue->ItemUnion.yoyoYarnWeightParam.action;
                            stepItem->yoyo_yarn[ countOfYOYOYarn - 1 ].variation = subValue->ItemUnion.yoyoYarnWeightParam.variation;
                            stepItem->yoyo_yarn[ countOfYOYOYarn - 1 ].position  = subValue->ItemUnion.yoyoYarnWeightParam.position;
                        }
                        break;

                        // 其他类型的处理可以根据需要添加
                    case 14:  // 固定选针数据
                              // status为1是进In，为0是out
                        if ( subValue->ItemUnion.selectionParam.status )
                        {
                            // 读取cfg文件
                            QString cfgFileName( subValue->ItemUnion.selectionParam.name );
                            if ( cfgFileName == "" )
                            {
                                qDebug() << "slz File is empty";
                            }
                            else
                            {
                                QString currentSlz = sokParser->getSlzCfg( cfgFileName );
                                if ( currentSlz != "" )
                                {
                                    // 根据'\n'分割字符串，将前两个分别放入data数组的前2个位置
                                    QStringList slzList = currentSlz.split( '\n', QString::SkipEmptyParts );

                                    if ( slzList.size() == 1 )
                                    {
                                        // 只有1个元素时，直接将数据插入stepItem->needle_temp_data
                                        if ( stepItem->needle_change_count < 10 )
                                        {
                                            // 获取当前needle_temp_data的索引
                                            quint8 tempIndex = stepItem->needle_change_count;

                                            // 设置角度：使用position
                                            stepItem->needle_temp_data[ tempIndex ].angle = subValue->ItemUnion.selectionParam.position;

                                            // 根据drum移位得到选针器
                                            stepItem->needle_temp_data[ tempIndex ].selector = ( 1 << subValue->ItemUnion.selectionParam.drum );

                                            // 预处理数据并生成value
                                            QString currentData = slzList[ 0 ];
                                            padDataTo8Bits( currentData );

                                            quint16 value = 0;
                                            // 直接遍历8位数据
                                            for ( int bitIndex = 0; bitIndex < 8 && bitIndex < currentData.size(); bitIndex++ )
                                            {
                                                if ( currentData.at( bitIndex ) == '1' )
                                                {
                                                    value |= ( 1 << bitIndex );  // 设置对应位为1
                                                }
                                            }

                                            stepItem->needle_temp_data[ tempIndex ].value = value;

                                            // 增加needle_change_count
                                            stepItem->needle_change_count++;
                                        }
                                    }
                                    else if ( slzList.size() >= 2 )
                                    {
                                        // 有2个或更多元素时，需要循环变更，加入slzVector
                                        SlzDataItem slzItem;
                                        slzItem.data[ 0 ] = slzList[ 0 ];
                                        slzItem.data[ 1 ] = slzList[ 1 ];

                                        // 预处理：将data数组的元素补足8位（如果不为空）
                                        for ( int dataIndex = 0; dataIndex < 2; dataIndex++ )
                                        {
                                            padDataTo8Bits( slzItem.data[ dataIndex ] );
                                        }

                                        slzItem.drum        = subValue->ItemUnion.selectionParam.drum;
                                        slzItem.position    = subValue->ItemUnion.selectionParam.position;
                                        slzItem.currentLine = 0;  // 初始化为使用第一行数据
                                        slzVector.append( slzItem );
                                    }
                                }
                            }
                        }
                        else
                        {
                            // 遍历 slzVector 查找匹配的元素
                            for ( int i = 0; i < slzVector.size(); ++i )
                            {
                                if ( slzVector[ i ].drum == subValue->ItemUnion.selectionParam.drum )
                                {
                                    // 如果找到匹配的元素，则删除它
                                    slzVector.removeAt( i );
                                    break;  // 找到后即可退出循环
                                }
                            }
                        }
                        break;
                    case 15:
                        // status为1是进In，为0是out
                        if ( subValue->ItemUnion.gussetParam.status )
                        {
                            // 读取cfg文件
                            QString cfgFileName( subValue->ItemUnion.gussetParam.name );
                            if ( cfgFileName == "" )
                            {
                                qDebug() << "gusset File is empty";
                            }
                            else
                            {
                                QString currentTas = sokParser->getTasCfg( cfgFileName );
                                if ( currentTas != "" )
                                {
                                    GussetDataItem gusItem;
                                    // 根据'\n'分割字符串，将前两个分别放入data数组的前2个位置
                                    QStringList tasList = currentTas.split( '\n', QString::SkipEmptyParts );
                                    gusItem.data[ 0 ]   = tasList.size() > 0 ? tasList[ 0 ] : "";
                                    gusItem.data[ 1 ]   = tasList.size() > 1 ? tasList[ 1 ] : "";
                                    gusItem.drum        = subValue->ItemUnion.gussetParam.drum;
                                    gusItem.position    = subValue->ItemUnion.gussetParam.position;
                                    gusItem.quantity    = subValue->ItemUnion.gussetParam.quantity;
                                    gusItem.piority     = subValue->ItemUnion.gussetParam.piority;
                                    gusItem.currentLine = 0;  // 初始化为使用第一行数据
                                    gussetVector.append( gusItem );
                                }
                            }
                        }
                        else
                        {
                            // 遍历 slzVector 查找匹配的元素
                            for ( int i = 0; i < gussetVector.size(); ++i )
                            {
                                if ( gussetVector[ i ].drum == subValue->ItemUnion.gussetParam.drum && gussetVector[ i ].piority == subValue->ItemUnion.gussetParam.piority )
                                {
                                    // 如果找到匹配的元素，则删除它
                                    gussetVector.removeAt( i );
                                    break;  // 找到后即可退出循环
                                }
                            }
                        }
                        break;
                }
            }
        }

        // 如果当前步骤没有花型环节，但是currentDisLine不为0，则需要继续计算花型
        if ( !hasPatternStep && currentDisLine > 0 && currentDisLine < currentDisData->height )
        {
            // 生成下一行数据
            for ( quint16 i = 0; i < sokFileData->headParam->needleNum; i++ )
            {
                // 如果宽度小于针数，则要循环取余
                int index                           = i % currentDisData->width + currentDisLine * currentDisData->width;
                stepItem->needle_data[ i ].selector = currentCfgMap.value( currentDisData->drumData[ index ] );
                // stepItem->needle_data[ i ].nozzle   = static_cast< quint8 >( currentDisData->yarnfingerData[ index ] ) > 0 ? currentCfgMap.value( currentDisData->yarnfingerData[ index ] ) : 0;
            }
            currentDisLine++;

            // 如果已经到达花型的最后一行，则重置
            if ( currentDisLine >= currentDisData->height )
            {
                currentDisLine = 0;
            }
        }
        // 固定选针 - 使用needle_temp_data
        if ( slzVector.size() > 0 )
        {
            // 遍历slz数据集，不应该出现drum相同的情况
            for ( int i = 0; i < slzVector.size(); ++i )
            {
                // 确保needle_change_count不超过数组大小
                if ( stepItem->needle_change_count < 10 )
                {
                    // 获取当前needle_temp_data的索引
                    quint8 tempIndex = stepItem->needle_change_count;

                    // 设置角度：使用position
                    stepItem->needle_temp_data[ tempIndex ].angle = slzVector[ i ].position;

                    // 根据drum移位得到选针器
                    stepItem->needle_temp_data[ tempIndex ].selector = ( 1 << slzVector[ i ].drum );

                    // 根据data数组的当前行元素得到value（已经预处理为8位）
                    QString currentData = slzVector[ i ].data[ slzVector[ i ].currentLine ];
                    quint16 value       = 0;

                    // 直接遍历8位数据（已经在预处理阶段补全）
                    for ( int bitIndex = 0; bitIndex < 8 && bitIndex < currentData.size(); bitIndex++ )
                    {
                        if ( currentData.at( bitIndex ) == '1' )
                        {
                            value |= ( 1 << bitIndex );  // 设置对应位为1
                        }
                        // 如果是'0'或其他字符，对应位保持为0
                    }

                    stepItem->needle_temp_data[ tempIndex ].value = value;

                    // 增加needle_change_count
                    stepItem->needle_change_count++;

                    // 切换到下一行数据（循环使用）
                    slzVector[ i ].currentLine = ( slzVector[ i ].currentLine + 1 ) % 2;
                }
            }
        }
        // Gusset - 按优先级处理
        if ( gussetVector.size() > 0 )
        {
            // 按优先级排序处理
            std::sort( gussetVector.begin(), gussetVector.end(), []( const GussetDataItem& a, const GussetDataItem& b ) { return a.piority < b.piority; } );

            for ( int i = 0; i < gussetVector.size(); ++i )
            {
                // 根据data数组的两行是否都不为空来决定使用哪行数据
                QString gussetData;
                if ( !gussetVector[ i ].data[ 0 ].isEmpty() && !gussetVector[ i ].data[ 1 ].isEmpty() )
                {
                    // 两行都不为空时，使用currentLine轮流切换
                    gussetData = gussetVector[ i ].data[ gussetVector[ i ].currentLine ];
                    // 切换到下一行数据（循环使用）
                    gussetVector[ i ].currentLine = ( gussetVector[ i ].currentLine + 1 ) % 2;
                }
                else
                {
                    // 只有一行或其中一行为空时，使用非空的那行
                    gussetData = !gussetVector[ i ].data[ 0 ].isEmpty() ? gussetVector[ i ].data[ 0 ] : gussetVector[ i ].data[ 1 ];
                }

                quint8 dataSize = gussetData.size();
                if ( dataSize > 0 )
                {
                    // 计算起始针位置
                    quint16 startNeedle = gussetVector[ i ].position / 360.0 * sokFileData->headParam->needleNum;
                    quint16 endNeedle   = startNeedle + gussetVector[ i ].quantity;

                    // 确保不超过针数范围
                    if ( endNeedle > sokFileData->headParam->needleNum )
                    {
                        endNeedle = sokFileData->headParam->needleNum;
                    }

                    // 根据drum设置选针器位
                    quint16 drumSelector = ( 1 << gussetVector[ i ].drum );

                    // 从位置position开始，连续quantity个针按data来修改
                    for ( quint16 j = startNeedle; j < endNeedle; j++ )
                    {
                        // 循环判断是'0'或者'1'
                        QChar dataChar = gussetData.at( ( j - startNeedle ) % dataSize );
                        if ( dataChar == '1' )
                        {
                            // 设置对应的drum位
                            stepItem->needle_data[ j ].selector |= drumSelector;
                        }
                        else if ( dataChar == '0' )
                        {
                            // 清除对应的drum位
                            stepItem->needle_data[ j ].selector &= ~drumSelector;
                        }
                        // 其他字符不处理
                    }
                }
            }
        }

        // 将处理好的步骤数据添加到映射中
        stepDataMap.insert( key, stepItem );
    }

    // 二次遍历：处理Function功能
    for ( auto it = sokFileData->chainData.begin(); it != sokFileData->chainData.end(); ++it )
    {
        int                           currentStepIndex = it.key();
        const SokParser::StepDataMap& stepDataMap_temp = it.value();

        // 遍历当前步骤中的所有数据项
        for ( SokParser::StepDataMap::const_iterator subIt = stepDataMap_temp.begin(); subIt != stepDataMap_temp.end(); ++subIt )
        {
            SokParser::ChainDataItem* subValue = subIt.value().data();

            // 只处理type为3的Function数据
            if ( subValue->type == 3 )
            {
                quint16 functionPram = subValue->ItemUnion.functionPram;
                // 从mainData->readCfg->getFunctionList()中查找对应的Function
                if ( mainData->readCfg->getFunctionList()->contains( functionPram ) )
                {
                    const auto& function = mainData->readCfg->getFunctionList()->value( functionPram );

                    // 遍历Function的actions
                    for ( const auto& action : function.actions )
                    {
                        // 计算目标步骤索引
                        int targetStepIndex = currentStepIndex + action.step;

                        // 检查目标步骤是否在有效范围内
                        if ( targetStepIndex < 0 || targetStepIndex > maxStepIndex || !stepDataMap.contains( targetStepIndex ) )
                        {
                            continue;  // 跳过无效的步骤索引
                        }

                        // 获取目标步骤的数据
                        QSharedPointer< CircleData > targetStepItem = stepDataMap.value( targetStepIndex );

                        // 根据action的type处理不同类型的动作
                        switch ( action.type )
                        {
                            case 0:  // 气阀动作
                            {
                                // 确保valve_count不超过数组大小
                                if ( targetStepItem->valve_count < 80 )
                                {
                                    targetStepItem->valve_actions[ targetStepItem->valve_count ].valve_id    = action.id;
                                    targetStepItem->valve_actions[ targetStepItem->valve_count ].angle       = action.position;  // position赋值给angle
                                    targetStepItem->valve_actions[ targetStepItem->valve_count ].valve_state = action.value;     // value赋值给valve_state
                                    targetStepItem->valve_count++;
                                }
                                break;
                            }
                            case 1:  // 电机数据
                            {
                                // 确保id在有效范围内
                                if ( action.id >= 0 && action.id < 9 )
                                {
                                    targetStepItem->motor_data[ action.id ].position = action.position;  // position赋值给position
                                    targetStepItem->motor_data[ action.id ].value    = action.value;     // value值的含义是速度
                                }
                                break;
                            }
                            case 2:  // 主电机
                            {
                                targetStepItem->main_motor.start_pos = action.position;  // position赋值给start_pos
                                targetStepItem->main_motor.speed     = action.value;     // value赋值给speed
                                targetStepItem->main_motor.delay     = action.delay;     // delay赋值给delay
                                break;
                            }
                            case 3:  // 选针器 - 加入needle_temp_data
                            {
                                // 确保needle_change_count不超过数组大小
                                if ( targetStepItem->needle_change_count < 10 )
                                {
                                    // 获取当前needle_temp_data的索引
                                    quint8 tempIndex = targetStepItem->needle_change_count;

                                    // 设置角度：直接使用action.position
                                    targetStepItem->needle_temp_data[ tempIndex ].angle = action.position;

                                    // 处理选针器逻辑
                                    if ( action.id == 0 )
                                    {
                                        // id为0时：selector为0，value值每1位表示选针器的变更情况，1为全上0为全下
                                        targetStepItem->needle_temp_data[ tempIndex ].selector = 0;
                                        targetStepItem->needle_temp_data[ tempIndex ].value    = action.value;
                                    }
                                    else
                                    {
                                        // id不为0时：selector的第(id-1)位设为1表示该选针器需要变更
                                        // value值每1位表示该选针器的刀片状态，1为上0为下
                                        targetStepItem->needle_temp_data[ tempIndex ].selector = action.id;
                                        targetStepItem->needle_temp_data[ tempIndex ].value    = action.value;
                                    }

                                    // 增加needle_change_count
                                    targetStepItem->needle_change_count++;
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    emit stepDataProcessCompleted( true );
    return true;
}

int SokBusiness::getMaxStepIndex() const
{
    return maxStepIndex;
}

QSharedPointer< CircleData > SokBusiness::getStepData( int stepIndex )
{
    if ( stepDataMap.contains( stepIndex ) )
    {
        return stepDataMap.value( stepIndex );
    }

    return QSharedPointer< CircleData >();
}

QSharedPointer< CircleData > SokBusiness::getCurrentStepData()
{
    if ( stepDataMap.contains( currentRunningStepIdx ) )
    {
        return stepDataMap.value( currentRunningStepIdx );
    }

    return QSharedPointer< CircleData >();
}

// 获取模块信息
SokBusiness::ModuleDataItem SokBusiness::getModuleInfo()
{
    // 遍历模块数据向量
    ModuleDataItem moduleData;
    moduleData.moduleName = "";
    moduleData.startStep  = 0;
    moduleData.endStep    = 0;
    // 遍历模块数据向量
    // 找当前位于那个域 module
    for ( int i = 0; i < sokFileData->moduleDataVector.size(); ++i )
    {
        if ( sokFileData->moduleDataVector[ i ].data()->startStepIndex <= currentRunningStepIdx && sokFileData->moduleDataVector[ i ].data()->endStepIndex >= currentRunningStepIdx )
        {
            // 直接从GBK编码的char数组创建QString
            QTextCodec* codec      = QTextCodec::codecForName( "GBK" );
            QString     moduleName = codec->toUnicode( sokFileData->moduleDataVector[ i ].data()->name );

            moduleData.moduleName = moduleName;
            moduleData.startStep  = sokFileData->moduleDataVector[ i ].data()->startStepIndex;
            moduleData.endStep    = sokFileData->moduleDataVector[ i ].data()->endStepIndex;

            break;
        }
    }
    return moduleData;
}

int SokBusiness::getSpeedSet()
{
    if ( sokFileData->chainData.contains( currentRunningStepIdx ) )
    {
        SokParser::StepDataMap stepMap = sokFileData->chainData.value( currentRunningStepIdx );
        for ( SokParser::StepDataMap::const_iterator subIt = stepMap.begin(); subIt != stepMap.end(); ++subIt )
        {
            SokParser::ChainDataItem* subValue = subIt.value().data();
            if ( subValue->type == 1 )
            {  // Speed set
                return subValue->ItemUnion.chainSpeedParam.speed;
            }
        }
    }

    return 0;
}

int SokBusiness::getDensityMotorSpeed( int motorIndex )
{
    if ( stepDataMap.contains( currentRunningStepIdx ) )
    {
        QSharedPointer< CircleData > stepItem = stepDataMap.value( currentRunningStepIdx );

        // 根据电机索引返回对应的速度
        switch ( motorIndex )
        {
            case 0:  // 密度电机1
                return stepItem->motor_data[ 3 ].value;
            case 1:  // 密度电机2
                return stepItem->motor_data[ 8 ].value;
            case 2:  // 密度电机3
                return stepItem->motor_data[ 7 ].value;
            default:
                return 0;
        }
    }

    return 0;
}

void SokBusiness::getAndCheckNextCircle()
{
    // 计算下一圈
    calculateNextCircle();

    // 检查是否越界
    if ( currentRunningStepIdx < 0 || currentRunningStepIdx > maxStepIndex )
    {
        // 触发完成信号
        emit AllStepDataSendSuccess();
        // 重置索引
        currentRunningStepIdx = -1;
        // 计算下一圈 which is circle 0
        calculateNextCircle();
    }
}

void SokBusiness::calculateNextCircle()
{
    // 循环检查当前圈是否在经济化列表中
    bool isInEconomization = false;
    int  i                 = 0;
    for ( i = 0; i < econoVector.size(); i++ )
    {
        if ( econoVector[ i ].step_index == currentRunningStepIdx )
        {
            isInEconomization    = true;
            economizationSetting = econoVector[ i ].economizations[ currentSize ];
            break;
        }
    }
    if ( !isInEconomization )
    {
        currentRunningStepIdx = currentRunningStepIdx + 1;  // 不在经济化列表中，直接返回下一圈
        currentEconomization  = 0;
        economizationSetting  = 0;
        return;
    }

    // 在经济化列表中的处理
    if ( econoVector[ i ].forStep == 255 )
    {
        // 普通经济化模式
        currentEconomization++;  // 增加当前经济化次数

        // 检查是否达到经济化次数上限
        if ( currentEconomization >= econoVector[ i ].economizations[ currentSize ] )
        {
            currentEconomization  = 0;                          // 重置经济化计数
            currentRunningStepIdx = currentRunningStepIdx + 1;  // 进入下一圈
            return;
        }
    }
    else
    {
        // forStep > 0 的情况
        // 检查是否达到经济化次数上限
        if ( currentEconomization >= econoVector[ i ].economizations[ currentSize ] )
        {
            currentEconomization  = 0;  // 重置经济化计数
            currentRunningStepIdx = currentRunningStepIdx + econoVector[ i ].forStep + 1;
            return;
        }
        else
        {
            // 计算下一个圈号
            int nextCircle = currentRunningStepIdx + 1;

            // 如果下一个圈号超出了范围，返回初始圈号
            if ( nextCircle >= currentRunningStepIdx + econoVector[ i ].forStep )
            {
                currentEconomization++;  // 增加当前经济化次数
                currentRunningStepIdx = econoVector[ i ].step_index;
                return;
            }
            currentRunningStepIdx = nextCircle;
        }
    }
}

int SokBusiness::getCurrentRunningStepIdx() const
{
    return currentRunningStepIdx;
}

int SokBusiness::getCurrentEconomization() const
{
    return currentEconomization;
}

int SokBusiness::getEconomizationSetting() const
{
    return economizationSetting;
}

bool SokBusiness::sendNextStepData()
{
    // 检查通信操作对象是否已设置
    if ( !m_commOperations )
    {
        qDebug() << "CommOperations object not set";
        return false;
    }

    // 获取当前步骤的数据
    QSharedPointer< CircleData > circleData = getStepData( currentRunningStepIdx );

    if ( !circleData )
    {
        qDebug() << "No circle data found for step index:" << currentRunningStepIdx;
        return false;
    }

    // 调用 CommOperations 的 sendStepData 函数发送数据
    m_commOperations->sendStepData( circleData.data() );

    return true;
}

void SokBusiness::resetCurrentRunningStepIdx()
{
    currentRunningStepIdx = -1;
}

void SokBusiness::decrementCurrentRunningStepIdx()
{
    // 减2到上上一圈，便于再执行getAndCheckNextCircle()时，跳到上一圈
    currentRunningStepIdx -= 2;
    if ( currentRunningStepIdx < 0 )
    {
        currentRunningStepIdx = -1;
    }
}

// 获取当前步骤的getSpecialFunctionStr
QString SokBusiness::getSpecialFunctionStr()
{
    QMutexLocker locker( &mutex );

    QString                       functionStr;
    const SokParser::StepDataMap& stepData = sokFileData->chainData.value( currentRunningStepIdx );
    for ( SokParser::StepDataMap::const_iterator subIt = stepData.begin(); subIt != stepData.end(); ++subIt )
    {
        SokParser::ChainDataItem* subValue = subIt.value().data();  // 获取子值
        if ( subValue->type == 3 )
        {
            functionStr += QString( "%1 " ).arg( subValue->ItemUnion.functionPram );
        }
    }
    return functionStr;
}

bool SokBusiness::setControlFlag( quint16 flagBit, bool value )
{
    QMutexLocker locker( &mutex );

    // 获取当前步骤的圈数据
    QSharedPointer< CircleData > circleData = getCurrentStepData();
    if ( !circleData )
    {
        qDebug() << "Failed to set control flag: No current step data";
        return false;
    }

    // 根据flagBit设置对应的控制标志位
    switch ( flagBit )
    {
        case 0:  // need_stop
            circleData->control_flags.bits.need_stop = value ? 1 : 0;
            break;
        case 1:  // need_slow
            circleData->control_flags.bits.need_slow = value ? 1 : 0;
            break;
        case 2:  // check_yarn
            circleData->control_flags.bits.check_yarn = value ? 1 : 0;
            break;
        case 3:  // check_needle
            circleData->control_flags.bits.check_needle = value ? 1 : 0;
            break;
        case 4:  // is_critical
            circleData->control_flags.bits.is_critical = value ? 1 : 0;
            break;
        case 5:  // is_running
            circleData->control_flags.bits.is_running = value ? 1 : 0;
            break;
        case 6:  // is_reset
            circleData->control_flags.bits.is_reset = value ? 1 : 0;
            break;
        case 7:  // stop_when_finish
            circleData->control_flags.bits.stop_when_finish = value ? 1 : 0;
            break;
        default:
            qDebug() << "Invalid control flag bit:" << flagBit;
            return false;
    }

    // 如果需要，可以在这里添加发送更新后的数据到下位机的代码
    // 例如：m_commOperations->sendCircleData(circleData.data());

    return true;
}

// 将字符串数据补足8位的通用函数
void SokBusiness::padDataTo8Bits( QString& data )
{
    // 如果数据为空，不进行处理
    if ( data.isEmpty() )
    {
        return;
    }

    // 如果数据不足8位，按照循环方式补全到8位
    if ( data.size() < 8 )
    {
        QString originalData = data;
        data.clear();
        for ( int bitIndex = 0; bitIndex < 8; bitIndex++ )
        {
            data += originalData.at( bitIndex % originalData.size() );
        }
    }
    else if ( data.size() > 8 )
    {
        // 如果超过8位，截取前8位
        data = data.left( 8 );
    }
    // 如果正好8位，保持不变
}
