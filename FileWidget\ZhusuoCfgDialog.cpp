#include "ZhusuoCfgDialog.h"
#include "ui_ZhusuoCfgDialog.h"

ZhusuoCfgDialog::ZhusuoCfgDialog( QWidget* parent, FATParser::PatternExtraData* patternExtra ) : QDialog( parent ), ui( new Ui::ZhusuoCfgDialog )
{
    ui->setupUi( this );

    if ( patternExtra != nullptr )
    {
        this->patternExtraData = patternExtra;
        memcpy( &this->zhusuo[ 0 ], &patternExtra->zhusuo[ 0 ], sizeof( FATParser::Zhusuo ) * 7 );
        memcpy( &this->zhusuo_num_cfg, &patternExtra->zhusuo_num_cfg, sizeof( FATParser::ZhusoNumCfg ) );
    }

    ui->tableWidget->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    ui->tableWidget->setColumnCount( 10 );
    QStringList headerList;
    headerList << "ID"
               << "R1"
               << "R2"
               << "R3"
               << "R4"
               << "R5"
               << "R6"
               << "R7"
               << "R8"
               << "RM";
    ui->tableWidget->setHorizontalHeaderLabels( headerList );

    // 填充数据
    ui->tableWidget->setRowCount( 7 );
    for ( int i = 0; i < 7; i++ )
    {
        QTableWidgetItem* coloredTextItem = new QTableWidgetItem( QString::number( i ) );
        coloredTextItem->setTextAlignment( Qt::AlignCenter );
        coloredTextItem->setForeground( Qt::black );
        coloredTextItem->setBackground( ZhusuoColorMap[ i ] );
        ui->tableWidget->setItem( i, 0, coloredTextItem );

        QCheckBox* checkbox1 = new QCheckBox();
        checkbox1->setChecked( this->zhusuo[ i ].R1 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout1 = new QHBoxLayout();
        QWidget*     widget1  = new QWidget( ui->tableWidget );
        hLayout1->addWidget( checkbox1 );
        hLayout1->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout1->setAlignment( checkbox1, Qt::AlignCenter );
        widget1->setLayout( hLayout1 );
        ui->tableWidget->setCellWidget( i, 1, widget1 );
        checkbox1->setProperty( "row_index", i );
        checkbox1->setProperty( "col_index", 1 );
        connect( checkbox1, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox2 = new QCheckBox();
        checkbox2->setChecked( this->zhusuo[ i ].R2 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout2 = new QHBoxLayout();
        QWidget*     widget2  = new QWidget( ui->tableWidget );
        hLayout2->addWidget( checkbox2 );
        hLayout2->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout2->setAlignment( checkbox2, Qt::AlignCenter );
        widget2->setLayout( hLayout2 );
        ui->tableWidget->setCellWidget( i, 2, widget2 );
        checkbox2->setProperty( "row_index", i );
        checkbox2->setProperty( "col_index", 2 );
        connect( checkbox2, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox3 = new QCheckBox();
        checkbox3->setChecked( this->zhusuo[ i ].R3 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout3 = new QHBoxLayout();
        QWidget*     widget3  = new QWidget( ui->tableWidget );
        hLayout3->addWidget( checkbox3 );
        hLayout3->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout3->setAlignment( checkbox3, Qt::AlignCenter );
        widget3->setLayout( hLayout3 );
        ui->tableWidget->setCellWidget( i, 3, widget3 );
        checkbox3->setProperty( "row_index", i );
        checkbox3->setProperty( "col_index", 3 );
        connect( checkbox3, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox4 = new QCheckBox();
        checkbox4->setChecked( this->zhusuo[ i ].R4 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout4 = new QHBoxLayout();
        QWidget*     widget4  = new QWidget( ui->tableWidget );
        hLayout4->addWidget( checkbox4 );
        hLayout4->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout4->setAlignment( checkbox4, Qt::AlignCenter );
        widget4->setLayout( hLayout4 );
        ui->tableWidget->setCellWidget( i, 4, widget4 );
        checkbox4->setProperty( "row_index", i );
        checkbox4->setProperty( "col_index", 4 );
        connect( checkbox4, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox5 = new QCheckBox();
        checkbox5->setChecked( this->zhusuo[ i ].R5 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout5 = new QHBoxLayout();
        QWidget*     widget5  = new QWidget( ui->tableWidget );
        hLayout5->addWidget( checkbox5 );
        hLayout5->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout5->setAlignment( checkbox5, Qt::AlignCenter );
        widget5->setLayout( hLayout5 );
        ui->tableWidget->setCellWidget( i, 5, widget5 );
        checkbox5->setProperty( "row_index", i );
        checkbox5->setProperty( "col_index", 5 );
        connect( checkbox5, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox6 = new QCheckBox();
        checkbox6->setChecked( this->zhusuo[ i ].R6 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout6 = new QHBoxLayout();
        QWidget*     widget6  = new QWidget( ui->tableWidget );
        hLayout6->addWidget( checkbox6 );
        hLayout6->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout6->setAlignment( checkbox6, Qt::AlignCenter );
        widget6->setLayout( hLayout6 );
        ui->tableWidget->setCellWidget( i, 6, widget6 );
        checkbox6->setProperty( "row_index", i );
        checkbox6->setProperty( "col_index", 6 );
        connect( checkbox6, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox7 = new QCheckBox();
        checkbox7->setChecked( this->zhusuo[ i ].R7 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout7 = new QHBoxLayout();
        QWidget*     widget7  = new QWidget( ui->tableWidget );
        hLayout7->addWidget( checkbox7 );
        hLayout7->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout7->setAlignment( checkbox7, Qt::AlignCenter );
        widget7->setLayout( hLayout7 );
        ui->tableWidget->setCellWidget( i, 7, widget7 );
        checkbox7->setProperty( "row_index", i );
        checkbox7->setProperty( "col_index", 7 );
        connect( checkbox7, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox8 = new QCheckBox();
        checkbox8->setChecked( this->zhusuo[ i ].R8 == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout8 = new QHBoxLayout();
        QWidget*     widget8  = new QWidget( ui->tableWidget );
        hLayout8->addWidget( checkbox8 );
        hLayout8->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout8->setAlignment( checkbox8, Qt::AlignCenter );
        widget8->setLayout( hLayout8 );
        ui->tableWidget->setCellWidget( i, 8, widget8 );
        checkbox8->setProperty( "row_index", i );
        checkbox8->setProperty( "col_index", 8 );
        connect( checkbox8, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );

        QCheckBox* checkbox9 = new QCheckBox();
        checkbox9->setChecked( this->zhusuo[ i ].RM == 0 ? Qt::Unchecked : Qt::Checked );
        QHBoxLayout* hLayout9 = new QHBoxLayout();
        QWidget*     widget9  = new QWidget( ui->tableWidget );
        hLayout9->addWidget( checkbox9 );
        hLayout9->setMargin( 0 );  // 必须添加, 否则CheckBox不能正常显示
        hLayout9->setAlignment( checkbox9, Qt::AlignCenter );
        widget9->setLayout( hLayout9 );
        ui->tableWidget->setCellWidget( i, 9, widget9 );
        checkbox9->setProperty( "row_index", i );
        checkbox9->setProperty( "col_index", 9 );
        connect( checkbox9, &QCheckBox::stateChanged, this, &ZhusuoCfgDialog::onCheckBoxStateChanged );
    }

    // 填充提前量和重叠量
    ui->le_advanceQ->setText( QString::number( this->zhusuo_num_cfg.advance_quantity ) );
    ui->le_overlapQ->setText( QString::number( this->zhusuo_num_cfg.overlap_quantity ) );
    ui->le_advanceQ->setProperty( "widget", "advance" );
    ui->le_overlapQ->setProperty( "widget", "overlap" );
    connect( ui->le_advanceQ, &MyLineEdit::mouseRelease, this, &ZhusuoCfgDialog::onShowNumberInputForm );
    connect( ui->le_overlapQ, &MyLineEdit::mouseRelease, this, &ZhusuoCfgDialog::onShowNumberInputForm );

    // 按键
    connect( ui->pbtn_ok, &QPushButton::clicked, this, [ & ]() {
        // save data
        memcpy( &this->patternExtraData->zhusuo[ 0 ], &this->zhusuo[ 0 ], sizeof( FATParser::Zhusuo ) * 7 );
        memcpy( &this->patternExtraData->zhusuo_num_cfg, &this->zhusuo_num_cfg, sizeof( FATParser::ZhusoNumCfg ) );
        this->close();
    } );
    connect( ui->pbtn_cancel, &QPushButton::clicked, this, [ & ]() { this->close(); } );
}

ZhusuoCfgDialog::~ZhusuoCfgDialog()
{
    delete ui;
    delete this->numberInputFrm;
    this->numberInputFrm = nullptr;
}

void ZhusuoCfgDialog::onShowNumberInputForm()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    if ( numberInputFrm == nullptr )
    {
        numberInputFrm = new NumberInputForm();
        connect( this->numberInputFrm, &NumberInputForm::InputFinished, this, &ZhusuoCfgDialog::onNumberInputFormFinished );
    }
    numberInputFrm->show();
}

void ZhusuoCfgDialog::onNumberInputFormFinished( QString str )
{
    if ( this->currentSelectedMyLineEdit != nullptr )
    {
        this->currentSelectedMyLineEdit->setText( str );
        QVariant var = this->currentSelectedMyLineEdit->property( "widget" );
        if ( var.isValid() )
        {
            QString widget = var.toString();
            if ( widget == "advance" )
            {
                this->zhusuo_num_cfg.advance_quantity = str.toInt();
            }
            else if ( widget == "overlap" )
            {
                this->zhusuo_num_cfg.overlap_quantity = str.toInt();
            }
        }
    }
}

void ZhusuoCfgDialog::onCheckBoxStateChanged( int state )
{
    QCheckBox* senderCheckBox = qobject_cast< QCheckBox* >( sender() );
    QVariant   var_row        = senderCheckBox->property( "row_index" );
    QVariant   var_col        = senderCheckBox->property( "col_index" );
    if ( var_row.isValid() && var_col.isValid() )
    {
        int row_index = var_row.toInt();
        int col_index = var_col.toInt();
        if ( col_index == 1 )
        {
            this->zhusuo[ row_index ].R1 = ( state == Qt::Checked );
        }
        else if ( col_index == 2 )
        {
            this->zhusuo[ row_index ].R2 = ( state == Qt::Checked );
        }
        else if ( col_index == 3 )
        {
            this->zhusuo[ row_index ].R3 = ( state == Qt::Checked );
        }
        else if ( col_index == 4 )
        {
            this->zhusuo[ row_index ].R4 = ( state == Qt::Checked );
        }
        else if ( col_index == 5 )
        {
            this->zhusuo[ row_index ].R5 = ( state == Qt::Checked );
        }
        else if ( col_index == 6 )
        {
            this->zhusuo[ row_index ].R6 = ( state == Qt::Checked );
        }
        else if ( col_index == 7 )
        {
            this->zhusuo[ row_index ].R7 = ( state == Qt::Checked );
        }
        else if ( col_index == 8 )
        {
            this->zhusuo[ row_index ].R8 = ( state == Qt::Checked );
        }
        else if ( col_index == 9 )
        {
            this->zhusuo[ row_index ].RM = ( state == Qt::Checked );
        }
    }
}
