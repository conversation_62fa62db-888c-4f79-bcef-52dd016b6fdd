#include "FATDecode.h"

#include <QDebug>
#include <QtCore>
#include <cstring>
#include <fstream>
#include <iostream>
#include <stdio.h>

#define KEY_LEN 52

const qint16 DATAPosition1 = 972;
const qint16 DATAPosition2 = 1996;
const qint16 DATAPosition3 = 1024;

using namespace std;

FATDecode::FATDecode() {}

FATDecode::~FATDecode() {}

/**
 * 从数据中获取指定密钥集的密钥。
 *
 * @param data 指向包含所有密钥数据的字符数组的指针。
 * @param key 指向用于存储提取的密钥的字符数组的指针。
 * @param keyOffset 指定要提取的密钥集的索引。
 *
 * @note 函数假定 KEY_LEN 为密钥长度，该长度应为在函数外部定义的常量。
 */
void FATDecode::GetKey( char* data, char* key, int keyOffset )
{
    // 遍历密钥长度，从data中按指定keyOffset位置提取密钥
    for ( int i = 0; i < KEY_LEN; i++ )
    {
        key[ i ] = data[ keyOffset + i ];
    }
}

/**
 * @brief 使用指定的密钥对指定数据进行解码
 *
 * 该函数通过循环遍历输入数据，并使用密钥对每个字节进行异或操作，从而实现解码。
 * 解码操作从指定的偏移量开始，作用于数据长度指定的字节数。
 *
 * @param data 指向需要解码的数据的指针
 * @param key 指向解码密钥的指针
 * @param offset 解码操作的起始偏移量
 * @param datalen 需要解码的数据长度
 */
void FATDecode::Decoder( char* data, char* key, int offset, int datalen )
{
    // 遍历数据，使用密钥进行异或解码
    for ( int i = 0; i < datalen; i++ )
    {
        data[ offset + i ] = data[ offset + i ] ^ key[ i % KEY_LEN ];
    }
}

/**
 * 解码函数
 * 用于对指定数据进行多次解密，并提取文件头部信息。
 *
 * @param data 指向需要解密数据的指针。
 * @param fHead 指向存储文件头部信息的结构体的指针。
 * @param fileLen 需要解密数据的长度。
 */
void FATDecode::Decode( char* data, FatHead* fHead, int fileLen )
{
    char Key[ KEY_LEN ] = { 0 };
    // 第一次取KEY并解密数据
    GetKey( data, Key, DATAPosition1 );
    Decoder( data, Key, 0, fileLen );

    // 复制头部信息到fhead
    //  memcpy_s(fHead, 28, &data[80], 28);
    memcpy( fHead, &data[ 80 ], 28 );

    // 第二次取KEY并解密指定范围的数据
    GetKey( data, Key, DATAPosition2 );
    Decoder( data, Key, fHead->HeadDB, fHead->FirstDB - fHead->HeadDB );

    // 第三次取KEY并解密指定范围的数据
    GetKey( data, Key, fHead->FirstDB + DATAPosition2 );
    Decoder( data, Key, fHead->FirstDB, fHead->SecondDB - fHead->FirstDB );

    // 第四次取KEY并解密指定范围的数据
    GetKey( data, Key, fHead->SecondDB + DATAPosition2 - DATAPosition3 );
    Decoder( data, Key, fHead->SecondDB, fHead->ThirdDB - fHead->SecondDB );

    // 第五次取KEY并解密指定范围的数据
    GetKey( data, Key, fHead->ThirdDB );
    Decoder( data, Key, fHead->ThirdDB, fHead->Datalen - fHead->ThirdDB + DATAPosition3 );
}

/**
 * 测试函数
 * 根据传入的文件名参数，执行Decode操作，并打印解码结果。
 */
void FATDecode::Test( QString filePath )
{
    QFile file( filePath );
    if ( file.open( QIODevice::ReadOnly ) )
    {
        QByteArray data      = file.readAll();
        char*      dataArray = data.data();
        qDebug() << data.size();

        // 创建文件头部结构体
        FatHead fHead;

        // 执行解码操作
        Decode( dataArray, &fHead, data.size() );

        // 打印解码结果
        qDebug() << fHead.Datalen;
        qDebug() << fHead.HeadDB;
        qDebug() << fHead.FirstDB;
        qDebug() << fHead.SecondDB;
        qDebug() << fHead.ThirdDB;

        file.close();
    }
    else
    {
        qDebug() << "无法打开文件";
    }
}
