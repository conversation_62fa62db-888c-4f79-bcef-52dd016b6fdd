#include "UsbForm.h"
#include "ui_UsbForm.h"

void UsbForm::InitUsbAdminPage()
{
    if ( !isUsbAdminPageInited )
    {
        // 连接 le_machineType 的点击事件
        connect( ui->le_machineType, &MyLineEdit::mouseRelease, this, [ & ]() {
            if ( machineTypeComboForm != nullptr )
            {
                delete machineTypeComboForm;
                machineTypeComboForm = nullptr;
            }

            // 创建机型选项列表
            QMap< int, QString > machineTypes;
            machineTypes.insert( 0, "G616" );
            machineTypes.insert( 1, "G616D" );

            // 创建并显示 ComboForm
            machineTypeComboForm = new ComboForm( nullptr, &machineTypes, 2 );
            connect( machineTypeComboForm, &ComboForm::itemSelected, this, [ & ]( int index ) {
                // 保存选择的索引
                currentMachineTypeIndex = index;
                // 显示选择的文本
                ui->le_machineType->setText( index == 0 ? "G616" : "G616D" );
            } );
            machineTypeComboForm->show();
        } );

        // 连接 le_fileType 事件
        connect( ui->le_fileType, &MyLineEdit::mouseRelease, this, [ & ]() {
            if ( fileTypeComboForm != nullptr )
            {
                delete fileTypeComboForm;
                fileTypeComboForm = nullptr;
            }

            // 创建文件类型选项列表
            QMap< int, QString > fileTypes;
            fileTypes.insert( 0, "SOK文件" );
            fileTypes.insert( 1, "Dis文件" );
            fileTypes.insert( 2, "Slz文件" );
            fileTypes.insert( 3, "Tas文件" );
            fileTypes.insert( 4, "Dis配置" );
            fileTypes.insert( 5, "Disover配置" );
            fileTypes.insert( 6, "DisTal配置" );
            fileTypes.insert( 7, "Distr配置" );
            fileTypes.insert( 8, "DisTrMot配置" );

            // 创建并显示 ComboForm
            fileTypeComboForm = new ComboForm( nullptr, &fileTypes, 3 );  // 每行显示3个选项
            connect( fileTypeComboForm, &ComboForm::itemSelected, this, [ = ]( int index ) {
                // 保存选择的索引
                currentFileTypeIndex = index;
                // 显示选择的文本
                QString selectedText;
                switch ( index )
                {
                    case 0:
                        selectedText = "SOK文件";
                        break;
                    case 1:
                        selectedText = "Dis文件";
                        break;
                    case 2:
                        selectedText = "Slz文件";
                        break;
                    case 3:
                        selectedText = "Tas文件";
                        break;
                    case 4:
                        selectedText = "Dis配置";
                        break;
                    case 5:
                        selectedText = "Disover配置";
                        break;
                    case 6:
                        selectedText = "DisTal配置";
                        break;
                    case 7:
                        selectedText = "Distr配置";
                        break;
                    case 8:
                        selectedText = "DisTrMot配置";
                        break;
                    case 9:
                        selectedText = "电眼参数";
                        break;
                    default:
                        selectedText = "SOK文件";
                }
                ui->le_fileType->setText( selectedText );

                // 更新本地文件视图
                updateLocalFileView( index );
            } );
            fileTypeComboForm->show();
        } );

        // 设置默认显示
        ui->le_machineType->setText( "G616" );
        ui->le_fileType->setText( "SOK文件" );

        // 初始化本地文件视图
        initLocalFileView();
        // 初始化U盘文件视图
        initUsbFileView();

        // 连接双击事件，用于进入文件夹
        connect( ui->Usb_tableView_usb, &QTableView::doubleClicked, this, [ & ]( const QModelIndex& index ) {
            if ( usbFileModel && usbFileModel->fileInfo( index ).isDir() )
            {
                // 如果是文件夹，则进入该文件夹
                ui->Usb_tableView_usb->setRootIndex( index );
                // 更新当前U盘路径
                currentUsbPath = usbFileModel->filePath( index );
            }
        } );

        // 连接返回上级目录按钮
        connect( ui->pbtn_backDir, &QPushButton::clicked, this, [ & ]() {
            if ( usbFileModel )
            {
                QModelIndex currentIndex = ui->Usb_tableView_usb->rootIndex();
                if ( currentIndex.isValid() )
                {
                    QString     basePath    = checkUSBDirExists();  // 获取U盘根目录
                    QString     currentPath = usbFileModel->filePath( currentIndex );
                    QModelIndex parentIndex = currentIndex.parent();

                    // 检查是否已经在U盘根目录
                    if ( currentPath == basePath )
                    {
                        // 已经在U盘根目录，不做任何操作
                        return;
                    }

                    // 检查父目录是否是U盘根目录之上的目录
                    if ( parentIndex.isValid() )
                    {
                        QString parentPath = usbFileModel->filePath( parentIndex );
                        if ( parentPath == basePath || currentPath.startsWith( basePath ) )
                        {
                            // 如果父目录是U盘根目录或当前路径是U盘下的子目录，则允许返回
                            ui->Usb_tableView_usb->setRootIndex( parentIndex );
                            // 更新当前U盘路径
                            currentUsbPath = parentPath;
                        }
                        else
                        {
                            // 如果父目录在U盘根目录之上，则直接返回到U盘根目录
                            ui->Usb_tableView_usb->setRootIndex( usbFileModel->index( basePath ) );
                            // 更新当前U盘路径
                            currentUsbPath = basePath;
                        }
                    }
                }
            }
        } );

        // 连接复制到本地按钮
        connect( ui->pbtn_toThis, &QPushButton::clicked, this, [ & ]() {
            if ( currentUsbFile.isEmpty() )
            {
                QMessageBox::warning( this, "提示", "请先选择要复制的U盘文件" );
                return;
            }

            QFileInfo fileInfo( currentUsbFile );
            if ( fileInfo.isDir() )
            {
                QMessageBox::warning( this, "提示", "不支持复制文件夹" );
                return;
            }

            // 构造目标文件路径
            QString targetFile = currentLocalPath + fileInfo.fileName();

            // 检查文件是否已存在
            if ( QFile::exists( targetFile ) )
            {
                QMessageBox::StandardButton reply;
                reply = QMessageBox::question( this, "文件已存在", QString( "文件 %1 已存在，是否覆盖？" ).arg( fileInfo.fileName() ), QMessageBox::Yes | QMessageBox::No );

                if ( reply == QMessageBox::No )
                {
                    return;
                }
                // 如果文件存在且用户确认覆盖，先删除原文件
                QFile::remove( targetFile );
            }

            // 复制文件
            if ( QFile::copy( currentUsbFile, targetFile ) )
            {
                // 刷新本地视图
                updateLocalFileView( currentFileTypeIndex );
            }
            else
            {
                QMessageBox::warning( this, "错误", "文件复制失败" );
            }
        } );

        // 连接复制到U盘按钮
        connect( ui->pbtn_toUSB, &QPushButton::clicked, this, [ & ]() {
            if ( currentLocalFile.isEmpty() )
            {
                QMessageBox::warning( this, "提示", "请先选择要复制的本地文件" );
                return;
            }

            // 获取当前U盘路径
            QString targetPath = usbFileModel->filePath( ui->Usb_tableView_usb->rootIndex() );
            if ( !QDir( targetPath ).exists() )
            {
                QMessageBox::warning( this, "提示", "未检测到U盘设备" );
                return;
            }

            QFileInfo fileInfo( currentLocalFile );
            if ( fileInfo.isDir() )
            {
                QMessageBox::warning( this, "提示", "不支持复制文件夹" );
                return;
            }

            // 构造目标文件路径，使用当前显示的目录
            QString targetFile = targetPath + "/" + fileInfo.fileName();

            // 检查文件是否已存在
            if ( QFile::exists( targetFile ) )
            {
                QMessageBox::StandardButton reply;
                reply = QMessageBox::question( this, "文件已存在", QString( "文件 %1 已存在，是否覆盖？" ).arg( fileInfo.fileName() ), QMessageBox::Yes | QMessageBox::No );

                if ( reply == QMessageBox::No )
                {
                    return;
                }
                // 如果文件存在且用户确认覆盖，先删除原文件
                QFile::remove( targetFile );
            }

            // 复制文件
            if ( QFile::copy( currentLocalFile, targetFile ) )
            {
                // 获取当前目录
                QModelIndex currentIndex = ui->Usb_tableView_usb->rootIndex();
                QString     currentPath  = usbFileModel->filePath( currentIndex );

                // 刷新U盘视图，保持在当前目录
                refreshUsbFileView();
                if ( !currentPath.isEmpty() )
                {
                    ui->Usb_tableView_usb->setRootIndex( usbFileModel->index( currentPath ) );
                }
            }
            else
            {
                QMessageBox::warning( this, "错误", "文件复制失��" );
            }
        } );

        isUsbAdminPageInited = true;
    }

    // 每次显示页面时刷新视图
    refreshUsbFileView();
    updateLocalFileView( currentFileTypeIndex );
}

QString UsbForm::checkUSBDirExists()
{
    QString usbDirSt = "/run/media/sd";
    char    tag      = 'a';

    for ( int i = 0; i < 26; i++ )
    {
        // 修改为检测 sda1~sdz1
        QDir dir( QString( "%1%2%3/" ).arg( usbDirSt ).arg( tag ).arg( "1" ) );
        if ( !dir.exists() )
            break;
        else
        {
            tag++;
        }
    }

    // 返回找到的路径，如 /run/media/sda1/
    return QString( "%1%2%3/" ).arg( usbDirSt ).arg( --tag ).arg( "1" );
}

void UsbForm::initUsbFileView()
{
    if ( usbFileModel == nullptr )
    {
        usbFileModel = new QFileSystemModel( this );
        usbFileModel->setFilter( QDir::AllDirs | QDir::Files | QDir::NoDotAndDotDot );
        ui->Usb_tableView_usb->setModel( usbFileModel );

        // 设置排序方式
        ui->Usb_tableView_usb->setSortingEnabled( true );
        ui->Usb_tableView_usb->sortByColumn( 0, Qt::AscendingOrder );
        usbFileModel->sort( 0, Qt::AscendingOrder );

        // 隐藏 Type 列
        ui->Usb_tableView_usb->hideColumn( 2 );

        // 设置列宽自动调整
        ui->Usb_tableView_usb->horizontalHeader()->setStretchLastSection( true );
        ui->Usb_tableView_usb->horizontalHeader()->setSectionResizeMode( 0, QHeaderView::Stretch );
        ui->Usb_tableView_usb->horizontalHeader()->setSectionResizeMode( 1, QHeaderView::Fixed );
        ui->Usb_tableView_usb->horizontalHeader()->setSectionResizeMode( 3, QHeaderView::Fixed );
        ui->Usb_tableView_usb->setColumnWidth( 1, 80 );   // 大小列固定宽度
        ui->Usb_tableView_usb->setColumnWidth( 3, 150 );  // 修改日期列固定宽度

        // 设置选择模式
        ui->Usb_tableView_usb->setSelectionBehavior( QAbstractItemView::SelectRows );
        ui->Usb_tableView_usb->setSelectionMode( QAbstractItemView::SingleSelection );

        // 连接选择事件
        connect( ui->Usb_tableView_usb->selectionModel(), &QItemSelectionModel::currentChanged, this, [ & ]( const QModelIndex& current, const QModelIndex& previous ) {
            if ( current.isValid() )
            {
                currentUsbFile = usbFileModel->filePath( current );
            }
        } );

        // 创建提示标签，修改父窗口为 page_2
        if ( noUsbTipLabel == nullptr )
        {
            // 将父窗口改为包含 tableView 的页面
            noUsbTipLabel = new QLabel( ui->stackedWidget->widget( 1 ) );
            noUsbTipLabel->setAlignment( Qt::AlignCenter );
            noUsbTipLabel->setStyleSheet( "font-size: 16pt; color: #666666;" );
            noUsbTipLabel->setText( "未检测到U盘设备" );
            // 设置位置大小，使用 tableView 的几何信息
            noUsbTipLabel->setGeometry( ui->Usb_tableView_usb->geometry() );
            noUsbTipLabel->hide();
        }
    }
}

void UsbForm::refreshUsbFileView()
{
    if ( usbFileModel )
    {
        // 检查并获取U盘路径
        currentUsbPath = checkUSBDirExists();

        // 修改判断条件，检查路径是否为空
        if ( !currentUsbPath.isEmpty() )
        {
            // 设置根目录并展示
            usbFileModel->setRootPath( currentUsbPath );
            ui->Usb_tableView_usb->setRootIndex( usbFileModel->index( currentUsbPath ) );

            // 显示表格，隐藏提示
            ui->Usb_tableView_usb->show();
            if ( noUsbTipLabel )
            {
                noUsbTipLabel->hide();
            }

            // 据当前选择的文件类型设置过滤器
            QStringList filters;
            switch ( currentFileTypeIndex )
            {
                case 0:
                    filters << "*.SOK"
                            << "*.sok";
                    break;
                case 1:
                    filters << "*.DIS"
                            << "*.dis";
                    break;
                case 2:
                    filters << "*.SLZ"
                            << "*.slz";
                    break;
                case 3:
                    filters << "*.TAS"
                            << "*.tas";
                    break;
                case 4:
                    filters << "*.CFG"
                            << "*.cfg";
                    break;
                case 5:
                    filters << "*.CFG"
                            << "*.cfg";
                    break;
                case 6:
                    filters << "*.CFG"
                            << "*.cfg";
                    break;
                case 7:
                    filters << "*.CFG"
                            << "*.cfg";
                    break;
                case 8:
                    filters << "*.CFG"
                            << "*.cfg";
                    break;
                default:
                    filters << "*.*";
            }
            usbFileModel->setNameFilters( filters );
            // 修改为true，这样不匹配的文件会显示但是置灰，文件夹始终显示
            usbFileModel->setNameFilterDisables( true );
        }
        else
        {
            // U盘不存在时显示提示
            ui->Usb_tableView_usb->hide();
            if ( noUsbTipLabel )
            {
                noUsbTipLabel->raise();
                noUsbTipLabel->show();
            }
        }
    }
}

void UsbForm::initLocalFileView()
{
    if ( localFileModel == nullptr )
    {
        localFileModel = new QFileSystemModel( this );
        localFileModel->setFilter( QDir::AllDirs | QDir::Files | QDir::NoDotAndDotDot );
        ui->Usb_tableView_this->setModel( localFileModel );

        // 设置排序方式
        ui->Usb_tableView_this->setSortingEnabled( true );
        ui->Usb_tableView_this->sortByColumn( 0, Qt::AscendingOrder );
        localFileModel->sort( 0, Qt::AscendingOrder );

        // 隐藏 Type 列
        ui->Usb_tableView_this->hideColumn( 2 );

        // 设置列宽自动调整
        ui->Usb_tableView_this->horizontalHeader()->setStretchLastSection( true );
        ui->Usb_tableView_this->horizontalHeader()->setSectionResizeMode( 0, QHeaderView::Stretch );
        ui->Usb_tableView_this->horizontalHeader()->setSectionResizeMode( 1, QHeaderView::Fixed );
        ui->Usb_tableView_this->horizontalHeader()->setSectionResizeMode( 3, QHeaderView::Fixed );
        ui->Usb_tableView_this->setColumnWidth( 1, 80 );   // 大小列固定宽度
        ui->Usb_tableView_this->setColumnWidth( 3, 150 );  // 修改日期列固定宽度

        // 设置选择模式
        ui->Usb_tableView_this->setSelectionBehavior( QAbstractItemView::SelectRows );
        ui->Usb_tableView_this->setSelectionMode( QAbstractItemView::SingleSelection );

        // 连接选择事件
        connect( ui->Usb_tableView_this->selectionModel(), &QItemSelectionModel::currentChanged, this, [ & ]( const QModelIndex& current, const QModelIndex& previous ) {
            if ( current.isValid() )
            {
                currentLocalFile = localFileModel->filePath( current );
            }
        } );
    }
}

void UsbForm::updateLocalFileView( int fileType )
{
    if ( localFileModel )
    {
        QString     targetPath;
        QStringList filters;

        switch ( fileType )
        {
            case 0:  // SOK文件
                targetPath = SOK_DIR;
                filters << "*.SOK"
                        << "*.sok";
                break;
            case 1:  // Dis文件
                targetPath = DIS_DIR;
                filters << "*.DIS"
                        << "*.dis";
                break;
            case 2:  // Slz文件
                targetPath = SLZ_DIR;
                filters << "*.SLZ"
                        << "*.slz";
                break;
            case 3:  // Tas文件
                targetPath = TAS_DIR;
                filters << "*.TAS"
                        << "*.tas";
                break;
            case 4:  // Dis配置文件
                targetPath = CFG_DIR + "Dis/";
                filters << "*.CFG"
                        << "*.cfg";
                break;
            case 5:  // Disover配置文件
                targetPath = CFG_DIR + "Disover/";
                filters << "*.CFG"
                        << "*.cfg";
                break;
            case 6:  // DisTal配置文件
                targetPath = CFG_DIR + "DisTal/";
                filters << "*.CFG"
                        << "*.cfg";
                break;
            case 7:  // Distr配置文件
                targetPath = CFG_DIR + "Distr/";
                filters << "*.CFG"
                        << "*.cfg";
                break;
            case 8:  // DisTrMot配置文件
                targetPath = CFG_DIR + "DisTrMot/";
                filters << "*.CFG"
                        << "*.cfg";
                break;
            default:
                targetPath = SOK_DIR;
                filters << "*.SOK"
                        << "*.sok";
        }

        // 确保目录存在
        QDir dir( targetPath );
        if ( !dir.exists() )
        {
            dir.mkpath( targetPath );
        }

        // 更新视图
        currentLocalPath = targetPath;
        localFileModel->setRootPath( targetPath );
        ui->Usb_tableView_this->setRootIndex( localFileModel->index( targetPath ) );

        // 设置文件过滤器
        localFileModel->setNameFilters( filters );
        localFileModel->setNameFilterDisables( true );
    }
}
