#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>

// 初始化圆盘剪刀框架
void CraftParamForm::initSawFrame()
{
    // 清空之前的控件列表
    if (!sawIndexLabels.isEmpty()) {
        for (auto label : sawIndexLabels) {
            delete label;
        }
        sawIndexLabels.clear();
    }
    if (!sawNameLabels.isEmpty()) {
        for (auto label : sawNameLabels) {
            delete label;
        }
        sawNameLabels.clear();
    }
    if (!sawStepLabels.isEmpty()) {
        for (auto label : sawStepLabels) {
            delete label;
        }
        sawStepLabels.clear();
    }
    if (!sawProhibitLabels.isEmpty()) {
        for (auto label : sawProhibitLabels) {
            delete label;
        }
        sawProhibitLabels.clear();
    }
    if (!sawStartLabels.isEmpty()) {
        for (auto label : sawStartLabels) {
            delete label;
        }
        sawStartLabels.clear();
    }
    if (!sawEndLabels.isEmpty()) {
        for (auto label : sawEndLabels) {
            delete label;
        }
        sawEndLabels.clear();
    }
    if (!sawStartNewEdits.isEmpty()) {
        for (auto edit : sawStartNewEdits) {
            delete edit;
        }
        sawStartNewEdits.clear();
    }
    if (!sawEndNewEdits.isEmpty()) {
        for (auto edit : sawEndNewEdits) {
            delete edit;
        }
        sawEndNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout(ui->sawFrame);

    // 创建标题行
    QLabel* indexHeader = new QLabel("序号", ui->sawFrame);
    QLabel* nameHeader = new QLabel("步骤名称", ui->sawFrame);
    QLabel* stepHeader = new QLabel("步", ui->sawFrame);
    QLabel* prohibitHeader = new QLabel("P", ui->sawFrame);
    QLabel* startHeader = new QLabel("起始值", ui->sawFrame);
    QLabel* endHeader = new QLabel("结束值", ui->sawFrame);
    QLabel* startNewHeader = new QLabel("N起始值", ui->sawFrame);
    QLabel* endNewHeader = new QLabel("N结束值", ui->sawFrame);

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet(headerStyle);
    nameHeader->setStyleSheet(headerStyle);
    stepHeader->setStyleSheet(headerStyle);
    prohibitHeader->setStyleSheet(headerStyle);
    startHeader->setStyleSheet(headerStyle);
    endHeader->setStyleSheet(headerStyle);
    startNewHeader->setStyleSheet(headerStyle);
    endNewHeader->setStyleSheet(headerStyle);

    // 设置标题对齐方式
    indexHeader->setAlignment(Qt::AlignCenter);
    nameHeader->setAlignment(Qt::AlignCenter);
    stepHeader->setAlignment(Qt::AlignCenter);
    prohibitHeader->setAlignment(Qt::AlignCenter);
    startHeader->setAlignment(Qt::AlignCenter);
    endHeader->setAlignment(Qt::AlignCenter);
    startNewHeader->setAlignment(Qt::AlignCenter);
    endNewHeader->setAlignment(Qt::AlignCenter);

    // 添加标题到布局
    layout->addWidget(indexHeader, 0, 0);
    layout->addWidget(nameHeader, 0, 1);
    layout->addWidget(stepHeader, 0, 2);
    layout->addWidget(prohibitHeader, 0, 3);
    layout->addWidget(startHeader, 0, 4);
    layout->addWidget(endHeader, 0, 5);
    layout->addWidget(startNewHeader, 0, 6);
    layout->addWidget(endNewHeader, 0, 7);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->sawFrame);
        indexLabel->setAlignment(Qt::AlignCenter);
        sawIndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);
        
        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->sawFrame);
        nameLabel->setAlignment(Qt::AlignCenter);
        sawNameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);
        
        // 步标签
        QLabel* stepLabel = new QLabel("", ui->sawFrame);
        stepLabel->setAlignment(Qt::AlignCenter);
        sawStepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);
        
        // 禁止修改标签
        QLabel* prohibitLabel = new QLabel("", ui->sawFrame);
        prohibitLabel->setAlignment(Qt::AlignCenter);
        sawProhibitLabels.append(prohibitLabel);
        layout->addWidget(prohibitLabel, i + 1, 3);
        
        // 起始值标签
        QLabel* startLabel = new QLabel("", ui->sawFrame);
        startLabel->setAlignment(Qt::AlignCenter);
        sawStartLabels.append(startLabel);
        layout->addWidget(startLabel, i + 1, 4);
        
        // 结束值标签
        QLabel* endLabel = new QLabel("", ui->sawFrame);
        endLabel->setAlignment(Qt::AlignCenter);
        sawEndLabels.append(endLabel);
        layout->addWidget(endLabel, i + 1, 5);
        
        // 新起始值编辑框
        MyLineEdit* startNewEdit = new MyLineEdit(ui->sawFrame);
        startNewEdit->setAlignment(Qt::AlignCenter);
        startNewEdit->setReadOnly(true); // 初始设为只读
        startNewEdit->setProperty("row", i); // 存储行索引
        startNewEdit->setProperty("column", 6); // 存储列索引
        connect(startNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSawPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.elasticMotorParam.size() && !craftParams.elasticMotorParam[dataIndex]->prohibitsSawValueChange) {
                tableIndex = 5; // 圆盘剪刀表格
                tableEditRowIndex = i;
                tableEditColIndex = 6; // 新起始值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "锯齿新起始值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        sawStartNewEdits.append(startNewEdit);
        layout->addWidget(startNewEdit, i + 1, 6);
        
        // 新结束值编辑框
        MyLineEdit* endNewEdit = new MyLineEdit(ui->sawFrame);
        endNewEdit->setAlignment(Qt::AlignCenter);
        endNewEdit->setReadOnly(true); // 初始设为只读
        endNewEdit->setProperty("row", i); // 存储行索引
        endNewEdit->setProperty("column", 7); // 存储列索引
        connect(endNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSawPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.elasticMotorParam.size() && !craftParams.elasticMotorParam[dataIndex]->prohibitsSawValueChange) {
                tableIndex = 5; // 圆盘剪刀表格
                tableEditRowIndex = i;
                tableEditColIndex = 7; // 新结束值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "锯齿新结束值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        sawEndNewEdits.append(endNewEdit);
        layout->addWidget(endNewEdit, i + 1, 7);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 1);  // P
    layout->setColumnStretch(4, 2);  // 起始值
    layout->setColumnStretch(5, 2);  // 结束值
    layout->setColumnStretch(6, 2);  // N起始值
    layout->setColumnStretch(7, 2);  // N结束值
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->sawFrame->setLayout(layout);
}

// 更新圆盘剪刀页面
void CraftParamForm::updateSawPage()
{
    // 计算总页数
    totalSawPages = (craftParams.elasticMotorParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;
    
    // 确保当前页在有效范围内
    if (currentSawPage >= totalSawPages && totalSawPages > 0) {
        currentSawPage = totalSawPages - 1;
    }
    
    // 计算当前页的起始索引
    int startIndex = currentSawPage * ITEMS_PER_PAGE;
    
    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.elasticMotorParam.size()) {
            // 有数据，显示
            auto param = craftParams.elasticMotorParam[dataIndex];

            sawIndexLabels[i]->setText(QString::number(dataIndex + 1));
            sawNameLabels[i]->setText(QString::fromLatin1(param->blockName));
            sawStepLabels[i]->setText(QString::number(param->stepStart) + "~" + QString::number(param->stepEnd));
            sawProhibitLabels[i]->setText(param->prohibitsSawValueChange ? "N" : "Y");
            sawStartLabels[i]->setText(QString::number(param->sawStart[currentSize - 1]));
            sawEndLabels[i]->setText(QString::number(param->sawEnd[currentSize - 1]));
            sawStartNewEdits[i]->setText(QString::number(param->sawStart[currentSize - 1]));
            sawEndNewEdits[i]->setText(QString::number(param->sawEnd[currentSize - 1]));

            // 设置可编辑状态
            bool editable = !param->prohibitsSawValueChange;
            sawStartNewEdits[i]->setEnabled(editable);
            sawEndNewEdits[i]->setEnabled(editable);

            // 设置背景色
            if (editable) {
                sawStartNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
                sawEndNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
            } else {
                sawStartNewEdits[i]->setStyleSheet("");
                sawEndNewEdits[i]->setStyleSheet("");
            }

            // 显示所有控件
            sawIndexLabels[i]->setVisible(true);
            sawNameLabels[i]->setVisible(true);
            sawStepLabels[i]->setVisible(true);
            sawProhibitLabels[i]->setVisible(true);
            sawStartLabels[i]->setVisible(true);
            sawEndLabels[i]->setVisible(true);
            sawStartNewEdits[i]->setVisible(true);
            sawEndNewEdits[i]->setVisible(true);

            // 恢复正常样式（除了可编辑的控件）
            sawIndexLabels[i]->setStyleSheet("");
            sawNameLabels[i]->setStyleSheet("");
            sawStepLabels[i]->setStyleSheet("");
            sawProhibitLabels[i]->setStyleSheet("");
            sawStartLabels[i]->setStyleSheet("");
            sawEndLabels[i]->setStyleSheet("");
        } else {
            // 无数据，但保持控件可见以占据空间
            sawIndexLabels[i]->setText("");
            sawNameLabels[i]->setText("");
            sawStepLabels[i]->setText("");
            sawProhibitLabels[i]->setText("");
            sawStartLabels[i]->setText("");
            sawEndLabels[i]->setText("");
            sawStartNewEdits[i]->setText("");
            sawEndNewEdits[i]->setText("");

            // 控件保持可见，但设置为透明
            sawIndexLabels[i]->setVisible(true);
            sawNameLabels[i]->setVisible(true);
            sawStepLabels[i]->setVisible(true);
            sawProhibitLabels[i]->setVisible(true);
            sawStartLabels[i]->setVisible(true);
            sawEndLabels[i]->setVisible(true);
            sawStartNewEdits[i]->setVisible(true);
            sawEndNewEdits[i]->setVisible(true);

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            sawIndexLabels[i]->setStyleSheet(transparentStyle);
            sawNameLabels[i]->setStyleSheet(transparentStyle);
            sawStepLabels[i]->setStyleSheet(transparentStyle);
            sawProhibitLabels[i]->setStyleSheet(transparentStyle);
            sawStartLabels[i]->setStyleSheet(transparentStyle);
            sawEndLabels[i]->setStyleSheet(transparentStyle);
            sawStartNewEdits[i]->setStyleSheet(transparentStyle);
            sawEndNewEdits[i]->setStyleSheet(transparentStyle);

            // 禁用编辑
            sawStartNewEdits[i]->setEnabled(false);
            sawEndNewEdits[i]->setEnabled(false);
        }
    }

    // 更新翻页按钮状态
    ui->pbtn_prev->setEnabled(currentSawPage > 0);
    ui->pbtn_next->setEnabled(currentSawPage < totalSawPages - 1);
}
