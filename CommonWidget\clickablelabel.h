#ifndef CLICKABLELABEL_H
#define CLICKABLELABEL_H

#include <QLabel>
#include <QMouseEvent>
#include <QObject>

class ClickableLabel : public QLabel
{
    Q_OBJECT
public:
    ClickableLabel( QWidget* parent = nullptr ) : QLabel( parent ) {}

signals:
    void clicked();
    void doubleClicked();

protected:
    void mousePressEvent( QMouseEvent* event ) override
    {
        if ( event->button() == Qt::LeftButton )
        {
            emit clicked();
        }
    }
    void mouseDoubleClickEvent( QMouseEvent* event ) override
    {
        if ( event->button() == Qt::LeftButton )
        {
            emit doubleClicked();
        }
    }
};

#endif  // CLICKABLELABEL_H
