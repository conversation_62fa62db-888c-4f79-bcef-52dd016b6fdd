﻿#ifndef NeedleView_H
#define NeedleView_H

#include <QLabel>
#include <QWidget>

class NeedleView : public QLabel
{
    Q_OBJECT
public:
    NeedleView( quint8* buffer = Q_NULLPTR, QWidget* parent = nullptr );
    ~NeedleView();
    void setData( quint8* buffer );
    void mousePressEvent( QMouseEvent* event ) override
    {
        QLabel::mousePressEvent( event );
        emit mousePressed( event );
    }
    void mouseReleaseEvent( QMouseEvent* event ) override
    {
        QLabel::mouseReleaseEvent( event );
        emit mouseReleased( event );
    }

signals:
    void mousePressed( QMouseEvent* event );
    void mouseReleased( QMouseEvent* event );

protected:
    void paintEvent( QPaintEvent* event ) override;

private:
    int     w0;  //自定义控件类构造函数中QLabel宽度，为默认值
    int     h0;  //自定义控件类构造函数中QLabel高度，为默认值
    quint8* _buffer;
};

#endif  // NeedleView_H
