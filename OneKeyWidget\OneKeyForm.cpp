#include "OneKeyForm.h"
#include "ui_OneKeyForm.h"

OneKeyForm::OneKeyForm( QWidget* parent, Communication* comm, MainWidgetData* mainData ) : QWidget( parent ), ui( new Ui::OneKeyForm ), comm( comm ), mainData( mainData )
{
    ui->setupUi( this );

    /* 返回主菜单键连接  */
    connect( ui->OneKeyFormHome_btn, SIGNAL( clicked() ), this, SLOT( onHomeBtnClicked() ) );

    menuBtnGroup = new QButtonGroup( this );
    menuBtnGroup->addButton( ui->OneKey_user_btn, 1 );
    menuBtnGroup->addButton( ui->OneKey_warn_btn, 2 );
    menuBtnGroup->addButton( ui->OneKey_eye_btn, 3 );
    menuBtnGroup->addButton( ui->OneKey_weight_btn, 4 );
    menuBtnGroup->addButton( ui->OneKey_ktf_btn, 5 );
    connect( menuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onMenuBtnGroupClicked( int ) ) );

    // 使用 mainData 中的指针
    if ( mainData != nullptr )
    {
        readMachineFileConfig = mainData->readMachineFileConfig;
    }
    else
    {
        // 如果 mainData 为空，则创建新的对象（兼容旧代码）
        if ( readMachineFileConfig == nullptr )
        {
            readMachineFileConfig = new ReadMachineFileConfig();
            readMachineFileConfig->parseConfig( MAIN_CFG_DIR + "MachineFileConfig.json" );
        }
    }

    connect( ui->pbtn_save, &QPushButton::clicked, this, [&]() {
        if ( readMachineFileConfig->saveUserConfig( MAIN_CFG_DIR + "MachineFileConfig.json" ) == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "保存成功" );

            sendUserParam();
            retryCountParam  = 1;
            paramSendSuccess = false;
            paramSendTimer.start( COMM_INTERVAL );
        }
        else
        {
            QMessageBox::warning( nullptr, "错误提示", "保存失败" );
        }
    } );

    ui->stackedWidget->setCurrentIndex( 0 );
    ui->pbtn_save->setVisible( false );
    initUserParasetPage();
    initWarnParasetPage();

    initEEyePage();
    initKtfPage();
    // 连接电眼配置帧信号到槽函数
    connect( comm, &Communication::EEyeConfigFrameReceived, this, &OneKeyForm::onEEyeConfigFrameReceived );

    // 发送参数定时器
    paramSendTimer.setSingleShot( true );
    connect( &paramSendTimer, &QTimer::timeout, [&]() {
        if ( paramSendSuccess )
        {
            qDebug() << "UserParam frame sent successfully.";
            retryCountParam = 0;  // 发送成功，重置重发计数
        }
        else
        {
            qDebug() << "Failed to send UserParam frame.";
            sendUserParam();
            retryCountParam++;
            if ( retryCountParam >= COM_MAX_RETRIES )
            {
                qDebug() << "Failed to send UserParam frame after 3 retries. Exiting.";
                retryCountParam = 0;
                return;  // 退出程序
            }
            qDebug() << "Retrying in" << COMM_INTERVAL << "milliseconds...";
            paramSendTimer.start( COMM_INTERVAL );  // 重新开始定时器
        }
    } );
    connect( comm, &Communication::UserParamFrameReceived, this, [&]( qint16 size, quint8* data ) {
        if ( size > 1 )
        {
            if ( data[ 0 ] == 0x80 )
            {
                paramSendSuccess = true;
            }
        }
    } );
}

OneKeyForm::~OneKeyForm()
{
    delete ui;
}

/* 测试界面返回 主菜单键按下槽函数 */
void OneKeyForm::onHomeBtnClicked()
{
    if ( ui->stackedWidget->currentIndex() > 0 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->OnKey_Tiltle_label->setText( "用户参数" );
        ui->pbtn_save->setVisible( false );
    }
    else
    {
        this->close();
        emit OneKeyFormToMainWinToShowSignal();
    }
}

void OneKeyForm::onMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            ui->OnKey_Tiltle_label->setText( "用户参数" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->pbtn_save->setVisible( true );
            break;
        case 2:
            ui->OnKey_Tiltle_label->setText( "报警设置" );
            ui->stackedWidget->setCurrentIndex( 2 );
            ui->pbtn_save->setVisible( true );
            break;
        case 3:
            ui->OnKey_Tiltle_label->setText( "电眼设置" );
            ui->stackedWidget->setCurrentIndex( 3 );
            ui->pbtn_save->setVisible( false );
            break;
        case 4:
            ui->OnKey_Tiltle_label->setText( "称重" );
            ui->stackedWidget->setCurrentIndex( 4 );
            ui->pbtn_save->setVisible( false );
            break;
        case 5:
            ui->OnKey_Tiltle_label->setText( "纱线感应器" );
            ui->stackedWidget->setCurrentIndex( 5 );
            ui->pbtn_save->setVisible( false );
            break;
    }
}

void OneKeyForm::initUserParasetPage()
{
    if ( isUserParasetPageInit == false )
    {
        initUserParasetTab();
        initOtherParasetTab();
        isUserParasetPageInit = true;
    }
}

void OneKeyForm::initWarnParasetPage()
{
    if ( isWarnParasetPageInit == false )
    {
        // init
        initSocketWarnParasetTab();
        initFengtouWarnParasetTab();

        isWarnParasetPageInit = true;
    }
}

void OneKeyForm::sendUserParam()
{
    UserParams userParams = readMachineFileConfig->makeUserParamFrame();
    // 创建数组，将userParams的值赋值给数组，然后通过comm->pushDataTobuffer()发送
    quint8 buffer[ sizeof( UserParams ) ];
    memcpy( buffer, &userParams, sizeof( UserParams ) );
    comm->pushDataTobuffer( 0x02, buffer, sizeof( UserParams ) );
}
