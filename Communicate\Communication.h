﻿#ifndef COMMUNICATION_H
#define COMMUNICATION_H

#include "CCycleBuffer.h"
#include "Common/parameters.h"
#include "crc_16.h"
#include <QDebug>
#include <QMutex>
#include <QObject>
#include <QSemaphore>
#include <QSerialPort>
#include <QThread>

class Communication : public QObject
{
    Q_OBJECT
public:
    Communication( QObject* parent = nullptr );
    ~Communication();
    void pushDataTobuffer( uint8_t type, uint8_t* data, uint16_t size );
    bool readDataFromBuffer( uint8_t* type, uint8_t* data, uint16_t* size );

signals:
    void MachineParamFrameReceived( qint16 size, quint8* data );
    void UserParamFrameReceived( qint16 size, quint8* data );
    void StepDataFrameReceived( qint16 size, quint8* data );
    void valveSelfCheckFrameReceived( qint16 size, quint8* data );
    void StepMotorTestInfoFrameReceived( qint16 size, quint8* data );
    void ServoMotorTestInfoFrameReceived( qint16 size, quint8* data );
    void SensorTestFrameReceived( qint16 size, quint8* data );
    void CommandFrameReceived( qint16 size, quint8* data );
    void LowerReportFrameReceived( qint16 size, quint8* data );
    void EEyeConfigFrameReceived( qint16 size, quint8* data );

private:
    QSerialPort* m_serialPort;
    CCycleBuffer rxBuffer;  // 读写环形缓冲区指针
    QMutex       mutexR;
    QSemaphore   rxSemaphore;

    QThread* sendThread = nullptr;

    // Commnication Parse thread
    QThread* parseThread = nullptr;
    class ParseWorker : public QObject
    {
    public:
        explicit ParseWorker( Communication* parent ) : QObject( parent ), comm( parent ) {}
        void process();
        void stop();

    private:
        Communication* comm;
        bool           isRunning = true;
    };

    ParseWorker parseWorker;

    void startThreads();
    void stopThreads();
private slots:
    void onFrameReceived();
};

#endif  // COMMUNICATION_H
