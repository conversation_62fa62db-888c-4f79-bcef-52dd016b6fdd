﻿#include "Communication.h"

Communication::Communication( QObject* parent ) : QObject( parent ), rx<PERSON><PERSON><PERSON>( 8192 ), parseWorker( this )
{
    /* RS422初始化 */
    m_serialPort = new QSerialPort();
    if ( m_serialPort->isOpen() )  // 如果串口已经打开了 先给他关闭了
    {
        m_serialPort->clear();
        m_serialPort->close();
    }
    m_serialPort->setPortName( SERIAL_PORT );
    if ( !m_serialPort->open( QIODevice::ReadWrite ) )  // 用ReadWrite 的模式尝试打开串口
    {
        qDebug() << SERIAL_PORT << "open Failed!";
        return;
    }
    m_serialPort->setBaudRate( QSerialPort::Baud1500000, QSerialPort::AllDirections );  // 设置波特率和读写方向
    m_serialPort->setDataBits( QSerialPort::Data8 );                                    // 数据位为8位
    m_serialPort->setFlowControl( QSerialPort::NoFlowControl );                         // 无流控制
    m_serialPort->setParity( QSerialPort::NoParity );                                   // 无校验位
    m_serialPort->setStopBits( QSerialPort::OneStop );                                  // 一位停止位

    // 连接信号槽 当下位机发送数据QSerialPortInfo 会发送个 readyRead 信号,我们定义个槽void receiveInfo()解析数据
    connect( m_serialPort, SIGNAL( readyRead() ), this, SLOT( onFrameReceived() ) );

    startThreads();
}

Communication::~Communication()
{
    qDebug() << "~Communication ";
    if ( m_serialPort->isOpen() )  // 如果串口已经打开了 先给他关闭了
    {
        m_serialPort->clear();
        m_serialPort->close();
    }
    stopThreads();
}

void Communication::startThreads()
{
    if ( parseThread == nullptr )
    {
        parseThread = new QThread( this );
        parseWorker.setParent( nullptr );
        parseWorker.moveToThread( parseThread );
        // 连接线程开始信号到工作槽函数
        connect( parseThread, &QThread::started, &parseWorker, &ParseWorker::process );
        parseThread->start();
    }
    qDebug() << "startThreads ";
}

void Communication::stopThreads()
{
    parseWorker.stop();
    // PC will error when belows add
    if ( parseThread != nullptr )
    {
        parseThread->quit();
        parseThread->wait();
    }
    qDebug() << "stopThreads";
}

void Communication::pushDataTobuffer( uint8_t type, uint8_t* data, uint16_t size )
{
    //    qDebug() << "pushDataTobuffer" << QString::number( data[ 0 ], 16 );
    uint8_t dataArray[ size + 8 ];
    dataArray[ 0 ] = 0xA1;
    dataArray[ 1 ] = 0xA2;
    dataArray[ 2 ] = 0xA3;
    dataArray[ 3 ] = type;
    dataArray[ 4 ] = ( size + 8 ) % 256;
    dataArray[ 5 ] = ( size + 8 ) / 256;

    if ( size > 0 )
    {
        memcpy( &dataArray[ 6 ], data, size );
    }
    uint16_t crc = crc16( dataArray, size + 6 );
    memcpy( &dataArray[ size + 6 ], &crc, sizeof( crc ) );
    //    qDebug() << "pushDataTobuffer CRC" << crc;

    QByteArray byteArray( reinterpret_cast< const char* >( dataArray ), size + 8 );

    m_serialPort->write( byteArray );
    m_serialPort->waitForBytesWritten( 100 );
    //    QString hexString = byteArray.toHex( ' ' ).toUpper();  // ' '表示每个字节之间用空格分隔，toUpper转换为大写
    //    // 移除末尾的空格（如果有的话）
    //    hexString = hexString.trimmed();
    //    // 打印结果
    //    qDebug() << "Send: " << hexString;
}

bool Communication::readDataFromBuffer( uint8_t* type, uint8_t* data, uint16_t* size )
{
    // 发一条可能解析成2条
    //    if ( rxSemaphore.tryAcquire( 1, 50 ) )
    //    {
    //        return false;
    //    }
    QMutexLocker locker( &mutexR );

    // 先一直读，直到读到0xA1
    quint8 head = 0;
    while ( head != 0xA1 )
    {
        quint32 ret = rxBuffer.Read( &head, sizeof( head ) );
        //        qDebug() << "Head : " << head;
        // 读到缓存区空了
        if ( ret == 0 )
            break;
    }

    if ( head == 0xA1 )
    {
        // 如果为0xA1，继续读后两个，看是不是A2 A3
        quint8 headArr[ 2 ] = { 0 };
        // 能读到数
        if ( rxBuffer.Read( headArr, sizeof( headArr ) ) > 0 )
        {
            qDebug() << "Head1 : " << headArr[ 0 ] << headArr[ 1 ];
            if ( headArr[ 0 ] == 0xA2 && headArr[ 1 ] == 0xA3 )
            {
                // 读type
                if ( rxBuffer.Read( type, 1 ) > 0 )
                {
                    qDebug() << "type : " << *type;
                    // 读数据长度
                    quint8 lengthArr[ 2 ] = { 0 }, length = 0;
                    if ( rxBuffer.Read( lengthArr, sizeof( lengthArr ) ) > 0 )
                    {
                        qDebug() << "lengthArr : " << lengthArr[ 0 ] << lengthArr[ 1 ];
                        length = lengthArr[ 0 ] + lengthArr[ 1 ] * 256;
                        *size  = length - 8;
                        qDebug() << "size : " << *size;
                        rxBuffer.Read( data, *size );
                    }
                    // CRC Check
                    quint8 crcArr[ 2 ] = { 0 };
                    if ( rxBuffer.Read( crcArr, sizeof( crcArr ) ) > 0 )
                    {
                        quint8 dataAll[ length - 2 ];
                        dataAll[ 0 ] = 0xA1, dataAll[ 1 ] = 0xA2, dataAll[ 2 ] = 0xA3, dataAll[ 3 ] = *type, dataAll[ 4 ] = lengthArr[ 0 ], dataAll[ 5 ] = lengthArr[ 1 ];
                        memcpy( &dataAll[ 6 ], data, *size );
                        uint16_t crcCpt = crc16( dataAll, length - 2 );
                        qDebug() << "CRC" << crcArr[ 0 ] << crcArr[ 1 ] << crcCpt;
                        if ( crcCpt % 256 == crcArr[ 0 ] && crcCpt / 256 == crcArr[ 1 ] )
                        {
                            return true;
                        }
                    }
                }
            }
        }
    }
    return false;
}

void Communication::onFrameReceived()
{
    qDebug() << "onFrameReceived";

    // 实现从串口读取数据到rxBuffer的逻辑，注意同步访问
    QByteArray info = m_serialPort->readAll();
    if ( info.size() > 0 )
    {
        //        qDebug() << "RS422_ReadData: " << info.size();
        // 没有剩余空间，则等待
        while ( rxBuffer.GetFreeLength() < info.size() )
        {
        }

        //        qDebug() << "FreeLength" << rxBuffer.GetFreeLength();
        if ( rxBuffer.GetFreeLength() >= info.size() )
        {
            QMutexLocker locker( &mutexR );
            //        qDebug() << "locker Sender ";

            quint8* data = reinterpret_cast< quint8* >( info.data() );
            //            qDebug() << "Write Buffer" << data[ 3 ];
            rxBuffer.Write( data, info.size() );

            // 第一种方式，直接解析数据并抛出信号。这里缺解析
            //                emit this->comm->valveSelfCheckFrameReceived( data );

            QByteArray hexArray;
            foreach ( unsigned char byte, QByteArray( reinterpret_cast< const char* >( data ), info.size() ) )
            {
                hexArray.append( byte );
            }
            QString hexString = hexArray.toHex( ' ' ).toUpper();  // ' '表示每个字节之间用空格分隔，toUpper转换为大写

            // 移除末尾的空格（如果有的话）
            hexString = hexString.trimmed();
            // 打印结果
            qDebug() << "Receive: " << hexString;

            rxSemaphore.release();
        }
    }
}

void Communication::ParseWorker::process()
{
    qDebug() << "ParseWorker process: ";
    quint8  data[ 512 ];
    quint8  type;
    quint16 length;
    while ( Communication::ParseWorker::isRunning )
    {
        if ( this->comm->readDataFromBuffer( &type, data, &length ) )
        {
            //            qDebug() << "parseFrame" << type << length;
            // parse frame
            if ( type == 0x01 )
            {
                emit this->comm->MachineParamFrameReceived( length, data );
            }
            else if ( type == 0x02 )
            {
                emit this->comm->UserParamFrameReceived( length, data );
            }
            else if ( type == 0x03 )
            {
                emit this->comm->StepDataFrameReceived( length, data );
            }
            else if ( type == 0x04 )
            {
                emit this->comm->LowerReportFrameReceived( length, data );
            }
            else if ( type == 0x06 )
            {
                emit this->comm->CommandFrameReceived( length, data );
            }
            else if ( type == 0x07 )
            {
                emit this->comm->StepMotorTestInfoFrameReceived( length, data );
            }
            else if ( type == 0x09 )
            {
                emit this->comm->ServoMotorTestInfoFrameReceived( length, data );
            }
            else if ( type == 0x0C )
            {
                emit this->comm->EEyeConfigFrameReceived( length, data );
            }

            else if ( type == 0x08 )
            {
                emit this->comm->SensorTestFrameReceived( length, data );
            }
            else if ( type == 0x0E )
            {
                emit this->comm->valveSelfCheckFrameReceived( length, data );
            }

            // qDebug() << "type" << type << "length" << length;
            // QByteArray hexArray;
            // foreach ( unsigned char byte, QByteArray( reinterpret_cast< const char* >( data ), length ) )
            // {
            //     hexArray.append( byte );
            // }
            // QString hexString = hexArray.toHex( ' ' ).toUpper();  // ' '表示每个字节之间用空格分隔，toUpper转换为大写

            // // 移除末尾的空格（如果有的话）
            // hexString = hexString.trimmed();
            // // 打印结果
            // qDebug() << "Parse String: " << hexString;
        }
    }
}

void Communication::ParseWorker::stop()
{
    Communication::ParseWorker::isRunning = false;
}
