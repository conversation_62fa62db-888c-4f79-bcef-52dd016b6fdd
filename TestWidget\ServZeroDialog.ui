<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ServZeroDialog</class>
 <widget class="QDialog" name="ServZeroDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>461</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QCheckBox" name="chx_step1">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>60</y>
     <width>541</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QCheckBox{
	background-color: rgb(75, 75, 75);
	width:70px;
	height:40px;
    border-radius: 5px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
	margin-left: 10px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
	margin-left: 10px;
}</string>
   </property>
   <property name="text">
    <string>请确定针简可以轻松正向旋转,然后按“下一步”</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QCheckBox" name="chx_step2">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>130</y>
     <width>541</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QCheckBox{
	background-color: rgb(75, 75, 75);
	width:70px;
	height:40px;
    border-radius: 5px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
	margin-left: 10px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
	margin-left: 10px;
}</string>
   </property>
   <property name="text">
    <string>请等待伺服自动校零完成!</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QCheckBox" name="chx_step3">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>200</y>
     <width>541</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QCheckBox{
	background-color: rgb(75, 75, 75);
	width:70px;
	height:40px;
    border-radius: 5px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
	margin-left: 10px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
	margin-left: 10px;
}</string>
   </property>
   <property name="text">
    <string>等待伺服重新启动!</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>191</x>
     <y>14</y>
     <width>221</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>伺服零位校准</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>280</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>实际角度</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_ActAngle">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>280</y>
     <width>131</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="text">
    <string>0.0</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>280</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>编码器值</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_CodeValue">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>280</y>
     <width>131</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_next">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>390</y>
     <width>151</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
   </property>
   <property name="text">
    <string>下一步</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_cancel">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>390</y>
     <width>151</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
   </property>
   <property name="text">
    <string>退出</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>330</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>零位位置</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_ZeroPostion">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>330</y>
     <width>131</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_zeroStatus">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>330</y>
     <width>241</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
		color: red;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_confirm_MachineZero">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>390</y>
     <width>161</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
   </property>
   <property name="text">
    <string>确认机器零位</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
