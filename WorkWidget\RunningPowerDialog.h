#ifndef RUNNINGPOWERDIALOG_H
#define RUNNINGPOWERDIALOG_H

#include "Communicate/upInfoMessage.h"
#include <QScreen>
#include <QWidget>

namespace Ui
{
class RunningPowerDialog;
}

class RunningPowerDialog : public QWidget
{
    Q_OBJECT

public:
    explicit RunningPowerDialog( QWidget* parent = nullptr );
    ~RunningPowerDialog();

    // Update power information from upMain
    void updatePowerInfo( const upMain& mainInfo );

private slots:
    void on_pbtn_Close_clicked();

private:
    Ui::RunningPowerDialog* ui;
};

#endif  // RUNNINGPOWERDIALOG_H
