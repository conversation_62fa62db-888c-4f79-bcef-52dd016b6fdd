#include "valveeditform.h"
#include "ui_valveeditform.h"
#include <QDebug>
#include <QScreen>

ValveEditForm::ValveEditForm( FATParser::ValveData* data, QMap< int, QString >* list, QWidget* parent ) : QWidget( parent ), ui( new Ui::ValveEditForm )
{
    ui->setupUi( this );
    this->_data = new FATParser::ValveData();
    memcpy( _data, data, sizeof( FATParser::ValveData ) );
    this->_srcData = data;
    this->_list    = list;

    this->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
    this->setWindowTitle( tr( "气阀动作编辑" ) );
    this->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    connect( ui->pbtn_0, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_1, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_2, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_3, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_4, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_5, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_6, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_7, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_8, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_9, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_minus, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );
    connect( ui->pbtn_backspace, &QPushButton::clicked, this, &ValveEditForm::onBtnClicked );

    connect( this->ui->le_Index, &MyLineEdit::mouseRelease, this, &ValveEditForm::onLineEditClicked );
    connect( this->ui->le_Pos, &MyLineEdit::mouseRelease, this, &ValveEditForm::onLineEditClicked );

    connect( ui->pbtn_OK, &QPushButton::clicked, this, &ValveEditForm::onPushButtonClicked );
    connect( ui->pbtn_Cancel, &QPushButton::clicked, this, &ValveEditForm::onPushButtonClicked );
    showValveData();
}

ValveEditForm::~ValveEditForm()
{
    delete ui;
    delete this->_data;
}

void ValveEditForm::setValveData( FATParser::ValveData* data )
{
    //    delete this->_data;
    //    this->_data = new FATParser::ValveData();
    memcpy( _data, data, sizeof( FATParser::ValveData ) );
    this->_srcData = data;
    showValveData();
}

void ValveEditForm::showValveData()
{
    quint8  valve_num = this->_data->Valve_num;
    quint8  sign      = this->_data->sign;
    quint16 pos       = this->_data->Valve_pos / 2;  //这里有个坑，位置要除以2才准确
    quint8  state     = this->_data->Valve_state;

    this->ui->le_Index->setText( QString::number( valve_num ) );
    this->ui->le_Pos->setText( ( sign == 1 ? QString( "-" ) : QString( "" ) ) + QString::number( pos ) );
    this->ui->le_State->setText( state == 1 ? QString( "进" ) : QString( "退" ) );
    this->ui->label_Pos->setText( ( sign == 1 ? QString( "-" ) : QString( "" ) ) + QString::number( pos ) );
    this->ui->label_valveName->setText( _list->value( valve_num ) );
}

void ValveEditForm::onLineEditClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        // 把上一个的外框效果删除，再赋值新一个
        if ( this->currentLineEdit != nullptr )
        {
            this->currentLineEdit->setStyleSheet( "font-size:16px;" );
        }
        this->currentLineEdit = senderLineEdit;
        // 第一次点击后全选，可以全部删除
        senderLineEdit->selectAll();
        this->firsTimeToEditLineEdit = true;
        senderLineEdit->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );
    }
}

void ValveEditForm::onLineEditFinished()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        senderLineEdit->setStyleSheet( "" );
    }
}

void ValveEditForm::onBtnClicked()
{
    QObject* senderObj = sender();

    if ( this->currentLineEdit != nullptr )
    {
        if ( this->firsTimeToEditLineEdit )
        {
            // 全选时，则删除全部内容
            this->currentLineEdit->setText( "" );
            this->firsTimeToEditLineEdit = false;
        }

        if ( senderObj == ui->pbtn_0 )
        {
            if ( this->currentLineEdit->text().length() >= 1 && this->currentLineEdit->text() != "-" )
                this->currentLineEdit->setText( this->currentLineEdit->text() + '0' );
        }
        else if ( senderObj == ui->pbtn_1 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '1' );
        else if ( senderObj == ui->pbtn_2 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '2' );
        else if ( senderObj == ui->pbtn_3 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '3' );
        else if ( senderObj == ui->pbtn_4 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '4' );
        else if ( senderObj == ui->pbtn_5 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '5' );
        else if ( senderObj == ui->pbtn_6 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '6' );
        else if ( senderObj == ui->pbtn_7 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '7' );
        else if ( senderObj == ui->pbtn_8 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '8' );
        else if ( senderObj == ui->pbtn_9 )
            this->currentLineEdit->setText( this->currentLineEdit->text() + '9' );

        else if ( senderObj == ui->pbtn_backspace )
        {
            QString text = this->currentLineEdit->text();
            text.chop( 1 );
            this->currentLineEdit->setText( text );
        }
        else if ( senderObj == ui->pbtn_minus )
        {
            if ( this->currentLineEdit->text().length() == 0 )
            {
                this->currentLineEdit->setText( "-" );
            }
        }
    }

    if ( this->currentLineEdit == this->ui->le_Index )
    {
        int index = this->ui->le_Index->text().toInt();
        this->ui->label_valveName->setText( this->_list->value( index ) );
    }
}

void ValveEditForm::onPushButtonClicked()
{
    QPushButton* button = qobject_cast< QPushButton* >( sender() );
    if ( button == this->ui->pbtn_OK )
    {
        // Save Data
        this->_data->Valve_num   = this->ui->le_Index->text().toUShort();
        this->_data->sign        = this->ui->le_Pos->text().startsWith( "-" ) ? 1 : 0;
        this->_data->Valve_pos   = ( this->_data->sign == 1 ? this->ui->le_Pos->text().remove( '-' ).toUInt() : this->ui->le_Pos->text().toUInt() ) * 2;
        this->_data->Valve_state = this->ui->le_State->text() == "进" ? 1 : 0;
        memcpy( this->_srcData, this->_data, sizeof( FATParser::ValveData ) );
        qDebug() << this->_data->Valve_num << this->_data->sign << this->_data->Valve_pos << this->_data->Valve_state;
    }
    this->close();
    emit finished();
}

void ValveEditForm::closeEvent( QCloseEvent* event )
{
    QWidget::closeEvent( event );
    emit finished();
}
