#include "readWarnConfig.h"

ReadWarnConfig::ReadWarnConfig() {}

ReadWarnConfig::~ReadWarnConfig() {}

void ReadWarnConfig::parseConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        // 文件打开失败
        qDebug() << "Warn Json  Open Failed!";
        QMessageBox::warning( nullptr, "错误提示", "WarnConfig.json文件不存在" );
        return;
    }
    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        // Json文档无效
        qDebug() << "Json文档无效!";
        return;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonArray jsonArray = jsonDocument.array();

    for ( int i = 0; i < jsonArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = jsonArray.at( i ).toObject();

        // 获取属性
        WarnCategory warnCategory;
        warnCategory.id   = step[ "id" ].toInt();
        warnCategory.name = step[ "name" ].toString();

        QJsonArray itemArray = step[ "items" ].toArray();
        for ( int j = 0; j < itemArray.size(); ++j )
        {
            QJsonObject itemObj = itemArray.at( j ).toObject();
            WarnItem    item;
            item.id         = itemObj[ "id" ].toInt();
            item.name       = itemObj[ "name" ].toString();
            item.desc       = itemObj[ "desc" ].toString();
            item.resourceId = itemObj[ "resourceId" ].toInt();
            item.img        = itemObj[ "img" ].toString();
            warnCategory.items.insert( item.id, item );
        }

        // 对key和value进行处理
        this->_list.insert( warnCategory.id, warnCategory );
    }

    file.close();  // 关闭文件
}
