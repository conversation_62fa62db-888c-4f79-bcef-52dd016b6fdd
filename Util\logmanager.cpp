#include "logmanager.h"
#include <QDir>
#include <QApplication>

LogManager* LogManager::instance = nullptr;

LogManager::LogManager(QObject *parent) : QObject(parent)
{
    logFile = nullptr;
    textStream = nullptr;
}

LogManager::~LogManager()
{
    if (textStream) {
        delete textStream;
        textStream = nullptr;
    }
    if (logFile) {
        logFile->close();
        delete logFile;
        logFile = nullptr;
    }
}

LogManager* LogManager::getInstance()
{
    if (!instance) {
        instance = new LogManager();
    }
    return instance;
}

void LogManager::initLogFile()
{
    // 创建logs目录
    QString logPath = QApplication::applicationDirPath() + "/logs";
    QDir dir(logPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    // 创建日志文件
    QString fileName = logPath + "/" + 
                      QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss") + ".log";
    
    logFile = new QFile(fileName);
    if (logFile->open(QIODevice::WriteOnly | QIODevice::Text)) {
        textStream = new QTextStream(logFile);
        *textStream << "Log file created at: " << 
                      QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n";
        textStream->flush();
    }
}

void LogManager::messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QMutexLocker locker(&mutex);
    
    if (!textStream || !logFile || !logFile->isOpen()) {
        return;
    }

    QString txt;
    switch (type) {
    case QtDebugMsg:
        txt = QString("Debug: %1").arg(msg);
        break;
    case QtWarningMsg:
        txt = QString("Warning: %1").arg(msg);
        break;
    case QtCriticalMsg:
        txt = QString("Critical: %1").arg(msg);
        break;
    case QtFatalMsg:
        txt = QString("Fatal: %1").arg(msg);
        break;
    }

    QString logMsg = QString("%1 | %2 | %3:%4 | %5\n")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(txt)
                        .arg(context.file)
                        .arg(context.line)
                        .arg(context.function);
                        
    *textStream << logMsg;
    textStream->flush();
}