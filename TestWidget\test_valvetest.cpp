﻿#include "CircleButton.h"
#include "testform.h"
#include "ui_testform.h"
#include <QDebug>
#include <QFile>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QVector>
#include <cstring>
#include <iostream>

/* 气阀测试初始化函数 */
void TestForm::ValveTestInit()
{
    if ( isValveTestPageInit == false )
    {
        /* 气阀控制按键组 */
        ValveTestCtrlBtnGroup = new QButtonGroup( this );  // 定义气阀测试控制按键组
        this->AddValveTestCtrlBtnToBtnGroup();
        connect( this->ValveTestCtrlBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onValveTestCtrlBtnGroupClicked( int ) ) );

        // 频率相关
        this->ui->le_valveFreq->setText( QString::number( this->valveTestFreq ) );

        ShowValveButtonPage();
        isValveTestPageInit = true;
    }
}

/* 将气阀测试按键加入到按键组中 */
void TestForm::AddValveTestCtrlBtnToBtnGroup()
{
    ValveTestCtrlBtnGroup->addButton( ui->ValveTest_Auto_btn, 0 );
    ValveTestCtrlBtnGroup->addButton( ui->ValveTest_OverTurn_btn, 1 );
    ValveTestCtrlBtnGroup->addButton( ui->ValveTest_Clear_btn, 2 );
    ValveTestCtrlBtnGroup->addButton( ui->ValveTest_Continue_btn, 3 );
    ValveTestCtrlBtnGroup->addButton( ui->ValveTest_FreqPlus_btn, 4 );
    ValveTestCtrlBtnGroup->addButton( ui->ValveTest_FreqDec_btn, 5 );
}

// 显示气阀测试按钮页
void TestForm::ShowValveButtonPage()
{
    // 清空之前的按钮状态记录
    specialFunStates.clear();
    camStates.clear();
    yarnFingerStates.clear();
    yarn2States.clear();
    extendStates.clear();

    // 获取配置数据
    QMap< int, ReadCfg::ValveStruct >* specialFunList = mainData->readCfg->getSpecialFunList();
    QMap< int, ReadCfg::ValveStruct >* camList        = mainData->readCfg->getCamList();
    QMap< int, ReadCfg::ValveStruct >* yarnFingerList = mainData->readCfg->getYarnFingerList();
    QMap< int, ReadCfg::ValveStruct >* yarn2List      = mainData->readCfg->getYarn2List();
    QMap< int, ReadCfg::ValveStruct >* extendList     = mainData->readCfg->getExtendList();

    // 每行显示的按钮数量
    const int buttonsPerRow = 10;

    // 生成特殊功能tab
    if ( specialFunList != nullptr && !specialFunList->isEmpty() )
    {
        QHBoxLayout* specialFunLayout = new QHBoxLayout( this->ui->tab_specialFun );
        int          size             = specialFunList->size();
        int          lines            = ( size - 1 ) / buttonsPerRow + 1;

        auto iter = specialFunList->begin();
        for ( int line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
        {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for ( int col = 0; col < buttonsPerRow; col++ )
            {
                if ( line * buttonsPerRow + col < size )
                {
                    QPushButton* btn = new QPushButton( iter.value().value );
                    btn->setProperty( "id", iter.value().mappedId );
                    btn->setProperty( "tabType", TAB_SPECIAL_FUN );
                    iter++;
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #f8f8f8;"
                                        "    color: #333333;"
                                        "    border: 1px solid #dddddd;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #e8f4fc;"
                                        "    border: 1px solid #3498db;"
                                        "    color: #2980b9;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #3498db;"
                                        "    color: white;"
                                        "    border: 1px solid #2980b9;"
                                        "}" );
                    btn->setMinimumHeight( 40 );
                    connect( btn, &QPushButton::clicked, this, &TestForm::onValveButtonClicked );
                    columnLayout->addWidget( btn );

                    // 记录按钮状态
                    ValveButtonState state;
                    state.id                     = btn->property( "id" ).toInt();
                    state.isPressed              = false;
                    state.button                 = btn;
                    specialFunStates[ state.id ] = state;
                }
                else
                {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget( widget );
                    widget->setMinimumHeight( 40 );
                }
            }
            specialFunLayout->addLayout( columnLayout );
        }
    }

    // 生成凸轮tab
    if ( camList != nullptr && !camList->isEmpty() )
    {
        QHBoxLayout* camLayout = new QHBoxLayout( this->ui->tab_cam );
        int          size      = camList->size();
        int          lines     = ( size - 1 ) / buttonsPerRow + 1;

        auto iter = camList->begin();
        for ( int line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
        {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for ( int col = 0; col < buttonsPerRow; col++ )
            {
                if ( line * buttonsPerRow + col < size )
                {
                    QPushButton* btn = new QPushButton( iter.value().value );
                    btn->setProperty( "id", iter.value().mappedId );
                    btn->setProperty( "tabType", TAB_CAM );
                    iter++;
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #f8f8f8;"
                                        "    color: #333333;"
                                        "    border: 1px solid #dddddd;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #e8f4fc;"
                                        "    border: 1px solid #3498db;"
                                        "    color: #2980b9;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #3498db;"
                                        "    color: white;"
                                        "    border: 1px solid #2980b9;"
                                        "}"
                                        "QPushButton:focus {"
                                        "    outline: none;"
                                        "}" );
                    btn->setMinimumHeight( 40 );
                    connect( btn, &QPushButton::clicked, this, &TestForm::onValveButtonClicked );
                    columnLayout->addWidget( btn );

                    // 记录按钮状态
                    ValveButtonState state;
                    state.id              = btn->property( "id" ).toInt();
                    state.isPressed       = false;
                    state.button          = btn;
                    camStates[ state.id ] = state;
                }
                else
                {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget( widget );
                    widget->setMinimumHeight( 40 );
                }
            }
            camLayout->addLayout( columnLayout );
        }
    }

    // 生成纱嘴tab
    if ( yarnFingerList != nullptr && !yarnFingerList->isEmpty() )
    {
        QHBoxLayout* yarnFingerLayout = new QHBoxLayout( this->ui->tab_yarnFinger );
        int          size             = yarnFingerList->size();
        int          lines            = ( size - 1 ) / buttonsPerRow + 1;

        auto iter = yarnFingerList->begin();
        for ( int line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
        {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for ( int col = 0; col < buttonsPerRow; col++ )
            {
                if ( line * buttonsPerRow + col < size )
                {
                    QPushButton* btn = new QPushButton( iter.value().value );
                    btn->setProperty( "id", iter.value().mappedId );
                    btn->setProperty( "tabType", TAB_YARN_FINGER );
                    iter++;
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #f8f8f8;"
                                        "    color: #333333;"
                                        "    border: 1px solid #dddddd;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #e8f4fc;"
                                        "    border: 1px solid #3498db;"
                                        "    color: #2980b9;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #3498db;"
                                        "    color: white;"
                                        "    border: 1px solid #2980b9;"
                                        "}"
                                        "QPushButton:focus {"
                                        "    outline: none;"
                                        "}" );
                    btn->setMinimumHeight( 40 );
                    connect( btn, &QPushButton::clicked, this, &TestForm::onValveButtonClicked );
                    columnLayout->addWidget( btn );

                    // 记录按钮状态
                    ValveButtonState state;
                    state.id                     = btn->property( "id" ).toInt();
                    state.isPressed              = false;
                    state.button                 = btn;
                    yarnFingerStates[ state.id ] = state;
                }
                else
                {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget( widget );
                    widget->setMinimumHeight( 40 );
                }
            }
            yarnFingerLayout->addLayout( columnLayout );
        }
    }

    // 生成 Yarn2 tab
    if ( yarn2List != nullptr && !yarn2List->isEmpty() )
    {
        QHBoxLayout* yarn2Layout = new QHBoxLayout( this->ui->tab_yarn2 );
        int          size        = yarn2List->size();
        int          lines       = ( size - 1 ) / buttonsPerRow + 1;

        auto iter = yarn2List->begin();
        for ( int line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
        {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for ( int col = 0; col < buttonsPerRow; col++ )
            {
                if ( line * buttonsPerRow + col < size )
                {
                    QPushButton* btn = new QPushButton( iter.value().value );
                    btn->setProperty( "id", iter.value().mappedId );
                    btn->setProperty( "tabType", TAB_YARN2 );
                    iter++;
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #f8f8f8;"
                                        "    color: #333333;"
                                        "    border: 1px solid #dddddd;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #e8f4fc;"
                                        "    border: 1px solid #3498db;"
                                        "    color: #2980b9;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #3498db;"
                                        "    color: white;"
                                        "    border: 1px solid #2980b9;"
                                        "}"
                                        "QPushButton:focus {"
                                        "    outline: none;"
                                        "}" );
                    btn->setMinimumHeight( 40 );
                    connect( btn, &QPushButton::clicked, this, &TestForm::onValveButtonClicked );
                    columnLayout->addWidget( btn );

                    // 记录按钮状态
                    ValveButtonState state;
                    state.id                = btn->property( "id" ).toInt();
                    state.isPressed         = false;
                    state.button            = btn;
                    yarn2States[ state.id ] = state;
                }
                else
                {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget( widget );
                    widget->setMinimumHeight( 40 );
                }
            }
            yarn2Layout->addLayout( columnLayout );
        }
    }

    // 生成 extend tab
    if ( extendList != nullptr && !extendList->isEmpty() )
    {
        QHBoxLayout* extendLayout = new QHBoxLayout( this->ui->tab_extend );
        int          size         = extendList->size();
        int          lines        = ( size - 1 ) / buttonsPerRow + 1;

        auto iter = extendList->begin();
        for ( int line = 0; line < ( lines > 5 ? lines : 5 ); line++ )
        {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for ( int col = 0; col < buttonsPerRow; col++ )
            {
                if ( line * buttonsPerRow + col < size )
                {
                    QPushButton* btn = new QPushButton( iter.value().value );
                    btn->setProperty( "id", iter.value().mappedId );
                    btn->setProperty( "tabType", TAB_EXTEND );
                    iter++;
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #f8f8f8;"
                                        "    color: #333333;"
                                        "    border: 1px solid #dddddd;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #e8f4fc;"
                                        "    border: 1px solid #3498db;"
                                        "    color: #2980b9;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #3498db;"
                                        "    color: white;"
                                        "    border: 1px solid #2980b9;"
                                        "}"
                                        "QPushButton:focus {"
                                        "    outline: none;"
                                        "}" );
                    btn->setMinimumHeight( 40 );
                    connect( btn, &QPushButton::clicked, this, &TestForm::onValveButtonClicked );
                    columnLayout->addWidget( btn );

                    // 记录按钮状态
                    ValveButtonState state;
                    state.id                 = btn->property( "id" ).toInt();
                    state.isPressed          = false;
                    state.button             = btn;
                    extendStates[ state.id ] = state;
                }
                else
                {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget( widget );
                    widget->setMinimumHeight( 40 );
                }
            }
            extendLayout->addLayout( columnLayout );
        }
    }

    // 连接tab切换信号
    connect( ui->tabWidget_Valve, &QTabWidget::currentChanged, this, [ this ]( int index ) { currentValveTab = static_cast< ValveTabType >( index ); } );

    // 设置默认显示的tab为特殊功能tab
    ui->tabWidget_Valve->setCurrentIndex( TAB_SPECIAL_FUN );
}

void TestForm::onValveButtonClicked()
{
    if ( isValveAutoTestingRunning )
    {
        QMessageBox::warning( nullptr, "错误提示", "自动测试过程中不能操作按钮，请先停止自动测试" );
        return;
    }

    QPushButton* btn = qobject_cast< QPushButton* >( sender() );
    if ( btn )
    {
        int          id      = btn->property( "id" ).toInt();
        ValveTabType tabType = static_cast< ValveTabType >( btn->property( "tabType" ).toInt() );

        // 是否有连续任务正执行，没有的话，执行一次动作
        if ( this->testTimer == nullptr )
        {
            // 根据tab类型获取对应的状态map
            QMap< int, ValveButtonState >* stateMap = nullptr;
            switch ( tabType )
            {
                case TAB_SPECIAL_FUN:
                    stateMap = &specialFunStates;
                    break;
                case TAB_CAM:
                    stateMap = &camStates;
                    break;
                case TAB_YARN_FINGER:
                    stateMap = &yarnFingerStates;
                    break;
                case TAB_YARN2:
                    stateMap = &yarn2States;
                    break;
                case TAB_EXTEND:
                    stateMap = &extendStates;
                    break;
            }

            if ( stateMap )
            {
                ValveButtonState& state = ( *stateMap )[ id ];
                if ( state.isPressed )
                {
                    // 当前为按下状态，改为未按下
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #f8f8f8;"
                                        "    color: #333333;"
                                        "    border: 1px solid #dddddd;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #e8f4fc;"
                                        "    border: 1px solid #3498db;"
                                        "    color: #2980b9;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #3498db;"
                                        "    color: white;"
                                        "    border: 1px solid #2980b9;"
                                        "}"
                                        "QPushButton:focus {"
                                        "    outline: none;"
                                        "}" );
                    state.isPressed = false;
                }
                else
                {
                    // 当前为未按下状态，改为按下
                    btn->setStyleSheet( "QPushButton {"
                                        "    font-size: 9pt;"
                                        "    background-color: #e74c3c;"
                                        "    color: white;"
                                        "    border: 1px solid #c0392b;"
                                        "    border-radius: 5px;"
                                        "    padding: 5px;"
                                        "}"
                                        "QPushButton:hover {"
                                        "    background-color: #c0392b;"
                                        "    border: 1px solid #a93226;"
                                        "    color: white;"
                                        "}"
                                        "QPushButton:pressed {"
                                        "    background-color: #a93226;"
                                        "    color: white;"
                                        "    border: 1px solid #922b21;"
                                        "}"
                                        "QPushButton:focus {"
                                        "    outline: none;"
                                        "}" );
                    state.isPressed = true;
                }
                sendValveState( id );
            }
        }
    }
}

void TestForm::valveAutoTesting()
{
    QMap< int, ValveButtonState >* stateMap = nullptr;
    switch ( currentValveTab )
    {
        case TAB_SPECIAL_FUN:
            stateMap = &specialFunStates;
            break;
        case TAB_CAM:
            stateMap = &camStates;
            break;
        case TAB_YARN_FINGER:
            stateMap = &yarnFingerStates;
            break;
        case TAB_YARN2:
            stateMap = &yarn2States;
            break;
        case TAB_EXTEND:
            stateMap = &extendStates;
            break;
    }

    if ( !stateMap )
        return;

    // 第一次运行时收集所有已按下的按钮
    if ( autoTestIsFirstRun )
    {
        autoTestPressedButtons.clear();
        for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
        {
            if ( it.value().isPressed )
            {
                autoTestPressedButtons.append( it.key() );
            }
        }
        autoTestIsFirstRun   = false;
        autoTestCurrentIndex = 0;
    }

    // 如果没有按下的按钮，停止定时器
    if ( autoTestPressedButtons.isEmpty() )
    {
        stopAutoTask();
        return;
    }

    // 恢复上一个按钮的状态
    if ( autoTestCurrentIndex > 0 )
    {
        // 恢复上一个按钮
        int               lastId    = autoTestPressedButtons[ autoTestCurrentIndex - 1 ];
        ValveButtonState& lastState = ( *stateMap )[ lastId ];
        if ( lastState.button )
        {  // 检查按钮是否有效
            lastState.button->setStyleSheet( "QPushButton {"
                                             "    font-size: 9pt;"
                                             "    background-color: #e74c3c;"
                                             "    color: white;"
                                             "    border: 1px solid #c0392b;"
                                             "    border-radius: 5px;"
                                             "    padding: 5px;"
                                             "}" );
            sendValveState( lastId, 0 );
        }
    }
    else if ( autoTestCurrentIndex == 0 )
    {
        // 当前是第一个按钮，需要复最后一个按钮的状态
        int               lastId    = autoTestPressedButtons[ autoTestPressedButtons.size() - 1 ];
        ValveButtonState& lastState = ( *stateMap )[ lastId ];
        if ( lastState.button )
        {  // 检查按钮是否有效
            lastState.button->setStyleSheet( "QPushButton {"
                                             "    font-size: 9pt;"
                                             "    background-color: #e74c3c;"
                                             "    color: white;"
                                             "    border: 1px solid #c0392b;"
                                             "    border-radius: 5px;"
                                             "    padding: 5px;"
                                             "}" );
            sendValveState( lastId, 0 );
        }
    }

    // 处理当前按钮
    int               currentId    = autoTestPressedButtons[ autoTestCurrentIndex ];
    ValveButtonState& currentState = ( *stateMap )[ currentId ];
    if ( currentState.button )
    {
        currentState.button->setStyleSheet( "QPushButton {"
                                            "    font-size: 9pt;"
                                            "    background-color: #2ecc71;"
                                            "    color: white;"
                                            "    border: 1px solid #27ae60;"
                                            "    border-radius: 5px;"
                                            "    padding: 5px;"
                                            "}" );
        sendValveState( currentId, 1 );
    }

    // 移动到下一个按钮
    autoTestCurrentIndex++;
    if ( autoTestCurrentIndex >= autoTestPressedButtons.size() )
    {
        autoTestCurrentIndex = 0;
    }
}

void TestForm::valveContinuousTesting()
{
    QMap< int, ValveButtonState >* stateMap = nullptr;
    switch ( currentValveTab )
    {
        case TAB_SPECIAL_FUN:
            stateMap = &specialFunStates;
            break;
        case TAB_CAM:
            stateMap = &camStates;
            break;
        case TAB_YARN_FINGER:
            stateMap = &yarnFingerStates;
            break;
        case TAB_YARN2:
            stateMap = &yarn2States;
            break;
        case TAB_EXTEND:
            stateMap = &extendStates;
            break;
    }

    if ( !stateMap )
        return;

    static QList< int > allButtons;        // 记录当前tab所有按钮的ID
    static int          currentIndex = 0;  // 记录索引号

    // 第一次运行时收集所有按钮
    if ( continuousTestIsFirstRun )
    {
        allButtons.clear();
        // 先清除所有选中状态
        for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
        {
            ValveButtonState& state = it.value();
            state.isPressed         = false;  // 清除按钮的选中状态
            if ( state.button )
            {
                state.button->setStyleSheet( "QPushButton {"
                                             "    font-size: 9pt;"
                                             "    background-color: #f8f8f8;"
                                             "    color: #333333;"
                                             "    border: 1px solid #dddddd;"
                                             "    border-radius: 5px;"
                                             "    padding: 5px;"
                                             "}"
                                             "QPushButton:hover {"
                                             "    background-color: #e8f4fc;"
                                             "    border: 1px solid #3498db;"
                                             "    color: #2980b9;"
                                             "}"
                                             "QPushButton:pressed {"
                                             "    background-color: #3498db;"
                                             "    color: white;"
                                             "    border: 1px solid #2980b9;"
                                             "}"
                                             "QPushButton:focus {"
                                             "    outline: none;"
                                             "}" );
            }
            allButtons.append( it.key() );
        }
        continuousTestIsFirstRun = false;
        currentIndex             = 0;
    }

    // 恢复上一个按钮的状态
    if ( currentIndex > 0 )
    {
        int               lastId    = allButtons[ currentIndex - 1 ];
        ValveButtonState& lastState = ( *stateMap )[ lastId ];
        if ( lastState.button )
        {
            lastState.button->setStyleSheet( "QPushButton {"
                                             "    font-size: 9pt;"
                                             "    background-color: #f8f8f8;"
                                             "    color: #333333;"
                                             "    border: 1px solid #dddddd;"
                                             "    border-radius: 5px;"
                                             "    padding: 5px;"
                                             "}" );
            sendValveState( lastId, 0 );
        }
    }
    else if ( currentIndex == 0 )
    {
        // 当前是第一个按钮，需要恢复最后一个按钮的状态
        int               lastId    = allButtons[ allButtons.size() - 1 ];
        ValveButtonState& lastState = ( *stateMap )[ lastId ];
        if ( lastState.button )
        {
            lastState.button->setStyleSheet( "QPushButton {"
                                             "    font-size: 9pt;"
                                             "    background-color: #f8f8f8;"
                                             "    color: #333333;"
                                             "    border: 1px solid #dddddd;"
                                             "    border-radius: 5px;"
                                             "    padding: 5px;"
                                             "}" );
            sendValveState( lastId, 0 );
        }
    }

    // 处理当前按钮
    int               currentId    = allButtons[ currentIndex ];
    ValveButtonState& currentState = ( *stateMap )[ currentId ];
    if ( currentState.button )
    {
        currentState.button->setStyleSheet( "QPushButton {"
                                            "    font-size: 9pt;"
                                            "    background-color: #2ecc71;"
                                            "    color: white;"
                                            "    border: 1px solid #27ae60;"
                                            "    border-radius: 5px;"
                                            "    padding: 5px;"
                                            "}" );
        sendValveState( currentId, 1 );
    }
    continuousLastId = currentId;

    // 移动到下一个按钮
    currentIndex++;
    if ( currentIndex >= allButtons.size() )
    {
        currentIndex = 0;
    }
}

/* 气阀测试控制按键组按下槽函数 */
void TestForm::onValveTestCtrlBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 0: {  // 自动按键
            if ( this->isValveContinuousTestingRunning )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止连续测试任务" );
                return;
            }
            if ( this->testTimer == nullptr )
            {
                // 检查当前tab是否有按下的按钮
                QMap< int, ValveButtonState >* stateMap = nullptr;
                switch ( currentValveTab )
                {
                    case TAB_SPECIAL_FUN:
                        stateMap = &specialFunStates;
                        break;
                    case TAB_CAM:
                        stateMap = &camStates;
                        break;
                    case TAB_YARN_FINGER:
                        stateMap = &yarnFingerStates;
                        break;
                    case TAB_YARN2:
                        stateMap = &yarn2States;
                        break;
                    case TAB_EXTEND:
                        stateMap = &extendStates;
                        break;
                }

                bool hasPressed = false;
                if ( stateMap )
                {
                    for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
                    {
                        if ( it.value().isPressed )
                        {
                            hasPressed = true;
                            break;
                        }
                    }
                }

                if ( !hasPressed )
                {
                    QMessageBox::warning( nullptr, "错误提示", "请先选择需要测试的按钮" );
                    return;
                }

                // 重置自动测试相关变量
                autoTestPressedButtons.clear();
                autoTestCurrentIndex = 0;
                autoTestIsFirstRun   = true;

                this->testTimer = new QTimer( this );
                QObject::connect( this->testTimer, &QTimer::timeout, this, &TestForm::valveAutoTesting );
                this->ui->ValveTest_Auto_btn->setStyleSheet( "QPushButton {"
                                                             "    background-color: #3498db;"
                                                             "    color: white;"
                                                             "    border: 1px solid #2980b9;"
                                                             "    border-radius: 5px;"
                                                             "    padding: 5px;"
                                                             "    font-size: 9pt;"
                                                             "    font-weight: bold;"
                                                             "}" );

                // 频率为1秒多少次
                this->testTimer->setInterval( 1000 / valveTestFreq );
                this->testTimer->start();
                this->isValveAutoTestingRunning = true;

                // 禁用相关控件
                ui->tabWidget_Valve->setEnabled( false );
                ui->TestFormHome_btn->setEnabled( false );
                ui->ValveTest_OverTurn_btn->setEnabled( false );
                ui->ValveTest_Clear_btn->setEnabled( false );
                ui->ValveTest_Continue_btn->setEnabled( false );
            }
            else
            {
                stopAutoTask();

                // 恢复控件状态
                ui->tabWidget_Valve->setEnabled( true );
                ui->TestFormHome_btn->setEnabled( true );
                ui->ValveTest_OverTurn_btn->setEnabled( true );
                ui->ValveTest_Clear_btn->setEnabled( true );
                ui->ValveTest_Continue_btn->setEnabled( true );

                // 恢复按钮样式
                ui->ValveTest_Auto_btn->setStyleSheet( "QPushButton {"
                                                       "background-color: #f8f8f8;"
                                                       "border: 1px solid #dddddd;"
                                                       "border-radius: 5px;"
                                                       "padding: 5px;"
                                                       "color: #333333;"
                                                       "font-size: 9pt;"
                                                       "}" );
            }
            break;
        }
        case 1: {  // 翻转按键
            if ( this->testTimer != nullptr )
            {
                QMessageBox::warning( nullptr, "错误提示", "要先停止自动任务" );
                return;
            }

            // 获取当前tab的按钮状态map
            QMap< int, ValveButtonState >* stateMap = nullptr;
            switch ( currentValveTab )
            {
                case TAB_SPECIAL_FUN:
                    stateMap = &specialFunStates;
                    break;
                case TAB_CAM:
                    stateMap = &camStates;
                    break;
                case TAB_YARN_FINGER:
                    stateMap = &yarnFingerStates;
                    break;
                case TAB_YARN2:
                    stateMap = &yarn2States;
                    break;
                case TAB_EXTEND:
                    stateMap = &extendStates;
                    break;
            }

            if ( stateMap )
            {
                static bool isFlipped = false;  // 记录当前是否处于翻转状态

                // 遍历当前tab的所有按钮只翻转已选中的按钮
                for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
                {
                    ValveButtonState& state = it.value();
                    if ( state.isPressed )
                    {  // 只处理已选中的按钮
                        if ( state.button )
                        {
                            if ( !isFlipped )
                            {
                                // 第一次点击：翻转为未选中状态
                                state.button->setStyleSheet( "QPushButton {"
                                                             "    font-size: 9pt;"
                                                             "    background-color: #f8f8f8;"
                                                             "    color: #333333;"
                                                             "    border: 1px solid #dddddd;"
                                                             "    border-radius: 5px;"
                                                             "    padding: 5px;"
                                                             "}" );
                            }
                            else
                            {
                                // 第二次点击：恢复为选中状态
                                state.button->setStyleSheet( "QPushButton {"
                                                             "    font-size: 9pt;"
                                                             "    background-color: #e74c3c;"
                                                             "    color: white;"
                                                             "    border: 1px solid #c0392b;"
                                                             "    border-radius: 5px;"
                                                             "    padding: 5px;"
                                                             "}" );
                            }

                            sendValveState( state.id, isFlipped ? 1 : 0 );
                        }
                    }
                }

                // 切换翻转状态
                isFlipped = !isFlipped;
            }
            break;
        }
        case 2: {  // 清除按键
            if ( this->testTimer != nullptr )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止自动任务" );
                return;
            }

            // 获取当前tab的按钮状态map
            QMap< int, ValveButtonState >* stateMap = nullptr;
            switch ( currentValveTab )
            {
                case TAB_SPECIAL_FUN:
                    stateMap = &specialFunStates;
                    break;
                case TAB_CAM:
                    stateMap = &camStates;
                    break;
                case TAB_YARN_FINGER:
                    stateMap = &yarnFingerStates;
                    break;
                case TAB_YARN2:
                    stateMap = &yarn2States;
                    break;
                case TAB_EXTEND:
                    stateMap = &extendStates;
                    break;
            }

            if ( stateMap )
            {
                // 遍历当前tab的所有按钮，清除选中状态
                for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
                {
                    ValveButtonState& state = it.value();
                    if ( state.isPressed )
                    {
                        state.isPressed = false;
                        if ( state.button )
                        {
                            state.button->setStyleSheet( "QPushButton {"
                                                         "    font-size: 9pt;"
                                                         "    background-color: #f8f8f8;"
                                                         "    color: #333333;"
                                                         "    border: 1px solid #dddddd;"
                                                         "    border-radius: 5px;"
                                                         "    padding: 5px;"
                                                         "}" );
                        }
                        sendValveState( state.id );
                    }
                }
                // sendValveState();  // 发送状态更新
            }
            break;
        }
        case 3: {  // 连续按键
            if ( this->isValveAutoTestingRunning )
            {
                QMessageBox::warning( nullptr, "错误提示", "需要先停止自动测试任务" );
                return;
            }
            if ( this->testTimer == nullptr )
            {
                // 检查当前tab是否有按钮
                QMap< int, ValveButtonState >* stateMap = nullptr;
                switch ( currentValveTab )
                {
                    case TAB_SPECIAL_FUN:
                        stateMap = &specialFunStates;
                        break;
                    case TAB_CAM:
                        stateMap = &camStates;
                        break;
                    case TAB_YARN_FINGER:
                        stateMap = &yarnFingerStates;
                        break;
                    case TAB_YARN2:
                        stateMap = &yarn2States;
                        break;
                    case TAB_EXTEND:
                        stateMap = &extendStates;
                        break;
                }

                if ( !stateMap || stateMap->isEmpty() )
                {
                    QMessageBox::warning( nullptr, "错误提示", "当前页面没有可测试的按钮" );
                    return;
                }

                // 清空所有按钮的状态
                for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
                {
                    ValveButtonState& state = it.value();
                    if ( state.isPressed )
                    {
                        state.isPressed = false;
                        if ( state.button )
                        {
                            state.button->setStyleSheet( "QPushButton {"
                                                         "    font-size: 9pt;"
                                                         "    background-color: #f8f8f8;"
                                                         "    color: #333333;"
                                                         "    border: 1px solid #dddddd;"
                                                         "    border-radius: 5px;"
                                                         "    padding: 5px;"
                                                         "}" );
                        }
                        sendValveState( state.id );
                    }
                }
                // sendValveState();  // 发送更新后的状态

                continuousTestIsFirstRun = true;
                this->testTimer          = new QTimer( this );
                QObject::connect( this->testTimer, &QTimer::timeout, this, &TestForm::valveContinuousTesting );
                this->ui->ValveTest_Continue_btn->setStyleSheet( "QPushButton {"
                                                                 "    background-color: #3498db;"
                                                                 "    color: white;"
                                                                 "    border: 1px solid #2980b9;"
                                                                 "    border-radius: 5px;"
                                                                 "    padding: 5px;"
                                                                 "    font-size: 9pt;"
                                                                 "    font-weight: bold;"
                                                                 "}" );

                // 频率为1秒多少次
                this->testTimer->setInterval( 1000 / valveTestFreq );
                this->testTimer->start();
                this->isValveContinuousTestingRunning = true;

                // 禁用相关控件
                ui->tabWidget_Valve->setEnabled( false );
                ui->TestFormHome_btn->setEnabled( false );
                ui->ValveTest_OverTurn_btn->setEnabled( false );
                ui->ValveTest_Clear_btn->setEnabled( false );
                ui->ValveTest_Auto_btn->setEnabled( false );
            }
            else
            {
                stopAutoTask();

                // 恢复控件状态
                ui->tabWidget_Valve->setEnabled( true );
                ui->TestFormHome_btn->setEnabled( true );
                ui->ValveTest_OverTurn_btn->setEnabled( true );
                ui->ValveTest_Clear_btn->setEnabled( true );
                ui->ValveTest_Auto_btn->setEnabled( true );

                // 恢复按钮样式
                ui->ValveTest_Continue_btn->setStyleSheet( "QPushButton {"
                                                           "background-color: #f8f8f8;"
                                                           "border: 1px solid #dddddd;"
                                                           "border-radius: 5px;"
                                                           "padding: 5px;"
                                                           "color: #333333;"
                                                           "font-size: 9pt;"
                                                           "}" );
            }
            break;
        }
        case 4: {  // 频率加按键
            this->valveTestFreq++;
            this->ui->le_valveFreq->setText( QString::number( this->valveTestFreq ) );
            if ( this->testTimer != nullptr )
            {
                this->testTimer->setInterval( 1000 / valveTestFreq );
            }
            break;
        }
        case 5: {  // 频率减按键
            if ( this->valveTestFreq > 1 )
            {
                this->valveTestFreq--;
                this->ui->le_valveFreq->setText( QString::number( this->valveTestFreq ) );
            }
            if ( this->testTimer != nullptr )
            {
                this->testTimer->setInterval( 1000 / valveTestFreq );
            }
            break;
        }
    }
}

void TestForm::sendValveState( int buttonId, int buttonState )
{
    QMap< int, ValveButtonState >* stateMap = nullptr;
    switch ( currentValveTab )
    {
        case TAB_SPECIAL_FUN:
            stateMap = &specialFunStates;
            break;
        case TAB_CAM:
            stateMap = &camStates;
            break;
        case TAB_YARN_FINGER:
            stateMap = &yarnFingerStates;
            break;
        case TAB_YARN2:
            stateMap = &yarn2States;
            break;
        case TAB_EXTEND:
            stateMap = &extendStates;
            break;
    }

    if ( !stateMap )
        return;

    if ( buttonId >= 0 )
    {
        // 发送单个按钮的状态
        if ( stateMap->contains( buttonId ) )
        {
            const ValveButtonState& state = ( *stateMap )[ buttonId ];

            quint8 sendData[ 2 ];
            sendData[ 0 ] = static_cast< quint8 >( state.id );
            sendData[ 1 ] = ( buttonState >= 0 ) ? static_cast< quint8 >( buttonState ) : ( state.isPressed ? 1 : 0 );  // 按钮状态：1为按下，0为未按下

            // 发送数据
            comm->pushDataTobuffer( 0x0A, sendData, sizeof( sendData ) );

            // 查找Position并显示
            if ( sendData[ 1 ] == 1 )
            {
                // 根据currentValveTab查找对应的配置列表
                QMap< int, ReadCfg::ValveStruct >* cfgList = nullptr;
                switch ( currentValveTab )
                {
                    case TAB_SPECIAL_FUN:
                        cfgList = mainData->readCfg->getSpecialFunList();
                        break;
                    case TAB_CAM:
                        cfgList = mainData->readCfg->getCamList();
                        break;
                    case TAB_YARN_FINGER:
                        cfgList = mainData->readCfg->getYarnFingerList();
                        break;
                    case TAB_YARN2:
                        cfgList = mainData->readCfg->getYarn2List();
                        break;
                    case TAB_EXTEND:
                        cfgList = mainData->readCfg->getExtendList();
                        break;
                }

                if ( cfgList )
                {
                    // 遍历配置列表查找匹配的mappedId
                    for ( const auto& item : *cfgList )
                    {
                        if ( item.mappedId == state.id )
                        {
                            QString position = item.position;
                            ui->lbl_valPos->setText( position );
                            break;
                        }
                    }
                }
            }
        }
    }
    else
    {
        // 发送所有按钮的状态（用于清除和初始化时）
        for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
        {
            const ValveButtonState& state = it.value();
            quint8                  sendData[ 2 ];
            sendData[ 0 ] = static_cast< quint8 >( state.id );  // 按钮ID
            sendData[ 1 ] = state.isPressed ? 1 : 0;            // 按钮状态：1为按下，0为未按下

            // 发送数据
            comm->pushDataTobuffer( 0x0A, sendData, sizeof( sendData ) );
        }
    }
}
