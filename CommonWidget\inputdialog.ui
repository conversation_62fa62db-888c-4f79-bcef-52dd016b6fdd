<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>InputDialog</class>
 <widget class="QDialog" name="InputDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>533</width>
    <height>386</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>输入对话框</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog {
    border-radius: 10px;
    background-color: #f5f5f5;
    border: 1px solid #cccccc;
}

QPushButton {
    font-size: 10pt;
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}

QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}

QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

#pushBtn_OK {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
    font-weight: bold;
}

#pushBtn_OK:hover {
    background-color: #2980b9;
}

#pushBtn_OK:pressed {
    background-color: #1f6aa5;
}

#pushBtn_Cancel {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

#pushBtn_Cancel:hover {
    background-color: #c0392b;
}

#pushBtn_Cancel:pressed {
    background-color: #a93226;
}</string>
  </property>
  <widget class="QPushButton" name="btn_T">
   <property name="geometry">
    <rect>
     <x>470</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>T</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_1">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>1</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_0">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_R">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>R</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_W">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>W</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_9">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>9</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_E">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>E</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_5">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>5</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_S">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>S</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_X">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>X</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_J">
   <property name="geometry">
    <rect>
     <x>470</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>J</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_O">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>O</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_backspace">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>250</y>
     <width>91</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
    background-color: #e74c3c;
    color: white;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>←</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_Y">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>Y</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_F">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>F</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_4">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>4</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_H">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>H</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_M">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>M</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_I">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>I</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_6">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>6</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_line">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>-</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushBtn_OK">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>320</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #0062cc;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string>确定</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_L">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>L</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_D">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>D</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_Type">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>40</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    color: #2980b9;
}</string>
   </property>
   <property name="text">
    <string>输入</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushBtn_Cancel">
   <property name="geometry">
    <rect>
     <x>280</x>
     <y>320</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>退出</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_Q">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>Q</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_2">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>2</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_C">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>C</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_P">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>P</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_G">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>G</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_N">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>N</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_3">
   <property name="geometry">
    <rect>
     <x>470</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>3</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_8">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>8</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>40</y>
     <width>381</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLineEdit {
    background-color: white;
    border: 1px solid #3498db;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_V">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>V</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_A">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_U">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>U</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_Shift">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
    background-color: #3498db;
    color: white;
}</string>
   </property>
   <property name="text">
    <string>⇧</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_B">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>B</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_7">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>250</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>7</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_K">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>150</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>K</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_Z">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>200</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>Z</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
