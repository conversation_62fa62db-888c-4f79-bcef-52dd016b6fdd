#include "testform.h"
#include "ui_testform.h"

void TestForm::initServoParaPage()
{
    if ( isServoParaPageInit == false )
    {
        initGroupBoxDown();
        // Close Button
        connect( ui->btn_servoParaClose, &QPushButton::clicked, this, [ & ]() { ui->Test_stackedWidget->setCurrentIndex( 3 ); } );
        // SendALl Button
        connect( ui->btn_servoParaSendAll, &QPushButton::clicked, this, [ & ]() {
            MachineParams machineParams = mainData->readMachineFileConfig->makeMachineParamFrame();
            // 创建数组，将userParams的值赋值给数组，然后通过comm->pushDataTobuffer()发送
            quint8 buffer[ sizeof( machineParams ) ];
            memcpy( buffer, &machineParams, sizeof( MachineParams ) );
            comm->pushDataTobuffer( 0x01, buffer, sizeof( MachineParams ) );
        } );
        // 保存按钮
        connect( ui->btn_servoParaSave, &QPushButton::clicked, this, [ & ]() {
            if ( mainData->readMachineFileConfig->saveServoConfig( MAIN_CFG_DIR + "MachineFileConfig.json" ) == 1 )
            {
                QMessageBox::information( nullptr, "操作提示", "保存成功" );
            }
            else
            {
                QMessageBox::warning( nullptr, "错误提示", "保存失败" );
            };
        } );

        isServoParaPageInit = true;
    }
}

void TestForm::initGroupBoxDown()
{
    // 从mainData的ReadMachineFileConfig的 _servoList中读取项生成参数控件列表
    QMap< int, ReadMachineFileConfig::ServoCfgItem >* servoList = mainData->readMachineFileConfig->getServoList();

    // 创建水平布局
    QVBoxLayout* vLayout = new QVBoxLayout( ui->gBox_servoParaDown );
    vLayout->setSpacing( 10 );
    vLayout->setContentsMargins( 10, 10, 10, 10 );

    // 遍历servoList生成控件
    int count = 0;
    for ( auto it = servoList->begin(); it != servoList->end() && count < 10; ++it, ++count )
    {
        // 创建垂直布局容器
        QHBoxLayout* hLayout = new QHBoxLayout();

        // 标题标签
        QLabel* titleLabel = new QLabel( it.value().title );
        titleLabel->setAlignment( Qt::AlignCenter );
        titleLabel->setStyleSheet( "QLabel {"
                                   "    background-color: #3498db;"
                                   "    color: white;"
                                   "    border-radius: 5px;"
                                   "    padding: 2px;"
                                   "    font-size: 9pt;"
                                   "}" );
        hLayout->addWidget( titleLabel );

        // 值输入框
        MyLineEdit* valueEdit = new MyLineEdit();
        valueEdit->setText( QString::number( it.value().value ) );
        valueEdit->setEnabled( it.value().editable );
        valueEdit->setAlignment( Qt::AlignCenter );
        valueEdit->setProperty( "id", it.key() );
        valueEdit->setStyleSheet( "MyLineEdit {"
                                  "    background-color: white;"
                                  "    border: 1px solid #dddddd;"
                                  "    border-radius: 5px;"
                                  "    padding: 2px;"
                                  "    color: #333333;"
                                  "    font-size: 9pt;"
                                  "}"
                                  "MyLineEdit:focus {"
                                  "    border: 1px solid #3498db;"
                                  "    background-color: #e8f4fc;"
                                  "}" );
        // 点击事件
        connect( valueEdit, &MyLineEdit::mouseRelease, this, &TestForm::onServoParaShowNumberInputForm );
        hLayout->addWidget( valueEdit );

        // 单位标签
        QLabel* unitLabel = new QLabel( it.value().unit );
        unitLabel->setAlignment( Qt::AlignCenter );
        unitLabel->setStyleSheet(
            "QLabel {"
            "    background-color: #f8f8f8;"
            "    color: #333333;"
            "    border-radius: 5px;"
            "    padding: 2px;"
            "    font-size: 9pt;"
            "}" );
        hLayout->addWidget( unitLabel );

        vLayout->addLayout( hLayout );
    }

    // 不足10项用空Widget占位
    for ( int i = count; i < 10; i++ )
    {
        QWidget* placeholder = new QWidget();
        vLayout->addWidget( placeholder );
    }

    // 设置布局的拉伸因子
    vLayout->setStretch( 0, 1 );
    vLayout->setStretch( 1, 1 );
    vLayout->setStretch( 2, 1 );
    vLayout->setStretch( 3, 1 );
    vLayout->setStretch( 4, 1 );
    vLayout->setStretch( 5, 1 );
    vLayout->setStretch( 6, 1 );
    vLayout->setStretch( 7, 1 );
    vLayout->setStretch( 8, 1 );
    vLayout->setStretch( 9, 1 );
}

void TestForm::onServoParaShowNumberInputForm()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    if ( _numberInputFrm == nullptr )
    {
        _numberInputFrm = new NumberInputForm();
        connect( this->_numberInputFrm, &NumberInputForm::InputFinished, this, &TestForm::onSeroParaNumberInputFormFinished );
    }
    _numberInputFrm->show();
}

void TestForm::onSeroParaNumberInputFormFinished( QString str )
{
    if ( this->currentSelectedMyLineEdit != nullptr )
    {
        this->currentSelectedMyLineEdit->setText( str );
        QVariant var = this->currentSelectedMyLineEdit->property( "id" );
        if ( var.isValid() )
        {
            quint8                               key   = var.toUInt();
            quint16                              value = str.toUInt();
            ReadMachineFileConfig::ServoCfgItem& item  = ( *mainData->readMachineFileConfig->getServoList() )[ key ];
            item.value                                 = value;
        }
    }
}
