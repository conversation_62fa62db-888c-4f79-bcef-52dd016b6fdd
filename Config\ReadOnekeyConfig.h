#ifndef READONEKEYCONFIG_H
#define READONEKEYCONFIG_H

#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMessageBox>

class ReadOnekeyConfig
{
public:
    ReadOnekeyConfig();
    ~ReadOnekeyConfig();

#pragma pack( 1 )
    struct KtfCfgItem
    {
        quint16 id;
        QString name;
        quint8  enable;   // 1 Enable
        quint8  install;  // 0 Mpp (Elan)  1- Plus
        quint8  dir;      // 0 Clockwise   1 - AntiClockwise
    };

#pragma pack()

    void parseConfig( QString fileAddr );
    int  saveConfig( QString fileAddr );

    QMap< int, KtfCfgItem >* getKtfList()
    {
        return &_ktfList;
    }

private:
    QMap< int, KtfCfgItem > _ktfList;
};

#endif  // READONEKEYCONFIG_H
