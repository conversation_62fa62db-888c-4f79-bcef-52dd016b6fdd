#ifndef FLOATINPUTFORM_H
#define FLOATINPUTFORM_H

#include <QWidget>

namespace Ui
{
class FloatInputForm;
}

class FloatInputForm : public QWidget
{
    Q_OBJECT

public:
    explicit FloatInputForm( QWidget* parent = nullptr, QString title = "请输入数值", double minValue = -100000000.0, double maxValue = 100000000.0 );
    ~FloatInputForm();
    QString getInputText();
    void    setInputText( QString str );

protected:
    void showEvent( QShowEvent* event ) override;
    void closeEvent( QCloseEvent* event ) override;

signals:
    void InputFinished( float value );
    void finished();

private:
    Ui::FloatInputForm* ui;
    void                onBtnClicked();
    double              _min_Value;
    double              _max_Value;
};

#endif  // FLOATINPUTFORM_H
