#ifndef ZHUSUOCFGDIALOG_H
#define ZHUSUOCFGDIALOG_H

#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "FATParser.h"
#include "PatternViewer.h"
#include <QDebug>
#include <QDialog>
#include <QMessageBox>
#include <QTableWidget>

namespace Ui
{
class ZhusuoCfgDialog;
}

class ZhusuoCfgDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ZhusuoCfgDialog( QWidget* parent = nullptr, FATParser::PatternExtraData* patternExtra = nullptr );
    ~ZhusuoCfgDialog();

private:
    Ui::ZhusuoCfgDialog*         ui;
    FATParser::PatternExtraData* patternExtraData;
    NumberInputForm*             numberInputFrm            = nullptr;
    MyLineEdit*                  currentSelectedMyLineEdit = nullptr;
    FATParser::<PERSON><PERSON><PERSON>[ 7 ];
    FATParser::ZhusoNumCfg       zhusuo_num_cfg;

private slots:
    void onShowNumberInputForm();
    void onNumberInputFormFinished( QString value );
    void onCheckBoxStateChanged( int state );
};

#endif  // ZHUSUOCFGDIALOG_H
