#ifndef STEPTEST_H
#define STEPTEST_H
#include <QWidget>

struct StepTest
{
    uint16_t Device_ID;     //设备号
    uint16_t SetVal_Type;   //设定值的类型
    uint16_t SetValue;      //设定值
    uint16_t CurrentValue;  //当前值
    uint16_t EncoderValue;  //编码器值
};

/* 步进电机编号 */
enum StepTest_StepNum
{
    TEST_ELASTIC_STEP_NUM,           //橡筋
    TEST_NEEDLE_DENSITY_STEP_NUM,    //针桶密度
    TEST_WEAVING_AIR_DOOR_STEP_NUM,  //织袜风门
    TEST_HEAD_UP_DOWN_STEP_NUM,      //机头升降
    TEST_SCISSOR_DISC_STEP_NUM,      //剪刀盘
    TEST_SETTLING_MOTOR_STEP_NUM,    //沉降电机
    TEST_SINK_MOTOR_STEP_NUM,        //生克电机
    TEST_LEFT_CORNER_STEP_NUM,       //左棱角
    TEST_RIGHT_CORNER_STEP_NUM,      //右棱角
};

#endif  // STEPTEST_H
