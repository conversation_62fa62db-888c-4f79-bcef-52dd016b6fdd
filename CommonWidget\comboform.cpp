﻿#include "comboform.h"
#include "ui_comboform.h"
#include <QDebug>
#include <QScreen>
#include <QtWidgets>

ComboForm::ComboForm( QWidget* parent, QMap< int, QString >* list, int CountPerLine ) : QWidget( parent ), ui( new Ui::ComboForm )
{
    ui->setupUi( this );

    // 设置无边框窗口
    // setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);
    // setAttribute(Qt::WA_TranslucentBackground);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout( this );
    mainLayout->setContentsMargins( 0, 0, 0, 0 );
    mainLayout->setSpacing( 0 );

    // 创建内容容器
    QWidget* container = new QWidget( this );
    container->setObjectName( "container" );
    container->setStyleSheet( "#container {"
                              "    background-color: white;"
                              "    border: 1px solid #cccccc;"
                              "    border-radius: 8px;"
                              "}" );

    // 创建内容布局
    QVBoxLayout* containerLayout = new QVBoxLayout( container );
    containerLayout->setContentsMargins( 10, 10, 10, 10 );
    containerLayout->setSpacing( 10 );

    // 添加标题栏
    QWidget* titleBar = new QWidget( container );
    titleBar->setFixedHeight( 30 );
    QHBoxLayout* titleLayout = new QHBoxLayout( titleBar );
    titleLayout->setContentsMargins( 5, 0, 5, 0 );

    QLabel* titleLabel = new QLabel( "请选择", titleBar );
    titleLabel->setStyleSheet( "font-size: 8pt;"
                               "font-weight: bold;"
                               "color: #2980b9;"
                               "padding: 2px;" );

    QPushButton* closeButton = new QPushButton( "×", titleBar );
    closeButton->setFixedSize( 20, 20 );
    closeButton->setStyleSheet( "QPushButton {"
                                "    border: none;"
                                "    background-color: transparent;"
                                "    color: #666666;"
                                "    font-size: 10pt;"
                                "    font-weight: bold;"
                                "}"
                                "QPushButton:hover {"
                                "    color: #ff0000;"
                                "}" );
    connect( closeButton, &QPushButton::clicked, this, &ComboForm::close );

    titleLayout->addWidget( titleLabel );
    titleLayout->addStretch();
    titleLayout->addWidget( closeButton );

    // 添加分隔线
    QFrame* line = new QFrame( container );
    line->setFrameShape( QFrame::HLine );
    line->setFrameShadow( QFrame::Sunken );
    line->setStyleSheet( "background-color: #e0e0e0;"
                         "height: 1px;"
                         "margin: 0 5px;" );

    // 添加按钮网格
    QWidget*     buttonContainer = new QWidget( container );
    QGridLayout* gridLayout      = new QGridLayout( buttonContainer );
    gridLayout->setContentsMargins( 5, 5, 5, 5 );
    gridLayout->setSpacing( 10 );

    // 根据list长度调整页面布局
    int index = 0;
    for ( QMap< int, QString >::const_iterator it = list->constBegin(); it != list->constEnd(); ++it )
    {
        QPushButton* button;
        // &有特殊含义，不显示，必须两个&
        if ( it.value() == "&" )
            button = new QPushButton( "&&" );
        else
            button = new QPushButton( it.value() );

        button->setFixedSize( 100, 40 );
        button->setStyleSheet( "QPushButton {"
                               "    font-size: 8pt;"
                               "    background-color: #f8f8f8;"
                               "    border: 1px solid #dddddd;"
                               "    border-radius: 5px;"
                               "    padding: 5px;"
                               "    color: #333333;"
                               "    font-weight: normal;"
                               "}"
                               "QPushButton:hover {"
                               "    background-color: #e8f4fc;"
                               "    border: 1px solid #3498db;"
                               "    color: #2980b9;"
                               "}"
                               "QPushButton:pressed {"
                               "    background-color: #3498db;"
                               "    color: white;"
                               "    border: 1px solid #2980b9;"
                               "}" );

        gridLayout->addWidget( button, index / CountPerLine, index % CountPerLine );

        QVariant data;
        data.setValue( it.key() );
        button->setProperty( "Key", data );

        connect( button, &QPushButton::clicked, this, &ComboForm::onPushButtonClicked );
        index++;
    }

    // 添加所有组件到容器布局
    containerLayout->addWidget( titleBar );
    containerLayout->addWidget( line );
    containerLayout->addWidget( buttonContainer );

    // 添加容器到主布局
    mainLayout->addWidget( container );

    // 计算高和宽，如果元素个数大于CountPerLine，则一行有CountPerLine个，否则一行只有元素个数
    int width, height;
    if ( index > CountPerLine )
    {
        width = CountPerLine * 110 + 60;  // 增加一些边距
    }
    else
    {
        width = index * 110 + 60;  // 增加一些边距
    }
    // 计算行数，index / CountPerLine + 1行，加上标题栏和边距
    height = ( index / CountPerLine + 1 ) * 50 + 80;

    this->setFixedSize( width, height );
    // 居中
    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );
}

ComboForm::~ComboForm()
{
    delete ui;
}

void ComboForm::onPushButtonClicked()
{
    QPushButton* button  = qobject_cast< QPushButton* >( sender() );
    QVariant     itemKey = button->property( "Key" );
    emit         itemSelected( itemKey.toInt() );
    this->close();
}

void ComboForm::closeEvent( QCloseEvent* event )
{
    QWidget::closeEvent( event );
    emit finished();
}
