#include "FlowerAndLamaoLoopDialog.h"
#include "ui_FlowerAndLamaoLoopDialog.h"

FlowerAndLamaoLoopDialog::FlowerAndLamaoLoopDialog( QWidget* parent, FATParser::PatternExtraData* patternExtra ) : QDialog( parent ), ui( new Ui::FlowerAndLamaoLoopDialog )
{
    ui->setupUi( this );

    if ( patternExtra != nullptr )
    {
        this->patternExtraData = patternExtra;
        memcpy( &this->flowerData[ 0 ], &patternExtra->flower_loop[ 0 ], sizeof( FATParser::FLowerAndLaMaoLoop ) * 10 );
        memcpy( &this->laomaoData[ 0 ], &patternExtra->lamao_loop[ 0 ], sizeof( FATParser::FLowerAndLaMaoLoop ) * 10 );
    }

    ui->tableWidget->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    ui->tableWidget->setColumnCount( 3 );
    QStringList headerList;
    headerList << QString::fromUtf8( "开始" ) << QString::fromUtf8( "结束" ) << QString::fromUtf8( "圈数" );
    ui->tableWidget->setHorizontalHeaderLabels( headerList );
    ui->tableWidget->verticalHeader()->setVisible( true );

    connect( ui->rb_flower, &QRadioButton::clicked, this, &FlowerAndLamaoLoopDialog::onRadioButtonClicked );
    connect( ui->rb_lamao, &QRadioButton::clicked, this, &FlowerAndLamaoLoopDialog::onRadioButtonClicked );

    connect( ui->tableWidget, &QTableWidget::itemClicked, this, &FlowerAndLamaoLoopDialog::onTableItemClicked );

    connect( ui->pbtn_insert, &QPushButton::clicked, this, [ & ]() {
        int data_length = 0;
        if ( ui->rb_flower->isChecked() )
        {
            data_length = getDataLength( 0 );
            if ( data_length == 10 )
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "不能超过10个" ) );
            else
            {
                this->flowerData[ data_length ].loop_start = 1;
                switchData( 0 );
            }
        }
        else if ( ui->rb_lamao->isChecked() )
        {
            data_length = getDataLength( 1 );
            if ( data_length == 10 )
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "不能超过10个" ) );
            else
            {
                this->laomaoData[ data_length ].loop_start = 1;
                switchData( 1 );
            }
        }
    } );
    connect( ui->pbtn_delete, &QPushButton::clicked, this, [ & ]() {
        int data_length = 0;
        if ( ui->rb_flower->isChecked() )
        {
            data_length = getDataLength( 0 );
            if ( data_length == 1 )
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "最后1个不允许删除" ) );
            else
            {
                this->flowerData[ data_length - 1 ].loop_start = 0;
                this->flowerData[ data_length - 1 ].loop_end   = 0;
                this->flowerData[ data_length - 1 ].loop_num   = 0;
                switchData( 0 );
            }
        }
        else if ( ui->rb_lamao->isChecked() )
        {
            data_length = getDataLength( 1 );
            if ( data_length == 1 )
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "最后1个不允许删除" ) );
            else
            {
                this->laomaoData[ data_length - 1 ].loop_start = 0;
                this->laomaoData[ data_length - 1 ].loop_end   = 0;
                this->laomaoData[ data_length - 1 ].loop_num   = 0;
                switchData( 1 );
            }
        }
    } );
    connect( ui->pbtn_ok, &QPushButton::clicked, this, [ & ]() {
        // save data
        memcpy( &this->patternExtraData->flower_loop[ 0 ], &this->flowerData[ 0 ], sizeof( FATParser::FLowerAndLaMaoLoop ) * 10 );
        memcpy( &this->patternExtraData->lamao_loop[ 0 ], &this->laomaoData[ 0 ], sizeof( FATParser::FLowerAndLaMaoLoop ) * 10 );
        this->close();
    } );
    connect( ui->pbtn_cancel, &QPushButton::clicked, this, [ & ]() { this->close(); } );
    switchData( 0 );
}

FlowerAndLamaoLoopDialog::~FlowerAndLamaoLoopDialog()
{
    delete ui;
    delete this->numberInputFrm;
    this->numberInputFrm = nullptr;
}

void FlowerAndLamaoLoopDialog::onRadioButtonClicked()
{
    if ( ui->rb_flower->isChecked() )
        switchData( 0 );
    else if ( ui->rb_lamao->isChecked() )
        switchData( 1 );
}

int FlowerAndLamaoLoopDialog::getDataLength( int type )
{
    int data_length = 0;
    if ( type == 0 )
    {
        for ( int i = 0; i < 10; i++ )
        {
            if ( this->flowerData[ i ].loop_start != 0 )
                data_length++;
            else
                break;
        }
    }
    else if ( type == 1 )
    {
        for ( int i = 0; i < 10; i++ )
        {
            if ( this->laomaoData[ i ].loop_start != 0 )
                data_length++;
            else
                break;
        }
    }
    //    qDebug() << "getDataLength" << type << data_length;
    return data_length;
}

void FlowerAndLamaoLoopDialog::switchData( int type )
{
    int data_length = getDataLength( type );
    if ( type == 0 )
    {
        ui->tableWidget->setRowCount( data_length );
        for ( int i = 0; i < data_length; i++ )
        {
            QTableWidgetItem* item1 = new QTableWidgetItem( QString::number( this->flowerData[ i ].loop_start ) );
            QTableWidgetItem* item2 = new QTableWidgetItem( QString::number( this->flowerData[ i ].loop_end ) );
            QTableWidgetItem* item3 = new QTableWidgetItem( QString::number( this->flowerData[ i ].loop_num ) );
            this->ui->tableWidget->setItem( i, 0, item1 );
            this->ui->tableWidget->setItem( i, 1, item2 );
            this->ui->tableWidget->setItem( i, 2, item3 );
        }
    }
    else if ( type == 1 )
    {
        ui->tableWidget->setRowCount( data_length );
        for ( int i = 0; i < data_length; i++ )
        {
            QTableWidgetItem* item1 = new QTableWidgetItem( QString::number( this->laomaoData[ i ].loop_start ) );
            QTableWidgetItem* item2 = new QTableWidgetItem( QString::number( this->laomaoData[ i ].loop_end ) );
            QTableWidgetItem* item3 = new QTableWidgetItem( QString::number( this->laomaoData[ i ].loop_num ) );
            this->ui->tableWidget->setItem( i, 0, item1 );
            this->ui->tableWidget->setItem( i, 1, item2 );
            this->ui->tableWidget->setItem( i, 2, item3 );
        }
    }
}

void FlowerAndLamaoLoopDialog::onTableItemClicked( QTableWidgetItem* item )
{
    tableEditRowIndex = item->row();
    tableEditColIndex = item->column();

    if ( this->numberInputFrm == nullptr )
    {
        this->numberInputFrm = new NumberInputForm();
        connect( this->numberInputFrm, &NumberInputForm::InputFinished, this, &FlowerAndLamaoLoopDialog::onNumberInputFormFinished );
    }
    this->numberInputFrm->show();
}

void FlowerAndLamaoLoopDialog::onNumberInputFormFinished( QString value )
{
    this->ui->tableWidget->setItem( tableEditRowIndex, tableEditColIndex, new QTableWidgetItem( value ) );
    if ( this->ui->rb_flower->isChecked() )
    {
        if ( tableEditColIndex == 0 )
            this->flowerData[ tableEditRowIndex ].loop_start = value.toInt();
        else if ( tableEditColIndex == 1 )
            this->flowerData[ tableEditRowIndex ].loop_end = value.toInt();
        else if ( tableEditColIndex == 2 )
            this->flowerData[ tableEditRowIndex ].loop_num = value.toInt();
    }
    else if ( this->ui->rb_lamao->isChecked() )
    {
        if ( tableEditColIndex == 0 )
            this->laomaoData[ tableEditRowIndex ].loop_start = value.toInt();
        else if ( tableEditColIndex == 1 )
            this->laomaoData[ tableEditRowIndex ].loop_end = value.toInt();
        else if ( tableEditColIndex == 2 )
            this->laomaoData[ tableEditRowIndex ].loop_num = value.toInt();
    }
}
