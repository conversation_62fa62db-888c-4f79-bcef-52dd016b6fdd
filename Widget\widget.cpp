﻿#include "widget.h"
#include "ui_widget.h"

// 在构造函数中初始化CommOperations对象
Widget::Widget( QWidget* parent ) : QWidget( parent ), ui( new Ui::Widget )
{
    ui->setupUi( this );

    /* 添加 二级菜单的按键组 */
    mainMenuBtnGroup = new QButtonGroup( this );
    mainMenuBtnGroup->addButton( ui->MainWin_run_btn, 0 );
    mainMenuBtnGroup->addButton( ui->MainWin_test_btn, 1 );
    mainMenuBtnGroup->addButton( ui->MainWin_craft_btn, 2 );
    mainMenuBtnGroup->addButton( ui->MainWin_set_btn, 3 );
    mainMenuBtnGroup->addButton( ui->MainWin_file_btn, 4 );
    mainMenuBtnGroup->addButton( ui->MainWin_fengtou_btn, 5 );
    mainMenuBtnGroup->addButton( ui->MainWin_gaiji_btn, 6 );
    mainMenuBtnGroup->addButton( ui->MainWin_help_btn, 7 );
    connect( mainMenuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onMaindMenuBtnGroupClicked( int ) ) );

    mainData = new MainWidgetData();
    ReadMainCfg();

    comm = new Communication( this );

    // 初始化通信操作对象
    m_commOperations = new CommOperations( this );
    m_commOperations->init( comm, mainData );  // 直接传入mainData而不是mainData->readMachineFileConfig

    // 连接信号 - 使用同一个槽函数处理所有通信失败情况
    connect( m_commOperations, &CommOperations::machineParamSendFailed, this, &Widget::onCommSendFailed );
    connect( m_commOperations, &CommOperations::userParamSendFailed, this, &Widget::onCommSendFailed );
    connect( m_commOperations, &CommOperations::sockResetSendFailed, this, &Widget::onCommSendFailed );

    // 发送机器参数
    m_commOperations->sendMachineParam();
    // 发送用户参数
    m_commOperations->sendUserParam();

    // 初始化键盘通信
    m_keyboard = new CommKeyboard( this );
    qDebug() << "键盘通信初始化完成";
    // 连接键盘单键按下信号
    connect( m_keyboard, &CommKeyboard::keyPressed, this, [&]( int key ) {
        // 如果测试窗口存在且可见,则更新键值状态
        if ( testForm != nullptr && testForm->isVisible() )
        {
            testForm->resetAllKeyStatus();
            testForm->updateKeyStatus( key, true );
        }
    } );
    // 连接键盘组合键按下信号
    connect( m_keyboard, &CommKeyboard::combinationKeyPressed, this, [&]( int key1, int key2 ) {
        // 如果测试窗口存在且可见,则更新组合键值状态
        if ( testForm != nullptr && testForm->isVisible() )
        {
            testForm->resetAllKeyStatus();
            testForm->updateKeyStatus( key1, true );
            testForm->updateKeyStatus( key2, true );
        }
        // 如果两个键值为1和18，则发送织袜复位动作
        if ( key1 == 1 && key2 == 18 || key1 == 18 && key2 == 1 )
        {
            // 发送织袜复位动作
            // if ( mainData->machineState != MachineState::SockReset )
            m_commOperations->sendSockResetAction( true );
        }
    } );

    //    initMachineInfo();

    // 新建mqttClient，开设服务
    //    initMqttClient();

    // 定期上报数据帧放在主widget中执行
    connect( comm, &Communication::LowerReportFrameReceived, this, &Widget::onLowerReportFrameReceived );
    connect( comm, &Communication::CommandFrameReceived, this, [&]( qint16 size, quint8* data ) {
        if ( size == 8 )
        {
            quint16 step   = data[ 2 ] + data[ 3 ] * 256;
            quint16 result = data[ 4 ] + data[ 5 ] * 256;
            if ( selfCheckForm != nullptr && selfCheckForm->isVisible() )
            {
                selfCheckForm->updateStepResult( step, result );
            }
        }
    } );

    // lastUpInfoMessage 需要清零
    memset( &lastUpInfoMessage, 0, sizeof( upInfoMessage ) );
    // 使用QTimer延时初始化warnDialog
    QTimer::singleShot( 100, this, &Widget::initWarnDialog );

    // 初始化CAN通信
    m_canBus = new CommCan( this );
    // 连接信号槽
    connect( m_canBus, &CommCan::frameReceived, this, &Widget::handleCanFrame );
    connect( m_canBus, &CommCan::errorOccurred, this, &Widget::handleCanError );
    // 初始化CAN设备
    if ( m_canBus->initCan( "socketcan", "can1", 1000000 ) )
    {
        qDebug() << "CAN设备初始化成功";
        // 注册特定ID的帧处理函数
        // m_canBus->registerFrameHandler( 0x123, [ this ]( const QCanBusFrame& frame ) { this->processMotorStatusFrame( frame ); } );
        // m_canBus->registerFrameHandler( 0x456, [ this ]( const QCanBusFrame& frame ) { this->processSensorDataFrame( frame ); } );
        // 发送CAN帧
        //        QByteArray payload;
        //        payload.append( 0x01 );  // 设备ID
        //        payload.append( 0x02 );  // 命令类型
        //        payload.append( 0x03 );  // 参数1
        //        payload.append( 0x04 );  // 参数2
        //        if ( m_canBus->sendFrame( 0x100, payload ) )
        //        {
        //            qDebug() << "命令发送成功";
        //        }
        //        else
        //        {
        //            qDebug() << "命令发送失败:" << m_canBus->getLastError();
        //        }
    }
    else
    {
        qDebug() << "CAN设备初始化失败:" << m_canBus->getLastError();
    }
}

Widget::~Widget()
{
    if ( warnDialog )
    {
        delete warnDialog;
        warnDialog = nullptr;
    }

    // 释放键盘通信资源
    if ( m_keyboard )
    {
        delete m_keyboard;
        m_keyboard = nullptr;
    }

    if ( m_commOperations )
    {
        delete m_commOperations;
        m_commOperations = nullptr;
    }

    delete comm;
    delete ui;
}

void Widget::initMachineInfo()
{
    // 此处暂时新建MachineInfo
    if ( this->m_machineInfo == nullptr )
    {
        this->m_machineInfo           = new MachineInfo();
        this->m_machineInfo->deviceId = "001";
        this->m_machineInfo->d1       = 0;
        this->m_machineInfo->d2       = 0;
        this->m_machineInfo->d3       = 0;
        this->m_machineInfo->d4       = 0;
        this->m_machineInfo->d5       = "";
        this->m_machineInfo->d6       = 0;
        this->m_machineInfo->d7       = 0;
        this->m_machineInfo->d8       = 0;
        this->m_machineInfo->d9       = 0;
        this->m_machineInfo->d10      = "";
        this->m_machineInfo->d11      = "";
        this->m_machineInfo->d12      = 0;
        this->m_machineInfo->d13      = 0;
        this->m_machineInfo->d14      = 0;
        this->m_machineInfo->d15      = "";
        this->m_machineInfo->d16      = "";
        this->m_machineInfo->d17      = 0;
        this->m_machineInfo->d18      = 0;
        this->m_machineInfo->d19      = 0;
        this->m_machineInfo->d20      = 0;
        this->m_machineInfo->d21      = 0;
        this->m_machineInfo->d22      = 0;
        this->m_machineInfo->d23      = 0;
        this->m_machineInfo->d24      = 0;
        this->m_machineInfo->d25      = 0;
    }
}

// void Widget::initMqttClient()
//{

//    if ( this->m_mqttClient == nullptr )
//    {
//        this->m_mqttClient = new mqttClient( this, "114.132.190.186", this->m_machineInfo );
//    }
//}

/* 槽函数 */
/* 菜单按钮按下槽函数 */
void Widget::initWarnDialog()
{
    if ( warnDialog == nullptr )
    {
        warnDialog = new CommonWarnDialog( mainData->readWarnConfig, this );
        warnDialog->setWindowFlags( Qt::Window | Qt::WindowStaysOnTopHint );  // 设置为顶层窗口
        warnDialog->setWindowModality( Qt::NonModal );                        // 设置为非模态
                                                                              //        warnDialog->show();
    }

    // 开启断电续织功能
    if ( mainData->readMachineFileConfig->getBasicCfgList()->value( 15 ).value == 1 )
    {
        // 弹出编织模式对话框，选择断电续织或者重新编织
        WaveModeForm* waveModeForm = new WaveModeForm( this );
        // 设置窗口关闭后清理内存并退出
        waveModeForm->setAttribute( Qt::WA_DeleteOnClose );
        // 连接信号
        connect( waveModeForm, &WaveModeForm::waveModeSelected, this, [&]( int mode ) {
            if ( mode == WaveModeForm::ContinueMode )
            {
                // 处理断电续织模式
            }
            else
            {
                // 处理重新编织模式
            }
        } );
        waveModeForm->show();
    }

    // 自检对话框
    if ( selfCheckForm == nullptr )
    {
        selfCheckForm = new SelfCheckForm( this );
        // 设置窗口关闭后清理内存并退出
        selfCheckForm->setAttribute( Qt::WA_DeleteOnClose );
        // 连接destroyed信号，当窗口被销毁时将指针设置为nullptr
        connect( selfCheckForm, &QObject::destroyed, this, [this]() { selfCheckForm = nullptr; } );
        // 发送键盘测试帧
        if ( m_keyboard )
            m_keyboard->sendTestFrame();
        // 处理键盘测试结果信号
        connect( m_keyboard, &CommKeyboard::keyboardTestResult, this, [&]( bool success ) {
            if ( selfCheckForm != nullptr && selfCheckForm->isVisible() )
            {
                selfCheckForm->updateKeyBoardResult( success );
            }
        } );
    }
    if ( !selfCheckForm->isVisible() )
        selfCheckForm->show();
}

void Widget::onMaindMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 0:  // 运转信息
            if ( workForm == nullptr )
            {
                workForm = new WorkForm( this, mainData, comm, &lastUpInfoMessage, m_commOperations );
                connect( workForm, SIGNAL( WorkFormToMainWinToShowSignal( int ) ), this, SLOT( showMainWindowSlot() ) );
            }
            workForm->show();
            break;
        case 1:  // 测试及手动
            // 如果机器是运行状态，不允许切换
            if ( workForm != nullptr && mainData->machineState == MachineState::Running )
            {
                QMessageBox::warning( nullptr, "错误提示", "处于运行状态，不允许切换" );
                return;
            }
            if ( testForm == nullptr )
            {
                testForm = new TestForm( this, mainData, comm, m_keyboard );
                connect( testForm, SIGNAL( TestFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            testForm->show();
            break;
        case 2:  // 工艺参数
            if ( craftParamForm == nullptr )
            {
                craftParamForm = new CraftParamForm( this, mainData, comm );
                connect( craftParamForm, SIGNAL( CraftParamFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            craftParamForm->show();
            break;
        case 3:  // 系统参数
            // 如果机器是运行状态，不允许切换
            if ( workForm != nullptr && mainData->machineState == MachineState::Running )
            {
                QMessageBox::warning( nullptr, "错误提示", "处于运行状态，不允许切换" );
                return;
            }
            if ( paraSetForm == nullptr )
            {
                paraSetForm = new ParaSetForm( this, comm, mainData );
                connect( paraSetForm, SIGNAL( ParasetFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            paraSetForm->show();
            break;
        case 4:  // 文件及生产
            if ( usbForm == nullptr )
            {
                usbForm = new UsbForm( this );
                connect( usbForm, SIGNAL( UsbFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            usbForm->show();
            break;
        case 5:  // 传感器测试
            if ( sensorForm == nullptr )
            {
                sensorForm = new SensorForm( this, mainData, comm );
                connect( sensorForm, SIGNAL( SensorFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            sensorForm->show();
            break;
        case 6:  // 用户参数
            if ( oneKeyForm == nullptr )
            {
                oneKeyForm = new OneKeyForm( this, comm, mainData );
                connect( oneKeyForm, SIGNAL( OneKeyFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            oneKeyForm->show();
            break;
        case 7:  // 帮助
            if ( logForm == nullptr )
            {
                logForm = new LogForm( this, mainData, comm );
                connect( logForm, SIGNAL( LogFormToMainWinToShowSignal() ), this, SLOT( showMainWindowSlot() ) );
            }
            logForm->show();
            break;
    }
    ui->frame->hide();
}

void Widget::showMainWindowSlot()
{
    ui->frame->show();
}

void Widget::ReadMainCfg()
{
    /* 检查配置文件夹和FAT文件夹是否存 */
    QDir cfgDir( MAIN_CFG_DIR );
    qDebug() << "cfg Dir:" << cfgDir.absolutePath();
    if ( cfgDir.exists() )
    {
        /* 读取配置文件 */
        if ( mainData->readCfg == nullptr )
        {
            mainData->readCfg = new ReadCfg();
            mainData->readCfg->parseCfgConfig( MAIN_CFG_DIR + "Cfg.json" );
        }
        if ( mainData->readOneKeyConfig == nullptr )
        {
            mainData->readOneKeyConfig = new ReadOnekeyConfig();
            mainData->readOneKeyConfig->parseConfig( MAIN_CFG_DIR + "OneKeyConfig.json" );
        }
        // 初始化ReadWarnConfig
        if ( mainData->readWarnConfig == nullptr )
        {
            mainData->readWarnConfig = new ReadWarnConfig();
            mainData->readWarnConfig->parseConfig( MAIN_CFG_DIR + "WarnConfig.json" );
        }
        if ( mainData->readMachineFileConfig == nullptr )
        {
            mainData->readMachineFileConfig = new ReadMachineFileConfig();
            mainData->readMachineFileConfig->parseConfig( MAIN_CFG_DIR + "MachineFileConfig.json" );
        }
        if ( mainData->readResetConfig == nullptr )
        {
            mainData->readResetConfig = new ReadResetConfig();
            mainData->readResetConfig->parseResetConfig( MAIN_CFG_DIR + "ResetConfig.json" );
        }
        if ( mainData->configManager == nullptr )
        {
            mainData->configManager = new ConfigManager( this );
        }
    }
    else
    {
        QMessageBox::critical( nullptr, "错误提示", "配置文件夹不存在" );
    }
}

void Widget::onLowerReportFrameReceived( qint16 size, quint8* data )
{
    if ( size >= sizeof( upInfoMessage ) )
    {
        upInfoMessage currentMessage;
        memcpy( &currentMessage, data, size );

        // Update power dialog if it's visible
        if ( workForm != nullptr && workForm->isVisible() )
        {
            if ( workForm->powerDialog != nullptr && workForm->powerDialog->isVisible() )
            {
                workForm->powerDialog->updatePowerInfo( currentMessage.main );
            }

            // 更新角度和针位
            float   abAngelValue  = ( ( currentMessage.serv.encoderVal + 4096 - currentMessage.serv.zeroPos ) % 4096 ) * 360 / 4096.0f;
            float   needleNum     = mainData->readMachineFileConfig->getBasicCfgList()->value( 2 ).value;
            quint16 abNeedleValue = static_cast< quint16 >( abAngelValue * needleNum / 360 );

            workForm->refreshWidgetFromUpperMsg( abAngelValue, abNeedleValue + 1, currentMessage.main.realSpeed );
            workForm->refreshInfoPage( abNeedleValue, abAngelValue );
        }

        // 更新传感器状态
        if ( sensorForm != nullptr && sensorForm->isVisible() )
        {
            // 更新零位传感器状态
            quint8 zeroSensorData[ 4 ];
            memcpy( zeroSensorData, &currentMessage.zeroLimit, 4 );
            sensorForm->refreshZeroSensor( 4, zeroSensorData );

            // 更新指袜传感器状态
            quint8 socketWarnData[ 7 ];
            memcpy( socketWarnData, &currentMessage.zhiwaSignals, 7 );
            sensorForm->refreshSocketWarn( 8, socketWarnData );

            // 更新缝头传感器状态
            quint8 seamWarnData[ 6 ];
            memcpy( seamWarnData, &currentMessage.seamSignals, 6 );
            sensorForm->refreshFengtouWarn( 8, seamWarnData );
        }

        // 处理报警信息
        // 遍历11类报警
        for ( qint8 type_id = 1; type_id <= 11; type_id++ )
        {
            // 获取当前类型报警的字节数组
            const quint8* currentWarnBytes = nullptr;
            const quint8* lastWarnBytes    = nullptr;
            int           byteCount        = 0;

            // 根据报警类型获取对应的字节数组
            switch ( type_id )
            {
                case 1:  // 织气阀报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.zhi_qifa;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.zhi_qifa;
                    byteCount        = sizeof( currentMessage.warn.zhi_qifa );
                    break;
                case 2:  // 三角气阀报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.triangle;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.triangle;
                    byteCount        = sizeof( currentMessage.warn.triangle );
                    break;
                case 3:  // 特殊功能气阀报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.shuttle;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.shuttle;
                    byteCount        = sizeof( currentMessage.warn.shuttle );
                    break;
                case 4:  // 指梭报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.needle;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.needle;
                    byteCount        = sizeof( currentMessage.warn.needle );
                    break;
                case 5:  // 缝头报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.input;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.input;
                    byteCount        = sizeof( currentMessage.warn.input );
                    break;
                case 6:  // 机械报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.step_motor;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.step_motor;
                    byteCount        = sizeof( currentMessage.warn.step_motor );
                    break;
                case 7:  // 电机报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.servo.warns;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.servo.warns;
                    byteCount        = sizeof( currentMessage.warn.servo.warns );
                    if ( workForm != nullptr && workForm->isVisible() && currentMessage.warn.servo.error_code != lastUpInfoMessage.warn.servo.error_code )
                    {
                        workForm->refreshServoWarnLabel( currentMessage.warn.servo.error_code );
                    }
                    break;
                case 8:  // 通信报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.can;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.can;
                    byteCount        = sizeof( currentMessage.warn.can );
                    break;
                case 9:  // 系统报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.seam;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.seam;
                    byteCount        = sizeof( currentMessage.warn.seam );
                    break;
                case 10:  // 硬件报警  20是前面5个32位数值
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.photo_eye + 20;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.photo_eye + 20;
                    byteCount        = sizeof( currentMessage.warn.photo_eye ) - 20;
                    if ( workForm != nullptr && workForm->isVisible() )
                    {
                        if ( currentMessage.warn.photo_eye.yarn_break != lastUpInfoMessage.warn.photo_eye.yarn_break )
                            workForm->refreshEyeWarnView( 0, currentMessage.warn.photo_eye.yarn_break );
                        if ( currentMessage.warn.photo_eye.yarnight != lastUpInfoMessage.warn.photo_eye.yarnight )
                            workForm->refreshEyeWarnView( 1, currentMessage.warn.photo_eye.yarnight );
                        if ( currentMessage.warn.photo_eye.codeimeout != lastUpInfoMessage.warn.photo_eye.codeimeout )
                            workForm->refreshEyeWarnView( 2, currentMessage.warn.photo_eye.codeimeout );
                        if ( currentMessage.warn.photo_eye.sampleimeout != lastUpInfoMessage.warn.photo_eye.sampleimeout )
                            workForm->refreshEyeWarnView( 3, currentMessage.warn.photo_eye.sampleimeout );
                        if ( currentMessage.warn.photo_eye.code_err != lastUpInfoMessage.warn.photo_eye.code_err )
                            workForm->refreshEyeWarnView( 4, currentMessage.warn.photo_eye.code_err );
                    }
                    break;
                case 11:  // 软件应用报警
                    currentWarnBytes = ( const quint8* )&currentMessage.warn.software;
                    lastWarnBytes    = ( const quint8* )&lastUpInfoMessage.warn.software;
                    byteCount        = sizeof( currentMessage.warn.software );
                    break;
            }

            if ( currentWarnBytes && lastWarnBytes )
            {
                // 遍历每个字节
                for ( int byte_index = 0; byte_index < byteCount; byte_index++ )
                {
                    quint8 currentByte = currentWarnBytes[ byte_index ];
                    quint8 lastByte    = lastWarnBytes[ byte_index ];

                    // 如果字节值发生变化
                    if ( currentByte != lastByte )
                    {
                        // 检查每一位的变化
                        for ( int bit_index = 0; bit_index < 8; bit_index++ )
                        {
                            quint8 currentBit = ( currentByte >> bit_index ) & 0x01;
                            quint8 lastBit    = ( lastByte >> bit_index ) & 0x01;

                            if ( currentBit != lastBit )
                            {
                                // 计算报警ID
                                quint16 id = byte_index * 8 + bit_index + 1;
                                // 报警状态（0表示消除，1表示触发）
                                quint8 status = currentBit;

                                qDebug() << "Warn Change" << type_id << id << status;
                                // 刷新WorkForm的报警窗口
                                if ( workForm != nullptr && workForm->isVisible() )
                                {
                                    workForm->refreshWarnWidget( type_id, id, status );
                                }
                                // 增加报警日志
                                if ( status == 1 )
                                {
                                    WarnLogItem item;
                                    item.time = QDateTime::currentDateTime().toString( "yyyy-MM-dd hh:mm:ss" );
                                    item.type = type_id;
                                    item.id   = id;
                                    mainData->warnLogList.append( item );

                                    // 有报警，必须显示warnDialog并且更新
                                    if ( warnDialog != nullptr )
                                    {
                                        if ( !warnDialog->isVisible() )
                                            warnDialog->show();
                                        warnDialog->refreshWarnInfo( type_id, id, status );
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 保存当前消息为上一次消息
        lastUpInfoMessage = currentMessage;
    }
}

// 添加处理参数发送失败的槽函数
void Widget::onCommSendFailed()
{
    QMessageBox::warning( this, "错误提示", "无法与下位机进行通信" );
}

void Widget::handleCanFrame( const QCanBusFrame& frame )
{
    // 处理接收到的CAN帧
    qDebug() << "接收到CAN帧，ID:" << QString( "0x%1" ).arg( frame.frameId(), 0, 16 ) << "数据:" << frame.payload().toHex();

    // 可以在这里更新UI显示
}

void Widget::handleCanError( const QString& error )
{
    // 处理CAN错误
    qDebug() << "CAN错误:" << error;

    // 可以在这里显示错误信息或尝试重新连接
}
