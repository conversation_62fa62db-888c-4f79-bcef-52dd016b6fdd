#ifndef WIDGET_H
#define WIDGET_H

#include "Common/CommOperations.h"  // 添加CommOperations头文件
#include "Common/defines.h"
#include "CommonWidget/CommonWarnDialog.h"
#include "CommonWidget/SelfCheckForm.h"
#include "CommonWidget/WaveModeForm.h"
#include "Communicate/CommCan.h"
#include "Communicate/commkeyboard.h"  // 添加CommKeyboard头文件
#include "Communicate/upInfoMessage.h"
#include "CraftParamWidget/CraftParamForm.h"
#include "FileWidget/fileform.h"
#include "LogWidget/LogForm.h"
#include "Mqtt/mqttclient.h"
#include "OneKeyWidget/OneKeyForm.h"
#include "ParasetWidget/parasetform.h"
#include "SensorWidget/SensorForm.h"
#include "TestWidget/testform.h"
#include "UsbWidget/UsbForm.h"
#include "WorkWidget/RunningPowerDialog.h"
#include "WorkWidget/WorkForm.h"
#include <QButtonGroup>
#include <QWidget>

namespace Ui
{
class Widget;
}

class Widget : public QWidget
{
    Q_OBJECT

public:
    explicit Widget( QWidget* parent = nullptr );
    ~Widget();

private:
    Ui::Widget* ui;

    WorkForm*       workForm       = nullptr;
    TestForm*       testForm       = nullptr;
    ParaSetForm*    paraSetForm    = nullptr;
    UsbForm*        usbForm        = nullptr;
    CraftParamForm* craftParamForm = nullptr;
    SensorForm*     sensorForm     = nullptr;
    OneKeyForm*     oneKeyForm     = nullptr;
    LogForm*        logForm        = nullptr;
    SelfCheckForm*  selfCheckForm  = nullptr;  // 自检页面

    MainWidgetData* mainData = nullptr;

    QButtonGroup* mainMenuBtnGroup;  // 菜单按键组

    //    mqttClient*  m_mqttClient  = nullptr;  // mqtt客户端
    MachineInfo* m_machineInfo = nullptr;  // MachineInfo,保存运行信息

    /************* 422通信相关 *************/
    Communication* comm;
    upInfoMessage  lastUpInfoMessage;  // 保存上一次的upInfoMessage

    // CAN通信相关
    CommCan* m_canBus;

    // 键盘通信相关
    CommKeyboard* m_keyboard = nullptr;  // 添加键盘通信对象指针

    // 通信操作对象
    CommOperations* m_commOperations = nullptr;

    // function
    void ReadMainCfg();

    // 进入主页面即发送系统参数
    QTimer machineParamSendTimer;
    bool   machineParamSendSuccess = false;
    int    retryCountMachineParam  = 0;
    QTimer userParamSendTimer;
    bool   userParamSendSuccess = false;
    int    retryCountUserParam  = 0;
    // 发送机器参数
    void widgetSendMachineParam();
    // 发送用户参数
    void widgetSendUserParam();

    // 全局报警窗口指针
    CommonWarnDialog* warnDialog = nullptr;  // 添加WarnDialog成员指针
    
    // 添加处理织袜复位发送失败的槽函数
    private slots:
        void onMaindMenuBtnGroupClicked( int id );  // 菜单按键组按下槽函数
        void showMainWindowSlot();
        void initMachineInfo();
        void initWarnDialog();  // 添加这一行
        // 下位机定期上报数据帧
        void onLowerReportFrameReceived( qint16 size, quint8* data );
        //    void initMqttClient();
        void handleCanFrame( const QCanBusFrame& frame );
        void handleCanError( const QString& error );
    
        // 处理参数发送失败
        void onCommSendFailed();
};

#endif  // WIDGET_H
