<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ComboCheckDialog</class>
 <widget class="QDialog" name="ComboCheckDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>950</width>
    <height>550</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QGroupBox" name="gbox_combo">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>461</width>
     <height>451</height>
    </rect>
   </property>
   <property name="title">
    <string>组合测试</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_8">
   <property name="geometry">
    <rect>
     <x>590</x>
     <y>290</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>位置</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>290</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>序号</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>34</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>气阀名称</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveIndex">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>330</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>序号</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valvePos">
   <property name="geometry">
    <rect>
     <x>590</x>
     <y>330</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>序号</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveName">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>70</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>暂无选中</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveType">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>250</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>暂无选中</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>210</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>设备类型</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveKey">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>160</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>暂无选中</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>120</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>气阀序号</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_9">
   <property name="geometry">
    <rect>
     <x>820</x>
     <y>290</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>位置</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valvePos1">
   <property name="geometry">
    <rect>
     <x>820</x>
     <y>330</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>序号</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveName1">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>70</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>暂无选中</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_10">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>120</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>气阀序号</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_11">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>210</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>设备类型</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveIndex1">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>330</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>序号</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveKey1">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>160</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>暂无选中</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_12">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>30</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>气阀名称</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveType1">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>250</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>暂无选中</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_13">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>290</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>序号</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveFreq">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>420</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>1</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_14">
   <property name="geometry">
    <rect>
     <x>490</x>
     <y>380</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>频率设定</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_valveFreq1">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>420</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: #fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>1</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_15">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>380</y>
     <width>201</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {	
	background-color: rgb(42, 217, 93);
   color:#fff;
   border: 2px solid #0C3D1A;
}</string>
   </property>
   <property name="text">
    <string>频率设定</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ValveTest_FreqPlus_btn">
   <property name="geometry">
    <rect>
     <x>740</x>
     <y>490</y>
     <width>81</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(85, 170, 127);</string>
   </property>
   <property name="text">
    <string>频率+</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ValveTest_FreqDec_btn">
   <property name="geometry">
    <rect>
     <x>830</x>
     <y>490</y>
     <width>81</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(85, 170, 127);</string>
   </property>
   <property name="text">
    <string>频率-</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ValveTest_OverTurn_btn">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>480</y>
     <width>110</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string> 反转</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ValveTest_Continue_btn">
   <property name="geometry">
    <rect>
     <x>360</x>
     <y>480</y>
     <width>110</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>连续</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ValveTest_Clear_btn">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>480</y>
     <width>110</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string> 清除</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ValveTest_Auto_btn">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>480</y>
     <width>110</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>自动</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_quit">
   <property name="geometry">
    <rect>
     <x>470</x>
     <y>480</y>
     <width>110</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>退出</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
