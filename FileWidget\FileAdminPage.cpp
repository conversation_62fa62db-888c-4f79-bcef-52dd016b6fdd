#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "fileform.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QFileDialog>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

// 文件管理
void FileForm::initFileAdminPage()
{
    if ( isFileAdminPageInited )
        return;
    // 文件管理页三个按钮的槽和信号
    connect( this->ui->pushButton_clone, &QPushButton::clicked, this, [&]() {
        if ( this->currentFATFile.isEmpty() )
        {
            QMessageBox::information( nullptr, "错误提示", "请先选中文件" );
        }
        else
        {
            QString   cur = FAT_DIR + this->currentFATFile;
            QFileInfo fileInfo( this->currentFATFile );
            QString   baseName = fileInfo.baseName();
            QString   cloned   = FAT_DIR + baseName + ( "副本" ) + ".FAT";
            bool      exist    = QFile::exists( cloned );
            int       i        = 0;
            while ( exist )
            {
                i++;
                cloned = FAT_DIR + baseName + ( "副本" ) + QString::number( i ) + ".FAT";
                exist  = QFile::exists( cloned );
                qDebug() << "exist: " << exist;
            }
            qDebug() << cur << cloned;
            qDebug() << QFile::copy( cur, cloned );
            this->refreshFATFileList( FAT_DIR );
            this->initFATFileTableView();

            this->ui->File_tableView->selectRow( this->currentTableSelIndex );
        }
    } );
    connect( this->ui->pushButton_delete, &QPushButton::clicked, this, [&]() {
        if ( this->currentFATFile.isEmpty() )
        {
            QMessageBox::information( nullptr, ( "错误提示" ), ( "请先选中文件" ) );
        }
        else
        {
            QMessageBox::StandardButton reply;
            reply = QMessageBox::question( nullptr, ( "删除文件" ), ( "确认删除文件吗？" ), QMessageBox::Yes | QMessageBox::No );
            if ( reply == QMessageBox::Yes )
            {
                // 执行文件删除操作
                QString   filePath = FAT_DIR + this->currentFATFile;
                QFileInfo fileInfo( filePath );
                if ( fileInfo.exists() )
                {
                    QFile::remove( filePath );
                    // 刷新table
                    this->refreshFATFileList( FAT_DIR );
                    this->initFATFileTableView();
                }
                else
                {
                    QMessageBox::warning( nullptr, ( "错误提示" ), ( "文件不存在！" ) );
                }
            }
        }
    } );
    connect( this->ui->pushButton_rename, &QPushButton::clicked, this, [&]() {
        if ( this->currentFATFile.isEmpty() )
        {
            QMessageBox::information( nullptr, ( "错误提示" ), ( "请先选中文件" ) );
        }
        else
        {
            InputDialog dialog( this, ( "输入文本" ), ( "新文件名:" ) );
            int         result = dialog.exec();

            if ( result == QDialog::Accepted )
            {
                QString inputText = dialog.getInputText();
                qDebug() << "输入的文本是:" << inputText;
                // 用户点击了OK按钮并且输入了文本内容
                if ( !inputText.isEmpty() )
                {
                    qDebug() << "输入的文本内容为：" << inputText;
                    QFileInfo fileInfo( inputText );
                    QFile     file( FAT_DIR + this->currentFATFile );
                    QFileInfo fileInfoLast( FAT_DIR + this->currentFATFile );
                    QString   newName = fileInfo.baseName() + "." + fileInfoLast.suffix();

                    if ( file.rename( FAT_DIR + newName ) )
                    {
                        // 刷新table
                        this->refreshFATFileList( FAT_DIR );
                        this->initFATFileTableView();
                        // 选中重命名的行
                        this->ui->currentFileName->setText( newName );
                        this->currentFATFile = newName;
                        for ( int row = 0; row < this->_db_fat_table_model->rowCount(); ++row )
                        {
                            qDebug() << this->_db_fat_table_model->item( row, 0 )->text() << newName;
                            if ( this->_db_fat_table_model->item( row, 0 )->text() == newName )
                            {
                                this->ui->File_tableView->selectRow( row );
                                break;
                            }
                        }
                    }
                    else
                    {
                        QMessageBox::information( nullptr, ( "错误提示" ), ( "重名失败，检查是否重名！" ) );
                    }
                }
            }
            else
            {
                qDebug() << "对话框关闭了";
            }
        }
    } );

    // USB 文件导入
    connect( this->ui->pushButton_importUSB, &QPushButton::clicked, this, [&]() {
        ui->File_Tiltle_label->setText( "文件及生产/文件交换" );
        this->ui->File_stackedWidget->setCurrentIndex( 6 );
        initUSBTransferPage();
    } );

    // 点击确定按钮后，根据menuIndex的不同，跳转不同的页面
    connect( this->ui->btnOK, &QPushButton::clicked, this, [&]() {
        if ( this->currentFATFile.isEmpty() )
        {
            QMessageBox::information( nullptr, ( "错误提示" ), ( "请先选中文件" ) );
            return;
        }

        // import FAT File
        QString filePath = FAT_DIR + this->currentFATFile;
        // FAT文件解析
        FATParser FATParser;
        if ( fatDataMain != nullptr )
            delete fatDataMain;
        this->fatDataMain = new FATParser::FATDataMain();

        // 检查扩展名
        QFileInfo fileInfo( this->currentFATFile );
        QString   extension = fileInfo.suffix();
        if ( extension == "FAT" )
            FATParser.Parser( filePath, this->fatDataMain );
        else if ( extension == "MWW" )
            restoreFromMWWFile( this->currentFATFile );
        else
        {
            QMessageBox::information( nullptr, ( "错误提示" ), ( "文件类型错误" ) );
            return;
        }

        // 花型文件保存
        this->savePatternImage();

        // Read Json files
        if ( this->linkCfg == nullptr )
        {
            this->linkCfg = new ReadLinkConfig();
            this->linkCfg->parseLinkConfig( CFG_DIR + "link-data.json" );
        }

        // Default Chain
        ui->FileForm_pbtn_pattern->setVisible( true );
        ui->FileForm_pbtn_chain->setVisible( true );
        ui->FileForm_pbtn_fengtou->setVisible( true );
        ui->File_Tiltle_label->setText( "文件及生产/链条" );
        this->ui->File_stackedWidget->setCurrentIndex( 1 );
        this->initChainDataPage();
    } );

    /* 检查FAT文件夹是否存 */
    QDir fatDir( FAT_DIR );
    if ( !fatDir.exists() )
    {
        fatDir.mkdir( fatDir.absolutePath() );
        QMessageBox::information( nullptr, "错误提示", "FAT文件夹不存在" );
    }
    else
    {
        // 遍历FAT文件夹，获取FAT文件列表
        this->refreshFATFileList( FAT_DIR );
    }
    this->initFATFileTableView();
    isFileAdminPageInited = true;
}

void FileForm::refreshFATFileList( QString fileFATbaseAddr )
{
    if ( this->FATFileList == nullptr )
    {
        this->FATFileList = new QList< FileForm::FileInfoItem >();
    }
    else
    {
        this->FATFileList->clear();
    }

    // 遍历文件夹
    QDir        dir( fileFATbaseAddr );
    QStringList filters;
    filters << "*.FAT"
            << "*.MWW";  // 只匹配FAT文件
    dir.setNameFilters( filters );
    dir.setFilter( QDir::Files | QDir::NoSymLinks );

    QFileInfoList fileInfoList = dir.entryInfoList();
    for ( const QFileInfo& fileInfo : fileInfoList )
    {
        FileForm::FileInfoItem item;
        item.fileName     = fileInfo.fileName();
        item.fileSize     = QString::number( fileInfo.size() / 2024.0, 'f', 2 ) + " KB";
        item.lastModified = fileInfo.lastModified().toString( "yyyy-MM-dd HH:mm:ss" );
        this->FATFileList->append( item );
    }
}

void FileForm::initFATFileTableView()
{
    if ( this->_db_fat_table_model == nullptr )
    {
        this->_db_fat_table_model = new QStandardItemModel();
        this->ui->File_tableView->setModel( this->_db_fat_table_model );

        // 单击选中事件
        // 连接 clicked 信号到槽函数
        connect( this->ui->File_tableView, &QTableView::doubleClicked, this, [=]( const QModelIndex& index ) {
            // 获取点击的行号
            int row = index.row();
            //            qDebug() << "Table" << row;
            if ( row >= 0 )
            {
                // 输出选中行的数据
                //                qDebug() << "SelTableRow" << this->db_table_model_->data( db_table_model_->index( row, 0 ) ).toString() << db_table_model_->data( db_table_model_->index( row, 1 )
                //                ).toString()
                //                         << db_table_model_->data( db_table_model_->index( row, 2 ) ).toString();
                // 赋值
                QString    utf8String = this->_db_fat_table_model->data( _db_fat_table_model->index( row, 0 ) ).toString();
                QByteArray array      = utf8String.toUtf8();
                this->currentFATFile  = QString::fromLocal8Bit( array );
                this->ui->currentFileName->setText( utf8String );
                this->currentTableSelIndex = row;
            }
        } );
    }
    else
    {
        this->_db_fat_table_model->clear();
    }

    // 添加数据
    if ( this->FATFileList != nullptr )
    {
        for ( int i = 0; i < this->FATFileList->length(); i++ )
        {
            QString    string = FATFileList->at( i ).fileName;
            QByteArray array  = string.toLocal8Bit();
            //            qDebug() << array;
            QString utf8String = QString::fromUtf8( array );

            QList< QStandardItem* > add_items;
            add_items << new QStandardItem( utf8String );
            add_items << new QStandardItem( FATFileList->at( i ).fileSize );
            add_items << new QStandardItem( FATFileList->at( i ).lastModified );
            this->_db_fat_table_model->appendRow( add_items );
        }
    }

    QStringList table_h_headers;
    table_h_headers << "文件名"
                    << "文件大小"
                    << "上次修改时间";
    this->_db_fat_table_model->setHorizontalHeaderLabels( table_h_headers );

    //设置选中模式为选中行
    this->ui->File_tableView->setSelectionBehavior( QAbstractItemView::SelectRows );
    // 表格宽度随内容自动扩展
    this->ui->File_tableView->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    // 隐藏网格线
    this->ui->File_tableView->setShowGrid( false );
    // 启用排序
    this->ui->File_tableView->setSortingEnabled( true );
}
