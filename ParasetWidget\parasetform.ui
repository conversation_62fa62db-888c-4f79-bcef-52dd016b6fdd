<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ParaSetForm</class>
 <widget class="QWidget" name="ParaSetForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>15</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>50</y>
     <width>1024</width>
     <height>491</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="currentIndex">
    <number>1</number>
   </property>
   <widget class="QWidget" name="page">
    <widget class="QTabWidget" name="tabWidget">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>1024</width>
       <height>491</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabBar::tab{
	height: 50px;
	width:140px;
	border-radius: 5px 5px 0 0;
	margin-right: 2px;
	padding: 5px;
	background-color: #e0e0e0;
}
QTabBar::tab:selected {
	background-color: #3498db;
	color: white;
	font-weight: bold;
}
QTabWidget::pane {
	border: 1px solid #c0c0c0;
	background-color: #f5f5f5;
}
QWidget{
	background-color: #f5f5f5;
}</string>
     </property>
     <property name="currentIndex">
      <number>4</number>
     </property>
     <widget class="QWidget" name="BasicPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>基本参数</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="NeedlePara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>选针器设置</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="PeripheralPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>外设设置</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="PositionPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>气阀位置1</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="Position2Para_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>气阀位置2</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="FengtouPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>缝头参数</string>
      </attribute>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_4">
    <widget class="QTabWidget" name="tabWidget_stepperMotor">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>1021</width>
       <height>491</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabBar::tab{
	height: 50px;
	width:140px;
	border-radius: 5px 5px 0 0;
	margin-right: 2px;
	padding: 2px 5px;
	background-color: #e0e0e0;
}
QTabBar::tab:selected {
	background-color: #3498db;
	color: white;
	font-weight: bold;
}
QTabWidget::pane {
	border: 1px solid #c0c0c0;
	background-color: #f5f5f5;
}
QWidget{
	background-color: #f5f5f5;
}</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="SocketMotorPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>织袜电机</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="FengtouMotorPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>缝头电机</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="OtherMotorPara_tab">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <attribute name="title">
       <string>其他电机</string>
      </attribute>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_5">
    <widget class="QPushButton" name="pbtn_CircleAdd">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>170</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>圈控制插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_InsClear">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>120</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>指令清空</string>
     </property>
    </widget>
    <widget class="QListWidget" name="listWidget_reset_type">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>0</y>
       <width>151</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_InsDelete">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>70</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>指令删除</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_instructions">
     <property name="geometry">
      <rect>
       <x>310</x>
       <y>0</y>
       <width>551</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>指令</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_InsAdd">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>20</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>指令插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_circleDelete">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>220</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>圈控制删除</string>
     </property>
    </widget>
    <widget class="QListWidget" name="listWidget_circle">
     <property name="geometry">
      <rect>
       <x>170</x>
       <y>0</y>
       <width>131</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_6">
    <widget class="QGroupBox" name="gbox_valveList">
     <property name="geometry">
      <rect>
       <x>520</x>
       <y>10</y>
       <width>331</width>
       <height>471</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>指令与气阀</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_MotorList">
     <property name="geometry">
      <rect>
       <x>190</x>
       <y>240</y>
       <width>331</width>
       <height>241</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>电机</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionDelete">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>140</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作删除</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_fengtouStepName">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>10</y>
       <width>161</width>
       <height>81</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>组合名称</string>
     </property>
     <widget class="MyLineEdit" name="le_fengtouStepName">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>40</y>
        <width>141</width>
        <height>31</height>
       </rect>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QListWidget" name="lw_FtStepList">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>90</y>
       <width>161</width>
       <height>391</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_fengtouBase">
     <property name="geometry">
      <rect>
       <x>190</x>
       <y>10</y>
       <width>331</width>
       <height>231</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>基本信息</string>
     </property>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>30</y>
        <width>303</width>
        <height>191</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_14">
         <item>
          <widget class="QLabel" name="label_12">
           <property name="text">
            <string>动作序号: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_ftStepId">
           <property name="enabled">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_16">
         <item>
          <widget class="QLabel" name="label_13">
           <property name="text">
            <string>动作名称: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_ftStepName"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_17">
         <item>
          <widget class="QLabel" name="label_14">
           <property name="text">
            <string>动作延时: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ft_WarnDelay"/>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ftActionDelay"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_18">
         <item>
          <widget class="QLabel" name="label_15">
           <property name="text">
            <string>动作命令: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ftACommand"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_19">
         <item>
          <widget class="QLabel" name="label_19">
           <property name="text">
            <string>复位动作: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ftResetAction"/>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QPushButton" name="pbtn_actionAdd">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>80</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionClear">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>200</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_motorAdd">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>270</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>电机插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_motorDelete">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>330</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>电机删除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_motorClear">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>390</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>电机清空</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QPushButton" name="pbtn_tomachine">
   <property name="geometry">
    <rect>
     <x>210</x>
     <y>540</y>
     <width>120</width>
     <height>50</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>机器参数</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_toStepper">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>540</y>
     <width>120</width>
     <height>50</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>步进设置</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_toreset">
   <property name="geometry">
    <rect>
     <x>450</x>
     <y>540</y>
     <width>120</width>
     <height>50</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>快速复位</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_save">
   <property name="geometry">
    <rect>
     <x>690</x>
     <y>540</y>
     <width>120</width>
     <height>50</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string>保存</string>
   </property>
  </widget>
  <widget class="QPushButton" name="ParasetFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>0</y>
     <width>51</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="Paraset_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>0</y>
     <width>251</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>机器参数</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_toFengtou">
   <property name="geometry">
    <rect>
     <x>570</x>
     <y>540</y>
     <width>120</width>
     <height>50</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>缝头校准</string>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
