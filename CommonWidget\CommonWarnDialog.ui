<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CommonWarnDialog</class>
 <widget class="QWidget" name="CommonWarnDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>410</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>警告信息</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#CommonWarnDialog {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 8px;
}</string>
  </property>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>110</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
   </property>
   <property name="text">
    <string>错误</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_img">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>10</y>
     <width>361</width>
     <height>361</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_next">
   <property name="geometry">
    <rect>
     <x>550</x>
     <y>380</y>
     <width>31</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 2px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
   </property>
   <property name="text">
    <string>&gt;</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_desc">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>240</y>
     <width>121</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
   </property>
   <property name="text">
    <string>描述</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
   <property name="wordWrap">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_close">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>0</y>
     <width>31</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
    background-color: #e74c3c;
    border: 1px solid #c0392b;
    border-radius: 5px;
    padding: 2px;
    color: white;
}
QPushButton:hover {
    background-color: #c0392b;
    border: 1px solid #a93226;
}
QPushButton:pressed {
    background-color: #a93226;
    border: 1px solid #922b21;
}</string>
   </property>
   <property name="text">
    <string>X</string>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>30</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
   </property>
   <property name="text">
    <string>分类</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_count">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>380</y>
     <width>451</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #f8f8f8;
    color: #333333;
    border: 1px solid #dddddd;
    border-radius: 5px;
}</string>
   </property>
   <property name="text">
    <string>0/0</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_del">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>380</y>
     <width>31</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
    background-color: #e74c3c;
    border: 1px solid #c0392b;
    border-radius: 5px;
    padding: 2px;
    color: white;
}
QPushButton:hover {
    background-color: #c0392b;
    border: 1px solid #a93226;
}
QPushButton:pressed {
    background-color: #a93226;
    border: 1px solid #922b21;
}</string>
   </property>
   <property name="text">
    <string>Del</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_type">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>60</y>
     <width>131</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #f8f8f8;
    color: #333333;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
   </property>
   <property name="wordWrap">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_name">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>140</y>
     <width>131</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #f8f8f8;
    color: #333333;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
   </property>
   <property name="wordWrap">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_desc">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>270</y>
     <width>131</width>
     <height>111</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #f8f8f8;
    color: #333333;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
   </property>
   <property name="wordWrap">
    <bool>true</bool>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
