#ifndef HANDIMAGEWIDGET_H
#define HANDIMAGEWIDGET_H

#include <QApplication>
#include <QImage>
#include <QLabel>
#include <QPixmap>
#include <QResource>
#include <QWidget>

class HandImageWidget : public QWidget
{
    Q_OBJECT
public:
    HandImageWidget( QWidget* parent = nullptr ) : QWidget( parent )
    {
        // 加载图片
        QResource resource( ":/hand.png" );
        QImage    image;
        image.load( resource.absoluteFilePath() );

        QPixmap pixmap       = QPixmap::fromImage( image );
        QPixmap scaledPixmap = pixmap.scaledToWidth( 25, Qt::SmoothTransformation );

        // 创建一个QLabel来显示图片
        QLabel* label = new QLabel( this );
        label->setFixedSize( 50, 50 );
        label->setPixmap( scaledPixmap );
        label->setAlignment( Qt::AlignRight );

        //        QLabel* label = new QLabel( "------>", this );
        //        label->setAlignment( Qt::AlignCenter );

        // 初始位置设置为 20
        int initialY = 50;
        label->move( 0, initialY );
    }

    // 调整位置的函数
    void adjustLabelPosition( int y )
    {
        QLabel* label = findChild< QLabel* >();
        if ( label )
        {
            label->move( label->x(), y );
        }
    }
};

#endif  // HANDIMAGEWIDGET_H
