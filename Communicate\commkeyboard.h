#ifndef COMMKEYBOARD_H
#define COMMKEYBOARD_H

#include <QObject>
#include <QSerialPort>
#include <QDebug>
#include <QByteArray>
#include <QTimer>
#include "keyboardBeep.h" // 添加蜂鸣器头文件

// 定义串口名称宏
#define KEYBOARD_SERIAL_PORT "ttyAS4"

class CommKeyboard : public QObject
{
    Q_OBJECT
public:
    explicit CommKeyboard(QObject *parent = nullptr);
    ~CommKeyboard();

    // 初始化串口
    bool initSerialPort();
    // 关闭串口
    void closeSerialPort();
    // 发送数据到串口
    bool sendData(const QByteArray &data);
    // 更新LED状态
    bool updateLEDStatus(int ledNumber, bool isOn);
    // 发送测试帧
    bool sendTestFrame();

signals:
    // 单键按下信号
    void keyPressed(int keyValue);
    // 组合键按下信号
    void combinationKeyPressed(int keyValue1, int keyValue2);
    // 键盘测试结果信号
    void keyboardTestResult(bool success);

private slots:
    // 接收串口数据的槽函数
    void onDataReceived();
    // 测试超时处理
    void onTestTimeout();

private:
    QSerialPort *m_serialPort;
    QByteArray m_buffer;  // 用于存储接收到的数据
    keyboardBeep *m_beep; // 添加蜂鸣器对象
    QTimer *m_testTimer;  // 测试超时定时器
    int m_testRetryCount; // 测试重试计数
    
    // 解析接收到的数据
    void parseReceivedData();
    
    // 常量定义
    static const int TEST_TIMEOUT = 2000;     // 测试超时时间(毫秒)
    static const int MAX_TEST_RETRIES = 3;    // 最大测试重试次数
    static const int WRITE_TIMEOUT = 1000;    // 写入超时时间(毫秒)
    static const int BEEP_DURATION = 50;      // 蜂鸣器鸣叫时长(毫秒)
};

#endif  // COMMKEYBOARD_H
