<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NumberInputForm</class>
 <widget class="QWidget" name="NumberInputForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>320</width>
    <height>400</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>320</width>
    <height>400</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>数值输入</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#NumberInputForm {
    background-color: #f5f5f5;
    border: 1px solid #cccccc;
    border-radius: 10px;
}

QPushButton {
    font-size: 10pt;
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}

QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}

QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

#pbtn_ok {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
    font-weight: bold;
}

#pbtn_ok:hover {
    background-color: #2980b9;
}

#pbtn_ok:pressed {
    background-color: #1f6aa5;
}

#pbtn_cancel {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

#pbtn_cancel:hover {
    background-color: #c0392b;
}

#pbtn_cancel:pressed {
    background-color: #a93226;
}</string>
  </property>
  <widget class="QPushButton" name="pbtn_minus">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>320</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>-</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>80</y>
     <width>280</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLineEdit {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_0">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>320</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_backspace">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>140</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>←</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_1">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>260</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>1</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_2">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>260</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>2</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_3">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>260</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>3</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_4">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>200</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>4</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_5">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>200</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>5</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_6">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>200</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>6</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_7">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>140</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>7</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_8">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>140</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>8</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_9">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>140</y>
     <width>71</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>9</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_ok">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>320</y>
     <width>141</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Enter</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_cancel">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>200</y>
     <width>71</width>
     <height>121</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>退出</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_title">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>10</y>
     <width>271</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    color: #2980b9;
}</string>
   </property>
   <property name="text">
    <string>TextLabel</string>
   </property>
  </widget>
  <widget class="QLabel" name="lbl_range">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>40</y>
     <width>271</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    color: #666666;
}</string>
   </property>
   <property name="text">
    <string>TextLabel</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
