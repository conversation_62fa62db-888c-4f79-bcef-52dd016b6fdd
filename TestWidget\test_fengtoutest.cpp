
#include "testform.h"
#include "ui_testform.h"

void TestForm::FengtouTestInit()
{
    if ( isFengtouTestPageInit == false )
    {
        FTTestMotorBtnGroup = new QButtonGroup( this );
        FTTestMotorBtnGroup->addButton( ui->FTTest_MotorCW_btn, 0 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_MotorCCW_btn, 1 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_MotorGoZero_btn, 2 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_MoveBackAndForth_btn, 3 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_MotorStart_btn, 4 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_MotorStop_btn, 5 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_SetZero_btn, 6 );
        FTTestMotorBtnGroup->addButton( ui->FTTest_SelfCheck_btn, 7 );
        // 仍然使用步进电机的按钮功能
        connect( this->FTTestMotorBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onStepTestCtrlBtnGroupClicked( int ) ) );

        ShowFengtouButton();

        // 缝头动作按钮
        FTTestActionBtnGroup = new QButtonGroup( this );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_1, 0 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_2, 1 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_3, 2 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_4, 3 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_5, 4 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_6, 5 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_7, 6 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_8, 7 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_9, 8 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_10, 9 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_11, 10 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_12, 11 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_13, 12 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_14, 13 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_15, 14 );
        FTTestActionBtnGroup->addButton( ui->FTTest_Action_16, 15 );
        connect( this->FTTestActionBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onFengtouActionBtnGroupClicked( int ) ) );

        isFengtouTestPageInit = true;
    }
}

void TestForm::ShowFengtouButton()
{
    // Clear QGroupBox first
    QLayout* layout = this->ui->frm_FtValveButtonList->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->frm_FtValveButtonList );

    // 共24个，4*6
    for ( quint8 index = 0; index < 4; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;

        for ( quint8 j = 0; j < 6; j++ )
        {
            // 注意：数字从1开始，配置文件id也必须从1开始
            CircleButton* button = new CircleButton( this, 50, index * 6 + j + 1 );
            // 如果已选中，则换色
            if ( this->ftValveSelectList.contains( index * 6 + j + 1 ) )
            {
                button->setColor( "#D67977", "#774342" );
            }
            connect( button, &CircleButton::onClicked, this, &TestForm::onFtValveButtonClicked );
            rowLayoutx->addWidget( button );
        }
        mainLayout->addLayout( rowLayoutx );
    }
}

void TestForm::onFtValveButtonClicked()
{
    CircleButton* senderCircleButton = qobject_cast< CircleButton* >( sender() );
    int           no                 = senderCircleButton->getNumber();
    //    ReadTestConfig::ValveInfoStruct item               = mainData->readTestConfig->getValveList()->value( no );

    //    showValveInfo( &item );

    //    // 是否有连续任务正执行，没有的话，执行一次动作
    //    if ( this->testTimer == nullptr )
    //    {
    //        // TODO 执行一次进出操作
    //        quint8 state = this->realValveState[ ( no - 1 ) / 8 ];
    //        // See state 0 or 1
    //        if ( state & ( 1 << ( no - 1 ) % 8 ) )
    //        {
    //            this->realValveState[ ( no - 1 ) / 8 ] &= ~( 1 << ( no - 1 ) % 8 );
    //            senderCircleButton->setColor( "#3282F6", "#000C7B" );
    //            valveSelectList.removeOne( no );
    //        }
    //        else
    //        {
    //            this->realValveState[ ( no - 1 ) / 8 ] |= ( 1 << ( no - 1 ) % 8 );
    //            senderCircleButton->setColor( "#D67977", "#774342" );
    //            valveSelectList.insert( std::upper_bound( valveSelectList.begin(), valveSelectList.end(), no ), no );
    //        }
    //        memcpy( this->valveState, this->realValveState, sizeof( this->valveState ) );
    //        sendValveState();
    //    }
}

void TestForm::onFengtouActionBtnGroupClicked( int id )
{
    qDebug() << id;

    quint8 data[ 2 ] = { 0 };
    data[ 0 ]        = 0x00;
    data[ 1 ]        = id;
    comm->pushDataTobuffer( 0x0c, data, 2 );
}
