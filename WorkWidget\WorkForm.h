#ifndef WORKFORM_H
#define WORKFORM_H

#include "Common/CommOperations.h"
#include "Common/defines.h"
#include "Common/parameters.h"
#include "CommonWidget/EyeWarnView.h"
#include "CommonWidget/FileSelectForm.h"
#include "CommonWidget/comboform.h"
#include "CommonWidget/numberinputform.h"
#include "Communicate/Communication.h"
#include "Communicate/circleDataProtocol.h"
#include "CoreBusiness/SokBusiness.h"
#include "CoreBusiness/sokparser.h"
#include "CraftParamWidget/CraftParamForm.h"
#include "QNetworkInterface"
#include "WorkWidget/RunningPowerDialog.h"
#include <QMutex>
#include <QTextCodec>
#include <QTimer>
#include <QWidget>

class SokBusiness;

namespace Ui
{
class WorkForm;
}

class WorkForm : public QWidget
{
    Q_OBJECT

public:
    explicit WorkForm( QWidget* parent = nullptr, MainWidgetData* mainData = nullptr, Communication* com = nullptr, upInfoMessage* lastMessage = nullptr, CommOperations* commOps = nullptr );
    ~WorkForm();

    /************* 弹窗 *************/
    RunningPowerDialog* powerDialog = nullptr;

    void refreshWarnWidget( qint8 type_id, quint16 id, quint8 status );
    void refreshServoWarnLabel( quint8 errorCode );
    void refreshEyeWarnView( quint8 id, quint32 value );
    void refreshWidgetFromUpperMsg( float abAngle, quint16 abNeedle, qint16 realSpeed );
    // Info Page
    void refreshInfoPage( quint16 needle, float abAngel );

private:
    Ui::WorkForm*   ui;
    MainWidgetData* mainData;
    upInfoMessage*  lastUpInfoMessage = nullptr;  // 新增成员变量
    bool            isMainPageInited  = false;
    bool            isWarnPageInited  = false;
    bool            isInfoPageInited  = false;

    /************* 通用控件 *************/
    ComboForm*       comboForm       = nullptr;
    NumberInputForm* numberInputForm = nullptr;
    QTimer           sysTickTimer;  // 系统时间定时器

    int sysTickCounter;           // 系统时间计数器
    int timeThisProdCounter = 0;  // 本件用时计数器

    /************* 422通信相关 *************/
    Communication* comm;

    // 通信操作对象
    CommOperations* m_commOperations = nullptr;

    /************* sok文件相关 *************/
    QString         currentSokFile    = "";
    FileSelectForm* sokFileSelectForm = nullptr;

    /************* 工艺文件相关 *************/
    QString         currentCraftFile  = "";
    FileSelectForm* cftFileSelectForm = nullptr;

    /************* 产量和尺寸 *************/
    quint16 productNum    = 1;
    quint16 curProductNum = 0;

    /************* step数据Core *************/
    SokBusiness* sokBusiness = nullptr;

    /************* 报警相关 *************/
    QLabel*      servoWarnLabel   = nullptr;  // 伺服电机报警号
    EyeWarnView* yarnBreakEWV     = nullptr;
    EyeWarnView* yarnNightEWV     = nullptr;
    EyeWarnView* codeTimeoutEWV   = nullptr;
    EyeWarnView* sampleTimeoutEWV = nullptr;
    EyeWarnView* codeErrorEWV     = nullptr;

    void initMainPage();
    void initInfoPage();
    // 根据currentStepIndex的变化刷新主窗口显示内容
    void refreshWorkFormInfo();

    // Return to MainWindow
    void onWorkFormHomeBtnClicked();

    // Warn Page
    void initWarnPage();
    void GenerateWarnWidget( quint8 type_id );
    void refreshAllWarnWidgets();  // 新增函数声明

    // Info Page
    void refreshFingerAndValve();

signals:
    void WorkFormToMainWinToShowSignal( int id );

private slots:
    // 选择尺寸，从0开始
    void sizeSelected( int size );
    // 产品设定
    void prodCounterSet( QString text );
    // Toggle Machine
    void ToggleMachine();
    // All StepData Frame Sent
    void onAllStepDataSent();
    // Command Frame Received
    void onCommandReceived( qint16 size, quint8* data );
};

#endif  // WORKFORM_H
