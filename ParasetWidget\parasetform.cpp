﻿#include "parasetform.h"
#include "Config/readmachinefileconfig.h"
#include "ui_parasetform.h"

ParaSetForm::ParaSetForm( QWidget* parent, Communication* comm, MainWidgetData* mainData ) : QWidget( parent ), ui( new Ui::ParaSetForm ), comm( comm ), mainData( mainData )
{
    ui->setupUi( this );

    /* 返回主菜单键连接  */
    connect( ui->ParasetFormHome_btn, SIGNAL( clicked() ), this, SLOT( onParasetHomeBtnClicked() ) );
    connect( this, &ParaSetForm::onSwitchPageButtonClicked, this, &ParaSetForm::switchPage );
    connect( ui->pbtn_tomachine, &QPushButton::clicked, this, [&]() { emit onSwitchPageButtonClicked( 0 ); } );
    connect( ui->pbtn_toStepper, &QPushButton::clicked, this, [&]() { emit onSwitchPageButtonClicked( 1 ); } );
    connect( ui->pbtn_toreset, &QPushButton::clicked, this, [&]() { emit onSwitchPageButtonClicked( 2 ); } );
    connect( ui->pbtn_toFengtou, &QPushButton::clicked, this, [&]() { emit onSwitchPageButtonClicked( 3 ); } );
    connect( ui->pbtn_save, &QPushButton::clicked, this, &ParaSetForm::onBtnSaveClicked );

    // 使用 mainData 中的指针
    if ( mainData != nullptr )
    {
        readCfg               = mainData->readCfg;
        readMachineFileConfig = mainData->readMachineFileConfig;
        readResetCfg          = mainData->readResetConfig;
        if ( readLinkCfg == nullptr )
        {
            readLinkCfg = new ReadLinkConfig();
            readLinkCfg->parseLinkConfig( MAIN_CFG_DIR + "link-data.json" );
        }
    }
    else
    {
        // 如果 mainData 为空，则创建新的对象（兼容旧代码）
        if ( readCfg == nullptr )
        {
            readCfg = new ReadCfg();
            readCfg->parseCfgConfig( MAIN_CFG_DIR + "Cfg.json" );
        }
        if ( readMachineFileConfig == nullptr )
        {
            readMachineFileConfig = new ReadMachineFileConfig();
            readMachineFileConfig->parseConfig( MAIN_CFG_DIR + "MachineFileConfig.json" );
        }
        if ( readResetCfg == nullptr )
        {
            readResetCfg = new ReadResetConfig();
            readResetCfg->parseResetConfig( MAIN_CFG_DIR + "ResetConfig.json" );
        }
    }

    ui->stackedWidget->setCurrentIndex( 0 );
    initMachineParasetPage();
    initStepperMotorParasetPage();
    initFengtouCalParasetPage();

    // 发送参数定时器
    paramSendTimer.setSingleShot( true );
    connect( &paramSendTimer, &QTimer::timeout, [&]() {
        if ( paramSendSuccess )
        {
            qDebug() << "MachineParam frame sent successfully.";
            retryCountParam = 0;  // 发送成功，重置重发计数
        }
        else
        {
            qDebug() << "Failed to send MachineParam frame.";
            sendMachineParam();
            retryCountParam++;
            if ( retryCountParam >= COM_MAX_RETRIES )
            {
                qDebug() << "Failed to send MachineParam frame after 3 retries. Exiting.";
                retryCountParam = 0;
                return;  // 退出程序
            }
            qDebug() << "Retrying in" << COMM_INTERVAL << "milliseconds...";
            paramSendTimer.start( COMM_INTERVAL );  // 重新开始定时器
        }
    } );
    connect( comm, &Communication::MachineParamFrameReceived, this, [&]( qint16 size, quint8* data ) {
        qDebug() << size << data[ 0 ];
        if ( size > 1 )
        {
            if ( data[ 0 ] == 0x80 )
            {
                paramSendSuccess = true;
            }
        }
    } );
}

ParaSetForm::~ParaSetForm()
{
    delete ui;
}

/* 测试界面返回 主菜单键按下槽函数 */
void ParaSetForm::onParasetHomeBtnClicked()
{
    //    this->hide();
    this->close();

    emit ParasetFormToMainWinToShowSignal();

    /* 清空所有测试的数据 */
}

void ParaSetForm::switchPage( int id )
{
    if ( id == 0 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Paraset_Tiltle_label->setText( tr( "机器参数编辑" ) );
    }
    else if ( id == 1 )
    {
        ui->stackedWidget->setCurrentIndex( 1 );
        ui->Paraset_Tiltle_label->setText( tr( "步进电机设置" ) );
    }
    else if ( id == 2 )
    {
        ui->stackedWidget->setCurrentIndex( 2 );
        ui->Paraset_Tiltle_label->setText( tr( "快速复位" ) );
        initFastResetParasetPage();
    }
    else if ( id == 3 )
    {
        ui->stackedWidget->setCurrentIndex( 3 );
        ui->Paraset_Tiltle_label->setText( tr( "缝头校准" ) );
    }
}

void ParaSetForm::initMachineParasetPage()
{
    if ( isMachineParasetPageInit == false )
    {
        // init
        initBasicParasetTab();
        initNeedleParasetTab();
        initPeripheralParasetTab();
        initPositionParasetTab();
        initPosition2ParasetTab();
        initFengtouParasetTab();
        isMachineParasetPageInit = true;
    }
}

void ParaSetForm::initStepperMotorParasetPage()
{
    if ( isStepperMotorParasetPageInit == false )
    {
        // init
        initSocketMotorParasetTab();
        initFengtouMotorParasetTab();
        initOtherMotorParasetTab();

        isStepperMotorParasetPageInit = true;
    }
}

/* 显示具体的界面 */
void ParaSetForm::ShowMenu( int id )
{
    if ( id == 0 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Paraset_Tiltle_label->setText( tr( "机器参数编辑" ) );
        initMachineParasetPage();
    }
    else if ( id == 1 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Paraset_Tiltle_label->setText( tr( "文件/花型预览" ) );
    }
    else if ( id == 2 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Paraset_Tiltle_label->setText( tr( "文件/链条文件" ) );
    }
}

void ParaSetForm::onBtnSaveClicked()
{
    if ( ui->stackedWidget->currentIndex() < 2 )
    {
        if ( readMachineFileConfig->saveMachineConfig( MAIN_CFG_DIR + "MachineFileConfig.json" ) == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "保存成功" );
            sendMachineParam();
            retryCountParam  = 1;
            paramSendSuccess = false;
            paramSendTimer.start( COMM_INTERVAL );
        }
        else
        {
            QMessageBox::warning( nullptr, "错误提示", "保存失败" );
        }
    }
    else if ( ui->stackedWidget->currentIndex() == 2 )
    {
        if ( readResetCfg->saveResetConfig( MAIN_CFG_DIR + "ResetConfig.json" ) == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "保存成功" );
        }
        else
        {
            QMessageBox::warning( nullptr, "错误提示", "保存失败" );
        }
    }
}

void ParaSetForm::sendMachineParam()
{
    MachineParams machineParams = readMachineFileConfig->makeMachineParamFrame();
    // 创建数组，将machineParams的值赋值给数组，然后通过comm->pushDataTobuffer()发送
    quint8 buffer[ sizeof( MachineParams ) ];
    memcpy( buffer, &machineParams, sizeof( MachineParams ) );
    comm->pushDataTobuffer( 0x01, buffer, sizeof( MachineParams ) );
}
