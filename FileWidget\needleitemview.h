﻿#ifndef NEEDLEITEMVIEW_H
#define NEEDLEITEMVIEW_H

#include <QLabel>
#include <QWidget>

class NeedleItemView : public QLabel
{
    Q_OBJECT
public:
    NeedleItemView( bool state = 0, QWidget* parent = nullptr );
    ~NeedleItemView();
    void mousePressEvent( QMouseEvent* event ) override
    {
        QLabel::mousePressEvent( event );
        emit mousePressed( event );
    }
    void mouseReleaseEvent( QMouseEvent* event ) override
    {
        QLabel::mouseReleaseEvent( event );
        emit mouseReleased( event );
    }

signals:
    void mousePressed( QMouseEvent* event );
    void mouseReleased( QMouseEvent* event );

protected:
    void paintEvent( QPaintEvent* event ) override;

private:
    int  w0;  //自定义控件类构造函数中QLabel宽度，为默认值
    int  h0;  //自定义控件类构造函数中QLabel高度，为默认值
    bool _state;
};

#endif  // NEEDLEITEMVIEW_H
