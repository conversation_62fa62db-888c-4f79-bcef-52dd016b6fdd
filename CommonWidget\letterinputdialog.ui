<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LetterInputDialog</class>
 <widget class="QDialog" name="LetterInputDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>409</width>
    <height>320</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>字母输入对话框</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog {
    border-radius: 10px;
    background-color: #f5f5f5;
    border: 1px solid #cccccc;
}

QPushButton {
    font-size: 10pt;
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}

QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
    color: #2980b9;
}

QPushButton:pressed {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

#pushBtn_OK {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
    font-weight: bold;
}

#pushBtn_OK:hover {
    background-color: #2980b9;
}

#pushBtn_OK:pressed {
    background-color: #1f6aa5;
}

#pushBtn_Cancel {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

#pushBtn_Cancel:hover {
    background-color: #c0392b;
}

#pushBtn_Cancel:pressed {
    background-color: #a93226;
}</string>
  </property>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>40</y>
     <width>301</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLineEdit {
    background-color: white;
    border: 1px solid #3498db;
    border-radius: 5px;
    padding: 5px;
    color: #333333;
}</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_Type">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>50</y>
     <width>101</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    color: #2980b9;
}</string>
   </property>
   <property name="text">
    <string>输入</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushBtn_OK">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>240</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {
	background-color: #0062cc;
}
QPushButton:pressed {
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string>确定</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushBtn_Cancel">
   <property name="geometry">
    <rect>
     <x>210</x>
     <y>240</y>
     <width>101</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>取消</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_B">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>B</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_A">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_C">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>C</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_D">
   <property name="geometry">
    <rect>
     <x>180</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>D</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_E">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>E</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_F">
   <property name="geometry">
    <rect>
     <x>280</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>F</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_G">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>100</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>G</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_I">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>160</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>I</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_H">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>160</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>H</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_J">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>160</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>J</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_L">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>160</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>L</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_M">
   <property name="geometry">
    <rect>
     <x>280</x>
     <y>160</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>M</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_K">
   <property name="geometry">
    <rect>
     <x>180</x>
     <y>160</y>
     <width>41</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;
	background-color: #fff;
	color:#000;
}
QPushButton:pressed {
	color: #e64340;
}</string>
   </property>
   <property name="text">
    <string>K</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
