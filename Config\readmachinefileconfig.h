﻿#ifndef READMACHINEFILECONFIG_H
#define READMACHINEFILECONFIG_H

#include "Communicate/parasetProtocol.h"
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMessageBox>

class ReadMachineFileConfig
{
public:
    ReadMachineFileConfig();
    ~ReadMachineFileConfig();
    void          parseConfig( QString fileAddr );
    quint8        saveConfig( QString fileAddr );
    quint8        saveEEyeConfig( QString fileAddr );
    quint8        saveUserConfig( QString fileAddr );       // 新增函数，只保存用户配置
    quint8        saveServoConfig( QString fileAddr );      // 新增函数，只保存伺服电机配置
    quint8        saveStepMotorConfig( QString fileAddr );  // 新增函数，只保存步进电机配置
    quint8        saveMachineConfig( QString fileAddr );    // 新增函数，只保存机器相关配置
    MachineParams makeMachineParamFrame();
    UserParams    makeUserParamFrame();

#pragma pack( 1 )
    struct MachineFlieConfigItem
    {
        QString            title;
        float              value;
        QString            unit;
        QVector< QString > candidate;
        bool               editable;
    };

    struct XijiangjinConfigItem
    {
        QString title;
        float   value1;
        float   value2;
        QString unit;
        bool    editable;
    };

    struct StepperMotorConfigDataItem
    {
        float              value;
        QString            unit;
        QVector< QString > candidate;
        bool               editable;
    };

    struct StepperMotorConfigItem
    {
        QString                               title;
        QVector< StepperMotorConfigDataItem > data;
    };

    struct WarnConfigItem
    {
        QString title;
        bool    enable;
        int     sensitivity;
        int     direction;
    };

    // 快速复位相关
    struct CmdStruct
    {
        quint8  cmd_num;
        quint16 cmd_param;
        quint8  cmd_state;
    };
    struct CYActionStruct
    {
        quint8               cy_num;
        quint16              cy_circle_cnt;
        quint8               cy_state;
        QVector< CmdStruct > cmd_msg;
    };
    struct BuweiParamStruct
    {
        quint8 start_value;
        quint8 end_value;
        quint8 state;
    };
    struct FastResetConfigItem
    {
        QString                   step_name;  // 部位名称
        quint16                   chicun;     // 尺寸
        quint16                   speed;      // 速度
        BuweiParamStruct          xiangjin;   // 橡筋
        BuweiParamStruct          ktf1;
        BuweiParamStruct          ktf2;
        BuweiParamStruct          zhentong_Density;   // 针筒密度
        BuweiParamStruct          chenjiang_Density;  // 沉降密度
        BuweiParamStruct          shengke_Density;    // 生克密度
        BuweiParamStruct          zuo_lengjiao;       // 左棱角
        BuweiParamStruct          you_lengjiao;       // 右棱角
        quint16                   huaxing_shezhi;     // 1:有花型 0：无花型
        quint16                   lamao_shezhi;       // 1:有花型 0：无花型
        quint16                   dagen_zhongxin;     // 打跟中心
        quint16                   dagen_pianyi;       // 打跟偏移
        quint16                   fujia;              // 附加参数
        QVector< CYActionStruct > cy_action_msg;
    };

    struct FengtouMotorStruct
    {
        quint8  num;
        quint8  pattern;
        quint16 val;
    };

    struct FengtouSignalValveStruct
    {
        quint8  num;
        quint16 type;
        quint8  state;
    };
    struct FengtouStepStruct
    {
        quint16                             baojing_yanshi;
        quint16                             dongzuo_yanshi;
        quint16                             dongzuo_zhiling;
        quint16                             fuwei_dongzuo;
        QVector< FengtouSignalValveStruct > Valve_array;
        QVector< FengtouMotorStruct >       motor_array;
    };
    struct FengtouCalCfgItem
    {
        QVector< FengtouStepStruct > step;
    };

    struct ServoCfgItem
    {
        quint8  id;
        QString title;
        quint16 value;
        QString unit;
        bool    editable;
    };

    // 新增EEye相关结构体
    struct EEyeItemStruct
    {
        quint8  id;
        quint8  sensitivity;
        quint8  inductionDelay;
        quint8  stFreq;
        quint8  endFreq;
        quint8  mappedId;
        quint16 disConThreshold;
        quint16 wrapThreshold;
    };

    struct EEyeConfigStruct
    {
        quint8                    realCount;
        QVector< EEyeItemStruct > items;
    };

#pragma pack()

    QMap< int, MachineFlieConfigItem >* getBasicCfgList()
    {
        return &_basicCfgList;
    }

    QMap< int, MachineFlieConfigItem >* getNeedleCfgList()
    {
        return &_needleCfgList;
    }

    QMap< int, MachineFlieConfigItem >* getPeripheralCfgList()
    {
        return &_peripheralCfgList;
    }

    QMap< int, MachineFlieConfigItem >* getPositionCfgList()
    {
        return &_positionCfgList;
    }

    QMap< int, MachineFlieConfigItem >* getPosition2CfgList()
    {
        return &_position2CfgList;
    }

    QMap< int, MachineFlieConfigItem >* getUserCfgList()
    {
        return &_userCfgList;
    }

    QMap< int, MachineFlieConfigItem >* getFengtouCfgList()
    {
        return &_fengtouCfgList;
    }

    QMap< int, MachineFlieConfigItem >* getOtherCfgList()
    {
        return &_otherCfgList;
    }

    QMap< int, StepperMotorConfigItem >* getSocketMotorCfgList()
    {
        return &_socketMotorCfgList;
    }

    QMap< int, StepperMotorConfigItem >* getFengtouMotorCfgList()
    {
        return &_fengtouMotorCfgList;
    }

    QMap< int, StepperMotorConfigItem >* getOtherMotorCfgList()
    {
        return &_otherMotorCfgList;
    }

    QMap< int, WarnConfigItem >* getSocketWarnCfgList()
    {
        return &_socketWarnCfgList;
    }

    QMap< int, WarnConfigItem >* getFengtouWarnCfgList()
    {
        return &_fengtouWarnCfgList;
    }

    QMap< int, FastResetConfigItem >* getFastResetCfgList()
    {
        return &_fastResetCfgList;
    }

    QMap< int, FengtouCalCfgItem >* getFengtouCalCfgList()
    {
        return &_fengtouCalCfgList;
    }

    QMap< int, QString >* getZeroSensorList()
    {
        return &_zeroSensorList;
    }

    QMap< int, ServoCfgItem >* getServoList()
    {
        return &_servoList;
    }

    // 新增EEye相关访问方法
    EEyeConfigStruct* getEEyeCfg()
    {
        return &_eeyeCfg;
    }

private:
    QMap< int, MachineFlieConfigItem >  _basicCfgList;
    QMap< int, MachineFlieConfigItem >  _needleCfgList;
    QMap< int, MachineFlieConfigItem >  _peripheralCfgList;
    QMap< int, MachineFlieConfigItem >  _positionCfgList;
    QMap< int, MachineFlieConfigItem >  _position2CfgList;
    QMap< int, MachineFlieConfigItem >  _userCfgList;
    QMap< int, MachineFlieConfigItem >  _fengtouCfgList;
    QMap< int, MachineFlieConfigItem >  _otherCfgList;
    QMap< int, StepperMotorConfigItem > _socketMotorCfgList;
    QMap< int, StepperMotorConfigItem > _fengtouMotorCfgList;
    QMap< int, StepperMotorConfigItem > _otherMotorCfgList;
    QMap< int, WarnConfigItem >         _socketWarnCfgList;
    QMap< int, WarnConfigItem >         _fengtouWarnCfgList;
    QMap< int, FastResetConfigItem >    _fastResetCfgList;
    QMap< int, FengtouCalCfgItem >      _fengtouCalCfgList;
    QMap< int, QString >                _zeroSensorList;
    QMap< int, ServoCfgItem >           _servoList;
    // 新增EEye配置项
    EEyeConfigStruct _eeyeCfg;
};

#endif  // READMACHINEFILECONFIG_H
