﻿#ifndef PARASETFORM_H
#define PARASETFORM_H

#include "Common/defines.h"
#include "Common/parameters.h"
#include "CommonWidget/comboform.h"
#include "CommonWidget/floatinputform.h"
#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "Communicate/Communication.h"
#include "Config/readCfg.h"
#include "Config/readResetConfig.h"
#include "Config/readmachinefileconfig.h"
#include <QLabel>
#include <QListWidget>
#include <QMessageBox>
#include <QTableWidget>
#include <QTimer>
#include <QWidget>

namespace Ui
{
class ParaSetForm;
}

class ParaSetForm : public QWidget
{
    Q_OBJECT

public:
    explicit ParaSetForm( QWidget* parent = nullptr, Communication* comm = nullptr, MainWidgetData* mainData = nullptr );
    ~ParaSetForm();
    void ShowMenu( int id );

private:
    Ui::ParaSetForm*       ui;
    Communication*         comm;
    MainWidgetData*        mainData              = nullptr;
    // 使用 mainData 中的指针
    ReadCfg*               readCfg;
    ReadMachineFileConfig* readMachineFileConfig;
    ReadResetConfig*       readResetCfg;
    ReadLinkConfig*        readLinkCfg;

    // 参数发送定时器
    QTimer paramSendTimer;
    bool   paramSendSuccess = false;
    int    retryCountParam  = 0;

    ComboForm*                                comboFrm                      = nullptr;
    FloatInputForm*                           floatInputFrm                 = nullptr;
    NumberInputForm*                          numberInputFrm                = nullptr;
    MyLineEdit*                               currentSelectedMyLineEdit     = nullptr;
    bool                                      isMachineParasetPageInit      = false;
    bool                                      isXiangjinParasetPageInit     = false;
    bool                                      isStepperMotorParasetPageInit = false;
    bool                                      isFastResetParasetPageInit    = false;
    bool                                      isFengtouCalParasetPageInit   = false;
    int                                       currentActionIndex            = 0;  // 保存链条编辑页点击进入圈控制时的编号
    int                                       currentActionPage             = 0;  // 当前动作栏的页码
    ReadMachineFileConfig::FengtouCalCfgItem* currentFengtouCalItem         = nullptr;
    ReadMachineFileConfig::FengtouStepStruct* currentFengtouStepStruct      = nullptr;  // 保存当前正在编辑的缝头小步骤，便于修改后保存
    int                                       currentResetInstructionIndex  = 0;        // 用于保存当前正在编辑的快速复位指令的索引

    void initMachineParasetPage();
    void initBasicParasetTab();
    void onBasicParasetEditClicked();
    void onBasicFloatInputFormFinished( float value );

    void initNeedleParasetTab();
    void onNeedleParasetEditClicked();
    void onNeedleFloatInputFormFinished( float value );

    void initPeripheralParasetTab();
    void onPeripheralParasetEditClicked();
    void onPeripheralFloatInputFormFinished( float value );

    void initPositionParasetTab();
    void onPositionParasetEditClicked();
    void onPositionFloatInputFormFinished( float value );

    void initPosition2ParasetTab();
    void onPosition2ParasetEditClicked();
    void onPosition2FloatInputFormFinished( float value );

    void initFengtouParasetTab();
    void onFengtouParasetEditClicked();
    void onFengtouFloatInputFormFinished( float value );

    void initStepperMotorParasetPage();

    void initSocketMotorParasetTab();
    void onSocketMotorParasetEditClicked();
    void onSocketMotorFloatInputFormFinished( float value );

    void initFengtouMotorParasetTab();
    void onFengtouMotorParasetEditClicked();
    void onFengtouMotorFloatInputFormFinished( float value );

    void initOtherMotorParasetTab();
    void onOtherMotorParasetEditClicked();
    void onOtherMotorFloatInputFormFinished( float value );

    // 快速复位页面相关变量
    QVector< QLabel* >     stepLabels;
    QVector< MyLineEdit* > posEdits;
    QVector< MyLineEdit* > typeEdits;
    QVector< MyLineEdit* > idEdits;
    QVector< MyLineEdit* > valueEdits;

    void initFastResetParasetPage();
    void initResetInstructions();
    void showResetCircle( int resetTypeIndex );
    void showResetInstructions( int circleIndex );
    void updateResetActionValue( quint8 valueType, int newValue );

    void initFengtouCalParasetPage();
    void ShowFengtouStep( int index );
    void ShowFengtouMsg( int step_index );
    void ShowFengtouMotor();
    void ShowFengtouValve();

    // 发送机器参数
    void sendMachineParam();

signals:
    void ParasetFormToMainWinToShowSignal();   // 向主界面发送的显示主界面信号
    void onSwitchPageButtonClicked( int id );  // 切换Page的按钮按下

private slots:
    void onParasetHomeBtnClicked();    // 主菜单键按下槽函数
    void switchPage( int pageIndex );  // 切换不同的Page
    void ShowFengtouParam( QListWidgetItem* item );
    void onMotorNameClicked();       // 点击出现修改点击Pattern窗口
    void onMotorPatternClicked();    // 点击出现修改点击Pattern窗口
    void onFengtouVSTypeClicked();   // 点击出现修改气阀或信号类型窗口
    void onFengtouVSNameClicked();   // 点击出现修改气阀或信号名称窗口
    void onFengtouVSStateClicked();  // 点击出现修改气阀或信号状态窗口
    void onBtnSaveClicked();         // Clicked Save Button

    // 快速复位相关槽函数
    void onPosEditMouseRelease();
    void onTypeEditMouseRelease();
    void onIdEditMouseRelease();
    void onValueEditMouseRelease();
    void onCircleAddBtnClicked();     // 添加圈
    void onCircleDeleteBtnClicked();  // 删除圈
    void onInsAddBtnClicked();        // 添加指令
    void onInsDeleteBtnClicked();     // 删除指令
    void onInsClearBtnClicked();      // 清空本圈指令
};

#endif  // PARASETFORM_H
