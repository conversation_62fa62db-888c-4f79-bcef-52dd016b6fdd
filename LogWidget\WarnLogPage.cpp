#include "LogForm.h"
#include "ui_LogForm.h"

void LogForm::initWarnLogPage()
{
    if ( isWarnLogPageInited == false )
    {
        connect( ui->pbtn_listSame, &QPushButton::clicked, this, &LogForm::markSameTypeWarnLog );
        isWarnLogPageInited = true;
    }
    refreshWarnLog();
}

void LogForm::refreshWarnLog()
{
    // clear
    ui->tableView_warnLog->clear();

    ui->tableView_warnLog->setColumnCount( 3 );
    QStringList headerList;
    headerList << QString::fromUtf8( "时间" ) << QString::fromUtf8( "类型" ) << QString::fromUtf8( "内容" );
    ui->tableView_warnLog->setHorizontalHeaderLabels( headerList );
    ui->tableView_warnLog->verticalHeader()->setVisible( true );
    ui->tableView_warnLog->setSelectionBehavior( QAbstractItemView::SelectRows );
    ui->tableView_warnLog->setSelectionMode( QAbstractItemView::SingleSelection );

    // 水平方向标签拓展剩下的窗口部分
    ui->tableView_warnLog->horizontalHeader()->setStretchLastSection( true );
    ui->tableView_warnLog->setColumnWidth( 0, 150 );
    ui->tableView_warnLog->setColumnWidth( 1, 250 );

    int totalElements = mainData->warnLogList.size();
    ui->tableView_warnLog->setRowCount( totalElements );

    for ( int i = 0; i < totalElements; i++ )
    {
        WarnLogItem       item  = mainData->warnLogList[ i ];
        QTableWidgetItem* item1 = new QTableWidgetItem( item.time );
        QTableWidgetItem* item2 = new QTableWidgetItem( mainData->readWarnConfig->getList()->value( item.type ).name );
        QTableWidgetItem* item3 = new QTableWidgetItem( mainData->readWarnConfig->getList()->value( item.type ).items.value( item.id ).name );
        this->ui->tableView_warnLog->setItem( i, 0, item1 );
        this->ui->tableView_warnLog->setItem( i, 1, item2 );
        this->ui->tableView_warnLog->setItem( i, 2, item3 );
    }
}

void LogForm::markSameTypeWarnLog()
{
    //    warnLogList.pop_back();
    //    refreshWarnLog();

    // 没设置选取相同类型
    if ( !markSameWarnLog )
    {
        if ( ui->tableView_warnLog->selectedItems().isEmpty() )
        {
            QMessageBox::warning( nullptr, "提示", "请选择一项！" );
            return;
        }
        ui->pbtn_listSame->setText( "取消选择" );
        int     selectedRow  = ui->tableView_warnLog->selectedRanges().first().topRow();
        QString selectedType = ui->tableView_warnLog->item( selectedRow, 1 )->text();  // 获取选中行的 类型 列内容

        for ( int row = 0; row < ui->tableView_warnLog->rowCount(); ++row )
        {
            QString currentType = ui->tableView_warnLog->item( row, 1 )->text();  // 获取当前行的 类型 列内容

            if ( currentType == selectedType )
            {
                // 设置背景色为红色
                ui->tableView_warnLog->item( row, 0 )->setBackgroundColor( Qt::red );
                ui->tableView_warnLog->item( row, 1 )->setBackgroundColor( Qt::red );
                ui->tableView_warnLog->item( row, 2 )->setBackgroundColor( Qt::red );
                // 设置字体颜色为白色
                ui->tableView_warnLog->item( row, 0 )->setTextColor( Qt::white );
                ui->tableView_warnLog->item( row, 1 )->setTextColor( Qt::white );
                ui->tableView_warnLog->item( row, 2 )->setTextColor( Qt::white );
            }
        }
        markSameWarnLog = true;
    }
    else
    {
        ui->pbtn_listSame->setText( "选择相同" );
        for ( int row = 0; row < ui->tableView_warnLog->rowCount(); ++row )
        {
            // 设置背景色为红色
            ui->tableView_warnLog->item( row, 0 )->setBackgroundColor( Qt::white );
            ui->tableView_warnLog->item( row, 1 )->setBackgroundColor( Qt::white );
            ui->tableView_warnLog->item( row, 2 )->setBackgroundColor( Qt::white );
            // 设置字体颜色为白色
            ui->tableView_warnLog->item( row, 0 )->setTextColor( Qt::black );
            ui->tableView_warnLog->item( row, 1 )->setTextColor( Qt::black );
            ui->tableView_warnLog->item( row, 2 )->setTextColor( Qt::black );
        }
        markSameWarnLog = false;
    }
}
