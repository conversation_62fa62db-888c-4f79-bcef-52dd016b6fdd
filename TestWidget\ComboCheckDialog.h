#ifndef COMBOCHECKDIALOG_H
#define COMBOCHECKDIALOG_H

#include "CommonWidget/mylineedit.h"
#include "Communicate/Communication.h"
#include "Config/readtestconfig.h"
#include <QDialog>
#include <QHBoxLayout>
#include <QVBoxLayout>

namespace Ui
{
class ComboCheckDialog;
}

class ComboCheckDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ComboCheckDialog( QWidget* parent = nullptr, ReadTestConfig* testCfg = nullptr, Communication* comm = nullptr );
    ~ComboCheckDialog();

private:
    Ui::ComboCheckDialog* ui;
    Communication*        comm    = nullptr;
    ReadTestConfig*       testCfg = nullptr;

    void showCombos();

private slots:
    void switchCombos();
};

#endif  // COMBOCHECKDIALOG_H
