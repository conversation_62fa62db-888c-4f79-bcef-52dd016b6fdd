#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>
#include <QLabel>

// 初始化速度框架
void CraftParamForm::initSpeedFrame()
{
    // 清空之前的控件
    if ( !speedLabels.isEmpty() )
    {
        for ( auto label : speedLabels )
        {
            delete label;
        }
        speedLabels.clear();
    }

    if ( !countLabels.isEmpty() )
    {
        for ( auto label : countLabels )
        {
            delete label;
        }
        countLabels.clear();
    }

    if ( !speedNewEdits.isEmpty() )
    {
        for ( auto edit : speedNewEdits )
        {
            delete edit;
        }
        speedNewEdits.clear();
    }

    if ( !indexLabels.isEmpty() )
    {
        for ( auto label : indexLabels )
        {
            delete label;
        }
        indexLabels.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout( ui->speedFrame );

    // 创建标题行
    QLabel* indexHeader    = new QLabel( "序号", ui->speedFrame );
    QLabel* speedHeader    = new QLabel( "速度", ui->speedFrame );
    QLabel* countHeader    = new QLabel( "出现次数", ui->speedFrame );
    QLabel* speedNewHeader = new QLabel( "N速度", ui->speedFrame );

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet( headerStyle );
    speedHeader->setStyleSheet( headerStyle );
    countHeader->setStyleSheet( headerStyle );
    speedNewHeader->setStyleSheet( headerStyle );
    indexHeader->setAlignment( Qt::AlignCenter );
    speedHeader->setAlignment( Qt::AlignCenter );
    countHeader->setAlignment( Qt::AlignCenter );
    speedNewHeader->setAlignment( Qt::AlignCenter );

    // 添加标题到布局
    layout->addWidget( indexHeader, 0, 0 );
    layout->addWidget( speedHeader, 0, 1 );
    layout->addWidget( countHeader, 0, 2 );
    layout->addWidget( speedNewHeader, 0, 3 );

    // 为每一行创建控件
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        // 序号标签
        QLabel* indexLabel = new QLabel( QString::number( i + 1 ), ui->speedFrame );
        indexLabel->setAlignment( Qt::AlignCenter );
        indexLabels.append( indexLabel );
        layout->addWidget( indexLabel, i + 1, 0 );

        // 速度标签
        QLabel* speedLabel = new QLabel( "", ui->speedFrame );
        speedLabel->setAlignment( Qt::AlignCenter );
        speedLabels.append( speedLabel );
        layout->addWidget( speedLabel, i + 1, 1 );

        // 次数标签
        QLabel* countLabel = new QLabel( "", ui->speedFrame );
        countLabel->setAlignment( Qt::AlignCenter );
        countLabels.append( countLabel );
        layout->addWidget( countLabel, i + 1, 2 );

        // 新速度编辑框
        MyLineEdit* speedNewEdit = new MyLineEdit( ui->speedFrame );
        speedNewEdit->setAlignment( Qt::AlignCenter );
        speedNewEdit->setReadOnly( true );      // 初始设为只读
        speedNewEdit->setProperty( "row", i );  // 存储行索引
        connect( speedNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSpeedPage * ITEMS_PER_PAGE + i;

            if ( dataIndex < craftParams.speedParam.size() )
            {
                // 清空之前的内存
                if ( this->numberInputForm != nullptr )
                {
                    delete this->numberInputForm;
                    this->numberInputForm = nullptr;
                }

                // 创建数字输入表单
                this->numberInputForm = new NumberInputForm( nullptr, "请输入速度", 10, 400 );

                // 存储当前编辑的行
                tableEditRowIndex = dataIndex;
                tableIndex        = 1;  // 速度表格

                // 连接完成信号
                connect( this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onSpeedEditFinished );

                // 显示表单
                this->numberInputForm->show();
            }
        } );
        speedNewEdits.append( speedNewEdit );
        layout->addWidget( speedNewEdit, i + 1, 3 );
    }

    // 设置布局属性
    layout->setColumnStretch( 0, 1 );
    layout->setColumnStretch( 1, 2 );
    layout->setColumnStretch( 2, 2 );
    layout->setColumnStretch( 3, 2 );
    layout->setSpacing( 10 );
    layout->setContentsMargins( 10, 10, 10, 10 );

    // 应用布局
    ui->speedFrame->setLayout( layout );

    // 连接翻页按钮
    connect( ui->pbtn_prev, &QPushButton::clicked, this, &CraftParamForm::onPrevPageClicked );
    connect( ui->pbtn_next, &QPushButton::clicked, this, &CraftParamForm::onNextPageClicked );
}


// 速度编辑完成
void CraftParamForm::onSpeedEditFinished(QString value)
{
    if (tableIndex == 1 && tableEditRowIndex >= 0 && tableEditRowIndex < craftParams.speedParam.size()) {
        // 更新数据模型
        craftParams.speedParam[tableEditRowIndex]->speed_new = value.toInt();

        // 更新界面显示
        updateSpeedPage();
    }
}

void CraftParamForm::onSpeedIncClicked()
{
    // 所有的speed_new都加5
    for ( const auto& param : craftParams.speedParam )
    {
        param->speed_new += 5;
    }
    // 更新页面显示
    updateSpeedPage();
}

void CraftParamForm::onSpeedDecClicked()
{
    // 所有的speed_new都减5
    for ( const auto& param : craftParams.speedParam )
    {
        param->speed_new -= 5;
    }
    // 更新页面显示
    updateSpeedPage();
}

