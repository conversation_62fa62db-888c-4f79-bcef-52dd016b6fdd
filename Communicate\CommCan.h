#ifndef COMMCAN_H
#define COMMCAN_H

#include <QByteArray>
#include <QCanBus>
#include <QCanBusDevice>
#include <QCanBusFrame>
#include <QDebug>
#include <QElapsedTimer>
#include <QHash>
#include <QMap>
#include <QMutex>
#include <QObject>
#include <QTimer>
#include <QVector>

// 定义多帧数据的协议标识
#define MULTI_FRAME_START 0x80     // 多帧数据的起始帧标识
#define MULTI_FRAME_CONTINUE 0x40  // 多帧数据的中间帧标识
#define MULTI_FRAME_END 0x20       // 多帧数据的结束帧标识
#define MULTI_FRAME_SINGLE 0x10    // 单帧数据标识

class CommCan : public QObject
{
    Q_OBJECT

public:
    explicit CommCan( QObject* parent = nullptr );
    ~CommCan();

    // 初始化CAN设备
    bool initCan( const QString& interface = "socketcan", const QString& device = "can0", int bitRate = 500000 );

    // 关闭CAN设备
    void closeCan();

    // 发送CAN帧
    bool sendFrame( quint32 frameId, const QByteArray& payload, bool isExtended = false );

    // 发送大数据帧 (支持超过8字节的数据)
    bool sendLargeFrame( quint32 frameId, const QByteArray& payload, bool isExtended = false );

    // 发送远程请求帧
    bool sendRemoteRequestFrame( quint32 frameId, bool isExtended = false );

    // 注册帧ID的回调处理
    void registerFrameHandler( quint32 frameId, std::function< void( const QCanBusFrame& ) > handler );

    // 注册大数据帧ID的回调处理
    void registerLargeFrameHandler( quint32 frameId, std::function< void( const QByteArray& ) > handler );

    // 取消注册帧ID的回调处理
    void unregisterFrameHandler( quint32 frameId );

    // 取消注册大数据帧ID的回调处理
    void unregisterLargeFrameHandler( quint32 frameId );

    // 检查CAN设备是否已连接
    bool isConnected() const;

    // 获取设备错误信息
    QString getLastError() const;

    // 设置CAN总线位速率
    bool setBitRate( int bitRate );

    void processMultiFrameData( quint32 frameId, const QByteArray& payload );

signals:
    // 接收到CAN帧信号
    void frameReceived( const QCanBusFrame& frame );

    // 接收到大数据帧信号
    void largeFrameReceived( quint32 frameId, const QByteArray& payload );

    // 发送CAN帧信号
    void frameSent( const QCanBusFrame& frame );

    // 设备错误信号
    void errorOccurred( const QString& error );

    // 设备状态变化信号
    void stateChanged( QCanBusDevice::CanBusDeviceState state );

private slots:
    // 处理接收到的帧
    void processReceivedFrames();

    // 处理设备错误
    void handleDeviceError( QCanBusDevice::CanBusError error );

    // 处理设备状态变化
    void handleDeviceStateChange( QCanBusDevice::CanBusDeviceState state );

private:
    QCanBusDevice*                                                m_canDevice;
    QString                                                       m_lastError;
    QMutex                                                        m_mutex;
    QMap< quint32, std::function< void( const QCanBusFrame& ) > > m_frameHandlers;
    QMap< quint32, std::function< void( const QByteArray& ) > >   m_largeFrameHandlers;
    bool                                                          m_isConnected;
    QString                                                       m_interface;
    QString                                                       m_deviceName;
    int                                                           m_bitRate;

    // 配置CAN设备
    bool configureDevice();

    // 用于存储多帧数据的临时缓冲区
    struct MultiFrameBuffer
    {
        QByteArray      data;
        quint8          expectedFrames;
        quint8          receivedFrames;
        QVector< bool > receivedFrameFlags;
        QElapsedTimer   timer;
    };

    QHash< quint32, MultiFrameBuffer > m_multiFrameBuffers;

    // 清理超时的多帧缓冲区
    void cleanupTimeoutBuffers();

    // 超时时间 (毫秒)
    static const int MULTI_FRAME_TIMEOUT = 1000;
};

#endif  // COMMCAN_H
