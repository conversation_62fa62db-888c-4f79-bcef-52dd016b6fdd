﻿#include "needleitemview.h"
#include <QDebug>
#include <QPainter>
#include <QPen>

NeedleItemView::NeedleItemView( bool state, QWidget* parent ) : QLabel( parent )
{
    //此处值为默认初始值
    w0     = this->width();
    h0     = this->height();
    _state = state;
}

NeedleItemView::~NeedleItemView() {}

void NeedleItemView::paintEvent( QPaintEvent* event )
{
    //此处值为执行主程序中后修正过的值
    int w = this->width();
    int h = this->height();

    //    qDebug() << "NeedleView1" << w << h;
    QPainter painter( this );
    painter.setPen( QPen( Qt::red, 19 ) );
    //    painter.drawLine( 0, 0, w, h );
    //    painter.drawPie( QRect( 10, 10, w - 10, h - 10 ), 0, 315 * 16 );

    // x起始位置为2+9=11
    // y值是反复测试过的，不改变QScrollArea控件大小，不要变
    if ( !_state )
    {
        painter.drawLine( 11, 0 + h - 12, 11, 0 + h / 2 + 6 );
    }
    else
    {
        painter.drawLine( 11, 0 + h - 12, 11, 10 );
    }
}
