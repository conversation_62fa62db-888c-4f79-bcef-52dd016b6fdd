#include "SelfCheckForm.h"
#include "ui_SelfCheckForm.h"
#include <QApplication>
#include <QScreen>
#include <QLabel>

SelfCheckForm::SelfCheckForm( QWidget* parent ) : QWidget( parent ), ui( new Ui::SelfCheckForm )
{
    ui->setupUi( this );

    // 设置窗口标题和属性
    setWindowTitle( "机器自检" );
    setWindowFlags( Qt::Dialog | Qt::WindowCloseButtonHint );

    // 窗口居中显示
    QScreen* screen         = QGuiApplication::primaryScreen();
    QRect    screenGeometry = screen->geometry();
    int      x              = ( screenGeometry.width() - width() ) / 2;
    int      y              = ( screenGeometry.height() - height() ) / 2;
    move( x, y );

    // 连接退出按钮信号
    connect( ui->pBtn_quit, &QPushButton::clicked, this, &SelfCheckForm::close );

    // 初始化清空所有结果
    clearAllResults();
}

SelfCheckForm::~SelfCheckForm()
{
    delete ui;
}

// 获取指定步骤的结果标签
QLabel* SelfCheckForm::getStepResultLabel(int stepIndex)
{
    switch (stepIndex)
    {
        case 0: return ui->lbl_res_step1;
        case 1: return ui->lbl_res_step2;
        case 2: return ui->lbl_res_step3;
        case 3: return ui->lbl_res_step4;
        case 4: return ui->lbl_res_step5;
        case 5: return ui->lbl_res_step6;
        case 6: return ui->lbl_res_step7;
        case 7: return ui->lbl_res_step8;
        case 8: return ui->lbl_res_step9;
        case 9: return ui->lbl_res_step10;
        case 10: return ui->lbl_res_step11;
        case 11: return ui->lbl_res_step12;
        case 12: return ui->lbl_res_step13;
        case 13: return ui->lbl_res_step14;
        case 14: return ui->lbl_res_step15;
        case 15: return ui->lbl_res_step16;
        default: return nullptr;
    }
}

// 更新步骤的自检结果
void SelfCheckForm::updateStepResult( quint16 step, quint16 result )
{
    // 遍历前15个步骤
    for (int i = 0; i < 15; i++)
    {
        // 检查step的第i位是否为1
        if (step & (1 << i))
        {
            // 获取对应步骤的结果标签
            QLabel* resultLabel = getStepResultLabel(i);
            if (!resultLabel) continue;
            
            // 检查result的第i位，确定结果
            bool stepResult = (result & (1 << i)) != 0;
            
            // 设置结果文本和样式
            if (stepResult)
            {
                resultLabel->setText("OK");
                resultLabel->setStyleSheet("color: #2ECC71; font-weight: bold; background-color: rgba(46, 204, 113, 0.2); border-radius: 3px; padding: 2px 8px;");
            }
            else
            {
                resultLabel->setText("ERR");
                resultLabel->setStyleSheet("color: #E74C3C; font-weight: bold; background-color: rgba(231, 76, 60, 0.2); border-radius: 3px; padding: 2px 8px;");
            }
        }
    }
}

// 更新键盘检测结果
void SelfCheckForm::updateKeyBoardResult(bool result)
{
    QLabel* resultLabel = getStepResultLabel(15); // 获取第16个步骤的标签
    if (!resultLabel) return;
    
    // 根据结果设置文本和样式
    if (result)
    {
        resultLabel->setText("OK");
        resultLabel->setStyleSheet("color: #2ECC71; font-weight: bold; background-color: rgba(46, 204, 113, 0.2); border-radius: 3px; padding: 2px 8px;");
    }
    else
    {
        resultLabel->setText("ERR");
        resultLabel->setStyleSheet("color: #E74C3C; font-weight: bold; background-color: rgba(231, 76, 60, 0.2); border-radius: 3px; padding: 2px 8px;");
    }
}

// 清空所有自检结果
void SelfCheckForm::clearAllResults()
{
    // 清空所有16个步骤的结果
    for (int i = 0; i < 16; i++)
    {
        QLabel* resultLabel = getStepResultLabel(i);
        if (resultLabel)
        {
            resultLabel->setText("");
            resultLabel->setStyleSheet("");
        }
    }

    // 清空气阀结果
    valveResults.clear();
}
