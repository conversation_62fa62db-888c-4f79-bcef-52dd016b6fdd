﻿#ifndef MYLINEEDIT_H
#define MYLINEEDIT_H
#include <QLineEdit>

class MyLineEdit : public QLineEdit
{
    Q_OBJECT
public:
    explicit MyLineEdit( QWidget* parent = 0 );
    //    void focusInEvent(QFocusEvent *event) override;    //覆盖这两个函数
    //    void focusOutEvent( QFocusEvent* event ) override;
    void mousePressEvent( QMouseEvent* ) override;
    void mouseReleaseEvent( QMouseEvent* ) override;
    void mouseDoubleClickEvent( QMouseEvent* ) override;
signals:
    //   void GetFocus();                   //获得焦点信号
    //    void LostFocus();  //失去焦点信号
    void mousePress();
    void mouseRelease();
    void mouseDoubleClick();
};

#endif  // MYLINEEDIT_H
