#include "RunningPowerDialog.h"
#include "ui_RunningPowerDialog.h"

RunningPowerDialog::RunningPowerDialog( QWidget* parent ) : QWidget( parent ), ui( new Ui::RunningPowerDialog )
{
    ui->setupUi( this );
    setWindowTitle( "功率相关" );
    setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
    setWindowFlags(
        /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );
}

RunningPowerDialog::~RunningPowerDialog()
{
    delete ui;
}

void RunningPowerDialog::updatePowerInfo( const upMain& mainInfo )
{
    // Display motor current
    ui->label_motor_curent->setText( QString::number( mainInfo.motor_curent / 10.0f, 'f', 1 ) );

    // Display auxiliary current
    ui->label_fuzhu_current->setText( QString::number( mainInfo.fuzhu_current / 10.0f, 'f', 1 ) );

    // Display total power
    ui->label_sumpower->setText( QString::number( mainInfo.sumpower ) );

    // Display power factor
    ui->label_power_factor->setText( QString::number( mainInfo.power_factor / 100.0f, 'f', 2 ) );

    // Split vot_phase into phase (high 2 bits) and voltage (low 14 bits)
    quint16 voltage = mainInfo.vot_phase & 0x3FFF;          // Get low 14 bits
    quint8  phase   = ( mainInfo.vot_phase >> 14 ) & 0x03;  // Get high 2 bits

    // Display phase
    ui->label_phase->setText( QString::number( phase ) );

    // Display voltage
    ui->label_volatage->setText( QString::number( voltage ) );

    // Display real speed
    ui->label_realSpeed->setText( QString::number( mainInfo.realSpeed ) );
}

void RunningPowerDialog::on_pbtn_Close_clicked()
{
    this->close();
}
