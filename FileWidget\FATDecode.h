#pragma once

#include <QString>

class FATDecode
{
public:
  struct FatHead
  {
    qint32 Datalen; // 长度-0x400 真实数据长度
    qint32 tmp1;
    qint32 tmp2;
    qint32 HeadDB;   // 头部数据截至地址
    qint32 FirstDB;  // 第一块数据截至地址
    qint32 SecondDB; // 点阵区块
    qint32 ThirdDB;  // 动作代码区域
  };
  /*点阵区块偏移
  +0 宽度
  +4 高度

  +0x400+宽度*4 真实点阵起始位置 //宽度*4这一段不清楚作用 应该是空针或者标记什么的

  点阵均为4字节一组
  */

  FATDecode();
  ~FATDecode();

  void Decode(char *data, FatHead *fhead, int filelen);
  void Test(QString filePath);

private:
  void GetKey(char *data, char *key, int keyset);
  void Decoder(char *data, char *key, int p, int datalen);
};
