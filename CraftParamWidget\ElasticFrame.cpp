#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>

// 初始化橡筋电机1框架
void CraftParamForm::initElastic1Frame()
{
    // 清空之前的控件列表
    if (!elastic1IndexLabels.isEmpty()) {
        for (auto label : elastic1IndexLabels) {
            delete label;
        }
        elastic1IndexLabels.clear();
    }
    if (!elastic1NameLabels.isEmpty()) {
        for (auto label : elastic1NameLabels) {
            delete label;
        }
        elastic1NameLabels.clear();
    }
    if (!elastic1StepLabels.isEmpty()) {
        for (auto label : elastic1StepLabels) {
            delete label;
        }
        elastic1StepLabels.clear();
    }
    if (!elastic1ProhibitLabels.isEmpty()) {
        for (auto label : elastic1ProhibitLabels) {
            delete label;
        }
        elastic1ProhibitLabels.clear();
    }
    if (!elastic1StartLabels.isEmpty()) {
        for (auto label : elastic1StartLabels) {
            delete label;
        }
        elastic1StartLabels.clear();
    }
    if (!elastic1EndLabels.isEmpty()) {
        for (auto label : elastic1EndLabels) {
            delete label;
        }
        elastic1EndLabels.clear();
    }
    if (!elastic1StartNewEdits.isEmpty()) {
        for (auto edit : elastic1StartNewEdits) {
            delete edit;
        }
        elastic1StartNewEdits.clear();
    }
    if (!elastic1EndNewEdits.isEmpty()) {
        for (auto edit : elastic1EndNewEdits) {
            delete edit;
        }
        elastic1EndNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout(ui->elastic1Frame);

    // 创建标题行
    QLabel* indexHeader = new QLabel("序号", ui->elastic1Frame);
    QLabel* nameHeader = new QLabel("步骤名称", ui->elastic1Frame);
    QLabel* stepHeader = new QLabel("步", ui->elastic1Frame);
    QLabel* prohibitHeader = new QLabel("P", ui->elastic1Frame);
    QLabel* startHeader = new QLabel("起始值", ui->elastic1Frame);
    QLabel* endHeader = new QLabel("结束值", ui->elastic1Frame);
    QLabel* startNewHeader = new QLabel("N起始值", ui->elastic1Frame);
    QLabel* endNewHeader = new QLabel("N结束值", ui->elastic1Frame);

    // 设置标题对齐方式
    indexHeader->setAlignment(Qt::AlignCenter);
    nameHeader->setAlignment(Qt::AlignCenter);
    stepHeader->setAlignment(Qt::AlignCenter);
    prohibitHeader->setAlignment(Qt::AlignCenter);
    startHeader->setAlignment(Qt::AlignCenter);
    endHeader->setAlignment(Qt::AlignCenter);
    startNewHeader->setAlignment(Qt::AlignCenter);
    endNewHeader->setAlignment(Qt::AlignCenter);

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet(headerStyle);
    nameHeader->setStyleSheet(headerStyle);
    stepHeader->setStyleSheet(headerStyle);
    prohibitHeader->setStyleSheet(headerStyle);
    startHeader->setStyleSheet(headerStyle);
    endHeader->setStyleSheet(headerStyle);
    startNewHeader->setStyleSheet(headerStyle);
    endNewHeader->setStyleSheet(headerStyle);

    // 添加标题到布局
    layout->addWidget(indexHeader, 0, 0);
    layout->addWidget(nameHeader, 0, 1);
    layout->addWidget(stepHeader, 0, 2);
    layout->addWidget(prohibitHeader, 0, 3);
    layout->addWidget(startHeader, 0, 4);
    layout->addWidget(endHeader, 0, 5);
    layout->addWidget(startNewHeader, 0, 6);
    layout->addWidget(endNewHeader, 0, 7);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->elastic1Frame);
        indexLabel->setAlignment(Qt::AlignCenter);
        elastic1IndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);

        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->elastic1Frame);
        nameLabel->setAlignment(Qt::AlignCenter);
        elastic1NameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);

        // 步标签
        QLabel* stepLabel = new QLabel("", ui->elastic1Frame);
        stepLabel->setAlignment(Qt::AlignCenter);
        elastic1StepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);

        // 禁止修改标签
        QLabel* prohibitLabel = new QLabel("", ui->elastic1Frame);
        prohibitLabel->setAlignment(Qt::AlignCenter);
        elastic1ProhibitLabels.append(prohibitLabel);
        layout->addWidget(prohibitLabel, i + 1, 3);

        // 起始值标签
        QLabel* startLabel = new QLabel("", ui->elastic1Frame);
        startLabel->setAlignment(Qt::AlignCenter);
        elastic1StartLabels.append(startLabel);
        layout->addWidget(startLabel, i + 1, 4);

        // 结束值标签
        QLabel* endLabel = new QLabel("", ui->elastic1Frame);
        endLabel->setAlignment(Qt::AlignCenter);
        elastic1EndLabels.append(endLabel);
        layout->addWidget(endLabel, i + 1, 5);

        // 新起始值编辑框
        MyLineEdit* startNewEdit = new MyLineEdit(ui->elastic1Frame);
        startNewEdit->setAlignment(Qt::AlignCenter);
        startNewEdit->setReadOnly(true); // 初始设为只读
        startNewEdit->setProperty("row", i); // 存储行索引
        startNewEdit->setProperty("column", 6); // 存储列索引
        connect(startNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentElastic1Page * ITEMS_PER_PAGE + i;

            if (dataIndex < craftParams.elasticMotorParam.size() && !craftParams.elasticMotorParam[dataIndex]->prohibitsValueChange1) {
                tableIndex = 3; // 橡筋电机1表格
                tableEditRowIndex = i;
                tableEditColIndex = 6; // 新起始值列

                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "橡筋1新起始值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        elastic1StartNewEdits.append(startNewEdit);
        layout->addWidget(startNewEdit, i + 1, 6);

        // 新结束值编辑框
        MyLineEdit* endNewEdit = new MyLineEdit(ui->elastic1Frame);
        endNewEdit->setAlignment(Qt::AlignCenter);
        endNewEdit->setReadOnly(true); // 初始设为只读
        endNewEdit->setProperty("row", i); // 存储行索引
        endNewEdit->setProperty("column", 7); // 存储列索引
        connect(endNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentElastic1Page * ITEMS_PER_PAGE + i;

            if (dataIndex < craftParams.elasticMotorParam.size() && !craftParams.elasticMotorParam[dataIndex]->prohibitsValueChange1) {
                tableIndex = 3; // 橡筋电机1表格
                tableEditRowIndex = i;
                tableEditColIndex = 7; // 新结束值列

                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "橡筋1新结束值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        elastic1EndNewEdits.append(endNewEdit);
        layout->addWidget(endNewEdit, i + 1, 7);
    }

    // 设置行高
    for (int i = 0; i < ITEMS_PER_PAGE + 1; i++) {
        layout->setRowMinimumHeight(i, 30);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 1);  // P
    layout->setColumnStretch(4, 2);  // 起始值
    layout->setColumnStretch(5, 2);  // 结束值
    layout->setColumnStretch(6, 2);  // N起始值
    layout->setColumnStretch(7, 2);  // N结束值
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->elastic1Frame->setLayout(layout);
}

// 更新橡筋电机1页面
void CraftParamForm::updateElastic1Page()
{
    // 计算总页数
    totalElastic1Pages = (craftParams.elasticMotorParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;

    // 确保当前页在有效范围内
    if (currentElastic1Page >= totalElastic1Pages && totalElastic1Pages > 0) {
        currentElastic1Page = totalElastic1Pages - 1;
    }

    // 计算当前页的起始索引
    int startIndex = currentElastic1Page * ITEMS_PER_PAGE;

    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.elasticMotorParam.size()) {
            // 有数据，显示
            auto param = craftParams.elasticMotorParam[dataIndex];

            elastic1IndexLabels[i]->setText(QString::number(dataIndex + 1));
            elastic1NameLabels[i]->setText(QString::fromLatin1(param->blockName));
            elastic1StepLabels[i]->setText(QString::number(param->stepStart) + "~" + QString::number(param->stepEnd));
            elastic1ProhibitLabels[i]->setText(param->prohibitsValueChange1 ? "N" : "Y");
            elastic1StartLabels[i]->setText(QString::number(param->elasticStart1[currentSize - 1]));
            elastic1EndLabels[i]->setText(QString::number(param->elasticEnd1[currentSize - 1]));
            elastic1StartNewEdits[i]->setText(QString::number(param->elasticStart1[currentSize - 1]));
            elastic1EndNewEdits[i]->setText(QString::number(param->elasticEnd1[currentSize - 1]));

            // 设置可编辑状态
            bool editable = !param->prohibitsValueChange1;
            elastic1StartNewEdits[i]->setEnabled(editable);
            elastic1EndNewEdits[i]->setEnabled(editable);

            // 设置背景色
            if (editable) {
                elastic1StartNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
                elastic1EndNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
            } else {
                elastic1StartNewEdits[i]->setStyleSheet("");
                elastic1EndNewEdits[i]->setStyleSheet("");
            }

            // 显示所有控件
            elastic1IndexLabels[i]->setVisible(true);
            elastic1NameLabels[i]->setVisible(true);
            elastic1StepLabels[i]->setVisible(true);
            elastic1ProhibitLabels[i]->setVisible(true);
            elastic1StartLabels[i]->setVisible(true);
            elastic1EndLabels[i]->setVisible(true);
            elastic1StartNewEdits[i]->setVisible(true);
            elastic1EndNewEdits[i]->setVisible(true);

            // 恢复正常样式（除了可编辑的控件）
            elastic1IndexLabels[i]->setStyleSheet("");
            elastic1NameLabels[i]->setStyleSheet("");
            elastic1StepLabels[i]->setStyleSheet("");
            elastic1ProhibitLabels[i]->setStyleSheet("");
            elastic1StartLabels[i]->setStyleSheet("");
            elastic1EndLabels[i]->setStyleSheet("");
        } else {
            // 无数据，但保持控件可见以占据空间
            elastic1IndexLabels[i]->setText("");
            elastic1NameLabels[i]->setText("");
            elastic1StepLabels[i]->setText("");
            elastic1ProhibitLabels[i]->setText("");
            elastic1StartLabels[i]->setText("");
            elastic1EndLabels[i]->setText("");
            elastic1StartNewEdits[i]->setText("");
            elastic1EndNewEdits[i]->setText("");

            // 控件保持可见，但设置为透明
            elastic1IndexLabels[i]->setVisible(true);
            elastic1NameLabels[i]->setVisible(true);
            elastic1StepLabels[i]->setVisible(true);
            elastic1ProhibitLabels[i]->setVisible(true);
            elastic1StartLabels[i]->setVisible(true);
            elastic1EndLabels[i]->setVisible(true);
            elastic1StartNewEdits[i]->setVisible(true);
            elastic1EndNewEdits[i]->setVisible(true);

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            elastic1IndexLabels[i]->setStyleSheet(transparentStyle);
            elastic1NameLabels[i]->setStyleSheet(transparentStyle);
            elastic1StepLabels[i]->setStyleSheet(transparentStyle);
            elastic1ProhibitLabels[i]->setStyleSheet(transparentStyle);
            elastic1StartLabels[i]->setStyleSheet(transparentStyle);
            elastic1EndLabels[i]->setStyleSheet(transparentStyle);
            elastic1StartNewEdits[i]->setStyleSheet(transparentStyle);
            elastic1EndNewEdits[i]->setStyleSheet(transparentStyle);

            // 禁用编辑
            elastic1StartNewEdits[i]->setEnabled(false);
            elastic1EndNewEdits[i]->setEnabled(false);
        }
    }




}
