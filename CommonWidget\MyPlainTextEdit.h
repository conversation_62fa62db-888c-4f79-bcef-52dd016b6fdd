﻿#ifndef MYPLAINTEXTEDIT_H
#define MYPLAINTEXTEDIT_H
#include <QPlainTextEdit>
#include <QScrollBar>

class MyPlainTextEdit : public QPlainTextEdit
{
    Q_OBJECT
public:
    MyPlainTextEdit( QWidget* parent = nullptr ) : QPlainTextEdit( parent )
    {
        // 设置文本框的行数限制为2行
        setMaximumBlockCount( 2 );
        // 隐藏垂直滚动条
        setVerticalScrollBarPolicy( Qt::ScrollBarAlwaysOff );
        // 禁用水平滚动条
        setHorizontalScrollBarPolicy( Qt::ScrollBarAlwaysOff );
    }
    void mousePressEvent( QMouseEvent* event ) override
    {
        QPlainTextEdit::mousePressEvent( event );
        emit mousePressed( event );
    }
    void mouseReleaseEvent( QMouseEvent* event ) override
    {
        QPlainTextEdit::mouseReleaseEvent( event );
        emit mouseReleased( event );
    }
signals:
    void mousePressed( QMouseEvent* event );
    void mouseReleased( QMouseEvent* event );

protected:
    // 重写sizeHint方法以确保正确的大小
    QSize sizeHint() const override
    {
        QSize        hint = QPlainTextEdit::sizeHint();
        QFontMetrics fm( font() );
        hint.setHeight( fm.lineSpacing() * 2 + 2 * frameWidth() );
        return hint;
    }
};

#endif  // MYPLAINTEXTEDIT_H
