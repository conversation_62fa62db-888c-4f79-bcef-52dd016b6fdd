#include "LogForm.h"
#include "ui_LogForm.h"

void LogForm::initNetworkPage()
{
    if ( isNetworkPageInited == false )
    {
        networkBtnGroup = new QButtonGroup( this );
        networkBtnGroup->addButton( ui->pbtn_net_test, 1 );
        networkBtnGroup->addButton( ui->pbtn_net_refresh, 2 );
        networkBtnGroup->addButton( ui->pbtn_net_upgrade, 3 );
        networkBtnGroup->addButton( ui->pbtn_net_setParam, 4 );
        networkBtnGroup->addButton( ui->pbtn_net_importParam, 5 );
        networkBtnGroup->addButton( ui->pbtn_net_exportParam, 6 );
        connect( networkBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onNetworkBtnGroupClicked( int ) ) );

        // 网络使能选择框，只有一个comboForm，因此只单独设置
        if ( comboForm == nullptr )
        {
            QMap< int, QString > list;
            list.insert( 0, "关闭" );
            list.insert( 1, "开启" );
            comboForm = new ComboForm( nullptr, &list );
        }
        connect( ui->le_net_enable, &MyLineEdit::mouseRelease, this, [&]() { comboForm->show(); } );
        connect( comboForm, &ComboForm::itemSelected, this, [&]( int itemKey ) { ui->le_net_enable->setText( itemKey == 0 ? "关闭" : "开启" ); } );

        // 数字输入
        connect( this->ui->le_net_machineIP1, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_machineIP2, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_machineIP3, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_machineIP4, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_mask1, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_mask2, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_mask3, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_mask4, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_gate1, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_gate2, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_gate3, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_gate4, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_servIP1, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_servIP2, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_servIP3, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_servIP4, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );
        connect( this->ui->le_net_servPort, &MyLineEdit::mouseRelease, this, &LogForm::showNumberInputForm );

        // 修改工厂名称和机器编号
        connect( this->ui->pbtn_editFactoryName, &QPushButton::clicked, this, [&]() {
            if ( inputDialog == nullptr )
            {
                inputDialog = new InputDialog( this, "输入文本", "工厂名称:" );
            }
            else
            {
                inputDialog->changeHint( "工厂名称:" );
            }
            int result = inputDialog->exec();
            if ( result == QDialog::Accepted )
            {
                QString inputText = inputDialog->getInputText();
                if ( !inputText.isEmpty() )
                {
                    this->ui->le_net_factoryName->setText( inputText );
                }
                else
                {
                    this->ui->le_net_factoryName->setText( "" );
                }
            }
        } );
        connect( this->ui->pbtn_editMachineId, &QPushButton::clicked, this, [&]() {
            if ( inputDialog == nullptr )
            {
                inputDialog = new InputDialog( this, "输入文本", "机器编号(新):" );
            }
            else
            {
                inputDialog->changeHint( "机器编号(新):" );
            }
            int result = inputDialog->exec();
            if ( result == QDialog::Accepted )
            {
                QString inputText = inputDialog->getInputText();
                if ( !inputText.isEmpty() )
                {
                    this->ui->le_net_machineIdN->setText( inputText );
                }
                else
                {
                    this->ui->le_net_machineIdN->setText( "" );
                }
            }
        } );

        isNetworkPageInited = false;
    }
    loadNetworkConfig();
}

void LogForm::onNetworkBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            break;
        case 2:
            break;
        case 3:
            break;
        case 4:
            saveNetworkConfig();
            break;
        case 5:
            break;
        case 6:
            break;
    }
}

void LogForm::loadNetworkConfig()
{
    if ( mainData != nullptr )
    {
        ui->le_net_enable->setText( mainData->networkConfig.enable ? "开启" : "关闭" );
        ui->le_net_machineId->setText( mainData->networkConfig.machineId );
        ui->le_net_machineIP1->setText( QString::number( mainData->networkConfig.machineIP[ 0 ] ) );
        ui->le_net_machineIP2->setText( QString::number( mainData->networkConfig.machineIP[ 1 ] ) );
        ui->le_net_machineIP3->setText( QString::number( mainData->networkConfig.machineIP[ 2 ] ) );
        ui->le_net_machineIP4->setText( QString::number( mainData->networkConfig.machineIP[ 3 ] ) );
        ui->le_net_mask1->setText( QString::number( mainData->networkConfig.mask[ 0 ] ) );
        ui->le_net_mask2->setText( QString::number( mainData->networkConfig.mask[ 1 ] ) );
        ui->le_net_mask3->setText( QString::number( mainData->networkConfig.mask[ 2 ] ) );
        ui->le_net_mask4->setText( QString::number( mainData->networkConfig.mask[ 3 ] ) );
        ui->le_net_gate1->setText( QString::number( mainData->networkConfig.netgate[ 0 ] ) );
        ui->le_net_gate2->setText( QString::number( mainData->networkConfig.netgate[ 1 ] ) );
        ui->le_net_gate3->setText( QString::number( mainData->networkConfig.netgate[ 2 ] ) );
        ui->le_net_gate4->setText( QString::number( mainData->networkConfig.netgate[ 3 ] ) );
        ui->le_net_servIP1->setText( QString::number( mainData->networkConfig.servIP[ 0 ] ) );
        ui->le_net_servIP2->setText( QString::number( mainData->networkConfig.servIP[ 1 ] ) );
        ui->le_net_servIP3->setText( QString::number( mainData->networkConfig.servIP[ 2 ] ) );
        ui->le_net_servIP4->setText( QString::number( mainData->networkConfig.servIP[ 3 ] ) );
        ui->le_net_servPort->setText( QString::number( mainData->networkConfig.servPort ) );
        ui->le_net_protocol->setText( mainData->networkConfig.protocal );
        ui->le_net_version->setText( mainData->networkConfig.version );
    }
}

void LogForm::showNumberInputForm()
{
    MyLineEdit* senderLineEdit    = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedLineEdit = senderLineEdit;
    if ( numberInputForm == nullptr )
    {
        numberInputForm = new NumberInputForm( this, "请输入数值", 0, 255 );
        connect( this->numberInputForm, &NumberInputForm::InputFinished, this, &LogForm::onNumberInputFinished );
    }
    else
    {
        if ( senderLineEdit == ui->le_net_servPort )
        {
            numberInputForm->setValueLimit( 0, 65535 );
        }
        else
        {
            numberInputForm->setValueLimit( 0, 255 );
        }
    }
    this->numberInputForm->show();
}

void LogForm::onNumberInputFinished( QString text )
{
    if ( currentSelectedLineEdit != nullptr )
    {
        currentSelectedLineEdit->setText( text );
    }
}

void LogForm::saveNetworkConfig()
{
    mainData->networkConfig.enable         = ui->le_net_enable->text() == "关闭" ? false : true;
    mainData->networkConfig.factoryName    = ui->le_net_factoryName->text();
    mainData->networkConfig.machineId      = ui->le_net_machineIdN->text();
    mainData->networkConfig.machineIP[ 0 ] = ui->le_net_machineIP1->text().toInt();
    mainData->networkConfig.machineIP[ 1 ] = ui->le_net_machineIP2->text().toInt();
    mainData->networkConfig.machineIP[ 2 ] = ui->le_net_machineIP3->text().toInt();
    mainData->networkConfig.machineIP[ 3 ] = ui->le_net_machineIP4->text().toInt();
    mainData->networkConfig.mask[ 0 ]      = ui->le_net_mask1->text().toInt();
    mainData->networkConfig.mask[ 1 ]      = ui->le_net_mask2->text().toInt();
    mainData->networkConfig.mask[ 2 ]      = ui->le_net_mask3->text().toInt();
    mainData->networkConfig.mask[ 3 ]      = ui->le_net_mask4->text().toInt();
    mainData->networkConfig.netgate[ 0 ]   = ui->le_net_gate1->text().toInt();
    mainData->networkConfig.netgate[ 1 ]   = ui->le_net_gate2->text().toInt();
    mainData->networkConfig.netgate[ 2 ]   = ui->le_net_gate3->text().toInt();
    mainData->networkConfig.netgate[ 3 ]   = ui->le_net_gate4->text().toInt();
    mainData->networkConfig.servIP[ 0 ]    = ui->le_net_servIP1->text().toInt();
    mainData->networkConfig.servIP[ 1 ]    = ui->le_net_servIP2->text().toInt();
    mainData->networkConfig.servIP[ 2 ]    = ui->le_net_servIP3->text().toInt();
    mainData->networkConfig.servIP[ 3 ]    = ui->le_net_servIP4->text().toInt();
    mainData->networkConfig.servPort       = ui->le_net_servPort->text().toInt();
}
