<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EyeParaSetForm</class>
 <widget class="QWidget" name="EyeParaSetForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>190</width>
    <height>50</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #f5f5f5;
    border-radius: 6px;
    border: 1px solid #d0d0d0;
    margin: 2px;
}</string>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="lbl_index">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>50</width>
     <height>31</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 4px;
    padding: 2px;
    font-size: 8pt;
    font-weight: bold;
}</string>
   </property>
   <property name="text">
    <string>TextLabel</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="MyLineEdit" name="le_params">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>10</y>
     <width>110</width>
     <height>31</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">MyLineEdit {
    background-color: white;
    color: #333333;
    border-radius: 4px;
    border: 1px solid #c0c0c0;
    padding: 2px;
    font-size: 8pt;
}

MyLineEdit:hover {
    border: 1px solid #3498db;
}</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="ColorBlockWidget" name="cb_status" native="true">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>17</y>
     <width>15</width>
     <height>15</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">ColorBlockWidget {
    border-radius: 7px;
    border: 1px solid #c0c0c0;
}</string>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
  <customwidget>
   <class>ColorBlockWidget</class>
   <extends>QWidget</extends>
   <header>FileWidget/ColorBlockWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
