#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>

// 初始化生克罩摆动框架
void CraftParamForm::initSinkerAngularFrame()
{
    // 清空之前的控件列表
    if (!sinkerAngularIndexLabels.isEmpty()) {
        for (auto label : sinkerAngularIndexLabels) {
            delete label;
        }
        sinkerAngularIndexLabels.clear();
    }
    if (!sinkerAngularNameLabels.isEmpty()) {
        for (auto label : sinkerAngularNameLabels) {
            delete label;
        }
        sinkerAngularNameLabels.clear();
    }
    if (!sinkerAngularStepLabels.isEmpty()) {
        for (auto label : sinkerAngularStepLabels) {
            delete label;
        }
        sinkerAngularStepLabels.clear();
    }
    if (!sinkerAngularIdEdits.isEmpty()) {
        for (auto edit : sinkerAngularIdEdits) {
            delete edit;
        }
        sinkerAngularIdEdits.clear();
    }
    if (!sinkerAngularProhibitLabels.isEmpty()) {
        for (auto label : sinkerAngularProhibitLabels) {
            delete label;
        }
        sinkerAngularProhibitLabels.clear();
    }
    if (!sinkerAngularStartLabels.isEmpty()) {
        for (auto label : sinkerAngularStartLabels) {
            delete label;
        }
        sinkerAngularStartLabels.clear();
    }
    if (!sinkerAngularEndLabels.isEmpty()) {
        for (auto label : sinkerAngularEndLabels) {
            delete label;
        }
        sinkerAngularEndLabels.clear();
    }
    if (!sinkerAngularStartNewEdits.isEmpty()) {
        for (auto edit : sinkerAngularStartNewEdits) {
            delete edit;
        }
        sinkerAngularStartNewEdits.clear();
    }
    if (!sinkerAngularEndNewEdits.isEmpty()) {
        for (auto edit : sinkerAngularEndNewEdits) {
            delete edit;
        }
        sinkerAngularEndNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout(ui->sinkerAngularFrame);

    // 创建标题行
    QLabel* indexHeader = new QLabel("序号", ui->sinkerAngularFrame);
    QLabel* nameHeader = new QLabel("步骤名称", ui->sinkerAngularFrame);
    QLabel* stepHeader = new QLabel("步", ui->sinkerAngularFrame);
    QLabel* idHeader = new QLabel("ID", ui->sinkerAngularFrame);
    QLabel* prohibitHeader = new QLabel("P", ui->sinkerAngularFrame);
    QLabel* startHeader = new QLabel("起始值", ui->sinkerAngularFrame);
    QLabel* endHeader = new QLabel("结束值", ui->sinkerAngularFrame);
    QLabel* startNewHeader = new QLabel("N起始值", ui->sinkerAngularFrame);
    QLabel* endNewHeader = new QLabel("N结束值", ui->sinkerAngularFrame);

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet(headerStyle);
    nameHeader->setStyleSheet(headerStyle);
    stepHeader->setStyleSheet(headerStyle);
    idHeader->setStyleSheet(headerStyle);
    prohibitHeader->setStyleSheet(headerStyle);
    startHeader->setStyleSheet(headerStyle);
    endHeader->setStyleSheet(headerStyle);
    startNewHeader->setStyleSheet(headerStyle);
    endNewHeader->setStyleSheet(headerStyle);

    // 设置标题对齐方式
    indexHeader->setAlignment(Qt::AlignCenter);
    nameHeader->setAlignment(Qt::AlignCenter);
    stepHeader->setAlignment(Qt::AlignCenter);
    idHeader->setAlignment(Qt::AlignCenter);
    prohibitHeader->setAlignment(Qt::AlignCenter);
    startHeader->setAlignment(Qt::AlignCenter);
    endHeader->setAlignment(Qt::AlignCenter);
    startNewHeader->setAlignment(Qt::AlignCenter);
    endNewHeader->setAlignment(Qt::AlignCenter);

    // 添加标题到布局
    layout->addWidget(indexHeader, 0, 0);
    layout->addWidget(nameHeader, 0, 1);
    layout->addWidget(stepHeader, 0, 2);
    layout->addWidget(idHeader, 0, 3);
    layout->addWidget(prohibitHeader, 0, 4);
    layout->addWidget(startHeader, 0, 5);
    layout->addWidget(endHeader, 0, 6);
    layout->addWidget(startNewHeader, 0, 7);
    layout->addWidget(endNewHeader, 0, 8);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->sinkerAngularFrame);
        indexLabel->setAlignment(Qt::AlignCenter);
        sinkerAngularIndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);
        
        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->sinkerAngularFrame);
        nameLabel->setAlignment(Qt::AlignCenter);
        sinkerAngularNameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);
        
        // 步标签
        QLabel* stepLabel = new QLabel("", ui->sinkerAngularFrame);
        stepLabel->setAlignment(Qt::AlignCenter);
        sinkerAngularStepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);
        
        // ID编辑框
        MyLineEdit* idEdit = new MyLineEdit(ui->sinkerAngularFrame);
        idEdit->setAlignment(Qt::AlignCenter);
        idEdit->setReadOnly(true); // 初始设为只读
        idEdit->setProperty("row", i); // 存储行索引
        idEdit->setProperty("column", 3); // 存储列索引
        connect(idEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSinkerAngularPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.sinkerMotorParam.size()) {
                tableIndex = 7; // 生克罩摆动表格
                tableEditRowIndex = i;
                tableEditColIndex = 3; // ID列
                
                // 创建字母输入对话框
                if (letterInputDialog != nullptr) {
                    delete letterInputDialog;
                    letterInputDialog = nullptr;
                }
                letterInputDialog = new LetterInputDialog(nullptr, "生克ID", "ID:");
                connect(this->letterInputDialog, &LetterInputDialog::InputFinished, this, &CraftParamForm::onLetterInputDialogFinished);
                letterInputDialog->show();
            }
        });
        sinkerAngularIdEdits.append(idEdit);
        layout->addWidget(idEdit, i + 1, 3);
        
        // 禁止修改标签
        QLabel* prohibitLabel = new QLabel("", ui->sinkerAngularFrame);
        prohibitLabel->setAlignment(Qt::AlignCenter);
        sinkerAngularProhibitLabels.append(prohibitLabel);
        layout->addWidget(prohibitLabel, i + 1, 4);
        
        // 起始值标签
        QLabel* startLabel = new QLabel("", ui->sinkerAngularFrame);
        startLabel->setAlignment(Qt::AlignCenter);
        sinkerAngularStartLabels.append(startLabel);
        layout->addWidget(startLabel, i + 1, 5);
        
        // 结束值标签
        QLabel* endLabel = new QLabel("", ui->sinkerAngularFrame);
        endLabel->setAlignment(Qt::AlignCenter);
        sinkerAngularEndLabels.append(endLabel);
        layout->addWidget(endLabel, i + 1, 6);
        
        // 新起始值编辑框
        MyLineEdit* startNewEdit = new MyLineEdit(ui->sinkerAngularFrame);
        startNewEdit->setAlignment(Qt::AlignCenter);
        startNewEdit->setReadOnly(true); // 初始设为只读
        startNewEdit->setProperty("row", i); // 存储行索引
        startNewEdit->setProperty("column", 7); // 存储列索引
        connect(startNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSinkerAngularPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.sinkerMotorParam.size() && !craftParams.sinkerMotorParam[dataIndex]->prohibitsAngularValueChange) {
                tableIndex = 7; // 生克罩摆动表格
                tableEditRowIndex = i;
                tableEditColIndex = 7; // 新起始值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "角度新起始值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        sinkerAngularStartNewEdits.append(startNewEdit);
        layout->addWidget(startNewEdit, i + 1, 7);
        
        // 新结束值编辑框
        MyLineEdit* endNewEdit = new MyLineEdit(ui->sinkerAngularFrame);
        endNewEdit->setAlignment(Qt::AlignCenter);
        endNewEdit->setReadOnly(true); // 初始设为只读
        endNewEdit->setProperty("row", i); // 存储行索引
        endNewEdit->setProperty("column", 8); // 存储列索引
        connect(endNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentSinkerAngularPage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.sinkerMotorParam.size() && !craftParams.sinkerMotorParam[dataIndex]->prohibitsAngularValueChange) {
                tableIndex = 7; // 生克罩摆动表格
                tableEditRowIndex = i;
                tableEditColIndex = 8; // 新结束值列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "角度新结束值", 0, 4095);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        sinkerAngularEndNewEdits.append(endNewEdit);
        layout->addWidget(endNewEdit, i + 1, 8);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 1);  // ID
    layout->setColumnStretch(4, 1);  // P
    layout->setColumnStretch(5, 2);  // 起始值
    layout->setColumnStretch(6, 2);  // 结束值
    layout->setColumnStretch(7, 2);  // N起始值
    layout->setColumnStretch(8, 2);  // N结束值
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->sinkerAngularFrame->setLayout(layout);
}

// 更新生克罩摆动页面
void CraftParamForm::updateSinkerAngularPage()
{
    // 计算总页数
    totalSinkerAngularPages = (craftParams.sinkerMotorParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;
    
    // 确保当前页在有效范围内
    if (currentSinkerAngularPage >= totalSinkerAngularPages && totalSinkerAngularPages > 0) {
        currentSinkerAngularPage = totalSinkerAngularPages - 1;
    }
    
    // 计算当前页的起始索引
    int startIndex = currentSinkerAngularPage * ITEMS_PER_PAGE;
    
    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.sinkerMotorParam.size()) {
            // 有数据，显示
            auto param = craftParams.sinkerMotorParam[dataIndex];

            sinkerAngularIndexLabels[i]->setText(QString::number(dataIndex + 1));
            sinkerAngularNameLabels[i]->setText(QString::fromLatin1(param->blockName));
            sinkerAngularStepLabels[i]->setText(QString::number(param->stepStart) + "~" + QString::number(param->stepEnd));
            sinkerAngularIdEdits[i]->setText(QString(param->sinkerId));
            sinkerAngularProhibitLabels[i]->setText(param->prohibitsAngularValueChange ? "N" : "Y");
            sinkerAngularStartLabels[i]->setText(QString::number(param->angularStart[currentSize - 1]));
            sinkerAngularEndLabels[i]->setText(QString::number(param->angularEnd[currentSize - 1]));
            sinkerAngularStartNewEdits[i]->setText(QString::number(param->angularStart[currentSize - 1]));
            sinkerAngularEndNewEdits[i]->setText(QString::number(param->angularEnd[currentSize - 1]));

            // 设置可编辑状态
            bool editable = !param->prohibitsAngularValueChange;
            sinkerAngularIdEdits[i]->setEnabled(true); // ID始终可编辑
            sinkerAngularStartNewEdits[i]->setEnabled(editable);
            sinkerAngularEndNewEdits[i]->setEnabled(editable);

            // 设置背景色
            sinkerAngularIdEdits[i]->setStyleSheet("background-color: #ffffc8;");
            if (editable) {
                sinkerAngularStartNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
                sinkerAngularEndNewEdits[i]->setStyleSheet("background-color: #ffffc8;");
            } else {
                sinkerAngularStartNewEdits[i]->setStyleSheet("");
                sinkerAngularEndNewEdits[i]->setStyleSheet("");
            }

            // 显示所有控件
            sinkerAngularIndexLabels[i]->setVisible(true);
            sinkerAngularNameLabels[i]->setVisible(true);
            sinkerAngularStepLabels[i]->setVisible(true);
            sinkerAngularIdEdits[i]->setVisible(true);
            sinkerAngularProhibitLabels[i]->setVisible(true);
            sinkerAngularStartLabels[i]->setVisible(true);
            sinkerAngularEndLabels[i]->setVisible(true);
            sinkerAngularStartNewEdits[i]->setVisible(true);
            sinkerAngularEndNewEdits[i]->setVisible(true);

            // 恢复正常样式（除了可编辑的控件）
            sinkerAngularIndexLabels[i]->setStyleSheet("");
            sinkerAngularNameLabels[i]->setStyleSheet("");
            sinkerAngularStepLabels[i]->setStyleSheet("");
            sinkerAngularProhibitLabels[i]->setStyleSheet("");
            sinkerAngularStartLabels[i]->setStyleSheet("");
            sinkerAngularEndLabels[i]->setStyleSheet("");
        } else {
            // 无数据，但保持控件可见以占据空间
            sinkerAngularIndexLabels[i]->setText("");
            sinkerAngularNameLabels[i]->setText("");
            sinkerAngularStepLabels[i]->setText("");
            sinkerAngularIdEdits[i]->setText("");
            sinkerAngularProhibitLabels[i]->setText("");
            sinkerAngularStartLabels[i]->setText("");
            sinkerAngularEndLabels[i]->setText("");
            sinkerAngularStartNewEdits[i]->setText("");
            sinkerAngularEndNewEdits[i]->setText("");

            // 控件保持可见，但设置为透明
            sinkerAngularIndexLabels[i]->setVisible(true);
            sinkerAngularNameLabels[i]->setVisible(true);
            sinkerAngularStepLabels[i]->setVisible(true);
            sinkerAngularIdEdits[i]->setVisible(true);
            sinkerAngularProhibitLabels[i]->setVisible(true);
            sinkerAngularStartLabels[i]->setVisible(true);
            sinkerAngularEndLabels[i]->setVisible(true);
            sinkerAngularStartNewEdits[i]->setVisible(true);
            sinkerAngularEndNewEdits[i]->setVisible(true);

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            sinkerAngularIndexLabels[i]->setStyleSheet(transparentStyle);
            sinkerAngularNameLabels[i]->setStyleSheet(transparentStyle);
            sinkerAngularStepLabels[i]->setStyleSheet(transparentStyle);
            sinkerAngularIdEdits[i]->setStyleSheet(transparentStyle);
            sinkerAngularProhibitLabels[i]->setStyleSheet(transparentStyle);
            sinkerAngularStartLabels[i]->setStyleSheet(transparentStyle);
            sinkerAngularEndLabels[i]->setStyleSheet(transparentStyle);
            sinkerAngularStartNewEdits[i]->setStyleSheet(transparentStyle);
            sinkerAngularEndNewEdits[i]->setStyleSheet(transparentStyle);

            // 禁用编辑
            sinkerAngularIdEdits[i]->setEnabled(false);
            sinkerAngularStartNewEdits[i]->setEnabled(false);
            sinkerAngularEndNewEdits[i]->setEnabled(false);
        }
    }
}
