﻿#ifndef FILEFORM_H
#define FILEFORM_H

#include "Common/parameters.h"
#include "CommonWidget/MyWidget.h"
#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "FileWidget/ColorBlockWidget.h"
#include "FileWidget/FATParser.h"
#include "FileWidget/FlowerAndLamaoLoopDialog.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/Zhusuo2CfgDialog.h"
#include "FileWidget/ZhusuoCfgDialog.h"
#include "FileWidget/densityform.h"
#include "FileWidget/needitemcopyform.h"
#include "FileWidget/needleitemview.h"
#include "FileWidget/valveeditform.h"
#include <CommonWidget/comboform.h>
#include <Config/readlinkconfig.h>
#include <QDateTime>
#include <QListWidget>
#include <QStandardItemModel>
#include <QTableView>
#include <QWidget>

namespace Ui
{
class FileForm;
}

class FileForm : public QWidget
{
    Q_OBJECT

public:
    explicit FileForm( QWidget* parent = nullptr );
    ~FileForm();

    struct FileInfoItem
    {
        QString fileName;
        QString fileSize;
        QString lastModified;
    };

private:
    Ui::FileForm* ui;

    QButtonGroup* headRedirectBtnGroup;

    QStandardItemModel*       _db_fat_table_model          = nullptr;
    QStandardItemModel*       _db_fat_table_this_model     = nullptr;
    QStandardItemModel*       _db_fat_table_usb_model      = nullptr;
    QList< FileInfoItem >*    FATFileList                  = nullptr;
    QList< FileInfoItem >*    FATUSBFileList               = nullptr;
    QString                   currentFATFile               = "";
    QString                   currentFATThisFile           = "";
    QString                   currentFATUSBFile            = "";
    QString                   FATUSBBaseAddr               = "";
    FlowerAndLamaoLoopDialog* _flowerAndLamaoLoopDialog    = nullptr;
    ZhusuoCfgDialog*          _zhusuoCfgDialog             = nullptr;
    Zhusuo2CfgDialog*         _zhusuo2CfgDialog            = nullptr;
    ComboForm*                _zhusuoCfg_comboFrm          = nullptr;
    ComboForm*                _stepName_comboFrm           = nullptr;
    ComboForm*                _FACommandName_comboFrm      = nullptr;
    ComboForm*                _fengtouStepName_comboFrm    = nullptr;
    ComboForm*                _motorPattern_comboFrm       = nullptr;
    ComboForm*                _fengtouAction_comboFrm      = nullptr;
    ComboForm*                _fengtouReset_comboFrm       = nullptr;
    ComboForm*                _motormap_comboFrm           = nullptr;
    ComboForm*                _fengtouVSType_comboFrm      = nullptr;
    ComboForm*                _fengtouVState_comboFrm      = nullptr;
    ComboForm*                _fengtouSState_comboFrm      = nullptr;
    ComboForm*                _fengtouVSName_comboFrm      = nullptr;
    ComboForm*                _needleState_comboFrm        = nullptr;
    ComboForm*                _cyState_comboFrm            = nullptr;
    ComboForm*                _tianshaColor_comboFrm       = nullptr;
    ComboForm*                _zhusuoColor_comboFrm        = nullptr;
    ComboForm*                _suduColor_comboFrm          = nullptr;
    ComboForm*                _xiangjinColor_comboFrm      = nullptr;
    ComboForm*                _lamaoColor_comboFrm         = nullptr;
    ComboForm*                _wagenlamaoColor_comboFrm    = nullptr;
    ComboForm*                _dasongsanjiaoColor_comboFrm = nullptr;
    ComboForm*                _chainPatternType_comboFrm   = nullptr;
    ComboForm*                _chainMotorDenType_comboFrm  = nullptr;
    DensityForm*              _densityFrm                  = nullptr;
    ValveEditForm*            _valveEditFrm                = nullptr;
    NumberInputForm*          _numberInputFrm              = nullptr;
    NeedItemCopyForm*         _needleItemCopyFrom          = nullptr;
    //    int                                             menuIndex                    = -1;  // 保存显示的菜单号，因为不管是花型还是链条，均要先选文件，再进入具体菜单
    int                                             currentTableSelIndex      = -1;
    int                                             currentActionIndex        = 0;   //保存链条编辑页点击进入圈控制时的编号
    int                                             currentActionDetailIndex  = -1;  //保存进入动作时的动作库编号
    FATParser::FATDataMain*                         fatDataMain               = nullptr;
    ReadLinkConfig*                                 linkCfg                   = nullptr;
    QPixmap*                                        patternPixmap             = nullptr;  //保存花型的Pixmap
    PatternViewer*                                  patternViewer             = nullptr;
    QSharedPointer< FATParser::FengtouStruct >      currentFengtouStruct      = nullptr;  //保存当前正在编辑的缝头大步骤，便于修改后保存
    QSharedPointer< FATParser::SockMachineStepMsg > currentChainStepStruct    = nullptr;  //保存当前正在编辑的链条步骤，便于修改后保存
    FATParser::FengtouStepStruct*                   currentFengtouStepStruct  = nullptr;  //保存当前正在编辑的缝头小步骤，便于修改后保存
    MyLineEdit*                                     currentSelectedMyLineEdit = nullptr;  //当前正在修改的LineEdit，用户修改完返回后设置Text,所有组件公用
    NeedleView*                                     currentSelectedNeedleView = nullptr;  //当前正在修改的NeedView，用户修改完返回后设置Text,所有组件公用
    NeedleItemView*                                 currentSelectedNeedleItem = nullptr;  //当前正在修改的NeedView，用户修改完返回后设置Text,所有组件公用
    quint8                                          tmpNeedle_data[ 32 ][ 25 ];           // 临时保存选针资料库数据
    quint8                                          tmpBloclNeedle_data[ 25 ];            // 临时保存块复制的选针资料库数据
    int                                             currentActionPage       = 0;          // 当前动作栏的页码
    bool                                            isFileAdminPageInited   = false;
    bool                                            isPatternDagePageInited = false;  // 确保页面只初始化1次，主要是按钮不能多次connect
    bool                                            isChainDagePageInited   = false;  // 确保页面只初始化1次，主要是按钮不能多次connect
    bool                                            isFengtouPageInited     = false;  // 确保页面只初始化1次，主要是按钮不能多次connect
    bool                                            isActionPageInited      = false;  // 确保页面只初始化1次，主要是按钮不能多次connect
    bool                                            isNeedleListPageInited  = false;  // 确保页面只初始化1次，主要是按钮不能多次connect
    bool                                            isUSBTransferPageInited = false;  // 确保页面只初始化1次，主要是按钮不能多次connect
    int                                             currentVActionPage      = 0;      // 当前动作编辑页气阀动作栏的页码
    int                                             currentNeedlePage       = 0;      // 当前动作编辑页选针栏的页码
    int                                             currentNeedleListPage   = 0;      // 当前选针资料编辑页选针栏的页码
    int                                             currentPatternType      = 0;      // 当前花型预览页的类型
    QColor                                          currentPatternEditColor = Qt::white;

    void    initFileAdminPage();
    void    refreshFATFileList( QString fileFATbaseAddr );
    void    refreshFATUSBFileList( QString fileFATbaseAddr );
    void    savePatternImage();
    void    initFATFileTableView();
    void    initFATFileThisTableView();
    void    initFATFileUSBTableView();
    QString checkUSBDirExists();
    void    initChainDataPage();
    void    initFengtouPage();
    void    initActionViewPage();
    void    initNeedleEditPage();
    void    initPatternPage();
    void    initUSBTransferPage();
    void    ShowBuweiParam( QListWidgetItem* item );
    void    ShowActionList();
    void    ShowFTCommand();
    void    ShowFengtouStep( int index );
    void    ShowFengtouMsg( quint32 step_index );
    void    ShowFengtouMotor();
    void    ShowFengtouValve();
    void    ShowVActionList();
    void    ShowNeedleView();
    void    ShowNeedleList( quint8 sel_index );
    void    ShowNeedleListItem( quint8 index );
    void    ShowPatternImage();
    void    ScalePatternImage( quint8 factor );
    void    ShowPatternColorPad();
    QString getPatternImageAddr();
    void    restoreFromMWWFile( QString filename );

    void restoreImageToPatterData();
signals:
    void FileFormToMainWinToShowSignal();  //向主界面发送的显示主界面信号

private slots:
    void onFileHomeBtnClicked();  //测试界面返回 主菜单键按下槽函数
    void onHeaderRedirectMenuBtnGroupClicked( int id );
    void onActionLineEditClicked();  // Action的LineEidt点击事件
    void onFTCommandNameClicked();   // 指令页名称点击事件，跳出修改框
    void onCyStateClicked();         // 圈控制页动作状态点击事件，跳出修改框
    void ShowFengtouParam( QListWidgetItem* item );
    void onMotorNameClicked();                      // 点击出现修改点击Pattern窗口
    void onMotorPatternClicked();                   // 点击出现修改点击Pattern窗口
    void onFengtouVSTypeClicked();                  // 点击出现修改气阀或信号类型窗口
    void onFengtouVSNameClicked();                  // 点击出现修改气阀或信号名称窗口
    void onFengtouVSStateClicked();                 // 点击出现修改气阀或信号状态窗口
    void onNeedleStateClicked();                    // 点击出现选针状态窗口
    void onValveItemClicked();                      //点击出现气阀动作编辑窗口
    void onNumberInputFormFinished( QString str );  // numberInputFrm输入完成
    void onMotorTypeClicked();                      //点击出现修改电机类型窗口
    void onPatternTypeClicked();                    //点击出现修改花型类型窗口

    void onShowNumberInputForm();  //点击myLineEdit出发数字修改
    void onColorPadClicked();      //点击花型预览页弹出颜色选择框

    void onMotorTypeSelected( int itemKey );
    void onPatternTypeSelected( int itemKey );

    void saveDataToMWWFile();
};

#endif  // FILEFORM_H
