#include "CommonWidget/mylineedit.h"
#include "Config/readmachinefileconfig.h"
#include "parasetform.h"
#include "ui_parasetform.h"
#include <QDebug>
#include <QGridLayout>
#include <QLabel>
#include <QLayout>
#include <QMessageBox>
#include <QWidget>

void ParaSetForm::initFastResetParasetPage()
{
    if ( isFastResetParasetPageInit == false )
    {
        // 清空列表
        ui->listWidget_reset_type->clear();
        ui->listWidget_circle->clear();

        // 添加复位类型选项
        ui->listWidget_reset_type->addItem( "快速复位" );
        ui->listWidget_reset_type->addItem( "扎口复位" );

        // 初始化指令显示区域
        initResetInstructions();

        // 连接信号槽
        connect( ui->listWidget_reset_type, &QListWidget::currentRowChanged, this, [ this ]( int row ) { showResetCircle( row ); } );
        connect( ui->listWidget_circle, &QListWidget::currentRowChanged, this, [ this ]( int row ) { showResetInstructions( row ); } );
        // 连接添加和删除圈按钮的信号槽
        connect( ui->pbtn_CircleAdd, &QPushButton::clicked, this, &ParaSetForm::onCircleAddBtnClicked );
        connect( ui->pbtn_circleDelete, &QPushButton::clicked, this, &ParaSetForm::onCircleDeleteBtnClicked );
        // 连接添加、删除和清空指令按钮的信号槽
        connect( ui->pbtn_InsAdd, &QPushButton::clicked, this, &ParaSetForm::onInsAddBtnClicked );
        connect( ui->pbtn_InsDelete, &QPushButton::clicked, this, &ParaSetForm::onInsDeleteBtnClicked );
        connect( ui->pbtn_InsClear, &QPushButton::clicked, this, &ParaSetForm::onInsClearBtnClicked );

        // 默认选中第一项
        ui->listWidget_reset_type->setCurrentRow( 0 );

        isFastResetParasetPageInit = true;
    }
}

void ParaSetForm::initResetInstructions()
{
    // 创建网格布局用于显示指令
    QGridLayout* gridLayout = new QGridLayout( ui->gbox_instructions );

    // 添加表头
    QLabel* headerStep  = new QLabel( "序号", ui->gbox_instructions );
    QLabel* headerPos   = new QLabel( "位置(°)", ui->gbox_instructions );
    QLabel* headerType  = new QLabel( "类型", ui->gbox_instructions );
    QLabel* headerId    = new QLabel( "ID", ui->gbox_instructions );
    QLabel* headerValue = new QLabel( "数值", ui->gbox_instructions );
    // 设置文字水平居中
    headerStep->setAlignment( Qt::AlignCenter );
    headerPos->setAlignment( Qt::AlignCenter );
    headerType->setAlignment( Qt::AlignCenter );
    headerId->setAlignment( Qt::AlignCenter );
    headerValue->setAlignment( Qt::AlignCenter );

    gridLayout->addWidget( headerStep, 0, 0 );
    gridLayout->addWidget( headerPos, 0, 1 );
    gridLayout->addWidget( headerType, 0, 2 );
    gridLayout->addWidget( headerId, 0, 3 );
    gridLayout->addWidget( headerValue, 0, 4 );

    // 清空现有控件引用
    stepLabels.clear();
    posEdits.clear();
    typeEdits.clear();
    idEdits.clear();
    valueEdits.clear();

    // 创建12行控件
    for ( int i = 0; i < 12; i++ )
    {
        QLabel*     stepLabel = new QLabel( QString( "M%1" ).arg( i + 1, 2, 10, QChar( '0' ) ), ui->gbox_instructions );
        MyLineEdit* posEdit   = new MyLineEdit( ui->gbox_instructions );
        MyLineEdit* typeEdit  = new MyLineEdit( ui->gbox_instructions );
        MyLineEdit* idEdit    = new MyLineEdit( ui->gbox_instructions );
        MyLineEdit* valueEdit = new MyLineEdit( ui->gbox_instructions );

        // 设置文字水平居中
        posEdit->setAlignment( Qt::AlignCenter );
        typeEdit->setAlignment( Qt::AlignCenter );
        idEdit->setAlignment( Qt::AlignCenter );
        valueEdit->setAlignment( Qt::AlignCenter );

        // 为posEdit添加鼠标释放事件处理
        connect( posEdit, &MyLineEdit::mouseRelease, this, [ this, i ]() {
            currentResetInstructionIndex = i;
            onPosEditMouseRelease();
        } );
        // 为typeEdit添加鼠标释放事件处理
        connect( typeEdit, &MyLineEdit::mouseRelease, this, [ this, i ]() {
            currentResetInstructionIndex = i;
            onTypeEditMouseRelease();
        } );
        // 为idEdit添加鼠标释放事件处理
        connect( idEdit, &MyLineEdit::mouseRelease, this, [ this, i ]() {
            currentResetInstructionIndex = i;
            onIdEditMouseRelease();
        } );
        // 为valueEdit添加鼠标释放事件处理
        connect( valueEdit, &MyLineEdit::mouseRelease, this, [ this, i ]() {
            currentResetInstructionIndex = i;
            onValueEditMouseRelease();
        } );

        // 设置默认值为"-"
        posEdit->setText( "-" );
        typeEdit->setText( "-" );
        idEdit->setText( "-" );
        valueEdit->setText( "-" );

        // 添加到布局
        gridLayout->addWidget( stepLabel, i + 1, 0 );
        gridLayout->addWidget( posEdit, i + 1, 1 );
        gridLayout->addWidget( typeEdit, i + 1, 2 );
        gridLayout->addWidget( idEdit, i + 1, 3 );
        gridLayout->addWidget( valueEdit, i + 1, 4 );

        // 保存控件引用
        stepLabels.append( stepLabel );
        posEdits.append( posEdit );
        typeEdits.append( typeEdit );
        idEdits.append( idEdit );
        valueEdits.append( valueEdit );
    }

    // 设置布局
    ui->gbox_instructions->setLayout( gridLayout );
}

void ParaSetForm::showResetCircle( int resetTypeIndex )
{
    ui->listWidget_circle->clear();

    if ( readResetCfg != nullptr )
    {
        // 使用新的 ResetType 枚举
        ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

        // 使用新的 getResetList 方法
        auto& resetList = readResetCfg->getResetList( resetType );
        for ( int i = 0; i < resetList.size(); i++ )
        {
            ui->listWidget_circle->addItem( QString( "第%1圈" ).arg( i + 1 ) );
        }
    }
}

// 显示复位指令函数
void ParaSetForm::showResetInstructions( int circleIndex )
{
    if ( readResetCfg != nullptr )
    {
        int                        resetTypeIndex = ui->listWidget_reset_type->currentRow();
        ReadResetConfig::ResetType resetType      = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

        auto& resetList = readResetCfg->getResetList( resetType );
        if ( circleIndex >= 0 && circleIndex < resetList.size() )
        {
            // 获取当前圈的数据
            const auto& circleData = resetList[ circleIndex ];

            // 重置所有控件
            for ( int i = 0; i < 12; i++ )
            {
                stepLabels[ i ]->setText( QString( "M%1" ).arg( i + 1, 2, 10, QChar( '0' ) ) );
                posEdits[ i ]->setText( "-" );
                typeEdits[ i ]->setText( "-" );
                idEdits[ i ]->setText( "-" );
                valueEdits[ i ]->setText( "-" );
            }

            // 填充数据 - 使用数组索引作为序号，而不是step字段
            for ( int i = 0; i < qMin( circleData.size(), 12 ); i++ )
            {
                const auto& action = circleData[ i ];

                stepLabels[ i ]->setText( QString( "M%1" ).arg( i + 1, 2, 10, QChar( '0' ) ) );
                posEdits[ i ]->setText( QString::number( action.position ) );
                typeEdits[ i ]->setText( action.type == 0 ? "气阀" : "电机" );
                idEdits[ i ]->setText( QString::number( action.id ) );
                if ( action.type == 0 )
                {
                    valueEdits[ i ]->setText( action.value == 0 ? "出" : "进" );
                }
                else
                {
                    valueEdits[ i ]->setText( QString::number( action.value ) );
                }
            }
        }
    }
}

void ParaSetForm::onPosEditMouseRelease()
{
    // 获取当前圈号和复位类型
    int circleIndex    = ui->listWidget_circle->currentRow();
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // currentResetInstructionIndex大于当前数据的长度，不做处理
    if ( currentResetInstructionIndex >= readResetCfg->getResetList( resetType )[ circleIndex ].size() )
        return;

    // 创建数字输入窗口并设置自动删除
    NumberInputForm* numberInputFrm = new NumberInputForm( nullptr, "输入位置", 0, 359 );
    numberInputFrm->setWindowTitle( "输入位置" );
    numberInputFrm->setAttribute( Qt::WA_ShowModal, true );
    numberInputFrm->setAttribute( Qt::WA_DeleteOnClose, true );  // 设置关闭时自动删除
    numberInputFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
    numberInputFrm->show();

    connect( numberInputFrm, &NumberInputForm::InputFinished, this, [ & ]( QString text ) {
        // 更新文本框内容
        posEdits[ currentResetInstructionIndex ]->setText( text );

        // text转换为数字
        int pos = text.toInt();
        updateResetActionValue( 0, pos );
    } );
}

void ParaSetForm::onTypeEditMouseRelease()
{
    // 获取当前圈号和复位类型
    int circleIndex    = ui->listWidget_circle->currentRow();
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // currentResetInstructionIndex大于当前数据的长度，不做处理
    if ( currentResetInstructionIndex >= readResetCfg->getResetList( resetType )[ circleIndex ].size() )
        return;

    // 创建下拉选择窗口
    QMap< int, QString > typeMap;
    typeMap.insert( 0, "气阀" );
    typeMap.insert( 1, "电机" );
    typeMap.insert( 2, "主电机" );

    // 创建组合框窗口并设置自动删除
    ComboForm* comboFrm = new ComboForm( nullptr, &typeMap );
    comboFrm->setWindowTitle( "选择类型" );
    comboFrm->setAttribute( Qt::WA_ShowModal, true );
    comboFrm->setAttribute( Qt::WA_DeleteOnClose, true );  // 设置关闭时自动删除
    comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
    comboFrm->show();

    connect( comboFrm, &ComboForm::itemSelected, this, [ & ]( int id ) {
        QString text;
        if ( id == 0 )
            text = "气阀";
        else if ( id == 1 )
            text = "电机";
        else if ( id == 2 )
            text = "主电机";

        typeEdits[ currentResetInstructionIndex ]->setText( text );
        updateResetActionValue( 1, id );
    } );
}

void ParaSetForm::onIdEditMouseRelease()
{
    // 获取当前圈号和复位类型
    int circleIndex    = ui->listWidget_circle->currentRow();
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // currentResetInstructionIndex大于当前数据的长度，不做处理
    if ( currentResetInstructionIndex >= readResetCfg->getResetList( resetType )[ circleIndex ].size() )
        return;

    // 创建数字输入窗口并设置自动删除
    NumberInputForm* numberInputFrm = new NumberInputForm( nullptr, "输入Id", 1, 159 );
    numberInputFrm->setWindowTitle( "输入Id" );
    numberInputFrm->setAttribute( Qt::WA_ShowModal, true );
    numberInputFrm->setAttribute( Qt::WA_DeleteOnClose, true );  // 设置关闭时自动删除
    numberInputFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
    numberInputFrm->show();

    connect( numberInputFrm, &NumberInputForm::InputFinished, this, [ & ]( QString text ) {
        idEdits[ currentResetInstructionIndex ]->setText( text );
        // text转换为数字
        int id = text.toInt();
        updateResetActionValue( 2, id );
    } );
}

void ParaSetForm::onValueEditMouseRelease()
{
    // 获取当前圈号和复位类型
    int circleIndex    = ui->listWidget_circle->currentRow();
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // currentResetInstructionIndex大于当前数据的长度，不做处理
    if ( currentResetInstructionIndex >= readResetCfg->getResetList( resetType )[ circleIndex ].size() )
        return;

    // 获取当前类型
    QString typeText = typeEdits[ currentResetInstructionIndex ]->text();
    if ( typeText == "-" )
        return;  // 如果类型未设置，不处理

    // 判断类型值
    bool isValve = ( typeText == "气阀" );

    // 获取当前值
    QString currentText = valueEdits[ currentResetInstructionIndex ]->text();

    if ( isValve )
    {
        // 创建下拉选择窗口
        QMap< int, QString > typeMap;
        typeMap.insert( 0, "出" );
        typeMap.insert( 1, "进" );

        // 创建组合框窗口并设置自动删除
        ComboForm* comboFrm = new ComboForm( nullptr, &typeMap );
        comboFrm->setWindowTitle( "选择状态" );
        comboFrm->setAttribute( Qt::WA_ShowModal, true );
        comboFrm->setAttribute( Qt::WA_DeleteOnClose, true );  // 设置关闭时自动删除
        comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
        comboFrm->show();

        connect( comboFrm, &ComboForm::itemSelected, this, [ & ]( int id ) {
            valueEdits[ currentResetInstructionIndex ]->setText( id == 0 ? "出" : "进" );
            updateResetActionValue( 3, id );
        } );
    }
    else
    {
        // 创建数字输入窗口并设置自动删除
        NumberInputForm* numberInputFrm = new NumberInputForm( nullptr, "输入电机值", 0, 359 );
        numberInputFrm->setWindowTitle( "输入电机值" );
        numberInputFrm->setAttribute( Qt::WA_ShowModal, true );
        numberInputFrm->setAttribute( Qt::WA_DeleteOnClose, true );  // 设置关闭时自动删除
        numberInputFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
        numberInputFrm->show();

        connect( numberInputFrm, &NumberInputForm::InputFinished, this, [ & ]( QString text ) {
            valueEdits[ currentResetInstructionIndex ]->setText( text );
            // text转换为数字
            int id = text.toInt();
            updateResetActionValue( 3, id );
        } );
    }
}

// 更新复位动作值函数
void ParaSetForm::updateResetActionValue( quint8 valueType, int newValue )
{
    int circleIndex    = ui->listWidget_circle->currentRow();
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    auto& resetList = readResetCfg->getResetList( resetType );
    if ( circleIndex < resetList.size() && currentResetInstructionIndex < resetList[ circleIndex ].size() )
    {
        auto& circleData = resetList[ circleIndex ];

        // 更新值字段
        if ( valueType == 0 )
            circleData[ currentResetInstructionIndex ].position = newValue;
        else if ( valueType == 1 )
        {
            circleData[ currentResetInstructionIndex ].type = newValue;

            // 如果类型改变，可能需要更新值的显示方式
            if ( newValue == 0 )
            {  // 气阀
                valueEdits[ currentResetInstructionIndex ]->setText( circleData[ currentResetInstructionIndex ].value == 0 ? "出" : "进" );
            }
            else
            {  // 电机
                valueEdits[ currentResetInstructionIndex ]->setText( QString::number( circleData[ currentResetInstructionIndex ].value ) );
            }
        }
        else if ( valueType == 2 )
            circleData[ currentResetInstructionIndex ].id = newValue;
        else if ( valueType == 3 )
            circleData[ currentResetInstructionIndex ].value = newValue;
    }
}

// 添加圈按钮点击槽函数
void ParaSetForm::onCircleAddBtnClicked()
{
    // 获取当前复位类型
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // 获取当前选中的圈索引
    int currentCircleIndex = ui->listWidget_circle->currentRow();
    if ( currentCircleIndex < 0 )
        currentCircleIndex = -1;  // 如果没有选中，则在最后添加

    // 在当前选中圈之后插入新圈
    readResetCfg->insertCircle( currentCircleIndex + 1, resetType );

    // 刷新圈列表
    showResetCircle( resetTypeIndex );

    // 选中新插入的圈
    ui->listWidget_circle->setCurrentRow( currentCircleIndex + 1 );
}

// 删除圈按钮点击槽函数
void ParaSetForm::onCircleDeleteBtnClicked()
{
    // 获取当前复位类型
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    if ( readResetCfg == nullptr )
        return;

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // 获取当前选中的圈索引
    int currentCircleIndex = ui->listWidget_circle->currentRow();
    if ( currentCircleIndex < 0 )
        return;  // 如果没有选中，则不处理

    // 弹出确认对话框
    QMessageBox msgBox;
    msgBox.setWindowTitle( "确认删除" );
    msgBox.setText( QString( "确定要删除第%1圈吗？" ).arg( currentCircleIndex + 1 ) );
    msgBox.setStandardButtons( QMessageBox::Yes | QMessageBox::No );
    msgBox.setDefaultButton( QMessageBox::No );

    // 如果用户确认删除
    if ( msgBox.exec() == QMessageBox::Yes )
    {
        // 删除选中的圈
        readResetCfg->deleteCircle( currentCircleIndex, resetType );

        // 刷新圈列表
        showResetCircle( resetTypeIndex );

        // 选中合适的圈
        int newCircleCount = readResetCfg->getResetList( resetType ).size();
        if ( newCircleCount > 0 )
        {
            // 如果删除的是最后一个圈，则选中新的最后一个圈
            if ( currentCircleIndex >= newCircleCount )
            {
                ui->listWidget_circle->setCurrentRow( newCircleCount - 1 );
            }
            else
            {
                // 否则选中原来位置的圈
                ui->listWidget_circle->setCurrentRow( currentCircleIndex );
            }
        }
    }
}

// 添加指令按钮点击槽函数
void ParaSetForm::onInsAddBtnClicked()
{
    // 获取当前复位类型和圈索引
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    int circleIndex    = ui->listWidget_circle->currentRow();

    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;  // 必须选中圈

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // 获取当前圈的指令数量
    auto& resetList = readResetCfg->getResetList( resetType );
    if ( circleIndex >= resetList.size() )
        return;

    int currentInstructionCount = resetList[ circleIndex ].size();

    // 如果已经有12条指令，不再添加
    if ( currentInstructionCount >= 12 )
    {
        QMessageBox::warning( this, "警告", "每圈最多只能添加12条指令！" );
        return;
    }

    // 创建新指令
    ReadResetConfig::SockResetActionStruct newAction;
    newAction.position = 0;
    newAction.type     = 0;  // 默认为气阀
    newAction.id       = 1;  // 默认ID为1
    newAction.value    = 0;  // 默认值为0（出）

    // 添加新指令
    readResetCfg->addAction( circleIndex, newAction, resetType );

    // 刷新显示
    showResetInstructions( circleIndex );
}

// 删除指令按钮点击槽函数
void ParaSetForm::onInsDeleteBtnClicked()
{
    // 获取当前复位类型和圈索引
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    int circleIndex    = ui->listWidget_circle->currentRow();

    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;  // 必须选中圈

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // 获取当前选中的指令索引
    if ( currentResetInstructionIndex < 0 )
        return;  // 如果没有选中指令，不处理

    // 获取当前圈的指令数量
    auto& resetList = readResetCfg->getResetList( resetType );
    if ( circleIndex >= resetList.size() )
        return;

    // 确保选中的指令索引有效
    if ( currentResetInstructionIndex >= resetList[ circleIndex ].size() )
        return;

    // 弹出确认对话框
    QMessageBox msgBox;
    msgBox.setWindowTitle( "确认删除" );
    msgBox.setText( QString( "确定要删除第%1条指令吗？" ).arg( currentResetInstructionIndex + 1 ) );
    msgBox.setStandardButtons( QMessageBox::Yes | QMessageBox::No );
    msgBox.setDefaultButton( QMessageBox::No );

    // 如果用户确认删除
    if ( msgBox.exec() == QMessageBox::Yes )
    {
        // 直接使用指令索引删除
        readResetCfg->removeAction( circleIndex, currentResetInstructionIndex, resetType );

        // 刷新显示
        showResetInstructions( circleIndex );
    }
}

// 清空本圈指令按钮点击槽函数
void ParaSetForm::onInsClearBtnClicked()
{
    // 获取当前复位类型和圈索引
    int resetTypeIndex = ui->listWidget_reset_type->currentRow();
    int circleIndex    = ui->listWidget_circle->currentRow();

    if ( circleIndex < 0 || readResetCfg == nullptr )
        return;  // 必须选中圈

    ReadResetConfig::ResetType resetType = ( resetTypeIndex == 0 ) ? ReadResetConfig::SOCK_RESET : ReadResetConfig::GATHER_RESET;

    // 弹出确认对话框
    QMessageBox msgBox;
    msgBox.setWindowTitle( "确认清空" );
    msgBox.setText( QString( "确定要清空第%1圈的所有指令吗？" ).arg( circleIndex + 1 ) );
    msgBox.setStandardButtons( QMessageBox::Yes | QMessageBox::No );
    msgBox.setDefaultButton( QMessageBox::No );

    // 如果用户确认清空
    if ( msgBox.exec() == QMessageBox::Yes )
    {
        // 清空当前圈的所有指令
        auto& resetList = readResetCfg->getResetList( resetType );
        if ( circleIndex < resetList.size() )
        {
            resetList[ circleIndex ].clear();

            // 保存配置
            readResetCfg->saveConfig();

            // 刷新显示
            showResetInstructions( circleIndex );
        }
    }
}
