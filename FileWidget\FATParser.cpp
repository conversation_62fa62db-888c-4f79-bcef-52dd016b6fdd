#include "FATParser.h"
#include <QDebug>
#include <QFile>
#include <fstream>
#include <iostream>
#include <stdio.h>
using namespace std;
FATParser::FATParser() {}

FATParser::~FATParser() {}

QSharedPointer< FATParser::NeedleValveDataBase > FATParser::GetActionDataBase( char* buffer )
{
    if ( buffer == Q_NULLPTR )
    {
        return Q_NULLPTR;
    }
    QSharedPointer< FATParser::NeedleValveDataBase > db = QSharedPointer< FATParser::NeedleValveDataBase >( new NeedleValveDataBase );
    /*mind logo*/
    memcpy( db->mind_logo, buffer, 0x48 );
    /*选针个数*/
    db->needle_cnt = *( buffer + 0x48 );
    /*动作个数*/
    db->action_cnt = *( buffer + 0x4a );
    /*申请选针、动作库内存*/
    // db->Needle_data = new NeedleData[db->xuanzhen_cnt];
    // db->action_data = new ActionStruct[db->action_cnt];
    // if (db->action_data == Q_NULLPTR || db->Needle_data == Q_NULLPTR)
    //{
    //	return Q_NULLPTR;
    // }
    /*读取选针库数据,0x400处开始*/
    quint16 xuanzhen_offset = 0x400;
    for ( int index = 0; index < db->needle_cnt; index++ )
    {
        char* ptr = buffer + index * 512 + xuanzhen_offset;
        // memcpy((char*)db->Needle_data[index], ptr, MachinNeedleCnt / 8);
        memcpy( ( char* )&db->Needle_data[ index ][ 0 ], ptr, MachinNeedleCnt / 8 );
    }
    /**读取动作库数据，每236byte表示一个动作，0x4400开始*/
    quint16 action_offset = 0x4400;
    for ( int index = 0; index < db->action_cnt; index++ )
    {
        char* ptr = buffer + index * 236 + action_offset;
        // memcpy((char*)&db->action_data[index], ptr, 236);
        memcpy( ( char* )&db->action_data[ index ], ptr, 236 );
    }
    return db;
}

QSharedPointer< FATParser::DensityData > FATParser::GetDensityData( char* buffer )
{
    if ( buffer == Q_NULLPTR )
    {
        return Q_NULLPTR;
    }
    QSharedPointer< FATParser::DensityData > Density = QSharedPointer< FATParser::DensityData >( new DensityData );
    /*mind logo*/
    memcpy( ( char* )Density.data(), buffer, sizeof( FATParser::DensityData ) );
    return Density;
}

FATParser::MachineStepVector FATParser::GetMachineStepMsg( char* buffer, quint32& offset_size )
{
    FATParser::MachineStepVector step_vec;
    if ( buffer == Q_NULLPTR )
        return step_vec;
    quint16 step_num    = 0;
    quint32 step_offset = 0;
    step_num            = *( buffer + step_offset );
    while ( step_num < 16 )
    {
        QSharedPointer< SockMachineStepMsg > step_msg = QSharedPointer< SockMachineStepMsg >( new SockMachineStepMsg );
        /*获取主程序参数*/
        memcpy( ( char* )&step_msg->main_param, buffer + step_offset, sizeof( MainProgamParam ) );
        /*获取动作数量*/
        step_msg->action_cnt = *( buffer + step_offset + sizeof( MainProgamParam ) );
        /*申请动作信息数据内存*/
        step_msg->cy_action_msg = new CYActionStruct[ step_msg->action_cnt ];
        /*拷贝动作信息数据*/
        memcpy( ( char* )step_msg->cy_action_msg, buffer + step_offset + sizeof( MainProgamParam ) + sizeof( quint16 ), ( step_msg->action_cnt * sizeof( CYActionStruct ) ) );
        /*计算此次步骤的偏移量*/
        step_offset += ( sizeof( MainProgamParam ) + sizeof( quint16 ) + step_msg->action_cnt * sizeof( CYActionStruct ) );
        /*更新step_num的值*/
        step_num = ( quint8 ) * ( buffer + step_offset );
        /*将步骤加到向量中*/
        step_vec.append( step_msg );
        //        qDebug() << step_num;
    }
    offset_size = step_offset;
    return step_vec;
}

QSharedPointer< FATParser::PatternData > FATParser::GetPatternData( char* buffer, FATDecode::FatHead& head, quint32 width_offset )
{
    QSharedPointer< FATParser::PatternData > pattern_data = QSharedPointer< FATParser::PatternData >( new PatternData );
    char*                                    ptr          = buffer;
    ptr += width_offset;
    pattern_data->width  = *( ( quint32* )( ptr ) );
    pattern_data->height = *( ( quint32* )ptr + 1 );
    ptr                  = Q_NULLPTR;
    quint32 offset       = head.ThirdDB - pattern_data->width * pattern_data->height * 4;
    // pattern_data->pattern_data = new quint32[pattern_data->width *
    // pattern_data->height];
    memcpy( ( char* )&pattern_data->pattern_data[ 0 ][ 0 ], buffer + offset, pattern_data->width * pattern_data->height * sizeof( quint32 ) );
    return pattern_data;
}

QSharedPointer< FATParser::PatternExtraData > FATParser::GetPatternExtraData( char* buffer, FATDecode::FatHead& head )
{
    QSharedPointer< FATParser::PatternExtraData > pattern_data = QSharedPointer< FATParser::PatternExtraData >( new PatternExtraData );
    // 花型循环数据为SecondDb+0xe8
    quint32 offset = head.SecondDB + 0xe8;
    memcpy( ( char* )&pattern_data->flower_loop, buffer + offset, 10 * sizeof( FATParser::FLowerAndLaMaoLoop ) );
    // 拉毛循环数据为SecondDb+0x234
    offset = head.SecondDB + 0x234;
    memcpy( ( char* )&pattern_data->lamao_loop, buffer + offset, 10 * sizeof( FATParser::FLowerAndLaMaoLoop ) );
    // 主梭配置数据从SecondDB+0xC8开始
    offset = head.SecondDB + 0xc8;
    memcpy( ( char* )&pattern_data->zhusuo, buffer + offset, 7 * sizeof( FATParser::Zhusuo ) );
    // 主梭提前量、重叠量位于SecondDB+0x230位置
    offset = head.SecondDB + 0x230;
    memcpy( ( char* )&pattern_data->zhusuo_num_cfg, buffer + offset, sizeof( FATParser::ZhusoNumCfg ) );
    // 主梭2的位置为SecondDB+0xe0开始
    offset = head.SecondDB + 0xe0;
    memcpy( ( char* )&pattern_data->zhusuo2, buffer + offset, 4 * sizeof( FATParser::Zhusuo2 ) );
    return pattern_data;
}

FATParser::FengtouStepVector FATParser::GetFengtouData( char* buffer, quint32 start_pos, quint32 end_pos )
{
    /*计算有多少缝头程序*/
    // quint32 fengtou_program_cnt = size / sizeof(FengtouStruct);
    char*                        ptr;
    FATParser::FengtouStepVector vec;
    quint32                      size = sizeof( FengtouStepStruct );
    for ( quint32 offset = start_pos; offset < end_pos; )
    {
        QSharedPointer< FengtouStruct > step = QSharedPointer< FengtouStruct >( new FengtouStruct );
        ptr                                  = buffer + offset;
        /*计算该缝头程序有多少step*/
        step->step_cnt = *( ( quint16* )( ptr + 126 ) );
        /*申请内存*/
        step->step           = new FengtouStepStruct[ step->step_cnt ];
        quint32 fengtou_size = 128 + step->step_cnt * size;
        if ( step->step_cnt != 0 )
            memcpy( ( char* )( step->step ), ( char* )ptr + 128, step->step_cnt * size );
        offset += fengtou_size;
        vec.append( step );
        //        qDebug() << step->step_cnt;
    }
    return vec;
}

int FATParser::Parser( QString filePath, FATDataMain* fat_main )
{
    QFile file( filePath );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "Open File Failed!\n";
        return -1;
    }

    QByteArray data   = file.readAll();
    char*      buffer = data.data();

    FATDecode::FatHead head;
    FATDecode          fat_decode;
    /*文件解密*/
    fat_decode.Decode( buffer, &head, data.size() );
    /*读取选针、动作库数据*/
    quint32 xuanzhen_offset  = 0x400;
    fat_main->NeedleActionDB = GetActionDataBase( buffer + xuanzhen_offset );
    /*读取密度数据*/
    quint32 Density_offset = 0xA430;
    fat_main->DentisyData  = GetDensityData( buffer + Density_offset );
    /*获取链条步骤数据*/
    quint32 step_offset     = 0;
    quint32 liantiao_offset = Density_offset + 0x800;
    fat_main->StepVector    = GetMachineStepMsg( buffer + liantiao_offset, step_offset );
    /*获取花型数据*/
    // quint32 pattern_offset = liantiao_offset + step_offset;
    quint32 pattern_offset = liantiao_offset + step_offset;
    fat_main->PatternData  = GetPatternData( buffer, head, pattern_offset );
    /*获取缝头动作数据*/
    fat_main->FengtouVector = GetFengtouData( buffer, head.ThirdDB + 0x400, data.size() );
    /* 获取花型的额外数据*/
    fat_main->PatternExtraData = GetPatternExtraData( buffer, head );

    file.close();
    return 1;
}
