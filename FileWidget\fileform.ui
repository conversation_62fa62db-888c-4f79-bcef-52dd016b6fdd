<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileForm</class>
 <widget class="QWidget" name="FileForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QStackedWidget" name="File_stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>50</y>
     <width>1024</width>
     <height>551</height>
    </rect>
   </property>
   <property name="currentIndex">
    <number>4</number>
   </property>
   <widget class="QWidget" name="page">
    <widget class="QTableView" name="File_tableView">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>60</y>
       <width>841</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
	selection-background-color: #007aff;
	selection-color: white;                            
}</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_CurrentFile">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>24</y>
       <width>111</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>当前选中：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="currentFileName">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>150</x>
       <y>20</y>
       <width>571</width>
       <height>31</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(255, 255, 255);
color: #FC5531;
border-radius: 5px;
border: 1px solid #000;</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btnOK">
     <property name="geometry">
      <rect>
       <x>740</x>
       <y>20</y>
       <width>89</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #FC5531;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>确定</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_delete">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>70</y>
       <width>131</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #e64340;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #ce3c39;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>删除文件</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_clone">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>140</y>
       <width>131</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #0062cc;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>复制文件</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_rename">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>210</y>
       <width>131</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>重命名</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_importUSB">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>280</y>
       <width>131</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #e64340;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>USB导入</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_2">
    <widget class="QGroupBox" name="gbox_main">
     <property name="geometry">
      <rect>
       <x>170</x>
       <y>0</y>
       <width>351</width>
       <height>401</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>部位参数</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <property name="spacing">
         <number>1</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label">
            <property name="text">
             <string>部位：             </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_StepName">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>尺寸：             </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_size">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_18">
            <property name="text">
             <string>     速度：     </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_speed">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>橡筋电机：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_xiangjin1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_xiangjin2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_xiangjin3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>橡筋KTF1：   </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_ktf11">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_ktf12">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_ktf13">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>橡筋KTF2：   </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_ktf21">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_ktf22">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_ktf23">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_6">
            <property name="text">
             <string>针筒密度：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_zhengtong1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_zhengtong2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_zhengtong3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_7">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_7">
            <property name="text">
             <string>沉降密度：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_chenjiang1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_chenjiang2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_chenjiang3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_8">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_8">
            <property name="text">
             <string>生克密度：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_shengke1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_shengke2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_shengke3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_9">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_9">
            <property name="text">
             <string>左棱角密度：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_leftlj1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_leftlj2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_leftlj3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_10">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_10">
            <property name="text">
             <string>右棱角密度：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_rightlj1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_rightlj2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_rightlj3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_12">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_11">
            <property name="text">
             <string>花型配置：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_huaixing">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_13">
          <item>
           <widget class="QLabel" name="label_16">
            <property name="text">
             <string>拉毛配置：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_lamao">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_11">
          <property name="spacing">
           <number>1</number>
          </property>
          <item>
           <widget class="QLabel" name="label_17">
            <property name="text">
             <string>袜跟配置：    </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_wagen1">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_wagen2">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MyLineEdit" name="le_wagen3">
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
    <widget class="QGroupBox" name="gbox_instructions">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>0</y>
       <width>331</width>
       <height>401</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>指令</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_actions">
     <property name="geometry">
      <rect>
       <x>170</x>
       <y>400</y>
       <width>691</width>
       <height>141</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>圈控制</string>
     </property>
    </widget>
    <widget class="QListWidget" name="listWidget_Stepname">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>0</y>
       <width>151</width>
       <height>541</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_ActionNextPage">
     <property name="geometry">
      <rect>
       <x>900</x>
       <y>470</y>
       <width>89</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #e64340;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #ce3c39;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>下一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_ActionPrevtPage">
     <property name="geometry">
      <rect>
       <x>900</x>
       <y>510</y>
       <width>89</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #0062cc;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>上一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_Density">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>420</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>密度设置</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toVAction">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>370</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>进入动作</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_FTCommandAdd">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>70</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>指令插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_FTCommandDelete">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>120</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>指令删除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_FTCommandClear">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>170</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>指令清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionSAdd">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>220</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>圈控制插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionSDelete">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>270</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>圈控制删除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionSClear">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>320</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>圈控制清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_PatternSave">
     <property name="geometry">
      <rect>
       <x>880</x>
       <y>20</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>保存</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_3">
    <widget class="QGroupBox" name="gbox_fengtouStepName">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>10</y>
       <width>161</width>
       <height>81</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>组合名称</string>
     </property>
     <widget class="MyLineEdit" name="le_fengtouStepName">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>40</y>
        <width>141</width>
        <height>31</height>
       </rect>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QGroupBox" name="gbox_fengtouBase">
     <property name="geometry">
      <rect>
       <x>180</x>
       <y>10</y>
       <width>331</width>
       <height>251</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>基本信息</string>
     </property>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>30</y>
        <width>303</width>
        <height>205</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_14">
         <item>
          <widget class="QLabel" name="label_12">
           <property name="text">
            <string>动作序号: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_ftStepId">
           <property name="enabled">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_16">
         <item>
          <widget class="QLabel" name="label_13">
           <property name="text">
            <string>动作名称: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_ftStepName"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_17">
         <item>
          <widget class="QLabel" name="label_14">
           <property name="text">
            <string>动作延时: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ft_WarnDelay"/>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ftActionDelay"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_18">
         <item>
          <widget class="QLabel" name="label_15">
           <property name="text">
            <string>动作命令: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ftACommand"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_19">
         <item>
          <widget class="QLabel" name="label_19">
           <property name="text">
            <string>复位动作: </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MyLineEdit" name="le_ftResetAction"/>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QGroupBox" name="gbox_MotorList">
     <property name="geometry">
      <rect>
       <x>180</x>
       <y>260</y>
       <width>331</width>
       <height>281</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>电机</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_valveList">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>10</y>
       <width>331</width>
       <height>531</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>指令与气阀</string>
     </property>
    </widget>
    <widget class="QListWidget" name="lw_FtStepList">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>90</y>
       <width>161</width>
       <height>451</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionAdd">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>180</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionDelete">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>240</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作删除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_actionClear">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>300</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_motorAdd">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>370</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>电机插入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_motorDelete">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>430</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>电机删除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_motorClear">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>490</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>电机清空</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_FengtouSave">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>30</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>保存</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_4">
    <widget class="QGroupBox" name="gbox_VActions">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>0</y>
       <width>821</width>
       <height>241</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>气阀动作库</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gbox_NeedleView">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>240</y>
       <width>821</width>
       <height>301</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
     <property name="title">
      <string>选针</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedlePrev">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>380</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #0062cc;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>选针上一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleNext">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>450</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #e64340;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #ce3c39;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>选针下一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_ValveNext">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>100</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #e64340;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #ce3c39;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>气阀下一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_ValvePrev">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>30</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #0062cc;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>气阀上一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toChainEdit">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>170</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>链条编辑</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toNeedleEdit">
     <property name="geometry">
      <rect>
       <x>860</x>
       <y>310</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>选针资料</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_5">
    <widget class="QGroupBox" name="gbox_NeedleStorage">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>180</y>
       <width>721</width>
       <height>361</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="title">
      <string>选针库</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleListPrev">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>410</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #007aff;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #0062cc;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>上一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleListNext">
     <property name="geometry">
      <rect>
       <x>750</x>
       <y>410</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #e64340;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #ce3c39;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>下一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toValveAction">
     <property name="geometry">
      <rect>
       <x>750</x>
       <y>480</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>动作编辑</string>
     </property>
    </widget>
    <widget class="QScrollArea" name="scrollArea_NeedleItem">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>40</y>
       <width>1001</width>
       <height>131</height>
      </rect>
     </property>
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>999</width>
        <height>129</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
     </widget>
    </widget>
    <widget class="QLabel" name="label_NeedleItemIndex">
     <property name="geometry">
      <rect>
       <x>310</x>
       <y>10</y>
       <width>321</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>未选中</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleClear">
     <property name="geometry">
      <rect>
       <x>750</x>
       <y>200</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>块清除</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleFill">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>200</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>块填充</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleCopy">
     <property name="geometry">
      <rect>
       <x>750</x>
       <y>270</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>块复制</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedPaste">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>270</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>块粘贴</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleCopyItem">
     <property name="geometry">
      <rect>
       <x>750</x>
       <y>340</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>复制</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_NeedleSave">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>340</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>保存</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_6">
    <widget class="QScrollArea" name="scrollAreaImage">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>10</y>
       <width>600</width>
       <height>500</height>
      </rect>
     </property>
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents_2">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>598</width>
        <height>498</height>
       </rect>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern0">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>40</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>添砂视图</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern1">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>110</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>主梭视图</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern2">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>180</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>速度视图</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern3">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>250</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>橡筋视图</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern4">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>320</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>拉毛视图</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern5">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>390</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>袜跟拉毛</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_pattern6">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>460</y>
       <width>121</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>打松三角</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternScale0">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>250</y>
       <width>71</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>×1</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternScale1">
     <property name="geometry">
      <rect>
       <x>850</x>
       <y>250</y>
       <width>71</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>×2</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternScale2">
     <property name="geometry">
      <rect>
       <x>930</x>
       <y>250</y>
       <width>71</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>×4</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternScale3">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>320</y>
       <width>71</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>×8</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternScale4">
     <property name="geometry">
      <rect>
       <x>850</x>
       <y>320</y>
       <width>71</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>×12</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternScale5">
     <property name="geometry">
      <rect>
       <x>930</x>
       <y>320</y>
       <width>71</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>×16</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_patternPos">
     <property name="geometry">
      <rect>
       <x>630</x>
       <y>10</y>
       <width>121</width>
       <height>17</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>X:0 Y:0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyWidget" name="widget_patternColor" native="true">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>20</y>
       <width>231</width>
       <height>211</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>7</pointsize>
      </font>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternZhusuo">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>390</y>
       <width>111</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>主梭配置</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternLoop">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>390</y>
       <width>111</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>花型循环</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternSave">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>460</y>
       <width>111</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>保存</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_patternMixcolor">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>460</y>
       <width>111</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
     </property>
     <property name="text">
      <string>混合色</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_7">
    <widget class="QTableView" name="File_tableView_this">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>20</y>
       <width>461</width>
       <height>521</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
	selection-background-color: #007aff;
	selection-color: white;                            
}</string>
     </property>
    </widget>
    <widget class="QTableView" name="File_tableView_usb">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>20</y>
       <width>461</width>
       <height>521</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
	selection-background-color: #007aff;
	selection-color: white;                            
}</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toUSB">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>420</y>
       <width>61</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>&gt;&gt;</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toThis">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>110</y>
       <width>61</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>&lt;&lt;</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QPushButton" name="FileFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>3</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="File_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>290</x>
     <y>10</y>
     <width>441</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>文件管理</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>2</width>
     <height>2</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_15"/>
  </widget>
  <widget class="QPushButton" name="FileForm_pbtn_pattern">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>5</y>
     <width>40</width>
     <height>40</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/logo_graph.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="FileForm_pbtn_chain">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>5</y>
     <width>40</width>
     <height>40</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/chain.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="FileForm_pbtn_fengtou">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>5</y>
     <width>40</width>
     <height>40</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/fengtou.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <zorder>layoutWidget</zorder>
  <zorder>FileFormHome_btn</zorder>
  <zorder>File_Tiltle_label</zorder>
  <zorder>File_stackedWidget</zorder>
  <zorder>FileForm_pbtn_pattern</zorder>
  <zorder>FileForm_pbtn_chain</zorder>
  <zorder>FileForm_pbtn_fengtou</zorder>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
  <customwidget>
   <class>MyWidget</class>
   <extends>QWidget</extends>
   <header>CommonWidget/MyWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
