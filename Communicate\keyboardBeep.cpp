#include "keyboardBeep.h"

keyboardBeep::keyboardBeep(QObject *parent) : QObject(parent)
{
    // 初始化时确保蜂鸣器关闭
    setBeep(false);
    qDebug() << "蜂鸣器初始化完成";
}

keyboardBeep::~keyboardBeep()
{
    // 析构时确保蜂鸣器关闭
    setBeep(false);
}

bool keyboardBeep::setBeep(bool on)
{
    // 高电平触发蜂鸣器，写入1开启，写入0关闭
    return writeToFile(BEEP_BRIGHTNESS_PATH, on ? "1" : "0");
}

bool keyboardBeep::beepOnce(int durationMs)
{
    // 蜂鸣器短鸣一声
    if (!setBeep(true)) {
        return false;
    }
    
    // 延时指定时间
    QThread::msleep(durationMs);
    
    // 关闭蜂鸣器
    return setBeep(false);
}

bool keyboardBeep::beepPattern(int onTimeMs, int offTimeMs, int count)
{
    // 按照指定模式鸣叫
    for (int i = 0; i < count; i++) {
        // 打开蜂鸣器
        if (!setBeep(true)) {
            return false;
        }
        
        // 延时开启时间
        QThread::msleep(onTimeMs);
        
        // 关闭蜂鸣器
        if (!setBeep(false)) {
            return false;
        }
        
        // 如果不是最后一次，则延时关闭时间
        if (i < count - 1) {
            QThread::msleep(offTimeMs);
        }
    }
    
    return true;
}

bool keyboardBeep::writeToFile(const QString &path, const QString &data)
{
    QFile file(path);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件:" << path << "，错误:" << file.errorString();
        return false;
    }
    
    qint64 bytesWritten = file.write(data.toUtf8());
    file.close();
    
    if (bytesWritten != data.toUtf8().length()) {
        qDebug() << "写入文件失败:" << path;
        return false;
    }
    
    return true;
}
