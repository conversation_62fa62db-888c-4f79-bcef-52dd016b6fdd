#ifndef DEFINES_H
#define DEFINES_H

#include "Config/ConfigManager.h"
#include "Config/ReadOnekeyConfig.h"
#include "Config/readCfg.h"
#include "Config/readResetConfig.h"
#include "Config/readWarnConfig.h"
#include "Config/readlinkconfig.h"
#include "Config/readmachinefileconfig.h"
#include "Config/readtestconfig.h"
#include <QList>

#pragma pack( 1 )
struct WarnLogItem
{
    QString time;
    quint8  type;
    quint16 id;
};
struct NetWorkConfig
{
    bool    enable;
    QString machineId;
    quint8  machineIP[ 4 ];
    quint8  mask[ 4 ];
    quint8  netgate[ 4 ];
    quint8  servIP[ 4 ];
    int     servPort;
    QString factoryName;
    QString protocal;
    QString version;
};

enum class MachineState
{
    Idle,
    Running,
    Paused,
    Stopped,
    Error,
    Manual,       // 手动
    Auto,         // 自动
    Test,         // 测试
    SockReset,    // 织袜复位
    MachineReset  // 机器复位
};

// 保存所有在8个功能之间相互传递的数据
struct MainWidgetData
{
    MachineState machineState;

    ReadCfg*               readCfg               = nullptr;
    ReadWarnConfig*        readWarnConfig        = nullptr;
    ReadMachineFileConfig* readMachineFileConfig = nullptr;
    ReadLinkConfig*        readLinkCfg           = nullptr;
    ReadTestConfig*        readTestConfig        = nullptr;
    ReadOnekeyConfig*      readOneKeyConfig      = nullptr;
    ReadResetConfig*       readResetConfig       = nullptr;
    ConfigManager*         configManager         = nullptr;

    // 报警日志,程序报警也需要产生，并在日志页浏览
    QVector< WarnLogItem > warnLogList;

    // 网络设置
    NetWorkConfig networkConfig;
};

#pragma pack()

#endif  // DEFINES_H
