#ifndef READWARNCONFIG_H
#define READWARNCONFIG_H

#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMessageBox>

class ReadWarnConfig
{
public:
    ReadWarnConfig();
    ~ReadWarnConfig();

#pragma pack( 1 )
    struct WarnItem
    {
        quint16 id;
        QString name;
        QString desc;        //处理方法
        quint16 resourceId;  //输入源
        QString img;         // image Addr
    };
    struct WarnCategory
    {
        quint8                    id;
        QString                   name;
        QMap< quint16, WarnItem > items;
    };

#pragma pack()

    void parseConfig( QString fileAddr );

    QMap< int, WarnCategory >* getList()
    {
        return &_list;
    }

private:
    QMap< int, WarnCategory > _list;
};

#endif  // READWARNCONFIG_H
