﻿#ifndef NUMBERINPUTFORM_H
#define NUMBERINPUTFORM_H

#include <QWidget>

namespace Ui
{
class NumberInputForm;
}

class NumberInputForm : public QWidget
{
    Q_OBJECT

public:
    explicit NumberInputForm( QWidget* parent = nullptr, QString title = "请输入数值", long minValue = -2147483648, long maxValue = 2147483647 );
    ~NumberInputForm();
    QString getInputText();
    void    setInputText( QString str );
    void    setValueLimit( long minValue, long maxValue );

protected:
    void showEvent( QShowEvent* event ) override;
    void closeEvent( QCloseEvent* event ) override;

signals:
    void InputFinished( QString text );
    void finished();

private:
    Ui::NumberInputForm* ui;
    void                 onBtnClicked();
    long                 _min_Value;
    long                 _max_Value;
};

#endif  // NUMBERINPUTFORM_H
