#include "mqttclient.h"
#include <QMessageBox>
#include <QtCore/QDateTime>

mqttClient::mqttClient( QObject* parent, QString hostAddr, MachineInfo* machineInfo )
{
    this->machineInfo = machineInfo;

    m_client = new QMqttClient( parent );
    m_client->setHostname( hostAddr );
    m_client->setPort( 1883 );
    m_client->connectToHost();

    connect( this->m_client, &QMqttClient::disconnected, this, &mqttClient::onDisconnected );
    connect( this->m_client, &QMqttClient::messageReceived, this, &mqttClient::onMessageReceived );
    connect( this->m_client, &QMqttClient::stateChanged, this, [&]() {
        const QString content = QDateTime::currentDateTime().toString() + QLatin1String( ": State Change" ) + QString::number( m_client->state() ) + QLatin1Char( '\n' );
        qDebug() << content;

        if ( m_client->state() == QMqttClient::ClientState::Connected )
        {
            // 连接成功以后再订阅，否则会报错
            QString topic        = "arx";
            auto    subscription = m_client->subscribe( topic, 0 );
            if ( !subscription )
            {
                QMessageBox::critical( nullptr, QString::fromLocal8Bit( "错误提示" ), QString::fromLocal8Bit( "无法订阅MQTT主题，请确认连接是否正常" ) );
                return;
            }
        }
    } );
}

mqttClient::~mqttClient()
{
    delete m_client;
}

void mqttClient::onDisconnected()
{
    //  断了重连
    this->m_client->connectToHost();
}

void mqttClient::onMessageReceived( const QByteArray& message, const QMqttTopicName& topic )
{
    const QString content = QDateTime::currentDateTime().toString() + QLatin1String( " Received Topic: " ) + topic.name() + QLatin1String( " Message: " ) + message + QLatin1Char( '\n' );
    qDebug() << content;

    if ( topic.name() != "arx" )
        return;

    Handler( message );
}

void mqttClient::Handler( const QByteArray& message )
{
    // QJsonParseError类用于在JSON解析期间报告错误。
    QJsonParseError jsonError;
    // 将json解析为UTF-8编码的json文档，并从中创建一个QJsonDocument。
    // 如果解析成功，返回QJsonDocument对象，否则返回null
    QJsonDocument doc = QJsonDocument::fromJson( message, &jsonError );
    // 判断是否解析失败
    if ( jsonError.error != QJsonParseError::NoError && !doc.isNull() )
    {
        qDebug() << "Json格式错误！" << jsonError.error;
        return;
    }

    QJsonObject rootObj  = doc.object();
    QJsonValue  deviceID = rootObj.value( "deviceID" );
    qDebug() << "deviceID = " << deviceID.toString();
    if ( deviceID != this->machineInfo->deviceId )
    {
        qDebug() << "Not My Message";
        return;
    }

    QJsonValue opcode     = rootObj.value( "opcode" );
    QString    opcode_str = opcode.toString();
    qDebug() << "opcode = " << opcode.toString();

    UpperMsgMaker msgMaker;
    // 请求产量消息
    if ( opcode_str == "01001" )
    {
        QString retMsg = msgMaker.MakeProductInfoMsg( this->machineInfo );
        qDebug() << retMsg;
        // TODO 通过MQTT转发
        QString topic = "ar";
        if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
            QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
    }
    // 请求机台信息
    else if ( opcode_str == "01002" )
    {
        QString retMsg = msgMaker.MakeMachineInfoMsg( this->machineInfo );
        qDebug() << retMsg;
        // TODO 通过MQTT转发
        QString topic = "ar";
        if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
            QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
    }
    // 请求运行状态信息
    else if ( opcode_str == "01003" )
    {
        QString retMsg = msgMaker.MakeRunningStatusMsg( this->machineInfo );
        qDebug() << retMsg;
        // TODO 通过MQTT转发
        QString topic = "ar";
        if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
            QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
    }
    // 请求报警信息
    else if ( opcode_str == "01004" )
    {
        QString retMsg = msgMaker.MakeAlarmMsg( this->machineInfo );
        qDebug() << retMsg;
        // TODO 通过MQTT转发
        QString topic = "ar";
        if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
            QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
    }
    // 请求功耗信息
    else if ( opcode_str == "01005" )
    {
        QString retMsg = msgMaker.MakePowerConsumptionMsg( this->machineInfo );
        qDebug() << retMsg;
        // TODO 通过MQTT转发
        QString topic = "ar";
        if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
            QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
    }
    // 文件鉴权请求
    else if ( opcode_str == "02001" )
    {
        // 解析文件名和权限类型
        QJsonValue payloadValue = rootObj.value( "payload" );
        if ( payloadValue.type() == QJsonValue::Object )
        {
            // 转换为QJsonObject类型
            QJsonObject payloadObj    = payloadValue.toObject();
            QJsonValue  typeValue     = payloadObj.value( "d1" );
            QJsonValue  filenameValue = payloadObj.value( "d2" );
            qDebug() << typeValue << filenameValue;
            // TODO 检查文件权限

            QString retMsg = msgMaker.MakeOprationResultMsg( machineInfo, true, 200, "" );
            qDebug() << retMsg;
            // TODO 通过MQTT转发
            QString topic = "ar";
            if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
                QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
        }
    }
    // 文件上传请求
    else if ( opcode_str == "02002" )
    {
        // 解析文件名、长度和内容
        QJsonValue payloadValue = rootObj.value( "payload" );
        if ( payloadValue.type() == QJsonValue::Object )
        {
            // 转换为QJsonObject类型
            QJsonObject payloadObj      = payloadValue.toObject();
            QJsonValue  filenameValue   = payloadObj.value( "d2" );
            QJsonValue  filelengthValue = payloadObj.value( "d3" );
            qDebug() << filenameValue << filelengthValue;
            // TODO 检查文件权限和文件长度是否合规，合规则保存，不合规则返回失败

            QString retMsg = msgMaker.MakeOprationResultMsg( machineInfo, true, 200, "" );
            qDebug() << retMsg;
            // TODO 通过MQTT转发
            QString topic = "ar";
            if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
                QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
        }
    }
    // 文件下载请求
    else if ( opcode_str == "02003" )
    {
        // 解析文件名
        QJsonValue payloadValue = rootObj.value( "payload" );
        if ( payloadValue.type() == QJsonValue::Object )
        {
            // 转换为QJsonObject类型
            QJsonObject payloadObj    = payloadValue.toObject();
            QJsonValue  filenameValue = payloadObj.value( "d2" );
            qDebug() << filenameValue;
            // TODO 检查文件下载权限，允许打包文件并提供下载，不允许则返回失败

            QString retMsg = msgMaker.MakeOprationResultMsg( machineInfo, true, 200, "" );
            qDebug() << retMsg;
            // TODO 通过MQTT转发
            QString topic = "ar";
            if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
                QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
        }
    }
    // 文件删除请求
    else if ( opcode_str == "02004" )
    {
        // 解析文件名
        QJsonValue payloadValue = rootObj.value( "payload" );
        if ( payloadValue.type() == QJsonValue::Object )
        {
            // 转换为QJsonObject类型
            QJsonObject payloadObj    = payloadValue.toObject();
            QJsonValue  filenameValue = payloadObj.value( "d2" );
            qDebug() << filenameValue;
            // TODO 检查文件删除权限，允许则删除文件并返回成功，不允许则返回失败

            QString retMsg = msgMaker.MakeOprationResultMsg( machineInfo, true, 200, "" );
            qDebug() << retMsg;
            // TODO 通过MQTT转发
            QString topic = "ar";
            if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
                QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
        }
    }
    // 文件列表请求
    else if ( opcode_str == "02005" )
    {
        // 解析文件名或文件路径
        QJsonValue payloadValue = rootObj.value( "payload" );
        if ( payloadValue.type() == QJsonValue::Object )
        {
            // 转换为QJsonObject类型
            QJsonObject payloadObj    = payloadValue.toObject();
            QJsonValue  filenameValue = payloadObj.value( "d2" );
            qDebug() << filenameValue;
            // TODO 检查文件列表权限，允许则列出文件并返回成功，不允许则返回失败

            QString retMsg = msgMaker.MakeOprationResultMsg( machineInfo, true, 200, "" );
            qDebug() << retMsg;
            // TODO 通过MQTT转发
            QString topic = "ar";
            if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
                QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
        }
    }
    // 联通测试
    else if ( opcode_str == "03002" )
    {
        // 解析payload消息
        QJsonValue payloadValue = rootObj.value( "payload" );
        QString    retMsg       = msgMaker.MakeConnectionMsg( machineInfo, payloadValue.toString() );
        qDebug() << retMsg;
        // TODO 通过MQTT转发
        QString topic = "ar";
        if ( m_client->publish( topic, retMsg.toUtf8() ) == -1 )
            QMessageBox::critical( nullptr, QLatin1String( "Error" ), QLatin1String( "Could not publish message" ) );
    }
}
