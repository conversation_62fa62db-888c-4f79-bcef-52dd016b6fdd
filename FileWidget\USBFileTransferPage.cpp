﻿#include "fileform.h"
#include "ui_fileform.h"

void FileForm::initUSBTransferPage()
{
    // 按键只能connect1次
    if ( !isUSBTransferPageInited )
    {
        connect( ui->pbtn_toThis, &QPushButton::clicked, this, [ & ]() {
            qDebug() << "currentFATUSBFile" << currentFATUSBFile;
            if ( this->currentFATUSBFile == "" )
            {
                QMessageBox::information( nullptr, "错误提示", "请先选中U盘中的文件" );
                return;
            }
            else
            {
                // 重名文件检查
                for ( const FileInfoItem& item : *FATFileList )
                {
                    if ( item.fileName == currentFATUSBFile )
                    {
                        QMessageBox::information( nullptr, "错误提示", "已存在重名文件" );
                        return;
                    }
                }
                // COPY
                QFile::copy( FATUSBBaseAddr + currentFATUSBFile, FAT_DIR + currentFATUSBFile );
                this->refreshFATFileList( FAT_DIR );
                this->initFATFileThisTableView();
            }
        } );
        connect( ui->pbtn_toUSB, &QPushButton::clicked, this, [ & ]() {
            qDebug() << "currentFATThisFile" << currentFATThisFile;
            if ( this->currentFATThisFile == "" )
            {
                QMessageBox::information( nullptr, "错误提示", "请先选中机器中的文件" );
                return;
            }
            else
            {
                // 重名文件检查
                for ( const FileInfoItem& item : *FATUSBFileList )
                {
                    if ( item.fileName == currentFATThisFile )
                    {
                        QMessageBox::information( nullptr, "错误提示", "已存在重名文件" );
                        return;
                    }
                }
                // COPY
                QFile::copy( FAT_DIR + currentFATThisFile, FATUSBBaseAddr + currentFATThisFile );
                this->refreshFATUSBFileList( FATUSBBaseAddr );
                this->initFATFileUSBTableView();
            }
        } );
    }

    isUSBTransferPageInited = true;

    // 显示两个文件夹内部的FAT文件
    this->refreshFATFileList( FAT_DIR );
    this->initFATFileThisTableView();

    // 检测USB文件夹是否存在
    FATUSBBaseAddr = checkUSBDirExists();
    qDebug() << "usbFileDdir" << FATUSBBaseAddr;
    this->refreshFATUSBFileList( FATUSBBaseAddr );
    this->initFATFileUSBTableView();
}

void FileForm::initFATFileThisTableView()
{
    if ( this->_db_fat_table_this_model == nullptr )
    {
        this->_db_fat_table_this_model = new QStandardItemModel();
        this->ui->File_tableView_this->setModel( this->_db_fat_table_this_model );

        // 单击选中事件
        // 连接 clicked 信号到槽函数
        connect( this->ui->File_tableView_this, &QTableView::doubleClicked, this, [ = ]( const QModelIndex& index ) {
            // 获取点击的行号
            int row = index.row();
            //            qDebug() << "Table" << row;
            if ( row >= 0 )
            {
                // 赋值
                QString    utf8String    = this->_db_fat_table_this_model->data( _db_fat_table_this_model->index( row, 0 ) ).toString();
                QByteArray array         = utf8String.toUtf8();
                this->currentFATThisFile = QString::fromLocal8Bit( array );
            }
        } );
    }
    else
    {
        this->_db_fat_table_this_model->clear();
    }

    // 添加数据
    if ( this->FATFileList != nullptr )
    {
        for ( int i = 0; i < this->FATFileList->length(); i++ )
        {
            QString    string = FATFileList->at( i ).fileName;
            QByteArray array  = string.toLocal8Bit();
            //            qDebug() << array;
            QString utf8String = QString::fromUtf8( array );

            QList< QStandardItem* > add_items;
            add_items << new QStandardItem( utf8String );
            add_items << new QStandardItem( FATFileList->at( i ).fileSize );
            add_items << new QStandardItem( FATFileList->at( i ).lastModified );
            this->_db_fat_table_this_model->appendRow( add_items );
        }
    }

    QStringList table_h_headers;
    table_h_headers << "文件名"
                    << "文件大小"
                    << "上次修改时间";
    this->_db_fat_table_this_model->setHorizontalHeaderLabels( table_h_headers );

    //设置选中模式为选中行
    this->ui->File_tableView_this->setSelectionBehavior( QAbstractItemView::SelectRows );
    // 表格宽度随内容自动扩展
    this->ui->File_tableView_this->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    // 隐藏网格线
    this->ui->File_tableView_this->setShowGrid( false );
    // 启用排序
    this->ui->File_tableView_this->setSortingEnabled( true );
}

void FileForm::refreshFATUSBFileList( QString fileFATbaseAddr )
{
    if ( this->FATUSBFileList == nullptr )
    {
        this->FATUSBFileList = new QList< FileForm::FileInfoItem >();
    }
    else
    {
        this->FATUSBFileList->clear();
    }

    // 遍历文件夹
    QDir        dir( fileFATbaseAddr );
    QStringList filters;
    filters << "*.FAT";  // 只匹配FAT文件
    dir.setNameFilters( filters );
    dir.setFilter( QDir::Files | QDir::NoSymLinks );

    QFileInfoList fileInfoList = dir.entryInfoList();
    for ( const QFileInfo& fileInfo : fileInfoList )
    {
        FileForm::FileInfoItem item;
        item.fileName     = fileInfo.fileName();
        item.fileSize     = QString::number( fileInfo.size() / 2024.0, 'f', 2 ) + " KB";
        item.lastModified = fileInfo.lastModified().toString( "yyyy-MM-dd HH:mm:ss" );
        this->FATUSBFileList->append( item );
    }
}

void FileForm::initFATFileUSBTableView()
{
    if ( this->_db_fat_table_usb_model == nullptr )
    {
        this->_db_fat_table_usb_model = new QStandardItemModel();
        this->ui->File_tableView_usb->setModel( this->_db_fat_table_usb_model );

        // 单击选中事件
        // 连接 clicked 信号到槽函数
        connect( this->ui->File_tableView_usb, &QTableView::doubleClicked, this, [ = ]( const QModelIndex& index ) {
            // 获取点击的行号
            int row = index.row();
            //            qDebug() << "Table" << row;
            if ( row >= 0 )
            {
                // 赋值
                QString    utf8String   = this->_db_fat_table_usb_model->data( _db_fat_table_usb_model->index( row, 0 ) ).toString();
                QByteArray array        = utf8String.toUtf8();
                this->currentFATUSBFile = QString::fromLocal8Bit( array );
            }
        } );
    }
    else
    {
        this->_db_fat_table_usb_model->clear();
    }

    // 添加数据
    if ( this->FATUSBFileList != nullptr )
    {
        for ( int i = 0; i < this->FATUSBFileList->length(); i++ )
        {
            QString    string = FATUSBFileList->at( i ).fileName;
            QByteArray array  = string.toLocal8Bit();
            //            qDebug() << array;
            QString utf8String = QString::fromUtf8( array );

            QList< QStandardItem* > add_items;
            add_items << new QStandardItem( utf8String );
            add_items << new QStandardItem( FATUSBFileList->at( i ).fileSize );
            add_items << new QStandardItem( FATUSBFileList->at( i ).lastModified );
            this->_db_fat_table_usb_model->appendRow( add_items );
        }
    }

    QStringList table_h_headers;
    table_h_headers << "文件名"
                    << "文件大小"
                    << "上次修改时间";
    this->_db_fat_table_usb_model->setHorizontalHeaderLabels( table_h_headers );

    //设置选中模式为选中行
    this->ui->File_tableView_usb->setSelectionBehavior( QAbstractItemView::SelectRows );
    // 表格宽度随内容自动扩展
    this->ui->File_tableView_usb->horizontalHeader()->setSectionResizeMode( QHeaderView::Stretch );
    // 隐藏网格线
    this->ui->File_tableView_usb->setShowGrid( false );
    // 启用排序
    this->ui->File_tableView_usb->setSortingEnabled( true );
}

QString FileForm::checkUSBDirExists()
{
    QString usbDirSt = "/run/media/sd";
    //    QString usbDirSt = "../sd";
    char tag = 'a';

    for ( int i = 0; i < 26; i++ )
    {
        QDir dir( usbDirSt + tag + "/" );
        if ( !dir.exists() )
            break;
        else
        {
            tag++;
        }
    }

    return usbDirSt + ( --tag ) + "/";
}
