
#include "testform.h"
#include "ui_testform.h"
#include <QDebug>

/* 步进点击测试相关初始化 */
void TestForm::StepTestInit()
{
    if ( isStepTestPageInit == false )
    {
        /* 步进电机测试控制按键按键组 */
        this->CreateStepTestCtrlBtnGroup();
        connect( this->StepTestCtrlBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onStepTestCtrlBtnGroupClicked( int ) ) );

        /* 步进电机测试初始化 */
        this->StepTestPageInit();

        isStepTestPageInit = true;
    }
}

/* 创建步进电机测试控制按键组 */
void TestForm::CreateStepTestCtrlBtnGroup()
{
    StepTestCtrlBtnGroup = new QButtonGroup( this );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_MotorCW_btn, 0 );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_MotorCCW_btn, 1 );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_MotorGoZero_btn, 2 );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_MoveBackAndForth_btn, 3 );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_MotorStart_btn, 4 );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_MotorStop_btn, 5 );
    StepTestCtrlBtnGroup->addButton( ui->StepTest_SetZero_btn, 6 );
}

/* 步进电机测试参数初始化 */
void TestForm::StepTestPageInit()
{
    /* 创建LineEdit连接 */
    this->CreateStepTestLineEditConnect();

    // 更新色块的颜色
    QColor color( 195, 195, 195 );
    ui->cb_StepTest_2_1->setColor( color );
    ui->cb_StepTest_3_1->setColor( color );
    ui->cb_StepTest_4_1->setColor( color );
    ui->cb_StepTest_5_1->setColor( color );
    ui->cb_StepTest_6_1->setColor( color );
    ui->cb_StepTest_7_1->setColor( color );
    ui->cb_StepTest_8_1->setColor( color );

    ui->cb_FTTest_1_1->setColor( color );
    ui->cb_FTTest_1_2->setColor( color );
    ui->cb_FTTest_2_1->setColor( color );
    ui->cb_FTTest_2_2->setColor( color );
    ui->cb_FTTest_3_1->setColor( color );
    ui->cb_FTTest_4_1->setColor( color );
    ui->cb_FTTest_5_1->setColor( color );
    ui->cb_FTTest_6_1->setColor( color );
    ui->cb_FTTest_6_2->setColor( color );
    ui->cb_FTTest_7_1->setColor( color );
    ui->cb_FTTest_8_1->setColor( color );
    ui->cb_FTTest_9_1->setColor( color );
    ui->cb_FTTest_10_1->setColor( color );
    ui->cb_FTTest_11_1->setColor( color );
}

/* 创建LineEdit的链接 */
void TestForm::CreateStepTestLineEditConnect()
{
    connect( ui->le_StepTest_1, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_2, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_3, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_4, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_5, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_6, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_7, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_8, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_StepTest_9, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );

    connect( ui->le_FTTest_1, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_2, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_3, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_4, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_5, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_6, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_7, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_8, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_9, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_10, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );
    connect( ui->le_FTTest_11, &MyLineEdit::mousePress, this, &TestForm::onParaLineEditClicked );

    connect( ui->le_StepTest_1, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_2, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_3, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_4, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_5, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_6, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_7, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_8, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_StepTest_9, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_1, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_2, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_3, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_4, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_5, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_6, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_7, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_8, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_9, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_10, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
    connect( ui->le_FTTest_11, &MyLineEdit::mouseDoubleClick, this, &TestForm::onParaLineEditDoubleClicked );
}

void TestForm::onParaLineEditClicked()
{
    qDebug() << "onParaLineEditClicked";
    if ( this->currentLineEdit != nullptr )
        this->currentLineEdit->setStyleSheet( "QLineEdit {"
                                              "background-color: white;"
                                              "color: #333333;"
                                              "border: 1px solid #dddddd;"
                                              "border-radius: 5px;"
                                              "padding: 2px;"
                                              "}" );
    this->currentLineEdit = qobject_cast< MyLineEdit* >( sender() );
    this->currentLineEdit->setStyleSheet( "MyLineEdit{"
                                          "border: 1px solid red;"
                                          "}" );
}

void TestForm::onParaLineEditDoubleClicked()
{
    this->currentLineEdit = qobject_cast< MyLineEdit* >( sender() );
    this->numFrm          = new NumberInputForm( this, "请输入数值", 0, 65535 );
    this->numFrm->setAttribute( Qt::WA_ShowModal, true );
    this->numFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
    connect( this->numFrm, &NumberInputForm::InputFinished, this, &TestForm::onParaInputFinish );
    connect( this->numFrm, &NumberInputForm::finished, this, [&]() {
        disconnect( this->numFrm, &NumberInputForm::InputFinished, this, &TestForm::onParaInputFinish );
        //        delete this->numFrm;
        this->numFrm = nullptr;
    } );
    this->numFrm->show();
}

/* 步进电机测试界面下键盘按下槽函数 */
void TestForm::onParaInputFinish( QString text )
{
    if ( this->currentLineEdit != nullptr )
    {
        this->currentLineEdit->setText( text );
    }
}

/* 步进电机测试界面下控制按键组按下槽函数 */
void TestForm::onStepTestCtrlBtnGroupClicked( int id )
{
    qDebug() << id;

    quint8 data[ 4 ] = { 0 };
    data[ 0 ]        = findCurrentStepMotorId();
    if ( data[ 0 ] == 0 )
        return;

    // 点击零位键按下，需要弹出对话框选择进一步的菜单
    if ( id == 6 )
    {
        if ( zeroCmdComboFrm == nullptr )
        {
            QMap< int, QString >* list = new QMap< int, QString >();
            list->insert( 1, "设置零位" );
            list->insert( 2, "清除零位" );
            list->insert( 3, "电机解锁" );
            list->insert( 4, "设置量程" );
            list->insert( 5, "退出" );
            zeroCmdComboFrm = new ComboForm( this, list, 1 );
            zeroCmdComboFrm->setAttribute( Qt::WA_ShowModal, true );
            zeroCmdComboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );
            connect( zeroCmdComboFrm, &ComboForm::itemSelected, this, &TestForm::zeroCmdFrmItemSelected );
        }
        zeroCmdComboFrm->show();
        return;
    }

    // Change Button Color，只有缝头才有7号按钮，电机自检
    if ( id < 7 )
    {
        QAbstractButton* clickedBtn = StepTestCtrlBtnGroup->button( id );
        clickedBtn->setStyleSheet( "QPushButton {"
                                   "background-color: #3498db;"
                                   "color: white;"
                                   "border: 1px solid #2980b9;"
                                   "border-radius: 5px;"
                                   "padding: 5px;"
                                   "}" );
        // 恢复其余按钮的默认背景颜色
        for ( QAbstractButton* button : StepTestCtrlBtnGroup->buttons() )
        {
            if ( button != clickedBtn )
            {
                button->setStyleSheet( "QPushButton {"
                                       "background-color: #f8f8f8;"
                                       "border: 1px solid #dddddd;"
                                       "border-radius: 5px;"
                                       "padding: 5px;"
                                       "color: #333333;"
                                       "}" );
            }
        }
    }
    QAbstractButton* clickedBtn1 = FTTestMotorBtnGroup->button( id );
    clickedBtn1->setStyleSheet( "QPushButton {"
                                "background-color: #3498db;"
                                "color: white;"
                                "border: 1px solid #2980b9;"
                                "border-radius: 5px;"
                                "padding: 5px;"
                                "}" );
    // 恢复其余按钮的默认背景颜色
    for ( QAbstractButton* button : FTTestMotorBtnGroup->buttons() )
    {
        if ( button != clickedBtn1 )
        {
            button->setStyleSheet( "" );
        }
    }

    data[ 1 ] = id + 1;
    if ( id >= 0 && id <= 5 )
    {
        data[ 1 ]     = id + 1;
        quint16 value = this->currentLineEdit->text().toUInt();
        memcpy( &data[ 2 ], &value, 2 );
    }
    comm->pushDataTobuffer( 0x07, data, 4 );
}

void TestForm::zeroCmdFrmItemSelected( int itemKey )
{
    // 5 退出
    if ( itemKey == 5 )
        return;

    quint8 stepMotorId = findCurrentStepMotorId();
    if ( stepMotorId == 0 )
        return;

    // 4 将输出值数值直接设置为最大量程
    if ( itemKey == 4 )
    {
        if ( this->currentLineEdit == nullptr )
            return;
        // 读配置文件MachineFileConfig.json
        quint8  tabId   = stepMotorId / 8;
        quint8  motorId = stepMotorId % 8;
        quint16 value   = this->currentLineEdit->text().toUInt();

        if ( tabId == 0 )
        {
            ReadMachineFileConfig::StepperMotorConfigItem& item = ( *mainData->readMachineFileConfig->getSocketMotorCfgList() )[ motorId ];
            item.data[ 4 ].value                                = value;
        }
        else if ( tabId == 1 )
        {
            ReadMachineFileConfig::StepperMotorConfigItem& item = ( *mainData->readMachineFileConfig->getFengtouMotorCfgList() )[ motorId ];
            item.data[ 4 ].value                                = value;
        }
        else if ( tabId == 2 )
        {
            ReadMachineFileConfig::StepperMotorConfigItem& item = ( *mainData->readMachineFileConfig->getOtherMotorCfgList() )[ motorId ];
            item.data[ 4 ].value                                = value;
        }
        if ( mainData->readMachineFileConfig->saveStepMotorConfig( MAIN_CFG_DIR + "MachineFileConfig.json" ) == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "保存成功" );
        }
        else
        {
            QMessageBox::warning( nullptr, "错误提示", "保存失败" );
        }
        quint8 data[ 4 ] = { 0 };
        data[ 0 ]        = stepMotorId;
        data[ 1 ]        = 10;
        data[ 2 ]        = value % 256;
        data[ 3 ]        = value / 256;
        comm->pushDataTobuffer( 0x07, data, 4 );
        return;
    }

    quint8 data[ 4 ] = { 0 };
    data[ 0 ]        = stepMotorId;
    if ( itemKey >= 1 && itemKey <= 3 )
    {
        data[ 1 ] = itemKey + 6;
    }
    if ( itemKey == 1 )
    {
        quint16 value = this->currentLineEdit->text().toUInt();
        data[ 2 ]     = value % 256;
        data[ 3 ]     = value / 256;
    }
    comm->pushDataTobuffer( 0x07, data, 4 );
}

/* 根据当前是哪个LineEdit按下了，找到对应的电机编号 */
quint8 TestForm::findCurrentStepMotorId()
{
    if ( this->currentLineEdit == nullptr )
    {
        QMessageBox::warning( nullptr, "错误提示", "请先选择对应的步进电机" );
        return 0;
    }
    if ( this->currentLineEdit == ui->le_StepTest_1 )
        return 1;
    if ( this->currentLineEdit == ui->le_StepTest_2 )
        return 2;
    if ( this->currentLineEdit == ui->le_StepTest_3 )
        return 3;
    if ( this->currentLineEdit == ui->le_StepTest_4 )
        return 4;
    if ( this->currentLineEdit == ui->le_StepTest_5 )
        return 5;
    if ( this->currentLineEdit == ui->le_StepTest_6 )
        return 6;
    if ( this->currentLineEdit == ui->le_StepTest_7 )
        return 7;
    if ( this->currentLineEdit == ui->le_StepTest_8 )
        return 8;
    if ( this->currentLineEdit == ui->le_StepTest_9 )
        return 9;
    if ( this->currentLineEdit == ui->le_FTTest_1 )
        return 10;
    if ( this->currentLineEdit == ui->le_FTTest_2 )
        return 11;
    if ( this->currentLineEdit == ui->le_FTTest_3 )
        return 12;
    if ( this->currentLineEdit == ui->le_FTTest_4 )
        return 13;
    if ( this->currentLineEdit == ui->le_FTTest_5 )
        return 14;
    if ( this->currentLineEdit == ui->le_FTTest_6 )
        return 15;
    if ( this->currentLineEdit == ui->le_FTTest_7 )
        return 16;
    if ( this->currentLineEdit == ui->le_FTTest_8 )
        return 17;
    if ( this->currentLineEdit == ui->le_FTTest_9 )
        return 18;
    if ( this->currentLineEdit == ui->le_FTTest_10 )
        return 19;
    if ( this->currentLineEdit == ui->le_FTTest_11 )
        return 20;

    return 0;
}

void TestForm::RefreshStepParams( quint8 deviceId, quint16 currentValue, quint16 codeValue, quint8 zeroValue, quint8 limitValue )
{
    qDebug() << "RefreshStepParams" << deviceId << currentValue << codeValue << zeroValue << limitValue;
    if ( deviceId == 2 )
    {
        ui->lbl_StepTest_2_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_2_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_2_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_2_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 3 )
    {
        ui->lbl_StepTest_3_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_3_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_3_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_3_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 4 )
    {
        ui->lbl_StepTest_4_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_4_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_4_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_4_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 5 )
    {
        ui->lbl_StepTest_5_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_5_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_5_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_5_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 6 )
    {
        ui->lbl_StepTest_6_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_6_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_6_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_6_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 7 )
    {
        ui->lbl_StepTest_7_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_7_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_7_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_7_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 8 )
    {
        ui->lbl_StepTest_8_1->setText( QString::number( currentValue ) );
        ui->lbl_StepTest_8_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_StepTest_8_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_StepTest_8_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 10 )
    {
        ui->lbl_FTTest_1_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_1_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_1_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_1_1->setColor( QColor( 195, 195, 195 ) );
        if ( limitValue == 1 )
            ui->cb_FTTest_1_2->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_1_2->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 11 )
    {
        ui->lbl_FTTest_2_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_2_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_2_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_2_1->setColor( QColor( 195, 195, 195 ) );
        if ( limitValue == 1 )
            ui->cb_FTTest_2_2->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_2_2->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 12 )
    {
        ui->lbl_FTTest_3_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_3_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_3_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_3_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 13 )
    {
        ui->lbl_FTTest_4_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_4_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_4_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_4_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 14 )
    {
        ui->lbl_FTTest_5_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_5_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_5_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_5_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 15 )
    {
        ui->lbl_FTTest_6_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_6_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_6_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_6_1->setColor( QColor( 195, 195, 195 ) );
        if ( limitValue == 1 )
            ui->cb_FTTest_6_2->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_6_2->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 16 )
    {
        ui->lbl_FTTest_7_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_7_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_7_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_7_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 17 )
    {
        ui->lbl_FTTest_8_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_8_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_8_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_8_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 18 )
    {
        ui->lbl_FTTest_9_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_9_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_9_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_9_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 19 )
    {
        ui->lbl_FTTest_10_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_10_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_10_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_10_1->setColor( QColor( 195, 195, 195 ) );
    }
    else if ( deviceId == 20 )
    {
        ui->lbl_FTTest_11_1->setText( QString::number( currentValue ) );
        ui->lbl_FTTest_11_2->setText( QString::number( codeValue ) );
        if ( zeroValue == 1 )
            ui->cb_FTTest_11_1->setColor( QColor( 160, 250, 80 ) );
        else
            ui->cb_FTTest_11_1->setColor( QColor( 195, 195, 195 ) );
    }
}
