#include "Config/readmachinefileconfig.h"
#include "parasetform.h"
#include "ui_parasetform.h"
#include <QDebug>
#include <QLayout>
#include <QMessageBox>
#include <QWidget>

void ParaSetForm::initFengtouCalParasetPage()
{
    if ( isFengtouCalParasetPageInit == false )
    {
        // 缝头组合名称修改，修改后会重新填充数据
        connect( this->ui->le_fengtouStepName, &MyLineEdit::mouseRelease, this, [&]() {
            this->ui->le_fengtouStepName->setStyleSheet( "border: 1px solid #FC5531;" );
            // 清空之前的内存
            if ( this->comboFrm != nullptr )
            {
                delete this->comboFrm;
                this->comboFrm = nullptr;
            }
            this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getFengtouList() );
            this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->comboFrm->setWindowTitle( tr( "选择组合名称" ) );
            this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

            connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                this->comboFrm->close();
                this->ui->le_fengtouStepName->setText( this->readLinkCfg->getFengtouList()->value( id ) );
                this->ShowFengtouStep( id );
            } );
            connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_fengtouStepName->setStyleSheet( "" ); } );

            this->comboFrm->show();
        } );

        // 动作插入、删除、清除
        connect( this->ui->pbtn_actionAdd, &QPushButton::clicked, this, [&]() {
            // 最大不超过12个
            if ( this->currentFengtouStepStruct->Valve_array.count() < 12 )
            {
                ReadMachineFileConfig::FengtouSignalValveStruct valve;
                this->currentFengtouStepStruct->Valve_array.append( valve );
                this->ShowFengtouValve();
            }
        } );
        connect( this->ui->pbtn_actionDelete, &QPushButton::clicked, this, [&]() {
            if ( this->currentFengtouStepStruct->Valve_array.count() > 0 )
            {
                this->currentFengtouStepStruct->Valve_array.pop_back();
                this->ShowFengtouValve();
            }
        } );
        connect( this->ui->pbtn_actionClear, &QPushButton::clicked, this, [&]() {
            this->currentFengtouStepStruct->Valve_array.clear();
            this->ShowFengtouValve();
        } );
        // 电机插入、删除、清除
        connect( this->ui->pbtn_motorAdd, &QPushButton::clicked, this, [&]() {
            // 最大不超过6个
            if ( this->currentFengtouStepStruct->motor_array.count() < 6 )
            {
                ReadMachineFileConfig::FengtouMotorStruct motor;
                this->currentFengtouStepStruct->motor_array.append( motor );
                this->ShowFengtouMotor();
            }
        } );
        connect( this->ui->pbtn_motorDelete, &QPushButton::clicked, this, [&]() {
            if ( this->currentFengtouStepStruct->motor_array.count() > 0 )
            {
                this->currentFengtouStepStruct->motor_array.pop_back();
                this->ShowFengtouMotor();
            }
        } );
        connect( this->ui->pbtn_motorClear, &QPushButton::clicked, this, [&]() {
            this->currentFengtouStepStruct->motor_array.clear();
            this->ShowFengtouMotor();
        } );

        // 缝头动作命令修改
        connect( this->ui->le_ftACommand, &MyLineEdit::mouseRelease, this, [&]() {
            this->ui->le_ftACommand->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );
            // 清空之前的内存
            if ( this->comboFrm != nullptr )
            {
                delete this->comboFrm;
                this->comboFrm = nullptr;
            }
            this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getFengtouCommandList() );
            this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->comboFrm->setWindowTitle( tr( "选择动作命令" ) );
            this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

            connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                this->comboFrm->close();
                this->ui->le_ftACommand->setText( this->readLinkCfg->getFengtouCommandList()->value( id ) );
                this->currentFengtouStepStruct->dongzuo_zhiling = id;
            } );
            connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_ftACommand->setStyleSheet( "font-size:16px;" ); } );
            this->comboFrm->show();
        } );
        // 缝头复位动作修改
        connect( this->ui->le_ftResetAction, &MyLineEdit::mouseRelease, this, [&]() {
            this->ui->le_ftResetAction->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );
            // 清空之前的内存
            if ( this->comboFrm != nullptr )
            {
                delete this->comboFrm;
                this->comboFrm = nullptr;
            }
            this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getFengtouResetList() );
            this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
            this->comboFrm->setWindowTitle( tr( "选择复位动作" ) );
            this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

            connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                this->comboFrm->close();
                this->ui->le_ftResetAction->setText( this->readLinkCfg->getFengtouResetList()->value( id ) );
                this->currentFengtouStepStruct->fuwei_dongzuo = id;
            } );
            connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->ui->le_ftResetAction->setStyleSheet( "font-size:16px;" ); } );
            this->comboFrm->show();
        } );

        this->ui->le_ft_WarnDelay->setProperty( "widget", "warnDelay" );

        this->ui->le_ftActionDelay->setProperty( "widget", "actionDelay" );

        connect( ui->lw_FtStepList, &QListWidget::itemClicked, this, &ParaSetForm::ShowFengtouParam );

        this->ui->le_fengtouStepName->setText( this->readLinkCfg->getFengtouList()->value( 0 ) );
        this->ShowFengtouStep( 0 );

        isFengtouCalParasetPageInit = true;
    }
}

void ParaSetForm::ShowFengtouStep( int index )
{
    int size = this->readMachineFileConfig->getFengtouCalCfgList()->size();
    if ( index >= size || index < 0 )
    {
        QMessageBox::warning( nullptr, "错误提示", "暂无数据" );
        ui->lw_FtStepList->clear();
        return;
    }

    ReadMachineFileConfig::FengtouCalCfgItem& cfg = ( *this->readMachineFileConfig->getFengtouCalCfgList() )[ index ];
    this->currentFengtouCalItem                   = &cfg;
    this->ui->lw_FtStepList->clear();
    for ( int i = 0; i < cfg.step.count(); i++ )
    {
        QListWidgetItem* configItem = new QListWidgetItem( ui->lw_FtStepList );
        configItem->setText( QString( "%1" ).arg( i ) );
        ui->lw_FtStepList->insertItem( index, configItem );
    }

    // 默认选中第一行
    ui->lw_FtStepList->setCurrentRow( 0 );
    QModelIndex test = ui->lw_FtStepList->currentIndex();
    //    qDebug() << "click" << test;
    emit ui->lw_FtStepList->clicked( test );
}

void ParaSetForm::ShowFengtouParam( QListWidgetItem* item )
{
    int step_index = ui->lw_FtStepList->currentIndex().row();
    ShowFengtouMsg( step_index );
}

void ParaSetForm::ShowFengtouMsg( int step_index )
{
    if ( step_index >= this->currentFengtouCalItem->step.count() || step_index < 0 )
        return;

    // start form 1
    this->currentFengtouStepStruct = &this->currentFengtouCalItem->step[ step_index ];
    this->ui->le_ftStepId->setText( QString( "%1" ).arg( step_index + 1 ) );
    this->ui->le_ft_WarnDelay->setText( QString( "%1" ).arg( this->currentFengtouStepStruct->baojing_yanshi ) );
    this->ui->le_ftActionDelay->setText( QString( "%1" ).arg( this->currentFengtouStepStruct->dongzuo_yanshi ) );
    this->ui->le_ftACommand->setText( this->readLinkCfg->getFengtouCommandList()->value( this->currentFengtouStepStruct->dongzuo_zhiling ) );
    this->ui->le_ftResetAction->setText( this->readLinkCfg->getFengtouResetList()->value( this->currentFengtouStepStruct->fuwei_dongzuo ) );

    this->ShowFengtouMotor();
    this->ShowFengtouValve();
}

void ParaSetForm::ShowFengtouMotor()
{
    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_MotorList->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_MotorList );

    QHBoxLayout* rowLayout = new QHBoxLayout;
    QLabel*      label1    = new QLabel( "电机" );
    QLabel*      label2    = new QLabel( "名称" );
    QLabel*      label3    = new QLabel( "命令" );
    QLabel*      label4    = new QLabel( "设置" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    rowLayout->addWidget( label1 );
    rowLayout->addWidget( label2 );
    rowLayout->addWidget( label3 );
    rowLayout->addWidget( label4 );
    rowLayout->setStretch( 0, 1 );
    rowLayout->setStretch( 1, 2 );
    rowLayout->setStretch( 2, 1 );
    rowLayout->setStretch( 3, 1 );
    rowLayout->setMargin( 0 );
    rowLayout->setSpacing( 1 );
    mainLayout->addLayout( rowLayout );

    // 只显示6个
    for ( quint8 index = 0; index < 6; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString( "%1" ).arg( ( index + 1 ), 2, 10, QChar( '0' ) ) + "." );
        label->setAlignment( Qt::AlignHCenter );
        rowLayoutx->addWidget( label );
        MyLineEdit* lineEdit1 = new MyLineEdit;
        MyLineEdit* lineEdit2 = new MyLineEdit;
        MyLineEdit* lineEdit3 = new MyLineEdit;
        lineEdit1->setAlignment( Qt::AlignHCenter );
        lineEdit2->setAlignment( Qt::AlignHCenter );
        lineEdit3->setAlignment( Qt::AlignHCenter );
        if ( this->currentFengtouStepStruct->motor_array.count() > 0 && index <= this->currentFengtouStepStruct->motor_array.count() - 1 )
        {
            ReadMachineFileConfig::FengtouMotorStruct cmd = this->currentFengtouStepStruct->motor_array[ index ];
            lineEdit1->setText( this->readLinkCfg->getMotorMapList()->value( cmd.num + 1 ) );
            lineEdit2->setText( this->readLinkCfg->getMotorPatternList()->value( cmd.pattern ) );
            lineEdit3->setText( QString( "%1" ).arg( cmd.val ) );
            lineEdit1->setProperty( "index", index );
            lineEdit2->setProperty( "index", index );
            lineEdit3->setProperty( "index", index );
            lineEdit3->setProperty( "widget", "motor_val" );
            // 点击跳出修改窗口
            connect( lineEdit1, &MyLineEdit::mouseRelease, this, &ParaSetForm::onMotorNameClicked );
            connect( lineEdit2, &MyLineEdit::mouseRelease, this, &ParaSetForm::onMotorPatternClicked );
        }
        else
        {
            lineEdit1->setText( "-" );
            lineEdit2->setText( "-" );
            lineEdit3->setText( "-" );
        }
        rowLayoutx->addWidget( lineEdit1 );
        rowLayoutx->addWidget( lineEdit2 );
        rowLayoutx->addWidget( lineEdit3 );
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 2 );
        rowLayoutx->setStretch( 2, 1 );
        rowLayoutx->setStretch( 3, 1 );
        rowLayoutx->setMargin( 0 );
        rowLayoutx->setSpacing( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setSpacing( 1 );
    }
}

void ParaSetForm::onMotorNameClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );

    // 清空之前的内存
    if ( this->comboFrm != nullptr )
    {
        delete this->comboFrm;
        this->comboFrm = nullptr;
    }

    this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getMotorMapList() );
    this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
    this->comboFrm->setWindowTitle( tr( "选择电机类型" ) );
    this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

    connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
        this->comboFrm->close();
        this->currentSelectedMyLineEdit->setText( this->readLinkCfg->getMotorMapList()->value( id ) );
        QVariant var = this->currentSelectedMyLineEdit->property( "index" );
        if ( var.isValid() )
        {
            int                                        index = var.toInt();
            ReadMachineFileConfig::FengtouMotorStruct* cmd   = &this->currentFengtouStepStruct->motor_array[ index ];
            cmd->num                                         = id - 1;
        }
    } );
    connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:16px;" ); } );
    this->comboFrm->show();
}

void ParaSetForm::onMotorPatternClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );

    // 清空之前的内存
    if ( this->comboFrm != nullptr )
    {
        delete this->comboFrm;
        this->comboFrm = nullptr;
    }
    this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getMotorPatternList() );
    this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
    this->comboFrm->setWindowTitle( tr( "选择电机命令" ) );
    this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

    connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
        //            qDebug() << id;
        this->comboFrm->close();
        this->currentSelectedMyLineEdit->setText( this->readLinkCfg->getMotorPatternList()->value( id ) );
        QVariant var = this->currentSelectedMyLineEdit->property( "index" );
        if ( var.isValid() )
        {
            int                                        index = var.toInt();
            ReadMachineFileConfig::FengtouMotorStruct* cmd   = &this->currentFengtouStepStruct->motor_array[ index ];
            cmd->pattern                                     = id;
        }
    } );
    connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:16px;" ); } );
    this->comboFrm->show();
}

void ParaSetForm::ShowFengtouValve()
{
    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_valveList->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_valveList );

    QHBoxLayout* rowLayout = new QHBoxLayout;
    QLabel*      label1    = new QLabel( "动作" );
    QLabel*      label2    = new QLabel( "类型" );
    QLabel*      label3    = new QLabel( "名称" );
    QLabel*      label4    = new QLabel( "状态" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    rowLayout->addWidget( label1 );
    rowLayout->addWidget( label2 );
    rowLayout->addWidget( label3 );
    rowLayout->addWidget( label4 );
    rowLayout->setStretch( 0, 1 );
    rowLayout->setStretch( 1, 1 );
    rowLayout->setStretch( 2, 2 );
    rowLayout->setStretch( 3, 1 );
    rowLayout->setMargin( 0 );
    rowLayout->setSpacing( 1 );
    mainLayout->addLayout( rowLayout );
    //    mainLayout->setStretch( 0, 1 );

    // 只显示12个
    for ( quint8 index = 0; index < 12; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString( "%1" ).arg( ( index + 1 ), 2, 10, QChar( '0' ) ) + "." );
        label->setAlignment( Qt::AlignHCenter );
        rowLayoutx->addWidget( label );
        MyLineEdit* lineEdit1 = new MyLineEdit;
        MyLineEdit* lineEdit2 = new MyLineEdit;
        MyLineEdit* lineEdit3 = new MyLineEdit;
        lineEdit1->setAlignment( Qt::AlignHCenter );
        lineEdit2->setAlignment( Qt::AlignHCenter );
        lineEdit3->setAlignment( Qt::AlignHCenter );
        if ( this->currentFengtouStepStruct->Valve_array.count() > 0 && index <= this->currentFengtouStepStruct->Valve_array.count() - 1 )
        {
            ReadMachineFileConfig::FengtouSignalValveStruct cmd = this->currentFengtouStepStruct->Valve_array[ index ];
            lineEdit1->setText( cmd.type == 0 ? QString( "气阀" ) : QString( "信号" ) );
            lineEdit2->setText( this->readLinkCfg->getAirValveList()->value( cmd.num ) );
            QString state = cmd.type == 0 ? cmd.state == 0 ? QString( "退" ) : QString( "进" ) : cmd.state == 0 ? QString( "无效" ) : QString( "有效" );
            lineEdit3->setText( state );
            lineEdit3->setProperty( "type", cmd.type );
            lineEdit1->setProperty( "index", index );
            lineEdit2->setProperty( "index", index );
            lineEdit3->setProperty( "index", index );
            // 点击跳出修改窗口
            connect( lineEdit1, &MyLineEdit::mouseRelease, this, &ParaSetForm::onFengtouVSTypeClicked );
            connect( lineEdit2, &MyLineEdit::mouseRelease, this, &ParaSetForm::onFengtouVSNameClicked );
            connect( lineEdit3, &MyLineEdit::mouseRelease, this, &ParaSetForm::onFengtouVSStateClicked );
        }
        else
        {
            lineEdit1->setText( "-" );
            lineEdit2->setText( "-" );
            lineEdit3->setText( "-" );
        }
        rowLayoutx->addWidget( lineEdit1 );
        rowLayoutx->addWidget( lineEdit2 );
        rowLayoutx->addWidget( lineEdit3 );
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 1 );
        rowLayoutx->setStretch( 2, 2 );
        rowLayoutx->setStretch( 3, 1 );
        rowLayoutx->setMargin( 0 );
        rowLayoutx->setSpacing( 1 );
        mainLayout->addLayout( rowLayoutx );
        mainLayout->setSpacing( 1 );
        //        mainLayout->setStretch( index + 1, 1 );
    }
}

void ParaSetForm::onFengtouVSTypeClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );
    // 清空之前的内存
    if ( this->comboFrm != nullptr )
    {
        delete this->comboFrm;
        this->comboFrm = nullptr;
    }
    this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getFengtouVSTypeList() );
    this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
    this->comboFrm->setWindowTitle( tr( "选择气阀或信号" ) );
    this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

    connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
        this->comboFrm->close();
        this->currentSelectedMyLineEdit->setText( this->readLinkCfg->getFengtouVSTypeList()->value( id ) );
        QVariant var = this->currentSelectedMyLineEdit->property( "index" );
        if ( var.isValid() )
        {
            int                                              index = var.toInt();
            ReadMachineFileConfig::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
            cmd->type                                              = id;
        }

        // 找到后面第2个LineEdit并添加type属性
        QLayout* vBoxLayout = this->ui->gbox_valveList->layout();
        for ( int i = 0; i < vBoxLayout->count(); ++i )
        {
            QHBoxLayout* hBoxLayout = qobject_cast< QHBoxLayout* >( vBoxLayout->itemAt( i )->layout() );
            if ( hBoxLayout )
            {
                // 遍历当前 QHBoxLayout 中的 QLineEdit
                for ( int j = 0; j < hBoxLayout->count(); ++j )
                {
                    QLineEdit* lineEdit = qobject_cast< QLineEdit* >( hBoxLayout->itemAt( j )->widget() );
                    if ( lineEdit == this->currentSelectedMyLineEdit )
                    {
                        // 添加type属性
                        //                            qDebug() << "LineEdit text:" << lineEdit->text();
                        QLineEdit* nextLineEdit = qobject_cast< QLineEdit* >( hBoxLayout->itemAt( j + 2 )->widget() );
                        nextLineEdit->setProperty( "type", id );
                        //                            qDebug() << "LineEdit text:" << nextLineEdit->text();
                    }
                }
            }
        }
    } );
    connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:16px;" ); } );
    this->comboFrm->show();
}

void ParaSetForm::onFengtouVSNameClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );
    // 清空之前的内存
    if ( this->comboFrm != nullptr )
    {
        delete this->comboFrm;
        this->comboFrm = nullptr;
    }
    this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getAirValveList(), 10 );
    this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
    this->comboFrm->setWindowTitle( tr( "选择气阀或信号" ) );
    this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

    connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
        //            qDebug() << id;
        this->comboFrm->close();
        this->currentSelectedMyLineEdit->setText( this->readLinkCfg->getAirValveList()->value( id ) );
        QVariant var = this->currentSelectedMyLineEdit->property( "index" );
        if ( var.isValid() )
        {
            int                                              index = var.toInt();
            ReadMachineFileConfig::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
            cmd->num                                               = id;
        }
    } );
    connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:16px;" ); } );
    this->comboFrm->show();
}

void ParaSetForm::onFengtouVSStateClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    senderLineEdit->setStyleSheet( "font-size:16px;border: 1px solid #FC5531;" );

    int type = senderLineEdit->property( "type" ).toInt();
    if ( type == 0 )
    {  // 清空之前的内存
        if ( this->comboFrm != nullptr )
        {
            delete this->comboFrm;
            this->comboFrm = nullptr;
        }
        this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getFengtouVStateList() );
        this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
        this->comboFrm->setWindowTitle( tr( "选择气阀状态" ) );
        this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

        connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            //                qDebug() << id;
            this->comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->readLinkCfg->getFengtouVStateList()->value( id ) );
            QVariant var = this->currentSelectedMyLineEdit->property( "index" );
            if ( var.isValid() )
            {
                int                                              index = var.toInt();
                ReadMachineFileConfig::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
                cmd->state                                             = id;
            }
        } );
        connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:16px;" ); } );
        this->comboFrm->show();
    }
    else
    {  // 清空之前的内存
        if ( this->comboFrm != nullptr )
        {
            delete this->comboFrm;
            this->comboFrm = nullptr;
        }
        this->comboFrm = new ComboForm( nullptr, this->readLinkCfg->getFengtouSStateList() );
        this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
        this->comboFrm->setWindowTitle( tr( "选择信号状态" ) );
        this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

        connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
            //                qDebug() << id;
            this->comboFrm->close();
            this->currentSelectedMyLineEdit->setText( this->readLinkCfg->getFengtouSStateList()->value( id ) );
            QVariant var = this->currentSelectedMyLineEdit->property( "index" );
            if ( var.isValid() )
            {
                int                                              index = var.toInt();
                ReadMachineFileConfig::FengtouSignalValveStruct* cmd   = &this->currentFengtouStepStruct->Valve_array[ index ];
                cmd->state                                             = id;
            }
        } );
        connect( this->comboFrm, &ComboForm::finished, this, [&]() { this->currentSelectedMyLineEdit->setStyleSheet( "font-size:16px;" ); } );
        this->comboFrm->show();
    }
}
