#pragma once
#include "FATDecode.h"
#include <QSharedPointer>
#include <QVector>

#define CmdCntPerCYAction 16
#define MachinNeedleCnt 200

typedef quint8( NeedleData )[ MachinNeedleCnt ];

class FATParser
{
public:
    /*步骤信息的枚举值*/
    enum StepMsg
    {
        SatrtStep = 0,       // 开始
        QikouStep,           // 起口
        LikouStep,           // 里口
        XianjinStep,         // 橡筋
        XiangjinzhakouStep,  // 橡筋扎口
        WatongStep,          // 袜口
        WagenStep,           // 袜跟
        WadiStep,            // 袜底
        GuoqiaoStep,         // 过桥
        WatouStep,           // 袜头
        JitouxianStep,       // 机头线
        FengkouStep,         // 封口
        FengtoujieshuStep,   // 缝头结束
        EndStep,             // 结束
        KuaisufuweiStep,     // 快速复位
        NullStep             // 空白块
    };
#pragma pack( 1 )
    /*指令页结构体*/
    struct CmdStruct
    {
        quint8  cmd_num;         // 指令页编号，TODO:需要确认指令编号对应的信息
        quint16 cmd_param : 12;  // 指令参数
        quint16 sign : 4;        // 正负号：0：正 8：负
        quint8  cmd_state;       // 指令状态：TODO:需要确认状态值对应的信息
    };

    /*CY动作结构，每个动作最多16个指令*/
    struct CYActionStruct
    {
        quint8 cy_num;                           // 动作编号
                                                 // 1-100，通过这个编号可以从动作库索引出气阀和固定选针信息数据
        quint16   cy_circle_cnt;                 // 圈数 0-999
        quint8    cy_state;                      // 特征值 TODO:需要补充特征值对应的信息
        CmdStruct cmd_msg[ CmdCntPerCYAction ];  // 每个动作包含的指令信息
        quint32   cmd_cnt;                       // 指令数
    };
    /*部位参数、袜跟参数、花型参数
      0X0800-0X087F：共128 Byte：
      1 2部位名称，3 4尺寸， 5 6速度，7 8/9
      10橡筋，11/12针筒，13/14沉降，15/16生克，19/20左菱角，21/22右菱角， 43
      44花型设置，45 46拉毛设置，47 48打跟中心，49 50打根偏移，51
      52附加，127动作数量
    */
    struct XiangJinStruct
    {
        quint16 start_value;     // 起始值
        quint16 state : 1;       // Bit31=0:表示常规 Bit31 = 1:表示渐进 若是常规，则16 -
                                 // 30bit无意义 若是渐进，则16 - 30bit表示结束值
        quint16 end_value : 15;  // 结束值
    };

    struct ShengkeStruct
    {
        quint16 start_value;  // 起始值

        quint16 end_value : 15;  // 结束值
        quint16 state : 1;       // Bit31=0:表示常规 Bit31 = 1:表示渐进 若是常规，则16 -
                                 // 30bit无意义 若是渐进，则16 - 30bit表示结束值
    };

    /*除了橡筋以外的部位参数*/
    struct BuweiParamStruct
    {
        quint8 start_value;    // 起始值
        quint8 state : 1;      // Bit15=0:表示常规 Bit15 = 1:表示渐进 若是常规，则8 -
                               // 14bit无意义 若是渐进，则8 - 14bit表示结束值（0 - 25）
        quint8 end_value : 7;  // 结束值
    };
    struct MainProgamParam
    {
        /*部位参数*/
        quint16          step_name;          // 部位名称
        quint16          chicun;             // 尺寸
        quint16          speed;              // 速度
        XiangJinStruct   xiangjin;           // 橡筋
        BuweiParamStruct zhentong_Density;   // 针筒密度
        BuweiParamStruct chenjiang_Density;  // 沉降密度
        BuweiParamStruct shengke_Density;    // 生克密度
        BuweiParamStruct null1;              // 生克密度
        BuweiParamStruct zuo_lengjiao;       // 左棱角
        BuweiParamStruct you_lengjiao;       // 右棱角
        char             null_value[ 8 ];    // 缺省区域
        XiangJinStruct   ktf1;
        XiangJinStruct   ktf2;
        char             null_value1[ 4 ];   // 缺省区域
        quint16          huaxing_shezhi;     // 1:有花型 0：无花型
        quint16          lamao_shezhi;       // 1:有花型 0：无花型
        quint16          dagen_zhongxin;     // 打跟中心
        quint16          dagen_pianyi;       // 打跟偏移
        quint16          fujia;              // 附加参数
        char             null_value2[ 74 ];  // 缺省区域
    };

    /********************************************************************************

    0xA0部分数据的结构,此部分数据暂无相关数据信息

    *********************************************************************************/

    /********************************************************************************

    0x400部分数据的结构,此部分包含选针资料数据、动作数据库

    *********************************************************************************/
    /*气阀数据结构体*/
    struct ValveData
    {
        quint8  Valve_num;        // 编号
        quint16 Valve_pos : 11;   // 位置
        quint16 sign : 1;         // 气阀位置正负号 1：负号，0；正数
        quint16 Valve_state : 4;  // 气阀状态 1：进 0：退
        quint8  null_value;
    };
    struct GudingNeedleIndex
    {
        quint8 Needle_index;  // 从选针库的索引值
        quint8 state;         // 0：无特殊作用 1：强制使用 2：上针提花 3：退针提花
    };
    /*每个动作的结构体
     * 4byte(有效气阀动作个数)+50*4byte(50个气阀数据)+16*2byte(固定选针数据，从选针库中索引)*/
    struct ActionStruct
    {
        quint32           valid_Valve_cnt;  // 有效气阀个数
        ValveData         Valve[ 50 ];
        GudingNeedleIndex Needle_index[ 16 ];
    };

    /*描述固定选针、气阀数据库信息*/
    struct NeedleValveDataBase
    {
        char         mind_logo[ 0x48 ];  // 明德Logo
        quint16      needle_cnt;         // 选针数据库中固定选针数量
        quint16      action_cnt;         // 动作库中的动作数量
        quint8       Needle_data[ 32 ][ 25 ];
        ActionStruct action_data[ 100 ];
        // NeedleData*
        // Needle_data;//选针数据，每512byte代表一段固定选针，0x400处开始
        // ActionStruct* action_data;//动作数据，每236byte表示一个动作，0x4400开始
    };

    /********************************************************************************

    0xA430部分数据的结构,此部分包含链条动作数据，以及花型数据

    *********************************************************************************/

    /******************************************************************************/
    /*描述密度数据的结构*/
    enum DensityIndex
    {
        ZhengtongDensityIndex = 0,
        ChenjiangDensityIndex,
        ShengkeDensityIndex,
        HafupanDensityIndex,
        ZuolengjiaoDensityIndex,
        YoulengjiaoDensityIndex
    };
    /*描述密度数据，从0（0xA430部分）地址偏移*/
    struct DensityData
    {
        char    mind_logo[ 0x48 ];
        quint32 needle_cnt;          // 针数，200
        quint16 Density[ 6 ][ 24 ];  // 密度数据
    };

    /*描述链条步骤信息，从0x800（0xA430部分）地址偏移*/
    struct SockMachineStepMsg
    {
        MainProgamParam main_param;     // 主程序参数
        quint16         action_cnt;     // 动作数量
        CYActionStruct* cy_action_msg;  // 动作信息数据，通过动作数量申请内存
    };

    /*花型数据，链条步骤结束后，偏移24byte*/

    /*花型数据每个单元存储四个字节，定义如下：
     * 1byte:表示添纱视图数据：取值为1-26，分别表示26把副梭  00000000:1F  00000001
     集圈 1C 00 00 80 浮线 2byte:表示速度视图数据： 取值0-7
       3byte:表示主梭视图：0x00,0x10,0x20,0x30,0x40,x50,0x60
       4byte:表示橡筋、拉毛、袜跟拉毛、打松三角
       橡筋取值：0x00,0x02,0x04,0x06:   bit0-bit2
       拉毛取值：0x00,0x08 : bit3
       袜跟拉毛：0x00,0x10:  bit4
       打松三角：0x00,0x20:  bit5
     *
    */
    struct PatternUnitData
    {
        quint8 tian_sha;
        quint8 speed;
        quint8 zhu_suo;
        quint8 xiangjin : 3;
        quint8 la_mao : 1;
        quint8 wa_gen_lamao : 1;
        quint8 dasong_sanjiao : 1;
        quint8 null : 2;
    };
    struct PatternData
    {
        quint32 width;   // 花型宽度，
        quint32 height;  // 花型高度
        // quint32* pattern_data;//存储花型数据，元素代表梭子编号
        PatternUnitData pattern_data[ 1000 ][ 200 ];  // 存储花型数据，元素代表梭子编号
    };

    /*花型数据结束是缝头动作数据：
      每个缝头程序的组成：126bytes + 2bytes(该程序的步骤个数) +
      256bytes(所有步骤数据)

      256bytes数据的组成方式:
      64bytes + 2bytes(报警延时) + 2bytes(动作延时) + 4bytes(动作指令) +
      4bytes(复位动作)
      + 8bytes + 4bytes(气阀/信号有效个数) + 4*12bytes（气阀/信号数据） +
      4bytes(有效电机数)
      + 6*4bytes(电机数据) + 92bytes

      电机数据格式：

      bytes0: bit0-bit4:编号 bit5-bit7:模式
      bytes1-3: value

       气阀/信号数据格式：
       bytes0:编号
       bytes1-2: 0:气阀 2：信号
       byte3: 0:退/无效  1：进/有效
      */

    struct FengtouMotorStruct
    {
        quint8  num : 5;
        quint8  pattern : 3;
        quint16 val;
        quint8  null;
    };

    struct FengtouSignalValveStruct
    {
        quint8  num;
        quint16 type;
        quint8  state;
    };
    struct FengtouStepStruct
    {
        quint8                   step_start[ 64 ];
        quint16                  baojing_yanshi;
        quint16                  dongzuo_yanshi;
        quint32                  dongzuo_zhiling;
        quint32                  fuwei_dongzuo;
        quint8                   null1[ 8 ];
        quint32                  Valve_cnt;
        FengtouSignalValveStruct Valve_array[ 12 ];
        quint32                  motor_cnt;
        FengtouMotorStruct       motor_array[ 6 ];
        quint8                   null2[ 92 ];
    };
    struct FengtouStruct
    {
        quint8             start[ 126 ];
        quint16            step_cnt;
        FengtouStepStruct* step;
    };

    struct FLowerAndLaMaoLoop
    {
        quint16 loop_start;
        quint16 loop_end;
        quint16 loop_num;
    };

    struct Zhusuo
    {
        quint8 R1 : 1;
        quint8 R2 : 1;
        quint8 R3 : 1;
        quint8 R4 : 1;
        quint8 R5 : 1;
        quint8 R6 : 1;
        quint8 R7 : 1;
        quint8 R8 : 1;
        quint8 RM : 1;
        quint8 null : 7;
    };

    struct ZhusoNumCfg
    {
        quint16 advance_quantity;
        quint16 overlap_quantity;
    };

    struct Zhusuo2
    {
        quint8 F1 : 1;
        quint8 RM : 1;
        quint8 R1 : 1;
        quint8 R2 : 1;
        quint8 R3 : 1;
        quint8 R4 : 1;
        quint8 R5 : 1;
        quint8 R6 : 1;
        quint8 R7 : 1;
        quint8 R8 : 1;
        quint8 null : 6;
    };

    struct PatternExtraData
    {
        FLowerAndLaMaoLoop flower_loop[ 10 ];
        FLowerAndLaMaoLoop lamao_loop[ 10 ];
        Zhusuo             zhusuo[ 7 ];
        ZhusoNumCfg        zhusuo_num_cfg;
        Zhusuo2            zhusuo2[ 4 ];
    };

    typedef QVector< QSharedPointer< SockMachineStepMsg > > MachineStepVector;
    typedef QVector< QSharedPointer< FengtouStruct > >      FengtouStepVector;

    // 存储所有数据的结构体，用于外部控制
    struct FATDataMain
    {
        QSharedPointer< NeedleValveDataBase >         NeedleActionDB;
        QSharedPointer< DensityData >                 DentisyData;
        MachineStepVector                             StepVector;
        QSharedPointer< FATParser::PatternData >      PatternData;
        FengtouStepVector                             FengtouVector;
        QSharedPointer< FATParser::PatternExtraData > PatternExtraData;
    };

    // 简化的工艺参数文件项定义
    struct CraftParam
    {
        quint16          step_name;          // 部位名称
        quint16          chicun;             // 尺寸
        quint16          speed;              // 速度
        XiangJinStruct   xiangjin;           // 橡筋
        BuweiParamStruct zhentong_Density;   // 针筒密度
        BuweiParamStruct chenjiang_Density;  // 沉降密度
        BuweiParamStruct shengke_Density;    // 生克密度
        BuweiParamStruct zuo_lengjiao;       // 左棱角
        BuweiParamStruct you_lengjiao;       // 右棱角
        XiangJinStruct   ktf1;
        XiangJinStruct   ktf2;
    };

#pragma pack()

    FATParser();
    ~FATParser();

    /**
     *@brief  解析出FAT所有链条数据
     *@param  QString & fat_path FAT文件路径
     *@return int
     *<AUTHOR>
     *@date   2021/05/06
     */
    int Parser( QString fat_path, FATDataMain* fat_main );

private:
    /**
     *@brief  获取选针、动作数据库数据
     *@param  char * buffer 文件指针
     *@return QT_NAMESPACE::QSharedPointer<FATParser::NeedleValveDataBase>
     *<AUTHOR>
     *@date   2021/05/06
     */
    QSharedPointer< NeedleValveDataBase > GetActionDataBase( char* buffer );

    /**
     *@brief
     *@param  char * buffer
     *@return QT_NAMESPACE::QSharedPointer<FATParser::DensityData>
     *<AUTHOR>
     *@date   2021/05/06
     */
    QSharedPointer< DensityData > GetDensityData( char* buffer );

    /**
     *@brief  获取FAT中机器链条步骤信息
     *@param  char * buffer FAT文件指针
     *@param  quint32 & offset_size 链条数据所占用的偏移大小
     *@return FATParser::MachineStepVector 该FAT中所有步骤的向量
     *<AUTHOR>
     *@date   2021/05/06
     */
    MachineStepVector GetMachineStepMsg( char* buffer, quint32& offset_size );

    /**
     *@brief   获取花型数据
     *@param  char * buffer
     *@return QT_NAMESPACE::QSharedPointer<FATParser::PatternData>
     *<AUTHOR>
     *@date   2021/05/07
     */
    QSharedPointer< PatternData > GetPatternData( char* buffer, FATDecode::FatHead& head, quint32 width_offset );

    QSharedPointer< PatternExtraData > GetPatternExtraData( char* buffer, FATDecode::FatHead& head );
    FengtouStepVector                  GetFengtouData( char* buffer, quint32 start_pos, quint32 end_pos );
};
