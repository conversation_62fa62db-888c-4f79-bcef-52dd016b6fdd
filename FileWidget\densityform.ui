<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DensityForm</class>
 <widget class="QWidget" name="DensityForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>660</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="main_View" native="true">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>10</y>
     <width>481</width>
     <height>391</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>420</y>
     <width>611</width>
     <height>39</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QPushButton" name="pbtn_prev">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>上页</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pbtn_next">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>下页</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pbtn_ok">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>确定</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pbtn_quit">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>退出</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QPushButton" name="pbtn_1">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>10</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>1</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_2">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>10</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>2</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_3">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>10</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>3</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_4">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>110</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>4</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_7">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>210</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>7</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_0">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>310</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_5">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>110</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>5</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_6">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>110</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>6</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_8">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>210</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>8</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_9">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>210</y>
     <width>41</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>9</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pbtn_back">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>310</y>
     <width>91</width>
     <height>91</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>Back Space</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
