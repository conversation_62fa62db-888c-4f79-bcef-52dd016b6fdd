#ifndef MACHINEINFO_H
#define MACHINEINFO_H

#include <QString>

struct MachineInfo
{
    QString deviceId;  // 设备编号
    quint16 d1;        // 设定件数
    quint16 d2;        // 完成件数
    quint16 d3;        // 上件用时
    quint16 d4;        // 本件用时
    QString d5;        // 激活花型
    quint16 d6;        // 上件机器运行用时秒
    quint16 d7;        // 本件机器运行用时秒
    quint16 d8;        // 报警类型
    quint16 d9;        // 报警参数
    QString d10;       // 报警描述
    QString d11;       // 机器名
    quint16 d12;       // 总针数
    quint16 d13;       // 连织件数
    quint16 d14;       // 总圈数
    QString d15;       // 机型
    QString d16;       // Mac地址
    quint16 d17;       // 运行状态（面板指示灯状态）
    quint16 d18;       // 当前限速
    quint16 d19;       // 袜机状态
    quint16 d20;       // 缝头状态
    quint16 d21;       // 电压
    quint16 d22;       // 电流
    quint16 d23;       // 电功率
    quint32 d24;       // 用电量
    quint16 d25;       // 控制器温度
};

#endif  // MACHINEINFO_H
