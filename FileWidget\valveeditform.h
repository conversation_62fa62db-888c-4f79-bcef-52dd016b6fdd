#ifndef VALVEEDITFORM_H
#define VALVEEDITFORM_H

#include "CommonWidget/mylineedit.h"
#include "FATParser.h"
#include <QWidget>

namespace Ui
{
class ValveEditForm;
}

class ValveEditForm : public QWidget
{
    Q_OBJECT

public:
    explicit ValveEditForm( FATParser::ValveData* data = nullptr, QMap< int, QString >* list = nullptr, QWidget* parent = nullptr );
    ~ValveEditForm();
    void setValveData( FATParser::ValveData* data );

private:
    Ui::ValveEditForm*    ui;
    FATParser::ValveData* _data;     //拷贝一份数据到本地，只修改本地数据
    FATParser::ValveData* _srcData;  // 保存数据原始指针，保存时会更新
    QMap< int, QString >* _list;     // 保存气阀名称和值的对应关系
    MyLineEdit*           currentLineEdit        = nullptr;
    bool                  firsTimeToEditLineEdit = false;
    void                  showValveData();
    void                  onBtnClicked();
private slots:
    void onLineEditClicked();
    void onLineEditFinished();
signals:
    void finished();

public slots:
    void onPushButtonClicked();

protected:
    void closeEvent( QCloseEvent* event ) override;
};

#endif  // VALVEEDITFORM_H
