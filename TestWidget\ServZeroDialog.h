#ifndef SERVZERODIALOG_H
#define SERVZERODIALOG_H

#include "CommonWidget/mylineedit.h"
#include "Communicate/Communication.h"
#include "Config/readmachinefileconfig.h"
#include "Common/defines.h"
#include <QButtonGroup>
#include <QDialog>
#include <QWidget>
#include <QMessageBox>

namespace Ui
{
class ServZeroDialog;
}

class ServZeroDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ServZeroDialog( QWidget* parent = nullptr, Communication* comm = nullptr, MainWidgetData* mainData = nullptr );
    ~ServZeroDialog();
    void refreshParams( quint16 codeValue, float actAngle );
    void setServZeroCalFinished();
    void setStatusLabel( QString str );

signals:
    void finished();

private:
    Ui::ServZeroDialog* ui;
    Communication*      comm;
    QButtonGroup*       checkBoxGroup;
    MainWidgetData*     mainData;
    quint16             zeroPos = 0;

    int step = 0;

protected:
    void closeEvent( QCloseEvent* event ) override;

private slots:
    void toNextStep();
    void ConfirmMachineZero();
};

#endif  // SERVZERODIALOG_H
