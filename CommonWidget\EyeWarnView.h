#ifndef EYEWARNVIEW_H
#define EYEWARNVIEW_H

#include <QWidget>

class EyeWarnView : public QWidget
{
    Q_OBJECT
public:
    explicit EyeWarnView(const QString& title, QWidget *parent = nullptr);
    void setValue(quint32 value);

protected:
    void paintEvent(QPaintEvent *event) override;

private:
    quint32 currentValue = 0;
    QString titleText;
    const int BLOCK_SIZE = 20;
    const int BORDER_WIDTH = 2;
    const int ROWS = 2;
    const int COLS_PER_ROW = 16;
    const int NUMBER_HEIGHT = 15;
    const int ROW_SPACING = 5;
    const int TITLE_WIDTH = 100;  // 标题宽度
    const int TITLE_MARGIN = 10;  // 标题和色块之间的间距
};

#endif // EYEWARNVIEW_H
