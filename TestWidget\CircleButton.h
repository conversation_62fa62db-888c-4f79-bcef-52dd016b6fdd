﻿#ifndef CIRCLEBUTTON_H
#define CIRCLEBUTTON_H
#include <QtWidgets>

class CircleButton : public QPushButton
{
    Q_OBJECT
public:
    CircleButton( QWidget* parent = nullptr, int size = 50, int number = 0 ) : QPushButton( parent )
    {
        setFixedSize( size, size );  // 设置按钮大小为100x100

        // 设置默认边框和背景
        setStyleSheet( QString( "QPushButton {"
                                "border: 2px solid #000C7B;"  // 设置边框为2px黑色实线
                                "border-radius: %1;"          // 设置边框半径为50px
                                "background-color: #3282F6;"
                                "color: white;"
                                "font-size: 16px;"
                                "}"
                                "QPushButton:focus {"
                                "border: 2px solid red;"
                                "border-radius: 2px;"
                                "}" )
                           .arg( m_size / 2 ) );
        // 设置文字
        m_number = number;
        // -1 means null
        if ( number != -1 )
        {
            QString formattedNumber = QString( "%1" ).arg( number, 3, 10, QChar( '0' ) ).leftJustified( 3, '0' );
            setText( formattedNumber );
        }
        else
        {
            setText( "NC" );
        }
        connect( this, &QPushButton::clicked, this, &CircleButton::clicked );
    }

    void setColor( QString color, QString borderColor )
    {
        QString styleSheet = QString( "QPushButton {"
                                      "border: 2px solid %1;"  // 设置边框颜色
                                      "border-radius: %2px;"
                                      "background-color: %3;"
                                      "color: white;"
                                      "font-size: 16px;"
                                      "}"
                                      "QPushButton:focus {"
                                      "border: 2px solid red;"
                                      "border-radius: 2px;"
                                      "}" )
                                 .arg( borderColor )
                                 .arg( m_size / 2 )
                                 .arg( color );
        //        qDebug() << styleSheet;
        setStyleSheet( styleSheet );
    }

    int getNumber()
    {
        return m_number;
    }

signals:
    void onClicked( int number );

private:
    int m_number = 0;
    int m_size   = 50;

    void clicked()
    {
        //        QMessageBox::information( this, "Button Clicked", QString::number( m_number ) );
        emit onClicked( m_number );
    }
};

#endif  // CIRCLEBUTTON_H
