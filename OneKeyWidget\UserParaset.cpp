#include "Config/readmachinefileconfig.h"
#include "OneKeyForm.h"
#include "ui_OneKeyForm.h"
#include <QDebug>
#include <QLayout>
#include <QWidget>

void OneKeyForm::initUserParasetTab()
{
    QHBoxLayout* mainLayout = new QHBoxLayout( this->ui->UserPara_tab );
    mainLayout->setContentsMargins( 20, 20, 20, 20 );
    mainLayout->setSpacing( 30 );

    int totalElements = this->readMachineFileConfig->getUserCfgList()->count();
    // 计算QVBoxLayer和QHBoxLayer的个数, 每个面板放n个
    int numsPerLayer  = 8;
    int numVBoxLayers = ( totalElements + numsPerLayer - 1 ) / numsPerLayer;

    for ( int i = 0; i < numVBoxLayers; i++ )
    {
        QVBoxLayout* vboxLayout = new QVBoxLayout();
        vboxLayout->setSpacing( 15 );
        mainLayout->addLayout( vboxLayout );

        for ( int j = 0; j < numsPerLayer; j++ )
        {
            QHBoxLayout* hboxLayer = new QHBoxLayout();
            hboxLayer->setSpacing( 10 );
            vboxLayout->addLayout( hboxLayer );
            vboxLayout->setMargin( 0 );

            if ( i * numsPerLayer + j < totalElements )
            {
                ReadMachineFileConfig::MachineFlieConfigItem item  = this->readMachineFileConfig->getUserCfgList()->value( i * numsPerLayer + j + 1 );
                QLabel*                                      label = new QLabel( QString( "%1.%2" ).arg( i * numsPerLayer + j + 1 ).arg( item.title ) );
                label->setMinimumWidth( 180 );
                label->setMinimumHeight( 40 );
                QFont font = label->font();
                font.setPointSize( 8 );
                font.setBold( false );
                label->setFont( font );
                MyLineEdit* lineEdit = new MyLineEdit();
                lineEdit->setAlignment( Qt::AlignHCenter );
                lineEdit->setMinimumHeight( 40 );
                // 配置index选项，用于获取和修改数据
                lineEdit->setProperty( "index", i * numsPerLayer + j + 1 );
                if ( item.editable )
                {
                    connect( lineEdit, &MyLineEdit::mouseRelease, this, &OneKeyForm::onUserParasetEditClicked );
                }
                if ( item.unit == "enum" )
                {
                    lineEdit->setText( item.candidate[ item.value ] );
                }
                else
                {
                    lineEdit->setText( QString( "%1%2" ).arg( item.value ).arg( item.unit ) );
                }
                hboxLayer->addWidget( label );
                hboxLayer->addWidget( lineEdit );
            }
            else
            {
                QLabel* label = new QLabel( QString( "%1.暂无选项" ).arg( i * numsPerLayer + j + 1 ) );
                label->setMinimumWidth( 180 );
                label->setMinimumHeight( 40 );
                QFont font = label->font();
                font.setPointSize( 8 );
                font.setBold( false );
                label->setFont( font );
                MyLineEdit* lineEdit = new MyLineEdit();
                lineEdit->setAlignment( Qt::AlignHCenter );
                lineEdit->setMinimumHeight( 40 );
                hboxLayer->addWidget( label );
                hboxLayer->addWidget( lineEdit );
            }
        }
    }
}

void OneKeyForm::onUserParasetEditClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    QVariant variant                = senderLineEdit->property( "index" );
    if ( variant.isValid() )
    {
        int                                          index = variant.toInt();
        ReadMachineFileConfig::MachineFlieConfigItem item  = this->readMachineFileConfig->getUserCfgList()->value( index );
        // 枚举类型跳出Combo选择框
        if ( item.unit == "enum" )
        {
            // 生成Combo选择框所需的key-value对
            QMap< int, QString > list;
            for ( int i = 0; i < item.candidate.length(); i++ )
            {
                list.insert( i, item.candidate[ i ] );
            }
            if ( this->comboFrm == nullptr )
            {
                this->comboFrm = new ComboForm( nullptr, &list );
                this->comboFrm->setAttribute( Qt::WA_DeleteOnClose );
                connect( this->comboFrm, &QObject::destroyed, this, [this]() { this->comboFrm = nullptr; } );
            }
            connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                //                qDebug() << id;
                QVariant variant = this->currentSelectedMyLineEdit->property( "index" );
                if ( variant.isValid() )
                {
                    int                                           index = variant.toInt();
                    ReadMachineFileConfig::MachineFlieConfigItem& item  = ( *this->readMachineFileConfig->getUserCfgList() )[ index ];
                    item.value                                          = id;
                    this->currentSelectedMyLineEdit->setText( item.candidate[ id ] );
                }
                this->comboFrm->close();
            } );
            this->comboFrm->show();
        }
        else
        {
            if ( this->floatInputFrm == nullptr )
            {
                this->floatInputFrm = new FloatInputForm();
                this->floatInputFrm->setAttribute( Qt::WA_DeleteOnClose );
                connect( this->floatInputFrm, &FloatInputForm::InputFinished, this, &OneKeyForm::onUserFloatInputFormFinished );
                connect( this->floatInputFrm, &QObject::destroyed, this, [this]() { this->floatInputFrm = nullptr; } );
            }
            this->floatInputFrm->show();
        }
    }
}

void OneKeyForm::onUserFloatInputFormFinished( float value )
{
    if ( this->currentSelectedMyLineEdit != nullptr )
    {
        QVariant variant = this->currentSelectedMyLineEdit->property( "index" );
        if ( variant.isValid() )
        {
            int                                           index = variant.toInt();
            ReadMachineFileConfig::MachineFlieConfigItem& item  = ( *this->readMachineFileConfig->getUserCfgList() )[ index ];
            item.value                                          = value;
            this->currentSelectedMyLineEdit->setText( QString( "%1%2" ).arg( value ).arg( item.unit ) );
        }
    }
}
