#include "letterinputdialog.h"
#include "ui_letterinputdialog.h"
#include <QApplication>
#include <QScreen>

LetterInputDialog::LetterInputDialog( QWidget* parent, QString title, QString hint ) : QDialog( parent ), ui( new Ui::LetterInputDialog )
{
    ui->setupUi( this );

    this->setAttribute( Qt::WA_ShowModal, true );  //属性设置true:模态;false:非模态
    this->setWindowTitle( tr( "请输入数值" ) );
    this->setWindowFlags(
        /*a->windowFlags()|*/ Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );  //使得任务栏不会有该窗口的图标

    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    this->setWindowTitle( title );
    ui->label_Type->setText( hint );

    // 连接A-M的按钮信号
    connect( ui->btn_A, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_B, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_C, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_D, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_E, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_F, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_G, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_H, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_I, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_J, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_K, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_L, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );
    connect( ui->btn_M, &QPushButton::clicked, this, &LetterInputDialog::onBtnClicked );

    //    // 取消和确定按钮
    connect( ui->pushBtn_OK, &QPushButton::clicked, this, [&]() {
        emit InputFinished( ui->lineEdit->text() );
        this->close();
    } );
    connect( ui->pushBtn_Cancel, &QPushButton::clicked, this, [&]() { this->close(); } );
}

LetterInputDialog::~LetterInputDialog()
{
    delete ui;
}

void LetterInputDialog::changeHint( QString hint )
{
    ui->label_Type->setText( hint );
    ui->lineEdit->setText( "" );
}

void LetterInputDialog::onBtnClicked()
{
    QObject* senderObj = sender();

    // 清空当前输入
    ui->lineEdit->clear();

    // 根据按钮输入对应的大写字母
    if ( senderObj == ui->btn_A )
        ui->lineEdit->setText( "A" );
    else if ( senderObj == ui->btn_B )
        ui->lineEdit->setText( "B" );
    else if ( senderObj == ui->btn_C )
        ui->lineEdit->setText( "C" );
    else if ( senderObj == ui->btn_D )
        ui->lineEdit->setText( "D" );
    else if ( senderObj == ui->btn_E )
        ui->lineEdit->setText( "E" );
    else if ( senderObj == ui->btn_F )
        ui->lineEdit->setText( "F" );
    else if ( senderObj == ui->btn_G )
        ui->lineEdit->setText( "G" );
    else if ( senderObj == ui->btn_H )
        ui->lineEdit->setText( "H" );
    else if ( senderObj == ui->btn_I )
        ui->lineEdit->setText( "I" );
    else if ( senderObj == ui->btn_J )
        ui->lineEdit->setText( "J" );
    else if ( senderObj == ui->btn_K )
        ui->lineEdit->setText( "K" );
    else if ( senderObj == ui->btn_L )
        ui->lineEdit->setText( "L" );
    else if ( senderObj == ui->btn_M )
        ui->lineEdit->setText( "M" );
}

QString LetterInputDialog::getInputText()
{
    return ui->lineEdit->text();
}
