#include "OneKeyForm.h"
#include "ui_OneKeyForm.h"

void OneKeyForm::initEEyePage()
{
    auto eeyeCfgList = mainData->readMachineFileConfig->getEEyeCfg();
    // 电眼工作数量修改
    eyeNum = eeyeCfgList->realCount;
    ui->le_eyeNum->setText( QString::number( eyeNum ) );

    // 这里不再直接使用items.size()，而是使用realCount
    refreshTabControls( 1 );
    refreshTabControls( 2 );
    refreshTabControls( 3 );
    refreshTabControls( 4 );
    refreshTabControls( 5 );
    refreshTabControls( 6 );
    refreshTabControls( 7 );

    if ( isEEyePageInited == false )
    {
        // 初始化电眼按钮组
        eeyeBtnGroup = new QButtonGroup( this );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_1, 0 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_2, 1 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_3, 2 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_4, 3 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_5, 4 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_6, 5 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_7, 6 );
        eeyeBtnGroup->addButton( ui->btn_Craft_eye_8, 7 );

        // 连接按钮组的信号槽
        connect( eeyeBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onEEyeBtnGroupClicked( int ) ) );

        connect( ui->le_eyeNum, &MyLineEdit::mouseRelease, this, [&]() {
            NumberInputForm* form = new NumberInputForm( this, "请输入数值", 1, 36 );
            form->setAttribute( Qt::WA_DeleteOnClose );
            form->show();

            connect( form, &NumberInputForm::InputFinished, this, [&]( QString text ) {
                eyeNum = text.toUInt();
                ui->le_eyeNum->setText( text );

                // 更新配置中的realCount
                auto eeyeCfgList       = mainData->readMachineFileConfig->getEEyeCfg();
                eeyeCfgList->realCount = eyeNum;

                // 刷新所有tab
                refreshTabControls( 1 );
                refreshTabControls( 2 );
                refreshTabControls( 3 );
                refreshTabControls( 4 );
                refreshTabControls( 5 );
                refreshTabControls( 6 );
                refreshTabControls( 7 );
            } );
        } );

        isEEyePageInited = true;
    }
}

// 刷新电眼控件,根据配置的数量自动生成
void OneKeyForm::refreshTabControls( quint8 type_id )
{
    quint8  sizeOfline = 8;
    quint16 totalSize  = mainData->readMachineFileConfig->getEEyeCfg()->items.size();  // 总电眼数量
    quint16 activeSize = this->eyeNum;                                                 // 激活的电眼数量
    quint16 lines      = ( totalSize - 1 ) / sizeOfline + 1;

    QWidget* currentWidget;
    if ( type_id == 1 )
        currentWidget = this->ui->tab_eye_1;
    else if ( type_id == 2 )
        currentWidget = this->ui->tab_eye_2;
    else if ( type_id == 3 )
        currentWidget = this->ui->tab_eye_3;
    else if ( type_id == 4 )
        currentWidget = this->ui->tab_eye_4;
    else if ( type_id == 5 )
        currentWidget = this->ui->tab_eye_5;
    else if ( type_id == 6 )
        currentWidget = this->ui->tab_eye_6;
    else if ( type_id == 7 )
        currentWidget = this->ui->tab_eye_7;
    else
        return;

    // 保存现有控件的引用，以便后续更新状态而不是重新创建
    QMap< int, EyeParaSetForm* > existingForms;

    QLayout* layout = currentWidget->layout();
    if ( layout )
    {
        // 遍历现有布局，查找EyeParaSetForm控件
        for ( int i = 0; i < layout->count(); i++ )
        {
            QLayoutItem* item = layout->itemAt( i );
            if ( item && item->layout() )
            {
                for ( int j = 0; j < item->layout()->count(); j++ )
                {
                    QLayoutItem* childItem = item->layout()->itemAt( j );
                    if ( childItem && childItem->widget() )
                    {
                        EyeParaSetForm* form = qobject_cast< EyeParaSetForm* >( childItem->widget() );
                        if ( form )
                        {
                            existingForms[ form->getIndex() ] = form;
                        }
                    }
                }
            }
        }

        // 如果有现有控件，尝试更新它们的状态而不是重新创建
        if ( !existingForms.isEmpty() )
        {
            for ( auto it = existingForms.begin(); it != existingForms.end(); ++it )
            {
                // 根据索引是否小于eyeNum来设置enable状态
                it.value()->setEnable( it.key() <= activeSize );
                // 从配置文件中读取对应的值并更新控件
                auto eeyeCfgList = mainData->readMachineFileConfig->getEEyeCfg();
                for ( const auto& item : eeyeCfgList->items )
                {
                    if ( existingForms.contains( item.id ) )
                    {
                        quint8 value = 0;
                        if ( type_id == 1 )
                            value = item.sensitivity;
                        else if ( type_id == 2 )
                            value = item.inductionDelay;
                        else if ( type_id == 3 )
                            value = item.stFreq;
                        else if ( type_id == 4 )
                            value = item.endFreq;
                        else if ( type_id == 5 )
                            value = item.disConThreshold;
                        else if ( type_id == 6 )
                            value = item.wrapThreshold;
                        else if ( type_id == 7 )
                            value = item.mappedId;

                        existingForms[ item.id ]->setParams( value );
                    }
                }
            }
            return;  // 已更新现有控件，不需要重新创建
        }

        // 如果没有现有控件或需要重新创建，清空布局
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QHBoxLayout* mainLayout = new QHBoxLayout( currentWidget );
    // 一行只显示8个
    for ( quint8 line = 0; line < lines; line++ )
    {
        QVBoxLayout* columnLayoutx = new QVBoxLayout();
        columnLayoutx->setMargin( 0 );
        columnLayoutx->setSpacing( 0 );

        for ( quint8 col = 0; col < sizeOfline; col++ )
        {
            if ( line * sizeOfline + col < totalSize )
            {
                // 从配置文件中读取
                auto   eeyeCfgList = mainData->readMachineFileConfig->getEEyeCfg();
                auto   eyeCfg      = eeyeCfgList->items[ line * sizeOfline + col ];
                quint8 value       = 0;
                if ( type_id == 1 )
                    value = eyeCfg.sensitivity;
                else if ( type_id == 2 )
                    value = eyeCfg.inductionDelay;
                else if ( type_id == 3 )
                    value = eyeCfg.stFreq;
                else if ( type_id == 4 )
                    value = eyeCfg.endFreq;
                else if ( type_id == 5 )
                    value = eyeCfg.disConThreshold;
                else if ( type_id == 6 )
                    value = eyeCfg.wrapThreshold;
                else if ( type_id == 7 )
                    value = eyeCfg.mappedId;

                // 根据索引是否小于eyeNum来设置enable状态
                bool            enable = eyeCfg.id <= activeSize;
                EyeParaSetForm* form   = new EyeParaSetForm( this, type_id, eyeCfg.id, value, enable );
                connect( form, &EyeParaSetForm::onParamsEdited, this, &OneKeyForm::onEyeParamsEdited );
                columnLayoutx->addWidget( form );
            }
            else
            {
                QWidget* widget = new QWidget();
                columnLayoutx->addWidget( widget );
            }
        }
        mainLayout->addLayout( columnLayoutx );
    }
}

// 电眼的工作参数修改了，此处应保存到配置文件或执行
void OneKeyForm::onEyeParamsEdited( quint8 type, quint8 index, quint16 params )
{
    qDebug() << "onEyeParamsEdited" << type << index << params;

    // 更新配置文件中的参数
    auto eeyeCfgList = mainData->readMachineFileConfig->getEEyeCfg();

    // 查找对应id的电眼配置
    for ( int i = 0; i < eeyeCfgList->items.size(); i++ )
    {
        if ( eeyeCfgList->items[ i ].id == index )
        {
            // 根据type更新不同的参数
            if ( type == 1 )
                eeyeCfgList->items[ i ].sensitivity = params;
            else if ( type == 2 )
                eeyeCfgList->items[ i ].inductionDelay = params;
            else if ( type == 3 )
                eeyeCfgList->items[ i ].stFreq = params;
            else if ( type == 4 )
                eeyeCfgList->items[ i ].endFreq = params;
            else if ( type == 5 )
                eeyeCfgList->items[ i ].disConThreshold = params;
            else if ( type == 6 )
                eeyeCfgList->items[ i ].wrapThreshold = params;
            else if ( type == 7 )
                eeyeCfgList->items[ i ].mappedId = params;
            break;
        }
    }
}

// 刷新电眼状态函数
void OneKeyForm::EEyeRefreshStatus( quint8* statusData, int dataLength )
{
    // 切换到tab_eye_7页面
    ui->tabWidget->setCurrentWidget( ui->tab_eye_7 );

    // 获取tab_eye_7的布局
    QLayout* layout = ui->tab_eye_7->layout();
    if ( !layout )
        return;

    // 保存现有控件的引用
    QMap< int, EyeParaSetForm* > eyeForms;

    // 遍历布局查找EyeParaSetForm控件
    for ( int i = 0; i < layout->count(); i++ )
    {
        QLayoutItem* item = layout->itemAt( i );
        if ( item && item->layout() )
        {
            for ( int j = 0; j < item->layout()->count(); j++ )
            {
                QLayoutItem* childItem = item->layout()->itemAt( j );
                if ( childItem && childItem->widget() )
                {
                    EyeParaSetForm* form = qobject_cast< EyeParaSetForm* >( childItem->widget() );
                    if ( form )
                    {
                        eyeForms[ form->getIndex() ] = form;
                    }
                }
            }
        }
    }

    // 根据传入的状态数据更新控件状态
    int bitCount = 0;
    for ( int byteIndex = 0; byteIndex < dataLength && bitCount < eyeNum; byteIndex++ )
    {
        quint8 currentByte = statusData[ byteIndex ];

        // 处理当前字节的8个位
        for ( int bitIndex = 0; bitIndex < 8 && bitCount < eyeNum; bitIndex++ )
        {
            // 计算当前处理的电眼索引
            int eyeIndex = byteIndex * 8 + bitIndex + 1;  // 索引从1开始

            // 检查该位是否为1
            bool status = ( currentByte & ( 1 << bitIndex ) ) != 0;

            // 更新对应控件的状态
            if ( eyeForms.contains( eyeIndex ) )
            {
                eyeForms[ eyeIndex ]->setStatus( status );
            }

            bitCount++;
        }
    }
}

// 处理电眼配置帧的槽函数
void OneKeyForm::onEEyeConfigFrameReceived( qint16 size, quint8* data )
{
    // 不是电眼设置页，不刷新
    if ( ui->stackedWidget->currentIndex() != 1 )
        return;

    qDebug() << "onEEyeConfigFrameReceived size:" << size;

    // 判断第0字节是否为0x00
    if ( data[ 0 ] == 0x00 )
    {
        // 判断数据长度是否足够
        if ( size >= 6 )
        {
            // 调用EEyeRefreshStatus方法刷新状态，传入剩余5字节
            EEyeRefreshStatus( &data[ 1 ], 5 );
        }
    }
    // 重新编址
    else if ( data[ 0 ] == 0x01 )
    {
        if ( data[ 1 ] == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "重新编址成功" );
        }
        else if ( data[ 1 ] == 0 )
        {
            QMessageBox::warning( nullptr, "错误提示", "重新编址失败" );
        }
    }
    // 单独编址
    else if ( data[ 0 ] == 0x02 )
    {
        if ( data[ 1 ] == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "单独编址成功" );
        }
        else if ( data[ 1 ] == 0 )
        {
            QMessageBox::warning( nullptr, "错误提示", "单独编址失败" );
        }
    }
    // 设置参数
    else if ( data[ 0 ] == 0x03 )
    {
        if ( data[ 1 ] == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "设置参数成功" );
        }
        else if ( data[ 1 ] == 0 )
        {
            QMessageBox::warning( nullptr, "错误提示", "设置参数失败" );
        }
    }

    // 判断是否为学习状态结束信号
    else if ( data[ 0 ] == 0x05 )
    {
        // 结束学习状态
        setEyeLearningState( false );
        if ( data[ 1 ] == 1 )
        {
            QMessageBox::information( nullptr, "操作提示", "重新学习成功" );
        }
        else if ( data[ 1 ] == 0 )
        {
            QMessageBox::warning( nullptr, "错误提示", "重新学习失败" );
        }
    }
}

void OneKeyForm::onEEyeBtnGroupClicked( int id )
{
    qDebug() << "onEEyeBtnGroupClicked" << id;
    switch ( id )
    {
        // 状态刷新
        case 0:
        {
            // 切换到tab_eye_7页面
            ui->tabWidget->setCurrentWidget( ui->tab_eye_7 );
            quint8 buffer[ 1 ] = { 0x00 };
            comm->pushDataTobuffer( 0x0C, buffer, 1 );
        }
        break;
        // 重新编址
        case 1:
        {
            quint8 buffer[ 2 ] = { 0x01, eyeNum };
            comm->pushDataTobuffer( 0x0C, buffer, 2 );
        }
        break;
        // 重新学习
        case 2:
        {
            // 切换学习状态
            bool newState = !isEyeLearning;
            setEyeLearningState( newState );

            // 发送学习命令到下位机
            quint8 buffer[ 1 ] = { 0x05 };
            comm->pushDataTobuffer( 0x0C, buffer, 1 );
        }
        break;
        // 自动测试
        case 3:
        {
            // 发送命令到下位机
            quint8 buffer[ 1 ] = { 0x06 };
            comm->pushDataTobuffer( 0x0C, buffer, 1 );
        }
        break;
        // 默认参数
        case 4:
        {
            // 获取电眼配置列表
            auto eeyeCfgList = mainData->readMachineFileConfig->getEEyeCfg();

            // 遍历所有电眼配置
            for ( auto& item : eeyeCfgList->items )
            {
                // mappedId设置为与id一致
                item.mappedId = item.id;
                // 其他参数设置为0
                item.sensitivity     = 0;
                item.inductionDelay  = 0;
                item.stFreq          = 0;
                item.endFreq         = 0;
                item.disConThreshold = 0;
                item.wrapThreshold   = 0;
            }

            // 刷新所有tab显示
            refreshTabControls( 1 );
            refreshTabControls( 2 );
            refreshTabControls( 3 );
            refreshTabControls( 4 );
            refreshTabControls( 5 );
            refreshTabControls( 6 );
            refreshTabControls( 7 );
        }
        break;
        // 单独编址
        case 5:
        {
            NumberInputForm* form = new NumberInputForm( this, "请输入光电眼编号", 1, 36 );
            form->setAttribute( Qt::WA_DeleteOnClose );
            form->show();

            connect( form, &NumberInputForm::InputFinished, this, [&]( QString text ) {
                quint8 num         = text.toUInt();
                quint8 buffer[ 2 ] = { 0x02, num };
                comm->pushDataTobuffer( 0x0C, buffer, 2 );
            } );
        }
        break;
        // 保存参数
        case 6:
            if ( mainData->readMachineFileConfig->saveEEyeConfig( MAIN_CFG_DIR + "MachineFileConfig.json" ) == 1 )
            {
                QMessageBox::information( nullptr, "操作提示", "保存成功" );
                // 发送配置到下位机
                // 注意:不能直接对指针进行memcpy,需要先获取实际的数据结构
                auto    eeyeCfgList = mainData->readMachineFileConfig->getEEyeCfg();
                quint16 bufferSize  = 1 + 36 * sizeof( ReadMachineFileConfig::EEyeItemStruct );
                quint8  buffer[ bufferSize ];
                buffer[ 0 ] = eeyeCfgList->realCount;
                for ( int i = 0; i < 36; i++ )
                {
                    ReadMachineFileConfig::EEyeItemStruct* configData = &eeyeCfgList->items[ i ];
                    memcpy( buffer + 1 + i * sizeof( ReadMachineFileConfig::EEyeItemStruct ), configData, sizeof( ReadMachineFileConfig::EEyeItemStruct ) );
                }
                comm->pushDataTobuffer( 0x0C, buffer, bufferSize );
                qDebug() << bufferSize;
            }
            else
            {
                QMessageBox::warning( nullptr, "错误提示", "保存失败" );
            }
            break;
        // 单独测试
        case 7:
        {
            NumberInputForm* form = new NumberInputForm( this, "请输入光电眼编号", 1, 36 );
            form->setAttribute( Qt::WA_DeleteOnClose );
            form->show();

            connect( form, &NumberInputForm::InputFinished, this, [&]( QString text ) {
                quint8 num         = text.toUInt();
                quint8 buffer[ 2 ] = { 0x07, num };
                comm->pushDataTobuffer( 0x0C, buffer, 2 );
            } );
        }
        break;
    }
}

// 设置电眼学习状态
void OneKeyForm::setEyeLearningState( bool learning )
{
    isEyeLearning = learning;

    // 设置学习按钮的样式
    if ( learning )
    {
        // 学习状态 - 设置浅蓝色背景
        ui->btn_Craft_eye_3->setStyleSheet( "background-color: lightblue;" );

        // 禁用其他按钮和电眼数量输入
        for ( int i = 0; i < eeyeBtnGroup->buttons().size(); i++ )
        {
            if ( i != 2 )  // 除了学习按钮
                eeyeBtnGroup->button( i )->setEnabled( false );
        }
        ui->le_eyeNum->setEnabled( false );
    }
    else
    {
        // 恢复正常状态
        ui->btn_Craft_eye_3->setStyleSheet( "" );

        // 启用所有按钮和电眼数量输入
        for ( int i = 0; i < eeyeBtnGroup->buttons().size(); i++ )
        {
            eeyeBtnGroup->button( i )->setEnabled( true );
        }
        ui->le_eyeNum->setEnabled( true );
    }
}
