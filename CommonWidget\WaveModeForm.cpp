#include "WaveModeForm.h"
#include "ui_WaveModeForm.h"
#include <QScreen>
#include <QApplication>

WaveModeForm::WaveModeForm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::WaveModeForm)
{
    ui->setupUi(this);
    
    // 设置窗口标题和属性
    setWindowTitle("编织模式选择");
    setWindowFlags(Qt::Dialog | Qt::WindowCloseButtonHint);
    
    // 窗口居中显示
    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);
    
    // 连接按钮信号到槽函数
    connect(ui->btnContinue, &QPushButton::clicked, this, &WaveModeForm::onContinueButtonClicked);
    connect(ui->btnRestart, &QPushButton::clicked, this, &WaveModeForm::onRestartButtonClicked);
}

WaveModeForm::~WaveModeForm()
{
    delete ui;
}

// 断电续织按钮点击处理
void WaveModeForm::onContinueButtonClicked()
{
    emit waveModeSelected(WaveMode::ContinueMode);
    close();
}

// 重新编织按钮点击处理
void WaveModeForm::onRestartButtonClicked()
{
    emit waveModeSelected(WaveMode::RestartMode);
    close();
}
