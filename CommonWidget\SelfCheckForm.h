#ifndef SELFCHECKFORM_H
#define SELFCHECKFORM_H

#include <QLabel>
#include <QMap>
#include <QWidget>

namespace Ui
{
class SelfCheckForm;
}

class SelfCheckForm : public QWidget
{
    Q_OBJECT

public:
    explicit SelfCheckForm( QWidget* parent = nullptr );
    ~SelfCheckForm();

    // 更新自检结果
    // step: 16位掩码，表示哪些步骤需要更新（1表示需要更新）
    // result: 16位掩码，表示各步骤的结果（1表示OK，0表示ERR）
    void updateStepResult( quint16 step, quint16 result );

    // 更新键盘检测结果
    // result: true表示OK，false表示ERR
    void updateKeyBoardResult(bool result);

    // 清空所有自检结果
    void clearAllResults();

private:
    Ui::SelfCheckForm*    ui;
    QMap< QString, bool > valveResults;  // 存储气阀检测结果

    // 获取指定步骤的结果标签
    QLabel* getStepResultLabel( int stepIndex );
};

#endif  // SELFCHECKFORM_H
