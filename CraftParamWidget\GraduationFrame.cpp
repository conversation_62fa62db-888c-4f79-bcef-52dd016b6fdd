#include "CommonWidget/letterinputdialog.h"
#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>
#include <QLabel>

// 初始化毕业电机框架
void CraftParamForm::initGraduationFrame()
{
    // 清空之前的控件
    if ( !graduationIndexLabels.isEmpty() )
    {
        for ( auto label : graduationIndexLabels )
        {
            delete label;
        }
        graduationIndexLabels.clear();
    }

    if ( !graduationNameLabels.isEmpty() )
    {
        for ( auto label : graduationNameLabels )
        {
            delete label;
        }
        graduationNameLabels.clear();
    }

    if ( !graduationStepLabels.isEmpty() )
    {
        for ( auto label : graduationStepLabels )
        {
            delete label;
        }
        graduationStepLabels.clear();
    }

    if ( !graduationIdEdits.isEmpty() )
    {
        for ( auto edit : graduationIdEdits )
        {
            delete edit;
        }
        graduationIdEdits.clear();
    }

    if ( !graduationProhibitLabels.isEmpty() )
    {
        for ( auto label : graduationProhibitLabels )
        {
            delete label;
        }
        graduationProhibitLabels.clear();
    }

    if ( !graduationStartLabels.isEmpty() )
    {
        for ( auto label : graduationStartLabels )
        {
            delete label;
        }
        graduationStartLabels.clear();
    }

    if ( !graduationEndLabels.isEmpty() )
    {
        for ( auto label : graduationEndLabels )
        {
            delete label;
        }
        graduationEndLabels.clear();
    }

    if ( !graduationStartNewEdits.isEmpty() )
    {
        for ( auto edit : graduationStartNewEdits )
        {
            delete edit;
        }
        graduationStartNewEdits.clear();
    }

    if ( !graduationEndNewEdits.isEmpty() )
    {
        for ( auto edit : graduationEndNewEdits )
        {
            delete edit;
        }
        graduationEndNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout( ui->graduationFrame );

    // 创建标题行
    QLabel* indexHeader    = new QLabel( "序号", ui->graduationFrame );
    QLabel* nameHeader     = new QLabel( "步骤名称", ui->graduationFrame );
    QLabel* stepHeader     = new QLabel( "步", ui->graduationFrame );
    QLabel* idHeader       = new QLabel( "ID", ui->graduationFrame );
    QLabel* prohibitHeader = new QLabel( "P", ui->graduationFrame );
    QLabel* startHeader    = new QLabel( "起始值", ui->graduationFrame );
    QLabel* endHeader      = new QLabel( "结束值", ui->graduationFrame );
    QLabel* startNewHeader = new QLabel( "N起始值", ui->graduationFrame );
    QLabel* endNewHeader   = new QLabel( "N结束值", ui->graduationFrame );

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet( headerStyle );
    nameHeader->setStyleSheet( headerStyle );
    stepHeader->setStyleSheet( headerStyle );
    idHeader->setStyleSheet( headerStyle );
    prohibitHeader->setStyleSheet( headerStyle );
    startHeader->setStyleSheet( headerStyle );
    endHeader->setStyleSheet( headerStyle );
    startNewHeader->setStyleSheet( headerStyle );
    endNewHeader->setStyleSheet( headerStyle );

    // 设置对齐方式
    indexHeader->setAlignment( Qt::AlignCenter );
    nameHeader->setAlignment( Qt::AlignCenter );
    stepHeader->setAlignment( Qt::AlignCenter );
    idHeader->setAlignment( Qt::AlignCenter );
    prohibitHeader->setAlignment( Qt::AlignCenter );
    startHeader->setAlignment( Qt::AlignCenter );
    endHeader->setAlignment( Qt::AlignCenter );
    startNewHeader->setAlignment( Qt::AlignCenter );
    endNewHeader->setAlignment( Qt::AlignCenter );

    // 添加标题到布局
    layout->addWidget( indexHeader, 0, 0 );
    layout->addWidget( nameHeader, 0, 1 );
    layout->addWidget( stepHeader, 0, 2 );
    layout->addWidget( idHeader, 0, 3 );
    layout->addWidget( prohibitHeader, 0, 4 );
    layout->addWidget( startHeader, 0, 5 );
    layout->addWidget( endHeader, 0, 6 );
    layout->addWidget( startNewHeader, 0, 7 );
    layout->addWidget( endNewHeader, 0, 8 );

    // 为每一行创建控件
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        // 序号标签
        QLabel* indexLabel = new QLabel( QString::number( i + 1 ), ui->graduationFrame );
        indexLabel->setAlignment( Qt::AlignCenter );
        graduationIndexLabels.append( indexLabel );
        layout->addWidget( indexLabel, i + 1, 0 );

        // 步骤名称标签
        QLabel* nameLabel = new QLabel( "", ui->graduationFrame );
        nameLabel->setAlignment( Qt::AlignCenter );
        graduationNameLabels.append( nameLabel );
        layout->addWidget( nameLabel, i + 1, 1 );

        // 步标签
        QLabel* stepLabel = new QLabel( "", ui->graduationFrame );
        stepLabel->setAlignment( Qt::AlignCenter );
        graduationStepLabels.append( stepLabel );
        layout->addWidget( stepLabel, i + 1, 2 );

        // ID编辑框
        MyLineEdit* idEdit = new MyLineEdit( ui->graduationFrame );
        idEdit->setAlignment( Qt::AlignCenter );
        idEdit->setReadOnly( true );         // 初始设为只读
        idEdit->setProperty( "row", i );     // 存储行索引
        idEdit->setProperty( "column", 3 );  // 存储列索引
        connect( idEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentGraduationPage * ITEMS_PER_PAGE + i;

            if ( dataIndex < craftParams.graduationMotorParam.size() && !craftParams.graduationMotorParam[ dataIndex ]->prohibitsValueChange )
            {
                // 清空之前的内存
                if ( this->letterInputDialog != nullptr )
                {
                    delete this->letterInputDialog;
                    this->letterInputDialog = nullptr;
                }

                // 创建字母输入对话框
                this->letterInputDialog = new LetterInputDialog( nullptr, "修改ID", "ID:" );

                // 存储当前编辑的行和列
                tableEditRowIndex = dataIndex;
                tableIndex        = 2;  // 毕业电机表格
                tableEditColIndex = 3;  // ID列

                // 连接完成信号
                connect( this->letterInputDialog, &LetterInputDialog::InputFinished, this, &CraftParamForm::onLetterInputDialogFinished );

                // 显示对话框
                this->letterInputDialog->show();
            }
        } );
        graduationIdEdits.append( idEdit );
        layout->addWidget( idEdit, i + 1, 3 );

        // 禁止修改标签
        QLabel* prohibitLabel = new QLabel( "", ui->graduationFrame );
        prohibitLabel->setAlignment( Qt::AlignCenter );
        graduationProhibitLabels.append( prohibitLabel );
        layout->addWidget( prohibitLabel, i + 1, 4 );

        // 起始值标签
        QLabel* startLabel = new QLabel( "", ui->graduationFrame );
        startLabel->setAlignment( Qt::AlignCenter );
        graduationStartLabels.append( startLabel );
        layout->addWidget( startLabel, i + 1, 5 );

        // 结束值标签
        QLabel* endLabel = new QLabel( "", ui->graduationFrame );
        endLabel->setAlignment( Qt::AlignCenter );
        graduationEndLabels.append( endLabel );
        layout->addWidget( endLabel, i + 1, 6 );

        // 新起始值编辑框
        MyLineEdit* startNewEdit = new MyLineEdit( ui->graduationFrame );
        startNewEdit->setAlignment( Qt::AlignCenter );
        startNewEdit->setReadOnly( true );         // 初始设为只读
        startNewEdit->setProperty( "row", i );     // 存储行索引
        startNewEdit->setProperty( "column", 7 );  // 存储列索引
        connect( startNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentGraduationPage * ITEMS_PER_PAGE + i;

            if ( dataIndex < craftParams.graduationMotorParam.size() && !craftParams.graduationMotorParam[ dataIndex ]->prohibitsValueChange )
            {
                // 清空之前的内存
                if ( this->numberInputForm != nullptr )
                {
                    delete this->numberInputForm;
                    this->numberInputForm = nullptr;
                }

                // 创建数字输入表单
                this->numberInputForm = new NumberInputForm( nullptr, "起始值", 0, 4095 );

                // 存储当前编辑的行和列
                tableEditRowIndex = dataIndex;
                tableIndex        = 2;  // 毕业电机表格
                tableEditColIndex = 7;  // 新起始值列

                // 连接完成信号
                connect( this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished );

                // 显示表单
                this->numberInputForm->show();
            }
        } );
        graduationStartNewEdits.append( startNewEdit );
        layout->addWidget( startNewEdit, i + 1, 7 );

        // 新结束值编辑框
        MyLineEdit* endNewEdit = new MyLineEdit( ui->graduationFrame );
        endNewEdit->setAlignment( Qt::AlignCenter );
        endNewEdit->setReadOnly( true );         // 初始设为只读
        endNewEdit->setProperty( "row", i );     // 存储行索引
        endNewEdit->setProperty( "column", 8 );  // 存储列索引
        connect( endNewEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentGraduationPage * ITEMS_PER_PAGE + i;

            if ( dataIndex < craftParams.graduationMotorParam.size() && !craftParams.graduationMotorParam[ dataIndex ]->prohibitsValueChange )
            {
                // 清空之前的内存
                if ( this->numberInputForm != nullptr )
                {
                    delete this->numberInputForm;
                    this->numberInputForm = nullptr;
                }

                // 创建数字输入表单
                this->numberInputForm = new NumberInputForm( nullptr, "结束值", 0, 4095 );

                // 存储当前编辑的行和列
                tableEditRowIndex = dataIndex;
                tableIndex        = 2;  // 毕业电机表格
                tableEditColIndex = 8;  // 新结束值列

                // 连接完成信号
                connect( this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished );

                // 显示表单
                this->numberInputForm->show();
            }
        } );
        graduationEndNewEdits.append( endNewEdit );
        layout->addWidget( endNewEdit, i + 1, 8 );
    }

    // 设置布局属性
    layout->setColumnStretch( 0, 1 );  // 序号
    layout->setColumnStretch( 1, 3 );  // 步骤名称
    layout->setColumnStretch( 2, 2 );  // 步
    layout->setColumnStretch( 3, 1 );  // ID
    layout->setColumnStretch( 4, 1 );  // P
    layout->setColumnStretch( 5, 2 );  // 起始值
    layout->setColumnStretch( 6, 2 );  // 结束值
    layout->setColumnStretch( 7, 2 );  // N起始值
    layout->setColumnStretch( 8, 2 );  // N结束值
    layout->setSpacing( 5 );
    layout->setContentsMargins( 5, 5, 5, 5 );

    // 应用布局
    ui->graduationFrame->setLayout( layout );
}

// 更新毕业电机页面
void CraftParamForm::updateGraduationPage()
{
    // 计算总页数
    totalGraduationPages = ( craftParams.graduationMotorParam.size() + ITEMS_PER_PAGE - 1 ) / ITEMS_PER_PAGE;

    // 确保当前页在有效范围内
    if ( currentGraduationPage >= totalGraduationPages )
    {
        currentGraduationPage = totalGraduationPages - 1;
    }
    if ( currentGraduationPage < 0 )
    {
        currentGraduationPage = 0;
    }

    // 计算当前页的起始索引
    int startIndex = currentGraduationPage * ITEMS_PER_PAGE;

    // 更新控件显示
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        int dataIndex = startIndex + i;

        if ( dataIndex < craftParams.graduationMotorParam.size() )
        {
            // 有数据，显示
            auto param = craftParams.graduationMotorParam[ dataIndex ];

            graduationIndexLabels[ i ]->setText( QString::number( dataIndex + 1 ) );
            graduationNameLabels[ i ]->setText( QString::fromLatin1( param->blockName ) );
            graduationStepLabels[ i ]->setText( QString::number( param->stepStart ) + "~" + QString::number( param->stepEnd ) );
            graduationIdEdits[ i ]->setText( QString( param->graduationId ) );
            graduationProhibitLabels[ i ]->setText( param->prohibitsValueChange ? "N" : "Y" );
            graduationStartLabels[ i ]->setText( QString::number( param->cylinderStart[ currentSize - 1 ] ) );
            graduationEndLabels[ i ]->setText( QString::number( param->cylinderEnd[ currentSize - 1 ] ) );
            graduationStartNewEdits[ i ]->setText( QString::number( param->cylinderStart[ currentSize - 1 ] ) );
            graduationEndNewEdits[ i ]->setText( QString::number( param->cylinderEnd[ currentSize - 1 ] ) );

            // 设置可编辑状态
            bool editable = !param->prohibitsValueChange;
            graduationIdEdits[ i ]->setEnabled( editable );
            graduationStartNewEdits[ i ]->setEnabled( editable );
            graduationEndNewEdits[ i ]->setEnabled( editable );

            // 设置背景色
            if ( editable )
            {
                graduationIdEdits[ i ]->setStyleSheet( "background-color: #ffffc8;" );
                graduationStartNewEdits[ i ]->setStyleSheet( "background-color: #ffffc8;" );
                graduationEndNewEdits[ i ]->setStyleSheet( "background-color: #ffffc8;" );
            }
            else
            {
                graduationIdEdits[ i ]->setStyleSheet( "" );
                graduationStartNewEdits[ i ]->setStyleSheet( "" );
                graduationEndNewEdits[ i ]->setStyleSheet( "" );
            }

            // 显示所有控件
            graduationIndexLabels[ i ]->setVisible( true );
            graduationNameLabels[ i ]->setVisible( true );
            graduationStepLabels[ i ]->setVisible( true );
            graduationIdEdits[ i ]->setVisible( true );
            graduationProhibitLabels[ i ]->setVisible( true );
            graduationStartLabels[ i ]->setVisible( true );
            graduationEndLabels[ i ]->setVisible( true );
            graduationStartNewEdits[ i ]->setVisible( true );
            graduationEndNewEdits[ i ]->setVisible( true );

            // 恢复正常样式（除了可编辑的控件）
            graduationIndexLabels[ i ]->setStyleSheet( "" );
            graduationNameLabels[ i ]->setStyleSheet( "" );
            graduationStepLabels[ i ]->setStyleSheet( "" );
            graduationProhibitLabels[ i ]->setStyleSheet( "" );
            graduationStartLabels[ i ]->setStyleSheet( "" );
            graduationEndLabels[ i ]->setStyleSheet( "" );
        }
        else
        {
            // 无数据，但保持控件可见以占据空间
            graduationIndexLabels[ i ]->setText( "" );
            graduationNameLabels[ i ]->setText( "" );
            graduationStepLabels[ i ]->setText( "" );
            graduationIdEdits[ i ]->setText( "" );
            graduationProhibitLabels[ i ]->setText( "" );
            graduationStartLabels[ i ]->setText( "" );
            graduationEndLabels[ i ]->setText( "" );
            graduationStartNewEdits[ i ]->setText( "" );
            graduationEndNewEdits[ i ]->setText( "" );

            // 控件保持可见，但设置为透明
            graduationIndexLabels[ i ]->setVisible( true );
            graduationNameLabels[ i ]->setVisible( true );
            graduationStepLabels[ i ]->setVisible( true );
            graduationIdEdits[ i ]->setVisible( true );
            graduationProhibitLabels[ i ]->setVisible( true );
            graduationStartLabels[ i ]->setVisible( true );
            graduationEndLabels[ i ]->setVisible( true );
            graduationStartNewEdits[ i ]->setVisible( true );
            graduationEndNewEdits[ i ]->setVisible( true );

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            graduationIndexLabels[ i ]->setStyleSheet( transparentStyle );
            graduationNameLabels[ i ]->setStyleSheet( transparentStyle );
            graduationStepLabels[ i ]->setStyleSheet( transparentStyle );
            graduationIdEdits[ i ]->setStyleSheet( transparentStyle );
            graduationProhibitLabels[ i ]->setStyleSheet( transparentStyle );
            graduationStartLabels[ i ]->setStyleSheet( transparentStyle );
            graduationEndLabels[ i ]->setStyleSheet( transparentStyle );
            graduationStartNewEdits[ i ]->setStyleSheet( transparentStyle );
            graduationEndNewEdits[ i ]->setStyleSheet( transparentStyle );

            // 禁用编辑
            graduationIdEdits[ i ]->setEnabled( false );
            graduationStartNewEdits[ i ]->setEnabled( false );
            graduationEndNewEdits[ i ]->setEnabled( false );
        }
    }


}
