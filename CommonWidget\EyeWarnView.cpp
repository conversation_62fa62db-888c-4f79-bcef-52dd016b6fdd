#include "EyeWarnView.h"
#include <QPainter>

EyeWarnView::EyeWarnView(const QString& title, QWidget *parent) 
    : QWidget(parent), titleText(title)
{
    // 设置固定大小：
    // 宽度：标题宽度 + 间距 + 16个色块 * (20px + 2px边框 * 2)
    // 高度：2行 * (20px + 2px边框 * 2) + 数字标签高度 * 2 + 行间距
    setFixedSize(TITLE_WIDTH + TITLE_MARGIN + COLS_PER_ROW * (BLOCK_SIZE + BORDER_WIDTH * 2), 
                ROWS * (BLOCK_SIZE + BORDER_WIDTH * 2) + NUMBER_HEIGHT * 2 + ROW_SPACING);
}

void EyeWarnView::setValue(quint32 value)
{
    if (currentValue != value)
    {
        currentValue = value;
        update();  // 触发重绘
    }
}

void EyeWarnView::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制标题
    painter.setPen(Qt::black);
    painter.setFont(QFont("Microsoft YaHei", 9));
    QRect titleRect(0, 0, TITLE_WIDTH, height());
    painter.drawText(titleRect, Qt::AlignVCenter | Qt::AlignLeft, titleText);

    // 遍历32位
    for (int i = 0; i < 32; i++)
    {
        // 计算行和列
        int row = i / COLS_PER_ROW;
        int col = i % COLS_PER_ROW;
        
        // 计算当前色块的位置，需要加上标题宽度和间距
        int x = TITLE_WIDTH + TITLE_MARGIN + col * (BLOCK_SIZE + BORDER_WIDTH * 2);
        int y = row * (BLOCK_SIZE + BORDER_WIDTH * 2 + NUMBER_HEIGHT + ROW_SPACING) + NUMBER_HEIGHT;
        
        // 检查对应位的值
        bool bitValue = (currentValue >> i) & 0x01;
        
        // 设置边框颜色（黑色）
        painter.setPen(QPen(Qt::black, BORDER_WIDTH));
        
        // 设置填充颜色（1为红色，0为绿色）
        painter.setBrush(bitValue ? Qt::red : Qt::green);
        
        // 绘制矩形
        painter.drawRect(x + BORDER_WIDTH, 
                        y + BORDER_WIDTH, 
                        BLOCK_SIZE, 
                        BLOCK_SIZE);

        // 在指定位置绘制数字标记
        if (i == 0 || i == 4 || i == 8 || i == 12 || i == 15 || i == 16 || 
            i == 20 || i == 24 || i == 28 || i == 31)
        {
            painter.setPen(Qt::black);
            painter.setFont(QFont("Arial", 8));
            painter.drawText(x + BORDER_WIDTH, 
                           y - NUMBER_HEIGHT + 2,
                           BLOCK_SIZE, 
                           NUMBER_HEIGHT - 2, 
                           Qt::AlignCenter, 
                           QString::number(i + 1));
        }
    }
}