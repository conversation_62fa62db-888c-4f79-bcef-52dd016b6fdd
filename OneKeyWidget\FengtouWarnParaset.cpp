#include "CommonWidget/mylineedit.h"
#include "Config/readmachinefileconfig.h"
#include "OneKeyForm.h"
#include "ui_OneKeyForm.h"
#include <QDebug>
#include <QLabel>
#include <QLayout>
#include <QMessageBox>
#include <QWidget>

void OneKeyForm::initFengtouWarnParasetTab()
{
    // 清除旧的控件（如果有）
    QList< QWidget* > childWidgets = ui->gbox_fengtouWarn->findChildren< QWidget* >();
    for ( QWidget* widget : childWidgets )
    {
        widget->deleteLater();
    }

    // 创建网格布局
    QGridLayout* gridLayout = new QGridLayout( ui->gbox_fengtouWarn );
    gridLayout->setSpacing( 10 );
    gridLayout->setContentsMargins( 15, 20, 15, 15 );

    // 创建标题行
    QLabel* numLabel         = new QLabel( "序号", ui->gbox_fengtouWarn );
    QLabel* nameLabel        = new QLabel( "名称", ui->gbox_fengtouWarn );
    QLabel* enableLabel      = new QLabel( "使能", ui->gbox_fengtouWarn );
    QLabel* sensitivityLabel = new QLabel( "灵敏度", ui->gbox_fengtouWarn );
    QLabel* directionLabel   = new QLabel( "方向", ui->gbox_fengtouWarn );

    // 设置标题样式
    QString headerStyle = "font-weight: bold; color: #3498db; font-size: 10pt;";
    numLabel->setStyleSheet( headerStyle );
    nameLabel->setStyleSheet( headerStyle );
    enableLabel->setStyleSheet( headerStyle );
    sensitivityLabel->setStyleSheet( headerStyle );
    directionLabel->setStyleSheet( headerStyle );

    numLabel->setAlignment( Qt::AlignCenter );
    nameLabel->setAlignment( Qt::AlignCenter );
    enableLabel->setAlignment( Qt::AlignCenter );
    sensitivityLabel->setAlignment( Qt::AlignCenter );
    directionLabel->setAlignment( Qt::AlignCenter );

    // 添加标题到布局
    gridLayout->addWidget( numLabel, 0, 0 );
    gridLayout->addWidget( nameLabel, 0, 1 );
    gridLayout->addWidget( enableLabel, 0, 2 );
    gridLayout->addWidget( sensitivityLabel, 0, 3 );
    gridLayout->addWidget( directionLabel, 0, 4 );

    // 获取总元素数量
    int totalElements = readMachineFileConfig->getFengtouWarnCfgList()->count();

    // 设置当前页和总页数
    fengtouWarnCurrentPage = 0;
    fengtouWarnTotalPages  = ( totalElements + ITEMS_PER_PAGE - 1 ) / ITEMS_PER_PAGE;

    // 创建并显示第一页数据
    updateFengtouWarnPage();

    // 连接翻页按钮信号
    connect( ui->pbtn_FengtouWarnPrev, &QPushButton::clicked, this, &OneKeyForm::onFengtouWarnPrevPage );
    connect( ui->pbtn_FengtouWarnNext, &QPushButton::clicked, this, &OneKeyForm::onFengtouWarnNextPage );

    // 四个按键
    connect( ui->pbtn_FengtouWarnEnAll, &QPushButton::clicked, this, [&]() {
        for ( int i = 0; i < readMachineFileConfig->getFengtouWarnCfgList()->count(); i++ )
        {
            ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ i + 1 ];
            item.enable                                 = true;
        }
        // 更新当前页面显示
        updateFengtouWarnPage();
    } );

    connect( ui->pbtn_FengtouWarnDisAll, &QPushButton::clicked, this, [&]() {
        for ( int i = 0; i < readMachineFileConfig->getFengtouWarnCfgList()->count(); i++ )
        {
            ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ i + 1 ];
            item.enable                                 = false;
        }
        // 更新当前页面显示
        updateFengtouWarnPage();
    } );

    connect( ui->pbtn_FengtouWarnDirPAll, &QPushButton::clicked, this, [&]() {
        for ( int i = 0; i < readMachineFileConfig->getFengtouWarnCfgList()->count(); i++ )
        {
            ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ i + 1 ];
            item.direction                              = 1;
        }
        // 更新当前页面显示
        updateFengtouWarnPage();
    } );

    connect( ui->pbtn_FengtouWarnDirNAll, &QPushButton::clicked, this, [&]() {
        for ( int i = 0; i < readMachineFileConfig->getFengtouWarnCfgList()->count(); i++ )
        {
            ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ i + 1 ];
            item.direction                              = 0;
        }
        // 更新当前页面显示
        updateFengtouWarnPage();
    } );
}

// 更新缝头报警页面显示
void OneKeyForm::updateFengtouWarnPage()
{
    // 清除旧的控件（保留标题行）
    QGridLayout* gridLayout = qobject_cast< QGridLayout* >( ui->gbox_fengtouWarn->layout() );
    if ( !gridLayout )
        return;

    // 清除旧的数据行控件
    for ( int row = 1; row <= ITEMS_PER_PAGE; row++ )
    {
        for ( int col = 0; col < 5; col++ )
        {
            QLayoutItem* item = gridLayout->itemAtPosition( row, col );
            if ( item )
            {
                if ( item->widget() )
                {
                    item->widget()->hide();
                    item->widget()->deleteLater();
                }
                gridLayout->removeItem( item );
            }
        }
    }

    // 获取总元素数量
    int totalElements = readMachineFileConfig->getFengtouWarnCfgList()->count();

    // 计算当前页的起始和结束索引
    int startIdx = fengtouWarnCurrentPage * ITEMS_PER_PAGE;
    int endIdx   = qMin( startIdx + ITEMS_PER_PAGE, totalElements );

    // 添加数据行
    for ( int i = startIdx; i < endIdx; i++ )
    {
        int row = i - startIdx + 1;  // 行索引从1开始（0是标题行）

        ReadMachineFileConfig::WarnConfigItem item = readMachineFileConfig->getFengtouWarnCfgList()->value( i + 1 );

        // 创建控件
        QLabel* numLabel  = new QLabel( QString::number( i + 1 ), ui->gbox_fengtouWarn );
        QLabel* nameLabel = new QLabel( item.title, ui->gbox_fengtouWarn );

        MyLineEdit* enableEdit = new MyLineEdit( ui->gbox_fengtouWarn );
        enableEdit->setText( item.enable ? "开" : "关" );
        enableEdit->setReadOnly( true );
        enableEdit->setAlignment( Qt::AlignCenter );
        enableEdit->setProperty( "row", i );
        enableEdit->setProperty( "type", "enable" );

        MyLineEdit* sensitivityEdit = new MyLineEdit( ui->gbox_fengtouWarn );
        sensitivityEdit->setText( QString::number( item.sensitivity ) );
        sensitivityEdit->setReadOnly( true );
        sensitivityEdit->setAlignment( Qt::AlignCenter );
        sensitivityEdit->setProperty( "row", i );
        sensitivityEdit->setProperty( "type", "sensitivity" );

        MyLineEdit* directionEdit = new MyLineEdit( ui->gbox_fengtouWarn );
        directionEdit->setText( item.direction == 1 ? "正" : "反" );
        directionEdit->setReadOnly( true );
        directionEdit->setAlignment( Qt::AlignCenter );
        directionEdit->setProperty( "row", i );
        directionEdit->setProperty( "type", "direction" );

        // 设置样式
        QString labelStyle = "background-color: transparent; color: #333; font-size: 10pt;";
        QString editStyle  = "background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 4px; padding: 2px; font-size: 9pt;";

        numLabel->setStyleSheet( labelStyle );
        nameLabel->setStyleSheet( labelStyle );
        enableEdit->setStyleSheet( editStyle );
        sensitivityEdit->setStyleSheet( editStyle );
        directionEdit->setStyleSheet( editStyle );

        // 添加到布局
        gridLayout->addWidget( numLabel, row, 0 );
        gridLayout->addWidget( nameLabel, row, 1 );
        gridLayout->addWidget( enableEdit, row, 2 );
        gridLayout->addWidget( sensitivityEdit, row, 3 );
        gridLayout->addWidget( directionEdit, row, 4 );

        // 连接信号
        connect( enableEdit, &MyLineEdit::mousePress, this, &OneKeyForm::onFengtouWarnEditClicked );
        connect( sensitivityEdit, &MyLineEdit::mousePress, this, &OneKeyForm::onFengtouWarnEditClicked );
        connect( directionEdit, &MyLineEdit::mousePress, this, &OneKeyForm::onFengtouWarnEditClicked );
    }

    // 设置列宽比例
    gridLayout->setColumnStretch( 0, 1 );  // 序号
    gridLayout->setColumnStretch( 1, 3 );  // 名称
    gridLayout->setColumnStretch( 2, 1 );  // 使能
    gridLayout->setColumnStretch( 3, 1 );  // 灵敏度
    gridLayout->setColumnStretch( 4, 1 );  // 方向

    // 添加垂直空白占位，防止标题行在数据不足时被拉伸
    int currentRows = endIdx - startIdx + 1;  // 当前显示的行数（数据行）
    if ( currentRows < ITEMS_PER_PAGE )
    {  // 如果不足10行数据
        QSpacerItem* verticalSpacer = new QSpacerItem( 20, 20, QSizePolicy::Minimum, QSizePolicy::Expanding );
        gridLayout->addItem( verticalSpacer, currentRows + 1, 0, 1, 5 );  // 在数据行之后添加，跨越所有5列
    }
}

// 处理上一页按钮点击
void OneKeyForm::onFengtouWarnPrevPage()
{
    if ( fengtouWarnCurrentPage > 0 )
    {
        fengtouWarnCurrentPage--;
        updateFengtouWarnPage();
    }
    else
    {
        QMessageBox::information( this, "提示", "已经是第一页" );
    }
}

// 处理下一页按钮点击
void OneKeyForm::onFengtouWarnNextPage()
{
    if ( fengtouWarnCurrentPage < fengtouWarnTotalPages - 1 )
    {
        fengtouWarnCurrentPage++;
        updateFengtouWarnPage();
    }
    else
    {
        QMessageBox::information( this, "提示", "已经是最后一页" );
    }
}

// 处理编辑控件点击
void OneKeyForm::onFengtouWarnEditClicked()
{
    MyLineEdit* senderEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( !senderEdit )
        return;

    int     row  = senderEdit->property( "row" ).toInt();
    QString type = senderEdit->property( "type" ).toString();

    if ( type == "sensitivity" )
    {
        if ( this->numberInputFrm == nullptr )
        {
            this->numberInputFrm = new NumberInputForm();
            this->numberInputFrm->setAttribute( Qt::WA_DeleteOnClose );
            connect( this->numberInputFrm, &QObject::destroyed, this, [this]() { this->numberInputFrm = nullptr; } );
            connect( this->numberInputFrm, &NumberInputForm::InputFinished, this, [=]( QString value ) {
                ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ row + 1 ];
                item.sensitivity                            = value.toInt();
                updateFengtouWarnPage();
            } );
        }
        this->numberInputFrm->setInputText( senderEdit->text() );

        this->numberInputFrm->show();
    }
    else if ( type == "enable" )
    {
        // 生成Combo选择框所需的key-value对
        QMap< int, QString > list;
        list.insert( 0, "关" );
        list.insert( 1, "开" );

        // 清空之前的内存
        if ( this->comboFrm != nullptr )
        {
            delete this->comboFrm;
            this->comboFrm = nullptr;
        }

        this->comboFrm = new ComboForm( nullptr, &list );
        this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
        this->comboFrm->setWindowTitle( tr( "选择状态" ) );
        this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

        connect( this->comboFrm, &ComboForm::itemSelected, this, [=]( int id ) {
            ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ row + 1 ];
            item.enable                                 = ( id == 1 );
            updateFengtouWarnPage();
            this->comboFrm->close();
        } );

        this->comboFrm->show();
    }
    else if ( type == "direction" )
    {
        // 生成Combo选择框所需的key-value对
        QMap< int, QString > list;
        list.insert( 0, "反" );
        list.insert( 1, "正" );

        // 清空之前的内存
        if ( this->comboFrm != nullptr )
        {
            delete this->comboFrm;
            this->comboFrm = nullptr;
        }

        this->comboFrm = new ComboForm( nullptr, &list );
        this->comboFrm->setAttribute( Qt::WA_ShowModal, true );
        this->comboFrm->setWindowTitle( tr( "选择状态" ) );
        this->comboFrm->setWindowFlags( Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint | Qt::Tool );

        connect( this->comboFrm, &ComboForm::itemSelected, this, [=]( int id ) {
            ReadMachineFileConfig::WarnConfigItem& item = ( *readMachineFileConfig->getFengtouWarnCfgList() )[ row + 1 ];
            item.direction                              = id;
            updateFengtouWarnPage();
            this->comboFrm->close();
        } );

        this->comboFrm->show();
    }
}
