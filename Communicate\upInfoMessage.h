#ifndef UPINFOMESSAGE_H
#define UPINFOMESSAGE_H

#include <QList>

#pragma pack( 1 )
// 下位机上报数据相关
struct upMain
{
    quint16 motor_curent;
    quint16 fuzhu_current;
    quint16 sumpower;      // 总功率
    quint16 power_factor;  // 功率因素
    quint16 vot_phase;     // 输入电压及相数
    quint16 realSpeed;
};

struct upServ
{
    quint16 encoderVal;     // 编码器值
    quint16 angleVal;       // 角度值
    quint16 absAngle;       // 绝对角度
    quint16 absNeedlePos;   // 绝对针位
    quint16 zeroPos;        // 机械零位
    quint16 circleCount;    // 圈数
};

struct stepMotorVal
{
    quint16 current;
    quint16 encoderVal;
};

struct upStep
{
    stepMotorVal xiangjin;
    stepMotorVal zhentong;
    stepMotorVal chenjiang;
    stepMotorVal shengke;
    stepMotorVal zuoLj;
    stepMotorVal youLj;
    stepMotorVal zwFengmen;
    stepMotorVal jitou;
    stepMotorVal zhuashou;
    stepMotorVal baibiZY;
    stepMotorVal baibiSX;
    stepMotorVal fengtou;
    stepMotorVal fengchi;
    stepMotorVal fangai;
    stepMotorVal tuigan;
    stepMotorVal watong;
    stepMotorVal ftFengmen;
};

// 零位信号位定义
struct upZeroLimit
{
    quint8 needle_density : 1;  // bit0: 针筒密度零位
    quint8 sock_fan : 1;        // bit1: 织袜风门零位
    quint8 head_updown : 1;     // bit2: 机头升降零位
    quint8 sink_motor : 1;      // bit3: 沉降电机零位
    quint8 pick_motor : 1;      // bit4: 生克电机零位
    quint8 seam_fan : 1;        // bit5: 缝头风门零位
    quint8 sock_lr : 1;         // bit6: 袜筒左右零位
    quint8 sock_updown : 1;     // bit7: 袜筒升降零位

    quint8 grab_updown : 1;  // bit8: 抓手上下零位
    quint8 grab_limit : 1;   // bit9: 抓手上下限位
    quint8 arm_lr : 1;       // bit10: 摆臂左右零位
    quint8 arm_limit : 1;    // bit11: 摆臂左右限位
    quint8 arm_updown : 1;   // bit12: 摆臂上下零位
    quint8 seam_motor : 1;   // bit13: 缝头电机零位
    quint8 seameeth : 1;     // bit14: 缝齿电机零位
    quint8 cover_motor : 1;  // bit15: 翻盖电机零位

    quint8 cover_limit : 1;     // bit16: 翻盖电机限位
    quint8 left_angle : 1;      // bit17: 左棱角零位
    quint8 right_angle : 1;     // bit18: 右棱角零位
    quint8 push_motor : 1;      // bit19: 推杆电机零位
    quint8 sockube_updown : 1;  // bit20: 袜筒上下零位
    quint8 run_flag : 1;        // bit21: 开车标志
    quint8 stop_flag : 1;       // bit22: 停车标志
    quint8 jog_flag : 1;        // bit23: 点动标志

    quint8 emg_flag : 1;        // bit24: 急停标志
    quint8 sensor_12v_oc : 1;   // bit25: 传感器12V过流
    quint8 valve_24v_oc : 1;    // bit26: 气阀24V过流
    quint8 power_off_flag : 1;  // bit27: 掉电标志
    quint8 reserved : 4;        // bit28-31: 预留
};

// 织袜部分输入信号状态结构体
struct ZhiWaSignals
{
    // M系列信号 (拨针相关)
    quint8 power_signal : 1;         // M01: 掉电信号
    quint8 left_select_signal : 1;   // M02: 左拨针信号
    quint8 right_select_signal : 1;  // M03: 右拨针信号
    quint8 sock_drop_alarm : 1;      // M04: 织袜落袜报警
    quint8 m_reserved : 4;

    // A系列信号 (1-16)
    quint8 left_angle_half : 1;   // A01: 左菱角半位
    quint8 super_needle : 1;      // A02: 超针刀
    quint8 triangle_1c221 : 1;    // A03: 船袜三角1C221
    quint8 left_active : 1;       // A04: 左活络头
    quint8 rubber_knife : 1;      // A05: 橡筋刀
    quint8 press_knife_1 : 1;     // A06: 压针刀1
    quint8 right_angle_half : 1;  // A07: 右菱角半位
    quint8 back_circle : 1;       // A08: 退圈刀

    // A系列信号 (9-16)
    quint8 seam_needle_up : 1;    // A09: 缝头起针
    quint8 t2_flower_knife : 1;   // A10: T2提花刀
    quint8 right_angle_full : 1;  // A11: 右菱角全位
    quint8 t1_flower_knife : 1;   // A12: T1提花刀
    quint8 right_active : 1;      // A13: 右活络头
    quint8 triangle_3c223 : 1;    // A14: 船袜三角3C223
    quint8 left_angle_full : 1;   // A15: 左菱角全位
    quint8 bottom_fix : 1;        // A16: 脚底加固三角

    // A系列信号 (17-24)
    quint8 triangle_2c222 : 1;  // A17: 船袜三角2C222
    quint8 yarn_break_1 : 1;    // A18: 线架断线1
    quint8 yarn_break_2 : 1;    // A19: 线架断线2
    quint8 yarn_break_3 : 1;    // A20: 线加断线3
    quint8 tube_updown : 1;     // A21: 铜管升降
    quint8 needle_oil : 1;      // A22: 针筒锁子
    quint8 rubberension_1 : 1;  // A23: 橡筋张力1
    quint8 rubberension_2 : 1;  // A24: 橡筋张力2

    // A系列信号 (25-32)
    quint8 flower_needle_foot : 1;   // A25: 提花针针脚
    quint8 grab_handle : 1;          // A26: 摇手柄
    quint8 needle_detector : 1;      // A27: 针杆探测器
    quint8 power_12v_short : 1;      // A28: 12V电源短路
    quint8 sockension : 1;           // A29: 袜跟张力
    quint8 needle_break_detect : 1;  // A30: 断针探测器
    quint8 needle_detect_1 : 1;      // A31: 针舌探测器1
    quint8 needle_detect_2 : 1;      // A32: 针舌探测器2

    // A系列信号 (33-40)
    quint8 oil_pressure_low : 1;  // A33: 油压低
    quint8 oil_shortage : 1;      // A34: 缺油报警
    quint8 head_separate : 1;     // A35: 机头分离
    quint8 throat_plate : 1;      // A36: 咽弗盘到位
    quint8 circle_cut : 1;        // A37: 圆盘剪刀
    quint8 cut_yarn : 1;          // A38: 剪刀缠线
    quint8 ktf_alarm_1 : 1;       // A39: 1号KTF报警
    quint8 ktf_alarm_2 : 1;       // A40: 2号KTF报警

    // A系列信号 (41-46)
    quint8 throat_combine : 1;  // A41: 咽弗头离合
    quint8 butterfly_door : 1;  // A42: 蝴蝶门
    quint8 sock_detect : 1;     // A43: 移袜检测
    quint8 ktf_alarm_3 : 1;     // A44: 3号KTF报警
    quint8 air_pressure : 1;    // A45: 气压报警
    quint8 ktf_alarm_4 : 1;     // A46: 4号KTF报警
    quint8 reserved : 2;        // 预留2位
};

// 缝头输入信号状态结构体
struct SeamSignals
{
    // F01-F08 翻袜管相关信号
    quint8 sockube_drive : 1;  // F01: 翻袜管摆动销子
    quint8 press_up : 1;       // F02: 压盘下降上
    quint8 press_down : 1;     // F03: 压盘下降下
    quint8 seam_cut : 1;       // F04: 缝头剪刀
    quint8 seamension : 1;     // F05: 缝头线张力
    quint8 tooth_press : 1;    // F06: 套齿压环
    quint8 tube_lock_in : 1;   // F07: 翻袜管上升锁进
    quint8 tube_lock_out : 1;  // F08: 翻袜管上升锁退

    // F09-F16 感应和风筒信号
    quint8 grab_door_sensor : 1;  // F09: 抓手门感应
    quint8 windube_up : 1;        // F10: 吸风筒上
    quint8 windube_down : 1;      // F11: 吸风筒下
    quint8 swing_protect : 1;     // F12: 摆臂保护
    quint8 nc_13 : 1;             // F13: NC
    quint8 nc_14 : 1;             // F14: NC
    quint8 nc_15 : 1;             // F15: NC
    quint8 nc_16 : 1;             // F16: NC

    // F17-F24 控制和按钮信号
    quint8 nc_17 : 1;            // F17: NC
    quint8 nc_18 : 1;            // F18: NC
    quint8 nc_19 : 1;            // F19: NC
    quint8 nc_20 : 1;            // F20: NC
    quint8 nc_21 : 1;            // F21: NC
    quint8 nc_22 : 1;            // F22: NC
    quint8 nc_23 : 1;            // F23: NC
    quint8 seam_jog_button : 1;  // F24: 缝头点动按钮

    // F25-F32 缝头操作信号
    quint8 seam_stop_button : 1;   // F25: 缝头停止按钮
    quint8 seam_drop_alarm : 1;    // F26: 缝头落袜报警
    quint8 nc_27 : 1;              // F27: NC
    quint8 tube_sensor : 1;        // F28: 翻袜筒感应
    quint8 bottom_press_down : 1;  // F29: 下筒压板下降
    quint8 bottom_press_up : 1;    // F30: 下筒压板上升
    quint8 sock_sensor_up : 1;     // F31: 探袜感应上
    quint8 sock_sensor_down : 1;   // F32: 探袜感应下

    // F33-F40 执行机构信号
    quint8 insert_release : 1;  // F33: 挂刺放松
    quint8 insertight : 1;      // F34: 挂刺收紧
    quint8 seam_back : 1;       // F35: 缝头机后退
    quint8 seam_forward : 1;    // F36: 缝头机前进
    quint8 insert_sensor : 1;   // F37: 套刺感应
    quint8 grab_sock_pos : 1;   // F38: 抓柄翻袜位
    quint8 grab_up_limit : 1;   // F39: 抓柄上限位
    quint8 tube_up : 1;         // F40: 上接翻袜管升

    // F41-F42 附加信号
    quint8 tube_down : 1;     // F41: 上接翻袜管降
    quint8 insert_drive : 1;  // F42: 套刺翻转销子
    quint8 reserved : 6;      // 预留6位
};

// 1、织袜电磁阀报警位定义(80个报警位,使用10个字节)
struct ValveWarn
{
    // 第1组(1-8)
    quint8 c1_1 : 1;    // [三角气阀错]---C1:1 排1个
    quint8 c2_1 : 1;    // [三角气阀错]---C2:1 排2个
    quint8 c5_1 : 1;    // [三角气阀错]---C5:1 排3个
    quint8 c6_1 : 1;    // [三角气阀错]---C6:1 排4个
    quint8 c8_1 : 1;    // [三角气阀错]---C8:1 排5个
    quint8 s19_1 : 1;   // [特殊功能气阀错]---S19:1 排6个
    quint8 s9_1 : 1;    // [特殊功能气阀错]---S9:1 排7个
    quint8 left_1 : 1;  // [LEFT气阀错]---1 排8个

    // 第2组(9-16)
    quint8 left_2_1 : 1;  // [LEFT气阀错]---2 排1个
    quint8 left_2_2 : 1;  // [LEFT气阀错]---2 排2个
    quint8 s61_2 : 1;     // [特殊功能气阀错]---S61:2 排3个
    quint8 s62_2 : 1;     // [特殊功能气阀错]---S62:2 排4个
    quint8 s63_2 : 1;     // [特殊功能气阀错]---S63:2 排5个
    quint8 s64_2 : 1;     // [特殊功能气阀错]---S64:2 排6个
    quint8 s59_2_1 : 1;   // [门闩打进气阀错]---S59:2 排7个
    quint8 s59_2_2 : 1;   // [门闩退出气阀错]---S59:2 排8个

    // 第3组(17-24)
    quint8 s10_3 : 1;     // [特殊功能气阀错]---S10:3 排1个
    quint8 s5_3 : 1;      // [特殊功能气阀错]---S5:3 排2个
    quint8 s6_3 : 1;      // [特殊功能气阀错]---S6:3 排3个
    quint8 s95_3 : 1;     // [特殊功能气阀错]---S95:3 排4个
    quint8 s96_3 : 1;     // [特殊功能气阀错]---S96:3 排5个
    quint8 left_3_1 : 1;  // [LEFT气阀错]---3 排6个
    quint8 left_3_2 : 1;  // [LEFT气阀错]---3 排7个
    quint8 left_3_3 : 1;  // [LEFT气阀错]---3 排8个

    // 第4组(25-32)
    quint8 epr11_4 : 1;  // [扎口盘上升气阀错]---EPR11:4 排1个
    quint8 s1_4 : 1;     // [特殊功能气阀错]---S1:4 排2个
    quint8 s66_4 : 1;    // [特殊功能气阀错]---S66:4 排3个
    quint8 left_4 : 1;   // [LEFT气阀错]---4 排4个
    quint8 s69_4 : 1;    // [特殊功能气阀错]---S69:4 排5个
    quint8 s15_4 : 1;    // [特殊功能气阀错]---S15:4 排6个
    quint8 s108_4 : 1;   // [特殊功能气阀错]---S108:4 排7个
    quint8 s89_4 : 1;    // [特殊功能气阀错]---S89:4 排8个

    // 第5组(33-40)
    quint8 left_5 : 1;  // [LEFT气阀错]---5 排1个
    quint8 s30_5 : 1;   // [特殊功能气阀错]---S30:5 排2个
    quint8 s21_5 : 1;   // [特殊功能气阀错]---S21:5 排3个
    quint8 s22_5 : 1;   // [特殊功能气阀错]---S22:5 排4个
    quint8 s70_5 : 1;   // [特殊功能气阀错]---S70:5 排5个
    quint8 s56_5 : 1;   // [特殊功能气阀错]---S56:5 排6个
    quint8 c20_5 : 1;   // [三角气阀错]---C20:5 排7个
    quint8 c106_5 : 1;  // [三角气阀错]---C106:5 排8个

    // 第6组(41-48)
    quint8 s34_6 : 1;   // [特殊功能气阀错]---S34:6 排1个
    quint8 s29_6 : 1;   // [特殊功能气阀错]---S29:6 排2个
    quint8 s153_6 : 1;  // [特殊功能气阀错]---S153:6 排3个
    quint8 c25_6 : 1;   // [三角气阀错]---C25:6 排4个
    quint8 c107_6 : 1;  // [三角气阀错]---C107:6 排5个
    quint8 c63_6 : 1;   // [三角气阀错]---C63:6 排6个
    quint8 c64_6 : 1;   // [三角气阀错]---C64:6 排7个
    quint8 c23_6 : 1;   // [三角气阀错]---C23:6 排8个

    // 第7组(49-56)
    quint8 c26_7 : 1;  // [三角气阀错]---C26:7 排1个
    quint8 c18_7 : 1;  // [三角气阀错]---C18:7 排2个
    quint8 s35_7 : 1;  // [特殊功能气阀错]---S35:7 排3个
    quint8 c96_7 : 1;  // [三角气阀错]---C96:7 排4个
    quint8 c24_7 : 1;  // [三角气阀错]---C24:7 排5个
    quint8 c86_7 : 1;  // [三角气阀错]---C86:7 排6个
    quint8 c84_7 : 1;  // [三角气阀错]---C84:7 排7个
    quint8 c19_7 : 1;  // [三角气阀错]---C19:7 排8个

    // 第8组(57-64)
    quint8 c97_8 : 1;   // [三角气阀错]---C97:8 排1个
    quint8 c61_8 : 1;   // [三角气阀错]---C61:8 排2个
    quint8 c62_8 : 1;   // [三角气阀错]---C62:8 排3个
    quint8 c17_8 : 1;   // [三角气阀错]---C17:8 排4个
    quint8 c11_8 : 1;   // [三角气阀错]---C11:8 排5个
    quint8 epr8_8 : 1;  // [头跟停车针气阀错]---EPR8:8 排6个
    quint8 s116_8 : 1;  // [特殊功能气阀错]---S116:8 排7个
    quint8 left_8 : 1;  // [LEFT气阀错]---8 排8个

    // 第9组(65-72)
    quint8 c21_9 : 1;     // [三角气阀错]---C21:9 排1个
    quint8 c22_9 : 1;     // [三角气阀错]---C22:9 排2个
    quint8 c10_9 : 1;     // [三角气阀错]---C10:9 排3个
    quint8 epr10_9 : 1;   // [梭子盘下拉气缸错]---EPR10:9 排4个
    quint8 left_9_1 : 1;  // [LEFT气阀错]---9 排5个
    quint8 left_9_2 : 1;  // [LEFT气阀错]---9 排6个
    quint8 f3_9 : 1;      // [加油气阀错]---F3:9 排7个
    quint8 left_9_3 : 1;  // [LEFT气阀错]---9 排8个

    // 第10组(73-80)
    quint8 s36_10 : 1;     // [特殊功能气阀错]---S36:10 排1个
    quint8 left_10_1 : 1;  // [LEFT气阀错]---10 排2个
    quint8 left_10_2 : 1;  // [LEFT气阀错]---10 排3个
    quint8 left_10_3 : 1;  // [LEFT气阀错]---10 排4个
    quint8 left_10_4 : 1;  // [LEFT气阀错]---10 排5个
    quint8 c221_10 : 1;    // [三角气阀错]---C221:10 排6个
    quint8 c222_10 : 1;    // [三角气阀错]---C222:10 排7个
    quint8 c223_10 : 1;    // [三角气阀错]---C223:10 排8个
};

// 2、 三角打出报警位定义
struct TriangleOutWarn
{
    // 第1组(1-8)
    quint8 c10_1 : 1;   // 三角 C10 打进错
    quint8 c11_1 : 1;   // 三角 C11 打进错
    quint8 c19_1 : 1;   // 三角 C19 打进错
    quint8 c20_1 : 1;   // 三角 C20 打进错
    quint8 c84_1 : 1;   // 三角 C84 打进错
    quint8 c86_1 : 1;   // 三角 C86 打进错
    quint8 c221_1 : 1;  // 三角 C221 打进错
    quint8 c222_1 : 1;  // 三角 C222 打进错

    // 第2组(9-16)
    quint8 c223_1 : 1;  // 三角 C223 打进错
    quint8 c96_1 : 1;   // 三角 C96 打进错
    quint8 c21_1 : 1;   // 三角 C21 打进错
    quint8 c23_1 : 1;   // 三角 C23 打进错
    quint8 c25_1 : 1;   // 三角 C25 打进错
    quint8 c26_1 : 1;   // 三角 C26 打进错
    quint8 c61_1 : 1;   // 三角 C61 打进错
    quint8 c63_1 : 1;   // 三角 C63 打进错

    // 第3组(17-24)
    quint8 c106_1 : 1;    // 三角 C106 打进错
    quint8 reserved : 7;  // 预留7位
};

struct TriangleInWarn
{
    // 第1组(1-8)
    quint8 c10_0 : 1;   // 三角 C10 打出错
    quint8 c11_0 : 1;   // 三角 C11 打出错
    quint8 c19_0 : 1;   // 三角 C19 打出错
    quint8 c20_0 : 1;   // 三角 C20 打出错
    quint8 c84_0 : 1;   // 三角 C84 打出错
    quint8 c86_0 : 1;   // 三角 C86 打出错
    quint8 c221_0 : 1;  // 三角 C221 打出错
    quint8 c222_0 : 1;  // 三角 C222 打出错

    // 第2组(9-16)
    quint8 c223_0 : 1;  // 三角 C223 打出错
    quint8 c96_0 : 1;   // 三角 C96 打出错
    quint8 c21_0 : 1;   // 三角 C21 打出错
    quint8 c23_0 : 1;   // 三角 C23 打出错
    quint8 c25_0 : 1;   // 三角 C25 打出错
    quint8 c26_0 : 1;   // 三角 C26 打出错
    quint8 c61_0 : 1;   // 三角 C61 打出错
    quint8 c63_0 : 1;   // 三角 C63 打出错

    // 第3组(17-24)
    quint8 c106_0 : 1;    // 三角 C106 打出错
    quint8 reserved : 7;  // 预留7位
};

// 三角报警信息结构体(包含打进和打出)
struct TriangleWarn
{
    TriangleInWarn  in;   // 打进报警
    TriangleOutWarn out;  // 打出报警
};

// 3、梭子报警相关
struct ShuttleInWarn
{
    // 第1组(1-8): 主梭1-8
    quint8 main1 : 1;  // 主梭1打进报警 (1001)
    quint8 main2 : 1;  // 主梭2打进报警 (1002)
    quint8 main3 : 1;  // 主梭3打进报警 (1003)
    quint8 main4 : 1;  // 主梭4打进报警 (1004)
    quint8 main5 : 1;  // 主梭5打进报警 (1005)
    quint8 main6 : 1;  // 主梭6打进报警 (1006)
    quint8 main7 : 1;  // 主梭7打进报警 (1007)
    quint8 main8 : 1;  // 主梭8打进报警 (1008)

    // 第2组(9-16): 主梭1(R)-8(R)
    quint8 main1r : 1;  // 主梭1(R)打进报警 (1009)
    quint8 main2r : 1;  // 主梭2(R)打进报警 (1010)
    quint8 main3r : 1;  // 主梭3(R)打进报警 (1011)
    quint8 main4r : 1;  // 主梭4(R)打进报警 (1012)
    quint8 main5r : 1;  // 主梭5(R)打进报警 (1013)
    quint8 main6r : 1;  // 主梭6(R)打进报警 (1014)
    quint8 main7r : 1;  // 主梭7(R)打进报警 (1015)
    quint8 main8r : 1;  // 主梭8(R)打进报警 (1016)

    // 第3组(17-24): 主口橡筋等
    quint8 rubber : 1;   // 主口橡筋打进报警 (1017)
    quint8 rubberR : 1;  // 主口橡筋(R)打进报警 (1018)
    quint8 f19 : 1;      // F19打进报警 (1019)
    quint8 rubber1 : 1;  // 橡筋梭1打进报警 (1020)
    quint8 rubber2 : 1;  // 橡筋梭2打进报警 (1021)
    quint8 sub1_1 : 1;   // 副梭1-1打进报警 (1022)
    quint8 sub1_2 : 1;   // 副梭1-2打进报警 (1023)
    quint8 sub1_3 : 1;   // 副梭1-3打进报警 (1024)

    // 第4组(25-32): 副梭2-1到4-3
    quint8 sub2_1 : 1;  // 副梭2-1打进报警 (1025)
    quint8 sub2_2 : 1;  // 副梭2-2打进报警 (1026)
    quint8 sub2_3 : 1;  // 副梭2-3打进报警 (1027)
    quint8 sub3_1 : 1;  // 副梭3-1打进报警 (1028)
    quint8 sub3_2 : 1;  // 副梭3-2打进报警 (1029)
    quint8 sub3_3 : 1;  // 副梭3-3打进报警 (1030)
    quint8 sub4_1 : 1;  // 副梭4-1打进报警 (1031)
    quint8 sub4_2 : 1;  // 副梭4-2打进报警 (1032)

    // 第5组(33-39): 副梭4-3到6-3
    quint8 sub4_3 : 1;    // 副梭4-3打进报警 (1033)
    quint8 sub5_1 : 1;    // 副梭5-1打进报警 (1034)
    quint8 sub5_2 : 1;    // 副梭5-2打进报警 (1035)
    quint8 sub5_3 : 1;    // 副梭5-3打进报警 (1036)
    quint8 sub6_1 : 1;    // 副梭6-1打进报警 (1037)
    quint8 sub6_2 : 1;    // 副梭6-2打进报警 (1038)
    quint8 sub6_3 : 1;    // 副梭6-3打进报警 (1039)
    quint8 reserved : 1;  // 预留
};

// 梭子打出报警位定义(39个报警位,使用5个字节)
struct ShuttleOutWarn
{
    // 第1组(1-8): 主梭1-8
    quint8 main1 : 1;  // 主梭1打出报警 (1001)
    quint8 main2 : 1;  // 主梭2打出报警 (1002)
    quint8 main3 : 1;  // 主梭3打出报警 (1003)
    quint8 main4 : 1;  // 主梭4打出报警 (1004)
    quint8 main5 : 1;  // 主梭5打出报警 (1005)
    quint8 main6 : 1;  // 主梭6打出报警 (1006)
    quint8 main7 : 1;  // 主梭7打出报警 (1007)
    quint8 main8 : 1;  // 主梭8打出报警 (1008)

    // 第2组(9-16): 主梭1(R)-8(R)
    quint8 main1r : 1;  // 主梭1(R)打出报警 (1009)
    quint8 main2r : 1;  // 主梭2(R)打出报警 (1010)
    quint8 main3r : 1;  // 主梭3(R)打出报警 (1011)
    quint8 main4r : 1;  // 主梭4(R)打出报警 (1012)
    quint8 main5r : 1;  // 主梭5(R)打出报警 (1013)
    quint8 main6r : 1;  // 主梭6(R)打出报警 (1014)
    quint8 main7r : 1;  // 主梭7(R)打出报警 (1015)
    quint8 main8r : 1;  // 主梭8(R)打出报警 (1016)

    // 第3组(17-24): 主口橡筋等
    quint8 rubber : 1;   // 主口橡筋打出报警 (1017)
    quint8 rubberR : 1;  // 主口橡筋(R)打出报警 (1018)
    quint8 f19 : 1;      // F19打出报警 (1019)
    quint8 rubber1 : 1;  // 橡筋梭1打出报警 (1020)
    quint8 rubber2 : 1;  // 橡筋梭2打出报警 (1021)
    quint8 sub1_1 : 1;   // 副梭1-1打出报警 (1022)
    quint8 sub1_2 : 1;   // 副梭1-2打出报警 (1023)
    quint8 sub1_3 : 1;   // 副梭1-3打出报警 (1024)

    // 第4组(25-32): 副梭2-1到4-3
    quint8 sub2_1 : 1;  // 副梭2-1打出报警 (1025)
    quint8 sub2_2 : 1;  // 副梭2-2打出报警 (1026)
    quint8 sub2_3 : 1;  // 副梭2-3打出报警 (1027)
    quint8 sub3_1 : 1;  // 副梭3-1打出报警 (1028)
    quint8 sub3_2 : 1;  // 副梭3-2打出报警 (1029)
    quint8 sub3_3 : 1;  // 副梭3-3打出报警 (1030)
    quint8 sub4_1 : 1;  // 副梭4-1打出报警 (1031)
    quint8 sub4_2 : 1;  // 副梭4-2打出报警 (1032)

    // 第5组(33-39): 副梭4-3到6-3
    quint8 sub4_3 : 1;    // 副梭4-3打出报警 (1033)
    quint8 sub5_1 : 1;    // 副梭5-1打出报警 (1034)
    quint8 sub5_2 : 1;    // 副梭5-2打出报警 (1035)
    quint8 sub5_3 : 1;    // 副梭5-3打出报警 (1036)
    quint8 sub6_1 : 1;    // 副梭6-1打出报警 (1037)
    quint8 sub6_2 : 1;    // 副梭6-2打出报警 (1038)
    quint8 sub6_3 : 1;    // 副梭6-3打出报警 (1039)
    quint8 reserved : 1;  // 预留
};

// 梭子报警信息结构体(包含打进和打出)
struct ShuttleWarn
{
    ShuttleInWarn  in;   // 打进报警
    ShuttleOutWarn out;  // 打出报警
};

// 4、 选针输出报警位定义(13个报警位,使用2个字节)
struct NeedleWarn
{

    // 第1组(1-8)
    quint8 board1_power : 1;  // 选针板1电源错 (1)
    quint8 selector1 : 1;     // 选针器T1错 (2)
    quint8 selector2 : 1;     // 选针器T2错 (3)
    quint8 selector3 : 1;     // 选针器T3错 (4)
    quint8 selector4 : 1;     // 选针器T4错 (5)
    quint8 board2_power : 1;  // 选针板2电源错 (6)
    quint8 selector5 : 1;     // 选针器T5错 (7)
    quint8 selector6 : 1;     // 选针器T6错 (8)

    // 第2组(9-13)
    quint8 selector7 : 1;     // 选针器T7错 (9)
    quint8 selector8 : 1;     // 选针器T8错 (10)
    quint8 board3_power : 1;  // 选针板3电源错 (11)
    quint8 selector9 : 1;     // 选针器T9错 (12)
    quint8 selector10 : 1;    // 选针器T10错 (13)
    quint8 reserved : 3;      // 预留3位
};

// 5、 输入报警位定义(32个报警位,使用4个字节)
struct InputWarn
{
    // 第1组(1-8)
    quint8 main_door_in : 1;    // 主梭门门进入错 (4)
    quint8 main_door_out : 1;   // 主梭门门退出错 (4)
    quint8 sock_yarn_init : 1;  // 袜机出袜感应信号错误的初始状态 (5)
    quint8 seam_yarn_init : 1;  // 缝头出袜感应信号错误的初始状态 (6)
    quint8 sock_yarn_err : 1;   // 袜机出袜报警 (1005)
    quint8 seam_yarn_err : 1;   // 缝头出袜报警 (1006)
    quint8 flower_brake : 1;    // 往复提花针剪车报警 (18)
    quint8 rubber_break1 : 1;   // 橡筋断纱报警1 (21)

    // 第2组(9-16)
    quint8 rubber_break2 : 1;  // 橡筋断纱报警2 (22)
    quint8 shuttle_limit : 1;  // 梭子盘下限位感应信号错 (23)
    quint8 yarn_break : 1;     // 纱线报警 (24)
    quint8 head_sensor : 1;    // 机头钳子感应错 (25)
    quint8 blade_err : 1;      // 剪刀报警 (26)
    quint8 needle_stop1 : 1;   // 断针舌停车针报警1 (27)
    quint8 needle_stop2 : 1;   // 断针舌停车针报警2 (28)
    quint8 yarn_rod : 1;       // 挑线杆报警 (29)

    // 第3组(17-24)
    quint8 circle_needle : 1;  // 往复环针报警 (30)
    quint8 foot_needle : 1;    // 断针脚停车针 (31)
    quint8 handle_signal : 1;  // 机器运行时摇手柄信号错 (37)
    quint8 air_pressure : 1;   // 气压报警 (39)
    quint8 oil_pressure : 1;   // 油压报警 (40)
    quint8 no_oil : 1;         // 无油报警 (44)
    quint8 yarn_sensor : 1;    // 抓袜移出感应器报警 (45)
    quint8 ktf_err : 1;        // KTF报警 (46)

    // 第4组(25-32)
    quint8 yarn_frame_up : 1;      // 纱架上报警 (49)
    quint8 yarn_frame_down : 1;    // 纱架下报警 (50)
    quint8 yarn_frame_rubber : 1;  // 纱架橡筋报警 (51)
    quint8 disk_blade_sync : 1;    // 圆盘剪刀感应报警(同步带错) (1100)
    quint8 disk_blade_signal : 1;  // 圆盘剪刀感应报警(信号错) (1101)
    quint8 disk_blade_motor : 1;   // 圆盘剪刀感应报警(电机错) (1102)
    quint8 input_signal : 1;       // 输入信号错 (XX)
    quint8 reserved : 1;           // 预留
};

// 步进电机报警位定义(24个报警位,使用3个字节)
struct StepMotorWarn
{
    // 第1组(1-8)
    quint8 yarn_disk : 1;   // [纱嘴盘升降]电机复位错误 (1)
    quint8 rubber : 1;      // [橡筋]电机复位错误 (2)
    quint8 pick_mada : 1;   // [生克马达]电机复位错误 (3)
    quint8 pick_swing : 1;  // [生克摆动]电机复位错误 (4)
    quint8 plate_ud : 1;    // [转移盘上下]电机复位错误 (5)
    quint8 density : 1;     // [密度针筒升降]电机复位错误 (6)
    quint8 seam_fan : 1;    // [缝头吸风]电机复位错误 (7)
    quint8 sock_fan : 1;    // [袜机吸风]电机复位错误 (8)

    // 第2组(9-16)
    quint8 plate_swing : 1;   // [转移盘摆动]电机复位错误 (9)
    quint8 sleeve_ud : 1;     // [反袜管升降]电机复位错误 (10)
    quint8 plate_rotate : 1;  // [缝盘转臂]电机复位错误 (11)
    quint8 plate : 1;         // [缝盘]电机复位错误 (12)
    quint8 arm_ud : 1;        // [抓臂马达升降]电机复位错误 (13)
    quint8 rod : 1;           // [拨杆]电机复位错误 (14)
    quint8 seam : 1;          // [缝头机]电机复位错误 (15)
    quint8 disk_blade : 1;    // [圆盘剪刀]电机复位错误 (16)

    // 第3组(17-24)
    quint8 pick_encoder : 1;     // 生克摆动编码值错误 (100)
    quint8 yarn_diskimeout : 1;  // 导纱盘升降超时 (200)
    quint8 plate_pos : 1;        // 扎口盘升降位置错误 (201)
    quint8 plateimeout : 1;      // 扎口盘升降超时错误 (202)
    quint8 plate_zero : 1;       // 扎口盘不在零位 (203)
    quint8 shuttle_encoder : 1;  // 梭子盘下降编码错误 (204)
    quint8 step_error : 1;       // [XX]步进马达错误=YY (XX)
    quint8 reserved : 1;         // 预留
};

// 7.伺服电机报警位定义(8个报警位,使用1个字节)
struct ServoWarnBits
{

    quint8 servo_error : 1;     // 伺服报错:伺服报错 (1)
    quint8 emg_stop : 1;        // 伺服报错:急停键按下 (2)
    quint8 encoder_dir : 1;     // 伺服报错:针筒马达方向检测错误 (3)
    quint8 speed_error : 1;     // 伺服报错:恢复摆动脉冲错误 (4)
    quint8 encoder_signal : 1;  // 伺服报错:主马达编码信号错 (5)
    quint8 zero_signal : 1;     // 伺服报错:主马达零位信号错 (6)
    quint8 needle_signal : 1;   // 伺服报错:主马达针信号错 (7)
    quint8 stop_signal : 1;     // 伺服报错:主马达针报警:停转 (8)
};

// 伺服电机报警信息结构体
struct ServoWarn
{
    ServoWarnBits warns;       // 8个报警位
    quint8        error_code;  // 错误代码(用于servo_error)
};

// 8、CAN通信及其它通讯报警位定义(8个报警位)
struct CanWarn
{
    quint8 can0 : 1;  // CAN0通信错误 (0) - 电机及电源板通信
    quint8 can1 : 1;  // CAN1通信错误 (1) - 缝头板
    quint8 can3 : 1;  // CAN2通信错误 (2) - 气阀板
    quint8 com1 : 1;  // 编码器通讯错误
    quint8 com2 : 1;  // 光电眼通讯错误
    quint8 com3 : 1;  // KTF眼通讯错误
    quint8 FPGA : 1;  // FPGA通讯
    quint8 SPI : 1;   // SPI通讯
};
// 9.1缝头报警位定义(32个报警位,使用4个字节)
struct SeamWarnBase
{
    // 第1组(1-8)
    quint8 beiyong1 : 1;            // [1:备用1]超时 (1)
    quint8 plate_pressimeout : 1;   // [2:转移压盘压下]超时 (2)
    quint8 hook_outimeout : 1;      // [3:转移盘勾放出]超时 (3)
    quint8 plate_midimeout : 1;     // [4:转移盘移到中间]超时 (4)
    quint8 beiyong2 : 1;            // [5:备用2]超时 (5)
    quint8 plate_upingimeout : 1;   // [6:转移盘升上]超时 (6)
    quint8 plate_needleimeout : 1;  // [7:转移盘移到机器针筒上方]超时 (7)
    quint8 fan_stopimeout : 1;      // [8:停止吸风]超时 (8)
                                    // 第2组(9-16)
    quint8 beiyong3 : 1;            // [9:备用3]超时 (9)
    quint8 plate_down1imeout : 1;   // [10:转移盘下一级]超时 (10)
    quint8 plate_down2imeout : 1;   // [11:转移盘向下二级]超时 (11)
    quint8 press_upimeout : 1;      // [12:转移压盘向上]超时 (12)
    quint8 plate_down3imeout : 1;   // [13:转移盘向下三级最低位]超时 (13)
    quint8 beiyong4 : 1;            // [14:备用4]超时 ](14)
    quint8 hookightimeout : 1;      // [15:转移盘勾收紧]超时 (15)
    quint8 plate_up1imeout : 1;     // [16:转移盘向上升一级]超时 (16)
    // 第3组(17-24)
    quint8 beiyong5 : 1;                 // [17:备用5]超时 (17)
    quint8 tube_downimeout : 1;          // [18:针筒铜管向下]超时 (18)
    quint8 pick_eyebrowimeout : 1;       // [19:生克眉毛收紧]超时 (19)
    quint8 press_downimeout : 1;         // [20:转移压盘向下压]超时 (20)
    quint8 plate_up2imeout : 1;          // [21:转移盘升上二级]超时 (21)
    quint8 pick_resetimeout : 1;         // [22:生克眉毛复位]超时 (22)
    quint8 tube_upimeout : 1;            // [23:针筒铜管向上]超时 (23)
    quint8 head_releaseimeout : 1;       // [24:针筒锁头释放]超时 (24)
                                         // 第4组(25-32)
    quint8 beiyong6 : 1;                 // [17:备用6]超时 (25)
    quint8 beiyong7 : 1;                 // [17:备用7]超时 (26)
    quint8 keepimeout : 1;               // [27:保持]超时 (27)
    quint8 head_lockimeout : 1;          // [28:剪刀架向下]超时 (28)
    quint8 plate_mid_outimeout : 1;      // [29:转移盘移出至中间]超时 (29)
    quint8 plate_up_posimeout : 1;       // [30:转移压盘向上]超时 (30)
    quint8 plate_zeroimeout : 1;         // [31:转移盘移动到零位]超时 (31)
    quint8 plate_down_level2imeout : 1;  // [32:吸风马达 1 位]超时 (32)
};
// 9.2缝头特殊报警1位定义(32个报警位,使用4个字节)
struct SeamSpecialWarn1
{
    // 第5组(33-40)
    quint8 suctionube_middleimeout : 1;          // [33:吸风管摆向中间]超时
    quint8 reverseube_upimeout : 1;              // [34:反袜管升上]超时
    quint8 reverseube_downimeout : 1;            // [35:反袜管向下]超时
    quint8 suction_motor_pos2imeout : 1;         // [36:吸风马达2位]超时
    quint8 suctionube_zeroimeout : 1;            // [37:吸风管收回归零位]超时
    quint8 scissor_frame_upimeout : 1;           // [38:剪刀架向上]超时
    quint8 sock_suction_protect_openimeout : 1;  // [39:袜子进入吸风管筒保护打开]超时
    quint8 suction_motor_pos3imeout : 1;         // [40:吸风马达3位]超时

    // 第6组(41-48)
    quint8 reverseube_pin_inimeout : 1;      // [41:反袜筒定位销进去]超时
    quint8 transfer_hook_releaseimeout : 1;  // [42:转移盘勾放开]超时
    quint8 transfer_plate_upimeout : 1;      // [43:转移盘升上]超时
    quint8 transfer_hookightenimeout : 1;    // [44:转移盘勾收紧]超时
    quint8 suction_motor_pos4imeout : 1;     // [46:吸风马达4位]超时
    quint8 reverseube_sand_downimeout : 1;   // [47:反袜管升上(同时导沙盘向下)]超时
    quint8 stop_suctionimeout : 1;           // [48:停止吸风]超时

    // 第7组(49-56)
    quint8 transferop_plate_upimeout : 1;   // [49:转移顶盘向上]超时
    quint8 grab_arm_down_level1imeout : 1;  // [50:抓臂(马达)向下一级]超时
    quint8 grab_arm_up_zeroimeout : 1;      // [51:抓臂(马达)向上零位]超时
    quint8 grab_arm_releaseimeout : 1;      // [52:抓臂放开]超时
    quint8 grab_armubeightimeout : 1;       // [53:抓臂抓紧反袜筒]超时
    quint8 grab_arm_downimeout : 1;         // [54:抓臂(马达)向下]超时
    quint8 grab_arm_release2imeout : 1;     // [55:抓臂放开]超时
    quint8 grab_arm_up_level1imeout : 1;    // [56:抓臂(马达)向上一级]超时

    // 第8组(57-64)
    quint8 grab_arm_release3imeout : 1;       // [57:抓臂放开]超时
    quint8 grab_armubeight2imeout : 1;        // [58:抓臂抓紧反袜筒]超时
    quint8 reverseube_sleeve_outimeout : 1;   // [59:反袜管套筒锁出]超时
    quint8 reverseube_sleeve_downimeout : 1;  // [60:反袜筒套筒向下]超时
    quint8 reserved8 : 1;                     // [61:备用8]超时
    quint8 reverseube_sleeve_lockimeout : 1;  // [62:反袜管套筒锁进]超时
    quint8 reverseube_half_downimeout : 1;    // [63:反袜筒马达向下半位]超时
    quint8 transferop_plate_downimeout : 1;   // [64:转移顶盘向下]超时
};

// 9.3缝头特殊报警2位定义(32个报警位,使用4个字节)
struct SeamSpecialWarn2
{
    // 第9组(65-72)
    quint8 transfer_plate_hookightenimeout : 1;    // [65:转移盘勾收紧]超时
    quint8 transfer_plate_down_halfimeout : 1;     // [66:转移盘向下半位]超时
    quint8 transfer_plate_hook_releaseimeout : 1;  // [67:转移盘勾放开]超时
    quint8 transfer_plate_middle_outimeout : 1;    // [68:转移盘移出至中间]超时
    quint8 transfer_plate_zero_downimeout : 1;     // [69:转移盘下降到零位]超时
    quint8 seam_plate_arm_inimeout : 1;            // [70:缝盘转臂进]超时
    quint8 seam_plate_lock_releaseimeout : 1;      // [71:缝盘锁头松开]超时
    quint8 suction_motor_pos5imeout : 1;           // [72:吸风马达5位]超时

    // 第10组(73-80)
    quint8 seam_plate_arm_reverseimeout : 1;  // [73:缝盘转臂反转]超时
    quint8 seam_machine_inimeout : 1;         // [74:缝头机进]超时
    quint8 reserved9 : 1;                     // [75:备用9]超时
    quint8 seam_plate_arm_outimeout : 1;      // [76:缝盘转臂出]超时
    quint8 plate_press_ring_downimeout : 1;   // [77:套盘压环压下]超时
    quint8 plate_press_ring_upimeout : 1;     // [78:套盘压环向上]超时
    quint8 plate_press_ring_down2imeout : 1;  // [79:套盘压环压下]超时
    quint8 reserved10 : 1;                    // [80:备用10]超时

    // 第11组(81-88)
    quint8 seam_cutter_forwardimeout : 1;    // [81:剪刀(缝头)工作向前]超时
    quint8 seam_cutter_backimeout : 1;       // [82:剪刀后退]超时
    quint8 seam_plate_rotateimeout : 1;      // [83:缝盘回转]超时
    quint8 seam_plate_motor_zeroimeout : 1;  // [84:缝盘马达归零]超时
    quint8 suction_startimeout : 1;          // [85:开始吸风]超时
    quint8 plate_press_ring_up2imeout : 1;   // [86:套盘压环向上]超时
    quint8 seam_plate_arm_in2imeout : 1;     // [87:缝盘转臂进]超时
    quint8 seam_machine_backimeout : 1;      // [88:缝头机后退]超时

    // 第12组(89-96)
    quint8 suction_stopimeout : 1;                // [89:停止吸风]超时
    quint8 suction_motor_pos6imeout : 1;          // [90:吸风马达6位置]超时
    quint8 seam_plate_arm_back_level1imeout : 1;  // [91:缝盘转臂转回一级]超时
    quint8 seam_plate_arm_zeroimeout : 1;         // [92:缝盘转臂归零]超时
    quint8 seam_plate_lock_inimeout : 1;          // [93:缝盘锁头锁进]超时
    quint8 suction_motor_pos7imeout : 1;          // [94:吸风马达7位]超时
    quint8 plate_press_ring_down3imeout : 1;      // [95:套盘压环向下]超时
    quint8 plate_press_ring_up3imeout : 1;        // [96:套盘压环向上]超时
};

// 9.4缝头特殊报警3位定义(32个报警位,使用4个字节)
struct SeamSpecialWarn3
{
    // 第13组(97-104)

    quint8 plate_up_level64imeout : 1;    // [97:套盘锁头放开]超时 (97)
    quint8 plate_rotate_back2imeout : 1;  // [98:缝盘转臂转回一级]超时 (98)
    quint8 sleeve_up2imeout : 1;          // [99:反袜筒套筒向上]超时 (99)
    quint8 arm_down_lowimeout : 1;        // [100:抓臂马达向下到最低]超时 (100)
    quint8 sleeve_down3imeout : 1;        // [101:反袜筒套筒向下]超时 (101)
    quint8 plate_rotate_zero2imeout : 1;  // [102:缝盘转臂归零]超时 (102)
    quint8 plate_lock_in2imeout : 1;      // [103:缝盘锁头锁进]超时 (103)
    quint8 ring_down4imeout : 1;          // [104:套盘压环向下]超时 (104)
    // 第14组(105-112)
    quint8 ring_up4imeout : 1;            // [105:套盘压环向上]超时 (105)
    quint8 plate_rotate_in3imeout : 1;    // [106:套盘转臂进]超时 (106)
    quint8 plate_rotate_back3imeout : 1;  // [107:缝盘转臂退]超时 (107)
    quint8 fan_stop4imeout : 1;           // [108:停止吸风]超时 (108)
    quint8 sleeve_up_level1imeout : 1;    // [109:反袜管马达向上一级]超时 (109)
    quint8 sleeve_lock_open3imeout : 1;   // [110:反袜管套筒锁头放开]超时 (110)
    quint8 sleeve_up3imeout : 1;          // [111:反袜筒套筒向上]超时 (111)
    quint8 arm_open4imeout : 1;           // [112:抓臂放开]超时 (112)
                                          // 第15组(113-120)
    quint8 sleeve_down4imeout : 1;        // [113:反袜管马达向下]超时 (113)
    quint8 arm_up_zero2imeout : 1;        // [114:抓臂马达向上归零]超时 (114)
    quint8 arm_open5imeout : 1;           // [115:抓臂放开]超时 (115)
    quint8 sleeve_lock_out2imeout : 1;    // [116:反袜管套筒锁出]超时 (116)
    quint8 protectimeout : 1;             // [117:保护杆收回,压盘下]超时 (117)
    quint8 plate_zero4imeout : 1;         // [118:转移盘归零]超时 (118)
    quint8 press_up3imeout : 1;           // [119:转移压盘向上]超时 (119)
    quint8 beiyong11 : 1;                 // [17:备用11]超时 (120)
                                          // 第16组(121-128)
    quint8 seam_motor_err2 : 1;           // 缝头同步,不能升降 (121)
    quint8 seam_motor_err3 : 1;           // 抓林感应盘检测异常 (122)
    quint8 fold_plate_drop : 1;           // 折叠盘脱落 (123)
    quint8 seam_break : 1;                // 缝头断线报警 (124)
    quint8 plate_protect : 1;             // 转移盘保护圈报警 (125)
    quint8 seam_reset_fail : 1;           // 缝头机尚未复位完成 (126)
    quint8 seam_yarn_fail : 1;            // 缝头出袜感应检测失败 (127)
    quint8 beiyong12 : 1;                 // [17:备用12]超时 (128)
};

// 9.5缝头特殊报警4位定义(32个报警位,使用4个字节)
struct SeamSpecialWarn4
{
    // 第17组(129-136)
    quint8 guide_motor_err3 : 1;       // [导纱器升起] 防夹盖没有到位不能升纱嘴盘并始缝头 (129)
    quint8 seam_firstimeout : 1;       // [130:缝头:对齐第一针]超时 (130)
    quint8 transfer_err1 : 1;          // 转移完成,但未排针成功 (131)
    quint8 transfer_err2 : 1;          // 转移完成,排针未转移成功 (132)
    quint8 seam_motor_err4 : 1;        // 管筒机头防护圈报警 (133)
    quint8 plate_needle_upimeout : 1;  // [134:转移盘接近针筒上方]超时 (134)
    quint8 guide_motor_err1 : 1;       // [导纱器升起] 针筒转动,不能升纱嘴盘 (135)
    quint8 guide_motor_err2 : 1;       // [导纱器升起] 导纱器不在原位不能升纱嘴盘并始缝头上针 (136)

    // 第18组(137-144)

    quint8 guide_motor_err4 : 1;  // [导纱器升起] 同步错,不能升纱嘴盘 (137)
    quint8 seam_err1 : 1;         // [50:机号（1号）] 向下一级长度>=0并>=0上升 (138)
    quint8 seam_err2 : 1;         // [114:机管分送到上车]向上位>=0和>=0上升 (139)
    quint8 seam_err3 : 1;         // [100:机针送到上车]向下位>=0和>=0上升 (140)
    quint8 seam_err4 : 1;         // [80:吸头]向外出点 (141)
    quint8 seam_err5 : 1;         // [80:吸头]向外出点 (142)
    quint8 seam_motor_err5 : 1;   // 缝头机空位加号故障 (143)
    quint8 seam_err6 : 1;         // [74:缝头机] 折叠盘未到位 (144)
    // 第19组(145-152)
    quint8 transfer_err3 : 1;  // [转移盘放点前上升] 开机后机头同未复位 (145)
    quint8 transfer_err4 : 1;  // [转移盘放点前上升] 导纱器不在上升位置 (146)
    quint8 transfer_err5 : 1;  // [7: 转移盘到机器针筒上升] 未到达高位 (147)
    quint8 seam_err7 : 1;      // [117:保护杆收回] 压盘下压后下压启动 (148)
    quint8 transfer_err6 : 1;  // [转动机构转动上升增针前上升] 针筒确实是原始点 (149)
    quint8 transfer_err7 : 1;  // [转动机构转动上升增针前上升] 针筒确实是原始点 (150)
    quint8 transfer_err8 : 1;  // [转动机构转动上升增针前上升] 针筒确实是原始点 (151)
    quint8 clear_wa : 1;       // 请清理废袜，完成后 F8 消除(152)
};

// 9.6 缝头电磁阀报警
struct SeamValveAlarm
{
    quint8 s11 : 1;  // [缝头气阀错]--1 排 1 个:反袜管套筒销锁住
    quint8 s12 : 1;  // [缝头气阀错]--1 排 2 个:反袜管套筒销放开
    quint8 s13 : 1;  // [缝头气阀错]--1 排 3 个:抓臂
    quint8 s14 : 1;  // [缝头气阀错]--1 排 4 个:反袜套筒下
    quint8 s15 : 1;  // [缝头气阀错]--1 排 5 个:反袜套筒上
    quint8 s16 : 1;  // [缝头气阀错]--1 排 6 个:压环
    quint8 s17 : 1;  // [缝头气阀错]--1 排 7 个:缝盘锁头
    quint8 s18 : 1;  // [缝头气阀错]--1 排  8 个:缝盘转臂销子

    quint8 s21 : 1;  // [缝头气阀错]--2 排 1 个:缝头机剪刀
    quint8 s22 : 1;  // [缝头气阀错]--2 排 2 个:缝头机进
    quint8 s23 : 1;  // [缝头气阀错]--2 排 3 个:缝头机退
    quint8 s24 : 1;  // [缝头气阀错]--2 排 4 个:转移盘勾放开
    quint8 s25 : 1;  // [缝头气阀错]--2 排 5 个:转移盘眉毛收紧
    quint8 s26 : 1;  // [缝头气阀错]--2 排 6 个:转移盘压盘
    quint8 s27 : 1;  // [缝头气阀错]--2 排 7 个:袜子进入吸风管筒保护
    quint8 s28 : 1;  // [缝头气阀错]--2 排 8 个:转移顶盘下

    quint8 s31 : 1;  // [缝头气阀错]--3 排 1 个:转移顶盘上
    quint8 s32 : 1;  // [缝头气阀错]--3 排 2 个:反袜筒固定锁头
    quint8 s33 : 1;  // [缝头气阀错]--3 排 3 个:转移盘压盘半级
    quint8 s34 : 1;  // [缝头气阀错]--3 排 4 个:吸风筒上升
    quint8 s35 : 1;  // [缝头气阀错]--3 排 5 个:吸风筒下降
    quint8 s36 : 1;  // [缝头气阀错]--3 排 6 个:长袜整理杆上
    quint8 s37 : 1;  // [缝头气阀错]--3 排 7 个:长袜整理杆下
    quint8 s38 : 1;  // [缝头气阀错]--3 排 8 个
};

// 9.7完整的缝头报警信息结构体
struct SeamWarnInfo
{
    SeamWarnBase     basic;     // 基本缝头报警
    SeamSpecialWarn1 special;   // 特殊缝头报警1
    SeamSpecialWarn2 special2;  // 特殊缝头报警2
    SeamSpecialWarn3 special3;  // 特殊缝头报警3
    SeamSpecialWarn4 special4;  // 特殊缝头报警4
    SeamValveAlarm   valve;     // 缝头电磁阀报警
};

// 10. 电眼报警相关
struct PhotoEyeWarn
{
    quint32 yarn_break : 32;    // XX号电眼断纱 最大32个电眼
    quint32 yarnight : 32;      // XX号电眼缠纱 最大32个电眼
    quint32 codeimeout : 32;    // XX号电眼编号超时 最大32个电眼
    quint32 sampleimeout : 32;  // XX号电眼数据采样应答超时 最大32个电眼
    quint32 code_err : 32;      // XX号电眼编号应答错 最大32个电眼
    quint8  sendimeout : 1;     // 向电眼发送字节超时 (65)
    quint8  sample_break : 1;   // 电眼采样中断太快 (66)
    quint8  version_err : 1;    // 版本查询 (73)
    quint8  commest : 1;        // 通信检测 (73)
    quint8  recover : 1;        // 故障恢复 (73)
    quint8  upgrade : 1;        // 软件升级 (73)
    quint8  sensitivity : 1;    // 灵敏度设置 (73)
    quint8  responseime : 1;    // 响应时间设置 (73)
    quint8  coding : 1;         // 编号 (73)
    quint8  single_code : 1;    // 单独编号 (73)
    quint8  speed_limit : 1;    // 速度上下限 (73)
    quint8  tightolerance : 1;  // 设置缠纱容错 (73)
    quint8  breakolerance : 1;  // 设置断纱容错 (73)
    quint8  learn : 1;          // 学习命令 (73)
    quint8  warn_query : 1;     // 报警查询 (73)
    quint8  reserved : 1;       // 预留1位
};

// 11. 软件应用报警相关
struct SoftwareWarn
{
    // 第1组(1-8)
    quint8 set_complete : 1;     // 设定件数完成 (1)
    quint8 seam_motor_err : 1;   // 缝头同步,主马达不能启动 (2)
    quint8 tube_yarn_err : 1;    // 针筒转动,不能升纱嘴盘 (3)
    quint8 sync_yarn_err : 1;    // 同步状态错,不能升纱嘴盘 (4)
    quint8 tube_down_err : 1;    // 铜管尚未降下 (5)
    quint8 head_state_err : 1;   // 准备机头升降,销子状态错:连接 (6)
    quint8 head_sensor_err : 1;  // 准备机头升降,销子感应错:连接 (7)
    quint8 c85_err : 1;          // C85打进错误:不在缝头拦针时打进 (8)

    // 第2组(9-16)
    quint8 head_pin_break : 1;     // 机头销端口状态错:断开 (9)
    quint8 head_pin_connect : 1;   // 机头销端口状态错:连接 (10)
    quint8 seam_cancel : 1;        // 缝头取消,请检查是否清理织物 (11)
    quint8 shuttle_lift_err : 1;   // 机器空转时梭子盘抬起过低 (12)
    quint8 shuttle_pos_err : 1;    // 梭子盘不在最高或最低位 (13)
    quint8 highemp : 1;            // 高温报警 (14)
    quint8 shuttle_press_err : 1;  // 梭子盘未压下 (15)
    quint8 shuttle_lift_fail : 1;  // 梭子盘升降失败 (16)

    // 第3组(17-24)
    quint8 cube_num_err : 1;      // 不正确的小立体圈数 (17)
    quint8 sock_check_fail : 1;   // 移袜检测失败,请人工清理后再开机 (18)
    quint8 shuttle_high_err : 1;  // 梭子盘过高,可能下落失败,请确认 (19)
    quint8 reserved1 : 5;         // 预留5位

    // 第4组(25-32)
    quint8 reserved2 : 8;  // 预留8位
};

// 总的报警信息结构体
struct WarnInfoStruct
{
    ValveWarn     zhi_qifa;    // 织气阀报警
    TriangleWarn  triangle;    // 三角打出报警
    ShuttleWarn   shuttle;     // 梭子报警
    NeedleWarn    needle;      // 选针输出报警
    InputWarn     input;       // 输入报警
    StepMotorWarn step_motor;  // 步进电机报警
    ServoWarn     servo;       // 伺服电机报警
    CanWarn       can;         // CAN通信报警
    SeamWarnInfo  seam;        // 缝头报警
    PhotoEyeWarn  photo_eye;   // 电眼报警
    SoftwareWarn  software;    // 软件应用报警
};

struct upInfoMessage
{
    upMain         main;  // 主电机相关
    upServ         serv;  // 编码器相关
    upStep         step;  // 步进电机电流、位置
    upZeroLimit    zeroLimit;
    ZhiWaSignals   zhiwaSignals;
    SeamSignals    seamSignals;
    WarnInfoStruct warn;  // 报警信息
};

#pragma pack()

#endif  // UPINFOMESSAGE_H
