﻿#ifndef READTESTCONFIG_H
#define READTESTCONFIG_H

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMessageBox>

class ReadTestConfig
{
public:
    ReadTestConfig();
    ~ReadTestConfig();
    void parseConfig( QString fileAddr );
    void saveConfig( QString fileAddr );

#pragma pack( 1 )
    struct ValveInfoStruct
    {
        quint8  key;
        QString value;
        QString type;   //设备类型
        quint8  index;  //板序号
        quint8  pos;    //板位置
    };
    struct ComboValveStruct
    {
        QString           name;
        QVector< quint8 > ids;
    };

#pragma pack()

    QMap< int, ValveInfoStruct >* getValveList()
    {
        return &_valveList;
    }

    QMap< int, ComboValveStruct >* getComboValveList()
    {
        return &_comboValveList;
    }

private:
    QMap< int, ValveInfoStruct >  _valveList;
    QMap< int, ComboValveStruct > _comboValveList;
};

#endif  // READTESTCONFIG_H
