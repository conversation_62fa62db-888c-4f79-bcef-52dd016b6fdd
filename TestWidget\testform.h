﻿#ifndef TESTFORM_H
#define TESTFORM_H

#include "CircleButton.h"
#include "ComboCheckDialog.h"
#include "Common/defines.h"
#include "Common/parameters.h"
#include "CommonWidget/comboform.h"
#include "CommonWidget/numberinputform.h"
#include "Communicate/Communication.h"
#include "Communicate/commkeyboard.h"  // 添加键盘通信头文件
#include "Config/readCfg.h"
#include "Config/readmachinefileconfig.h"
#include "Config/readtestconfig.h"
#include "ServZeroDialog.h"
#include "ValveSelfCheckDialog.h"
#include <QButtonGroup>
#include <QGridLayout>
#include <QLabel>
#include <QMessageBox>
#include <QTimer>
#include <QVector>
#include <QWidget>

namespace Ui
{
class TestForm;
}

class TestForm : public QWidget
{
    Q_OBJECT

public:
    explicit TestForm( QWidget* parent = nullptr, MainWidgetData* mainData = nullptr, Communication* com = nullptr, CommKeyboard* keyboard = nullptr );
    ~TestForm();
    void updateKeyStatus( int keyValue, bool pressed );  // 更新按键状态
    void resetAllKeyStatus();                            // 重置所有按键状态

private:
    Ui::TestForm* ui;

    MainWidgetData* mainData;
    Communication*  comm;
    CommKeyboard*   m_keyboard;     // 添加键盘通信成员变量
    ConfigManager*  configManager;  // 配置管理器

    // 菜单页面
    QButtonGroup* menuBtnGroup;  // 菜单按键组

    /************* 气阀测试相关参数 *************/
#pragma pack( 1 )
    struct ValveButtonState
    {
        int          id;  // 映射到MappedId
        bool         isPressed;
        QPushButton* button;
    };
#pragma pack()

    // 在气阀测试相关参数部分添加
    enum ValveTabType
    {
        TAB_SPECIAL_FUN = 0,
        TAB_CAM         = 1,
        TAB_YARN_FINGER = 2,
        TAB_YARN2       = 3,
        TAB_EXTEND      = 4
    };

    ValveTabType currentValveTab = TAB_SPECIAL_FUN;  // 当前显示的tab

    // 每个tab的按钮状态记录
    QMap< int, ValveButtonState > specialFunStates;  // 特殊功能tab的按钮状态
    QMap< int, ValveButtonState > camStates;         // 凸轮tab的按钮状态
    QMap< int, ValveButtonState > yarnFingerStates;  // 纱嘴tab的按钮状态
    QMap< int, ValveButtonState > yarn2States;       // 副梭的按钮状态
    QMap< int, ValveButtonState > extendStates;      // extend tab的按钮状态

    // 添加自动测试相关变量
    QList< int > autoTestPressedButtons;       // 记录当前自动测试的按钮ID列表
    int          autoTestCurrentIndex = 0;     // 当前处理的按钮索引
    bool         autoTestIsFirstRun   = true;  // 是否是第一次运行
    int          continuousLastId     = 0;     // 连续任务Last按钮索引

    bool isValveTestPageInit             = false;
    bool continuousTestIsFirstRun        = true;   // 是否是第一次运行
    bool isValveAutoTestingRunning       = false;  // 是否正运行自动测试，如果是的话，必须先停掉
    bool isValveContinuousTestingRunning = false;  // 是否正运行连续测试，如果是的话，必须先停掉
    int  valveTestFreq                   = 1;      // 保存气阀测试频率
    bool isFlipping                      = false;  // 是否执行翻转测试，为true的时候打进，为false打出。

    QTimer*       testTimer = nullptr;  // 用于执行测试动作的定时器
    QButtonGroup* ValveTestCtrlBtnGroup;

    void ValveTestInit();
    void AddValveTestCtrlBtnToBtnGroup();
    void ShowValveButtonPage();
    void onValveButtonClicked();
    void valveAutoTesting();
    void valveContinuousTesting();
    void sendValveState( int buttonId = -1, int state = -1 );

    /************* 选针器测试相关 *************/
    QList< int > selectList;
    bool         isSelectorAutoTestingRunning    = false;  // 是否正运行自动测试，如果是的话，必须先停掉
    bool         isSelectorAllTestingRunning     = false;  // 是否正运行自动测试，如果是的话，必须先停掉
    bool         isKnifeSingleLoopTestingRunning = false;  // 是否正运行单刀循环测试，如果是的话，必须先停掉
    int          selectorTestFreq                = 1;      // 保存气阀测试频率

    bool   isSelectTestPageInit  = false;
    qint8  selectedSelector      = -1;
    qint8  selectedKnife         = -1;
    quint8 oneplusone            = 0;  // 1X1   0-全0 1-1/3/5/7 2-2/4/6/8
    quint8 realSelectState[ 16 ] = { 0 };

    QButtonGroup* SelectKnifeBtnGroup;               // 选针器刀片按键组
    QButtonGroup* SelectBtnGroup;                    // 选针器按键组
    QButtonGroup* SelectCtrlBtnGroup;                // 选针器测试控制按键组
    void          SelectTestInit( void );            // 选针器测试相关初始化
    void          AddSelectRadioBtnToBtnGroup();     // 将选针器Radiobox加入到按键组中
    void          AddKnifeCheckBoxToBtnGroup();      // 将选针器刀片Checkbox加入到按键组中
    void          AddSelectTestCtrlBtnToBtnGroup();  // 将选针器测试控制按键加入到按键组中
    void          SelectAutoTesting();
    void          sendSelectorState();
    void          SelectAllTesting();
    void          setCheckBoxSelectedOnly( int id );
    void          setKnifeSelectedOnly( int id );
    void          SingleKnifeLoop();  // 单刀循环
    void          stopAutoTask();

    /************* 步进电机测试相关 *************/
    bool             isStepTestPageInit = false;
    QButtonGroup*    StepTestCtrlBtnGroup;
    MyLineEdit*      currentLineEdit = nullptr;  // LineEdit
    NumberInputForm* numFrm          = nullptr;
    ComboForm*       zeroCmdComboFrm = nullptr;
    void             StepTestInit( void );          // 步进电机测试相关初始化
    void             CreateStepTestCtrlBtnGroup();  // 创建步进电机测试控制按键组
    void             StepTestPageInit();            // 步进电机测试初始化
    void             CreateStepTestLineEditConnect();
    quint8           findCurrentStepMotorId();
    void             RefreshStepParams( quint8 deviceId, quint16 currentValue, quint16 codeValue, quint8 zeroValue, quint8 limitValue );

    /************* 伺服电机测试相关 *************/
    QButtonGroup*   ServoTestCtrlBtnGroup;
    ServZeroDialog* servZeroDialog      = nullptr;
    bool            isServTestPageInit  = false;
    quint16         servoSpeedSetValue  = 20;  // 伺服点击转速设置值
    quint8          servoDirectionValue = 0;
    quint8          servoElasticValue   = 0;
    quint8          servoStatusValue    = 0;
    void            ServoTestInit( void );               // 伺服电机测试相关初始化
    void            refreshServoSpeed( quint16 speed );  // 伺服转速
    void            refreshServoDirection( quint8 dir );
    void            refreshServoElastic( quint8 elastic );
    void            refreshServoStatus( quint8 status );
    void            sendServoTestCommond();
    void            CreateServoTestCtrlBtnGroup();  // 创建伺服电机测试控制按键组
    void            refreshServoParams( quint16 codeValue, quint16 speedValue, quint16 circleValue, quint16 abNeedleValue, quint16 zeroValue, float abAngelValue );

    /************* 伺服电机参数相关 *************/
    bool             isServoParaPageInit = false;
    void             initServoParaPage();
    void             initGroupBoxDown();
    MyLineEdit*      currentSelectedMyLineEdit = nullptr;
    NumberInputForm* _numberInputFrm           = nullptr;

    /************* 缝头测试相关 *************/
    bool          isFengtouTestPageInit = false;
    QList< int >  ftValveSelectList;
    QButtonGroup* FTTestMotorBtnGroup;
    QButtonGroup* FTTestActionBtnGroup;
    void          FengtouTestInit( void );  // 缝头测试相关初始化
    void          ShowFengtouButton();

    /************* 功能测试相关 *************/
    bool         isFunctionTestPageInit     = false;
    QPushButton* currentSelectedFunctionBtn = nullptr;     // 当前选中的功能按钮
    void         FunctionTestInit( void );                 // 功能测试相关初始化
    void         executeFunctionAction( int functionId );  // 执行功能对应的动作

    /************* 键盘测试相关 *************/
    bool                     isKeyboardTestPageInit = false;
    QMap< QString, QLabel* > keyLabels;           // 存储键盘标签
    void                     initKeyBoardTest();  // 初始化键盘测试界面
    void                     initGboxKeyBoard();

    /************* LED测试相关 *************/
    QMap< QString, QPushButton* > ledButtons;                                            // 存储LED按钮
    void                          initGboxLED();                                         // 初始化LED测试界面
    void                          updateLEDStatus( const QString& ledName, bool isOn );  // 更新LED状态
signals:
    void TestFormToMainWinToShowSignal();  // 测试界面向主界面发送的显示主界面信号

private slots:
    void onTestHomeBtnClicked();           // 测试界面返回 主菜单键按下槽函数
    void onMenuBtnGroupClicked( int id );  // 菜单按键组按下槽函数

    /***** 气阀测试相关槽函数 *****/
    void onValveTestCtrlBtnGroupClicked( int id );  // 气阀测试控制按键组按下槽函数

    /***** 选针器相关槽函数 *****/
    void onKnifeBtnGroupClicked( int id );
    void onSelectBtnGroupClicked( int id );
    void onSelectCtrlBtnGroupClicked( int id );

    /***** 步进电机相关槽函数 *****/
    void onParaLineEditClicked();                  // 选中该电机
    void onParaLineEditDoubleClicked();            // 显示数字输入框
    void onParaInputFinish( QString text );        // 步进电机测试界面键盘输入完成
    void onStepTestCtrlBtnGroupClicked( int id );  // 步进电机测试界面下控制按键组按下槽函数
    void onStepMotorTestInfoFrameReceived( qint16 size, quint8* data );
    void zeroCmdFrmItemSelected( int itemKey );

    /***** 伺服电机相关槽函数 *****/
    void onServoTestCtrlBtnGroupClicked( int id );
    void onServoTestSpeedLineEditClicked();                 // 伺服度设定LineEdit
    void onServoTestSpeedLineEditFinished( QString text );  // 伺服电机测试界面下控制按键组按下槽函数
    void onServoMotorTestInfoFrameReceived( qint16 size, quint8* data );

    /************* 伺服电机参数相关 *************/
    void onServoParaShowNumberInputForm();
    void onSeroParaNumberInputFormFinished( QString str );

    /***** 缝头相关槽函数 *****/
    void onFtValveButtonClicked();
    void onFengtouActionBtnGroupClicked( int id );

    /***** 功能测试相关槽函数 *****/
    void onFunctionButtonClicked();
};

#endif  // TESTFORM_H
