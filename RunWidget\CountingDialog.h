#ifndef COUNTINGDIALOG_H
#define COUNTINGDIALOG_H
#include <QApplication>
#include <QDialog>
#include <QLabel>
#include <QPushButton>
#include <QScreen>
#include <QTimer>
#include <QVBoxLayout>

class CountingDialog : public QDialog
{
    Q_OBJECT
public:
    CountingDialog( const QString& title, const QString& text, int waitingSeconds = 120, QWidget* parent = nullptr ) : QDialog( parent ), waitingSeconds( waitingSeconds )
    {
        setWindowTitle( title );

        QLabel* label1 = new QLabel( text, this );
        label1->setAlignment( Qt::AlignCenter );

        waitingLabel = new QLabel( "已等待0秒", this );
        waitingLabel->setAlignment( Qt::AlignCenter );

        timer = new QTimer( this );
        connect( timer, &QTimer::timeout, this, &CountingDialog::onTimerTimout );
        timer->start( 1000 );  // 120 秒

        QPushButton* button = new QPushButton( "取消", this );  // 连接按钮的点击信号到槽函数
        connect( button, &QPushButton::clicked, this, &CountingDialog::close );

        setLayout( new QVBoxLayout );
        layout()->addWidget( label1 );
        layout()->addWidget( waitingLabel );
        layout()->addWidget( button );

        this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );
    }

signals:
    void onCountingTimeout();

private:
    int     waitingSeconds;
    int     currentWaitingSeconds = 0;
    QLabel* waitingLabel;
    QTimer* timer;

    void onTimerTimout()
    {
        currentWaitingSeconds++;
        if ( currentWaitingSeconds >= waitingSeconds )
        {
            timer->stop();
            emit onCountingTimeout();
            this->close();
        }
        else
        {
            waitingLabel->setText( QString( "已等待%1秒" ).arg( currentWaitingSeconds ) );
        }
    }
};

#endif  // COUNTINGDIALOG_H
