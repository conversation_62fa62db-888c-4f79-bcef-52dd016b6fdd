#include "ComboCheckDialog.h"
#include "ui_ComboCheckDialog.h"
#include <QScreen>

ComboCheckDialog::ComboCheckDialog( QWidget* parent, ReadTestConfig* testCfg, Communication* comm ) : QDialog( parent ), ui( new Ui::ComboCheckDialog ), comm( comm ), testCfg( testCfg )
{
    ui->setupUi( this );
    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    connect( this->ui->pbtn_quit, &QPushButton::clicked, this, [&]() { this->close(); } );

    // 显示配置文件内容
    showCombos();
}

ComboCheckDialog::~ComboCheckDialog()
{
    delete ui;
}

void ComboCheckDialog::showCombos()
{
    QMap< int, ReadTestConfig::ComboValveStruct >* list = testCfg->getComboValveList();

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_combo );

    for ( quint8 index = 0; index < ( list->size() + 1 ) / 2; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;

        for ( int col_index = 0; col_index < 2; col_index++ )
        {
            MyLineEdit* lineEdit = new MyLineEdit;
            lineEdit->setAlignment( Qt::AlignHCenter );
            // Key start from 1
            lineEdit->setText( list->value( index * 2 + col_index ).name );
            lineEdit->setProperty( "key", index * 2 + col_index );

            lineEdit->setStyleSheet( "QLineEdit {"
                                     "    min-height: 50px;"
                                     "    background: #00A2E8;"
                                     "    border: 2px solid black;"
                                     "    font-size: 16px;"
                                     "}"
                                     "QLineEdit:focus {"
                                     "    border: 2px solid red;"
                                     "}" );

            connect( lineEdit, &MyLineEdit::mouseRelease, this, &ComboCheckDialog::switchCombos );

            rowLayoutx->addWidget( lineEdit );
        }
        mainLayout->addLayout( rowLayoutx );
    }

    QSpacerItem* verticalSpacer = new QSpacerItem( 0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding );
    mainLayout->addItem( verticalSpacer );
}

void ComboCheckDialog::switchCombos()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        QVariant var = senderLineEdit->property( "key" );
        if ( var.isValid() )
        {
            int               key = var.toInt();
            QVector< quint8 > ids = testCfg->getComboValveList()->value( key ).ids;

            quint8                          id1 = ids[ 0 ], id2 = ids[ 1 ];
            ReadTestConfig::ValveInfoStruct valve1 = testCfg->getValveList()->value( id1 );
            ReadTestConfig::ValveInfoStruct valve2 = testCfg->getValveList()->value( id2 );

            ui->lbl_valveName->setText( valve1.value );
            ui->lbl_valveKey->setText( QString::number( valve1.key ) );
            ui->lbl_valveType->setText( valve1.type );
            ui->lbl_valveIndex->setText( QString::number( valve1.index ) );
            ui->lbl_valvePos->setText( QString::number( valve1.pos ) );

            ui->lbl_valveName1->setText( valve2.value );
            ui->lbl_valveKey1->setText( QString::number( valve2.key ) );
            ui->lbl_valveType1->setText( valve2.type );
            ui->lbl_valveIndex1->setText( QString::number( valve2.index ) );
            ui->lbl_valvePos1->setText( QString::number( valve2.pos ) );
        }
    }
}
