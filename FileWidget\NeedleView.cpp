﻿#include "NeedleView.h"
#include <QDebug>
#include <QPainter>
#include <QPen>

NeedleView::NeedleView( quint8* buffer, QWidget* parent ) : QLabel( parent )
{
    //此处值为默认初始值
    w0      = this->width();
    h0      = this->height();
    _buffer = new quint8[ 25 ];
    memcpy( _buffer, buffer, 25 );
    //    qDebug() << "NeedleView" << w0 << h0;
}

NeedleView::~NeedleView()
{
    delete[] _buffer;
    _buffer = Q_NULLPTR;
}

void NeedleView::setData( quint8* buffer )
{
    memcpy( _buffer, buffer, 25 );
    this->update();
}

void NeedleView::paintEvent( QPaintEvent* event )
{
    //此处值为执行主程序中后修正过的值
    int w = this->width();
    int h = this->height();

    //    qDebug() << "NeedleView1" << w << h;
    QPainter painter( this );
    painter.setPen( QPen( Qt::red, 3 ) );
    //    painter.drawLine( 0, 0, w, h );
    //    painter.drawPie( QRect( 10, 10, w - 10, h - 10 ), 0, 315 * 16 );

    for ( quint32 index = 0; index < 200; index++ )
    {
        quint8 value = *( _buffer + index / 8 );
        bool   state = ( value >> ( index % 8 ) ) & 0x01;
        //        qDebug() << "value" << value << state;

        /*1：出针 0：不出*/
        if ( !state )
        {
            // 选中时有2px宽的边框，pen宽度为3，所以第一个刷子从第2+1=3px开始
            painter.drawLine( 3 + index * 3, 0 + h - 4, 3 + index * 3, 0 + h / 2 - 2 );
        }
        else
        {
            painter.drawLine( 3 + index * 3, 0 + h - 4, 3 + index * 3, 2 );
        }
    }
}
