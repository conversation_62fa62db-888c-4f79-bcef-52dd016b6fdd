#ifndef SENSORFORM_H
#define SENSORFORM_H

#include "Common/defines.h"
#include "Communicate/Communication.h"
#include <QHBoxLayout>
#include <QLabel>
#include <QMap>
#include <QWidget>

namespace Ui
{
class SensorForm;
}

class SensorForm : public QWidget
{
    Q_OBJECT

public:
    explicit SensorForm( QWidget* parent = nullptr, MainWidgetData* mainData = nullptr, Communication* com = nullptr );
    ~SensorForm();
    // 下位机上报数据帧刷新
    void refreshSocketWarn( qint16 size, quint8* data );
    void refreshFengtouWarn( qint16 size, quint8* data );
    void refreshZeroSensor( qint16 size, quint8* data );

private:
    Ui::SensorForm* ui;
    MainWidgetData* mainData;
    /************* 422通信相关 *************/
    Communication* comm;

    QMap< int, QLabel* > sockeWarnLabelList;
    QMap< int, QLabel* > fengtouWarnLabelList;
    QMap< int, QLabel* > zeroLabelList;

    /************* 报警测试相关 *************/
    void WarnTestPageInit( void );
    void GenerateSocketInputWidget();
    void GenerateSeamHeadInputWidget();
    void GenerateMotorZeroInputWidget();

signals:
    void SensorFormToMainWinToShowSignal();

private slots:
    void onSensorHomeBtnClicked();  //测试界面返回 主菜单键按下槽函数
};

#endif  // SENSORFORM_H
