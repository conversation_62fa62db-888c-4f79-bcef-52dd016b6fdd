﻿#include "testform.h"
#include "ui_testform.h"

TestForm::TestForm( QWidget* parent, MainWidgetData* mainData, Communication* com, CommKeyboard* keyboard )
    : QWidget( parent ), ui( new Ui::TestForm ), mainData( mainData ), comm( com ), m_keyboard( keyboard )
{
    ui->setupUi( this );

    /* 返回主菜单键连接  */
    connect( ui->TestFormHome_btn, SIGNAL( clicked() ), this, SLOT( onTestHomeBtnClicked() ) );

    // 刚启动时显示菜单页
    ui->Test_stackedWidget->setCurrentIndex( 0 );
    ui->Test_Title_label->setText( "机器手动" );

    /* 添加 二级菜单的按键组 */
    menuBtnGroup = new QButtonGroup( this );
    menuBtnGroup->addButton( ui->Test_valve_btn, 1 );
    menuBtnGroup->addButton( ui->Test_select_btn, 2 );
    menuBtnGroup->addButton( ui->Test_serve_btn, 3 );
    menuBtnGroup->addButton( ui->Test_step_btn, 4 );
    menuBtnGroup->addButton( ui->Test_fengtou_btn, 5 );
    menuBtnGroup->addButton( ui->Test_function_btn, 6 );
    menuBtnGroup->addButton( ui->Test_keyboard_btn, 8 );
    connect( menuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onMenuBtnGroupClicked( int ) ) );

    /* 气阀测试初始化*/
    this->ValveTestInit();
    /* 选针器测试相关初始化 */
    this->SelectTestInit();
    /* 步进电机测试相关初始化 */
    this->StepTestInit();
    /* 伺服电机测试相关初始化 */
    this->ServoTestInit();
    /* 缝头测试相关初始化 */
    this->FengtouTestInit();
    /* 功能测试相关初始化 */
    this->FunctionTestInit();
    /* 按键测试相关初始化 */
    this->initKeyBoardTest();

    connect( comm, &Communication::StepMotorTestInfoFrameReceived, this, &TestForm::onStepMotorTestInfoFrameReceived );
    connect( comm, &Communication::ServoMotorTestInfoFrameReceived, this, &TestForm::onServoMotorTestInfoFrameReceived );
}

TestForm::~TestForm()
{
    qDebug() << "~TestForm";
    delete ui;
}

/* 菜单按钮按下槽函数 */
void TestForm::onMenuBtnGroupClicked( int id )
{
    ui->Test_stackedWidget->setCurrentIndex( id );
    switch ( id )
    {
        case 1:
            ui->Test_Title_label->setText( "测试/气阀" );
            break;
        case 2:
            ui->Test_Title_label->setText( "测试/选针器" );
            break;
        case 3:
            ui->Test_Title_label->setText( "测试/伺服电机" );
            break;
        case 4:
            ui->Test_Title_label->setText( "测试/步进电机" );
            break;
        case 5:
            ui->Test_Title_label->setText( "测试/缝头测试" );
            break;
        case 6:
            ui->Test_Title_label->setText( "测试/功能测试" );
            break;
        case 8:
            ui->Test_Title_label->setText( "测试/按键测试" );
            break;
    }
}

/* 测试界面返回 主菜单键按下槽函数 */
void TestForm::onTestHomeBtnClicked()
{
    if ( ui->Test_stackedWidget->currentIndex() > 0 )
    {
        // 停止自动运行的任务
        stopAutoTask();

        // 如果是从伺服测试返回，需发送伺服退出指令
        if ( ui->Test_stackedWidget->currentIndex() == 3 )
        {
            quint8 data[ 6 ] = { 0 };
            data[ 0 ]        = 0x02;
            data[ 1 ]        = 0x02;
            comm->pushDataTobuffer( 0x09, data, 6 );
        }

        ui->Test_stackedWidget->setCurrentIndex( 0 );
        ui->Test_Title_label->setText( "机器手动" );
    }
    else
    {
        this->close();
        emit TestFormToMainWinToShowSignal();
    }
}

void TestForm::onStepMotorTestInfoFrameReceived( qint16 size, quint8* data )
{
    quint8 result[ size ];
    memcpy( result, data, size );
    quint8  deviceId     = result[ 0 ];
    quint16 currentValue = result[ 1 ] + result[ 2 ] * 256;
    quint16 codeValue    = result[ 3 ] + result[ 4 ] * 256;
    quint8  zeroValue    = result[ 5 ];
    quint8  limitValue   = result[ 6 ];
    RefreshStepParams( deviceId, currentValue, codeValue, zeroValue, limitValue );
}

void TestForm::onServoMotorTestInfoFrameReceived( qint16 size, quint8* data )
{
    quint8 result[ size ];
    memcpy( result, data, size );
    // 是下位机往上位机上传的消息
    if ( result[ 0 ] == 0x03 )
    {
        quint16 codeValue  = result[ 1 ] + result[ 2 ] * 256;
        qint16  speedValue = 0;
        memcpy( &speedValue, &result[ 3 ], 2 );
        speedValue = qRound( speedValue / 10.0 );
        //        quint16 speedValue    = result[ 3 ] + result[ 4 ] * 256;
        quint16 circleValue   = result[ 5 ] + result[ 6 ] * 256;
        quint16 zeroValue     = mainData->readMachineFileConfig->getBasicCfgList()->value( 3 ).value;
        float   abAngelValue  = ( ( codeValue + 4096 - zeroValue ) % 4096 ) * 360 / 4096.0f;
        float   needleNum     = mainData->readMachineFileConfig->getBasicCfgList()->value( 2 ).value;
        quint16 abNeedleValue = static_cast< quint16 >( abAngelValue * needleNum / 360 );
        // memcpy( &abAngelValue, &result[ 11 ], 4 );
        refreshServoParams( codeValue, speedValue, circleValue, abNeedleValue, zeroValue, abAngelValue );
    }
    else if ( result[ 0 ] == 0x05 )
    {
        quint16 codeValue    = result[ 1 ] + result[ 2 ] * 256;
        quint16 zeroValue    = mainData->readMachineFileConfig->getBasicCfgList()->value( 3 ).value;
        float   abAngelValue = ( ( codeValue + 4096 - zeroValue ) % 4096 ) * 360 / 4096.0f;
        quint8  statusValue  = result[ 5 ];

        if ( this->servZeroDialog != nullptr )
        {
            this->servZeroDialog->refreshParams( codeValue, abAngelValue );
            if ( statusValue == 1 )
            {
                this->servZeroDialog->setServZeroCalFinished();
            }
            else if ( statusValue == 2 )
            {
                this->servZeroDialog->setStatusLabel( "伺服电机无零位" );
            }
            else
            {
                this->servZeroDialog->setStatusLabel( "" );
            }
        }
    }
}

void TestForm::stopAutoTask()
{
    // 定时器相关;
    if ( this->testTimer != nullptr )
    {
        this->testTimer->stop();
    }

    // 任务相关  前3个选针器相关的 中间2个与气阀测试相关
    if ( isSelectorAutoTestingRunning )
    {
        QObject::disconnect( this->testTimer, &QTimer::timeout, this, &TestForm::SelectAutoTesting );
        this->ui->SelectTest_Auto_btn->setStyleSheet( "" );
        this->isSelectorAutoTestingRunning = false;
    }
    else if ( isSelectorAllTestingRunning )
    {
        QObject::disconnect( this->testTimer, &QTimer::timeout, this, &TestForm::SelectAllTesting );
        this->ui->SelectTest_Old_btn->setStyleSheet( "" );
        this->isSelectorAllTestingRunning = false;
    }
    else if ( isKnifeSingleLoopTestingRunning )
    {
        QObject::disconnect( this->testTimer, &QTimer::timeout, this, &TestForm::SingleKnifeLoop );
        this->ui->SelectTest_SignalLoop_btn->setStyleSheet( "" );
        this->isKnifeSingleLoopTestingRunning = false;
    }
    else if ( isValveAutoTestingRunning )
    {
        QObject::disconnect( this->testTimer, &QTimer::timeout, this, &TestForm::valveAutoTesting );
        this->ui->ValveTest_Auto_btn->setStyleSheet( "" );
        this->isValveAutoTestingRunning = false;

        // 恢复所有按钮状态
        QMap< int, ValveButtonState >* stateMap = nullptr;
        switch ( currentValveTab )
        {
            case TAB_SPECIAL_FUN:
                stateMap = &specialFunStates;
                break;
            case TAB_CAM:
                stateMap = &camStates;
                break;
            case TAB_YARN_FINGER:
                stateMap = &yarnFingerStates;
                break;
            case TAB_YARN2:
                stateMap = &yarn2States;
                break;
            case TAB_EXTEND:
                stateMap = &extendStates;
                break;
        }

        if ( stateMap )
        {
            for ( auto it = stateMap->begin(); it != stateMap->end(); ++it )
            {
                ValveButtonState& state = it.value();
                if ( state.isPressed )
                {
                    state.isPressed = false;  // 清除按钮的选中状态
                    if ( state.button )
                    {
                        state.button->setStyleSheet( "font-size: 8pt; background-color: rgb(255, 255, 255); color: black;" );
                    }
                    sendValveState( state.id );
                }
            }
            // sendValveState();  // 发送更新后的状态
        }

        // 重置自动测试相关变量
        autoTestPressedButtons.clear();
        autoTestCurrentIndex = 0;
        autoTestIsFirstRun   = true;
    }
    else if ( isValveContinuousTestingRunning )
    {
        QObject::disconnect( this->testTimer, &QTimer::timeout, this, &TestForm::valveContinuousTesting );
        this->ui->ValveTest_Continue_btn->setStyleSheet( "" );
        this->isValveContinuousTestingRunning = false;

        // 恢复所有按钮状态
        QMap< int, ValveButtonState >* stateMap = nullptr;
        switch ( currentValveTab )
        {
            case TAB_SPECIAL_FUN:
                stateMap = &specialFunStates;
                break;
            case TAB_CAM:
                stateMap = &camStates;
                break;
            case TAB_YARN_FINGER:
                stateMap = &yarnFingerStates;
                break;
            case TAB_YARN2:
                stateMap = &yarn2States;
                break;
            case TAB_EXTEND:
                stateMap = &extendStates;
                break;
        }

        ValveButtonState& currentState = ( *stateMap )[ continuousLastId ];
        if ( currentState.button )
        {
            currentState.button->setStyleSheet( "font-size: 8pt; background-color: rgb(255, 255, 255); color: black;" );
        }
        sendValveState( continuousLastId, 0 );
    }

    if ( this->testTimer != nullptr )
    {
        delete this->testTimer;
        this->testTimer = nullptr;
    }
}
