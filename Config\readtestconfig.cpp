﻿#include "readtestconfig.h"

#include <QDebug>
#include <QFile>

ReadTestConfig::ReadTestConfig() {}

ReadTestConfig::~ReadTestConfig() {}

void ReadTestConfig::parseConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        // 文件打开失败
        QMessageBox::warning( nullptr, "错误提示", "TestConfig.json文件不存在" );
        qDebug() << "Test Config Json  Open Failed!";
        return;
    }
    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        // Json文档无效
        qDebug() << "Test Config Json文档无效!";
        return;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObject = jsonDocument.object();

    // 获取stepNames数组
    QJsonArray valveNamesArray = jsonObject[ "airvalve" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < valveNamesArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = valveNamesArray.at( i ).toObject();

        // 获取key和value属性
        ValveInfoStruct item;
        item.key   = step[ "key" ].toInt();
        item.value = step[ "value" ].toString();
        item.type  = step[ "type" ].toString();
        item.index = step[ "index" ].toInt();
        item.pos   = step[ "pos" ].toInt();

        // 对key和value进行处理
        this->_valveList.insert( item.key, item );
    }

    // 获取combo数组
    QJsonArray comboValveArray = jsonObject[ "comboAirValve" ].toArray();
    // 遍历stepNames数组
    for ( int i = 0; i < comboValveArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject it = comboValveArray.at( i ).toObject();

        // 获取key和value属性
        ComboValveStruct item;
        item.name          = it[ "name" ].toString();
        QJsonArray idArray = it[ "ids" ].toArray();
        for ( int j = 0; j < idArray.size(); ++j )
        {
            item.ids.append( idArray.at( j ).toInt() );
        }

        // 对key和value进行处理,以id为索引，从0开始
        this->_comboValveList.insert( i, item );
    }

    file.close();  // 关闭文件
}
