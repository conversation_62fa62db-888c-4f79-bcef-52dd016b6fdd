<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UsbForm</class>
 <widget class="QWidget" name="UsbForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="Usb_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>419</x>
     <y>10</y>
     <width>191</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
   </property>
   <property name="text">
    <string>文件管理</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QPushButton" name="UsbFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>8</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>60</y>
     <width>1021</width>
     <height>541</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="page">
    <widget class="QLabel" name="label_10">
     <property name="geometry">
      <rect>
       <x>210</x>
       <y>290</y>
       <width>150</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>USB管理</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_14">
     <property name="geometry">
      <rect>
       <x>570</x>
       <y>290</y>
       <width>150</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>启用</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="Usb_enable_btn">
     <property name="geometry">
      <rect>
       <x>600</x>
       <y>171</y>
       <width>100</width>
       <height>100</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>100</width>
       <height>100</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/Setting (3).png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QPushButton" name="Usb_usb_btn">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>170</y>
       <width>100</width>
       <height>100</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>100</width>
       <height>100</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/file.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_2">
    <widget class="QPushButton" name="pbtn_toUSB">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>410</y>
       <width>61</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    border: 1px solid #27ae60;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #27ae60;
    border: 1px solid #229954;
}
QPushButton:pressed {
    background-color: #229954;
    border: 1px solid #1e8449;
}</string>
     </property>
     <property name="text">
      <string>&gt;&gt;</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toThis">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>140</y>
       <width>61</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    border: 1px solid #2980b9;
    border-radius: 5px;
    padding: 5px;
    color: white;
}
QPushButton:hover {
    background-color: #2980b9;
    border: 1px solid #2573a7;
}
QPushButton:pressed {
    background-color: #2573a7;
    border: 1px solid #1f618d;
}</string>
     </property>
     <property name="text">
      <string>&lt;&lt;</string>
     </property>
    </widget>
    <widget class="QTableView" name="Usb_tableView_this">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>50</y>
       <width>461</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    border: 1px solid #dddddd;
    border-radius: 5px;
    gridline-color: #e0e0e0;
    selection-background-color: #3498db;
    selection-color: white;
}

QHeaderView::section {
    background-color: #f0f0f0;
    border: 1px solid #dddddd;
    padding: 4px;
    font-weight: bold;
    color: #333333;
}</string>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QTableView" name="Usb_tableView_usb">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>50</y>
       <width>461</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    border: 1px solid #dddddd;
    border-radius: 5px;
    gridline-color: #e0e0e0;
    selection-background-color: #3498db;
    selection-color: white;
}

QHeaderView::section {
    background-color: #f0f0f0;
    border: 1px solid #dddddd;
    padding: 4px;
    font-weight: bold;
    color: #333333;
}</string>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>6</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>机型选择</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_machineType">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>0</y>
       <width>221</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit {
    background-color: white;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 2px;
    color: #333333;
}
MyLineEdit:focus {
    border: 1px solid #3498db;
    background-color: #e8f4fc;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>0</y>
       <width>91</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>文件类型</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_fileType">
     <property name="geometry">
      <rect>
       <x>460</x>
       <y>0</y>
       <width>221</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit {
    background-color: white;
    border: 1px solid #dddddd;
    border-radius: 5px;
    padding: 2px;
    color: #333333;
}
MyLineEdit:focus {
    border: 1px solid #3498db;
    background-color: #e8f4fc;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_backDir">
     <property name="geometry">
      <rect>
       <x>950</x>
       <y>0</y>
       <width>41</width>
       <height>41</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    border-radius: 5px;
    border-image: url(:/back.png);
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
}
QPushButton:hover {
    background-color: #e8f4fc;
    border: 1px solid #3498db;
}
QPushButton:pressed {
    background-color: #d4e6f1;
    border: 1px solid #2980b9;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </widget>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
