﻿#ifndef MYWIDGET_H
#define MYWIDGET_H

#include <QtWidgets>

class MyWidget : public QWidget
{
    Q_OBJECT
public:
    MyWidget( QWidget* parent = nullptr )
        : QWidget( parent ){

          };
    void mousePressEvent( QMouseEvent* ) override
    {
        emit mousePress();
    }
    void mouseReleaseEvent( QMouseEvent* ) override
    {
        emit mouseRelease();
    }
signals:
    void mousePress();
    void mouseRelease();
};
#endif  // MYWIDGET_H
