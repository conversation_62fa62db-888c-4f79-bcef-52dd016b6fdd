#ifndef ONEKEYFORM_H
#define ONEKEYFORM_H

#include "Common/defines.h"
#include "Common/parameters.h"
#include "CommonWidget/comboform.h"
#include "CommonWidget/floatinputform.h"
#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "Communicate/Communication.h"
#include "Config/ReadOnekeyConfig.h"
#include "EyeParaSetForm.h"
#include <QButtonGroup>
#include <QDebug>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QTableWidget>
#include <QTimer>
#include <QVBoxLayout>
#include <QWidget>

namespace Ui
{
class OneKeyForm;
}

class OneKeyForm : public QWidget
{
    Q_OBJECT

public:
    explicit OneKeyForm( QWidget* parent = nullptr, Communication* comm = nullptr, MainWidgetData* mainData = nullptr );
    ~OneKeyForm();

signals:
    void OneKeyFormToMainWinToShowSignal();  //向主界面发送的显示主界面信号

private:
    Ui::OneKeyForm* ui;
    Communication*  comm;
    MainWidgetData* mainData = nullptr;

    // 使用 mainData 中的指针
    ReadMachineFileConfig* readMachineFileConfig;

    // 参数发送定时器
    QTimer paramSendTimer;
    bool   paramSendSuccess = false;
    int    retryCountParam  = 0;

    // 菜单页面
    QButtonGroup* menuBtnGroup;  //菜单按键组

    ComboForm*       comboFrm                  = nullptr;
    FloatInputForm*  floatInputFrm             = nullptr;
    NumberInputForm* numberInputFrm            = nullptr;
    MyLineEdit*      currentSelectedMyLineEdit = nullptr;
    bool             isUserParasetPageInit     = false;
    int              warnTableEditRowIndex     = -1;
    int              warnTableEditColIndex     = -1;
    bool             isWarnParasetPageInit     = false;

    // 电眼设置
    quint8        eyeNum           = 0;
    bool          isEEyePageInited = false;
    bool          isEyeLearning    = false;  // 是否处于电眼学习状态
    QButtonGroup* eeyeBtnGroup;              // 菜单按键组
    void          initEEyePage();
    void          refreshTabControls( quint8 type_id );
    void          setEyeLearningState( bool learning );                     // 设置电眼学习状态
    void          EEyeRefreshStatus( quint8* statusData, int dataLength );  // 刷新电眼状态

    // ktf相关
    QButtonGroup* ktfMenuBtnGroup;  // 菜单按键组
    QButtonGroup* ktf1InstallBtnGroup;
    QButtonGroup* ktf2InstallBtnGroup;
    QButtonGroup* ktf3InstallBtnGroup;
    QButtonGroup* ktf4InstallBtnGroup;
    QButtonGroup* ktf1DirBtnGroup;
    QButtonGroup* ktf2DirBtnGroup;
    QButtonGroup* ktf3DirBtnGroup;
    QButtonGroup* ktf4DirBtnGroup;
    void          initKtfPage();
    void          saveKtfConfig();
    void          sendKtfConfigToLower();

    // 织袜报警和缝头报警分页相关
    static const int ITEMS_PER_PAGE         = 10;  // 每页显示的项目数
    int              socketWarnCurrentPage  = 0;   // 织袜报警当前页
    int              socketWarnTotalPages   = 0;   // 织袜报警总页数
    int              fengtouWarnCurrentPage = 0;   // 缝头报警当前页
    int              fengtouWarnTotalPages  = 0;   // 缝头报警总页数

    // Warn
    void initWarnParasetPage();

    // 织袜报警相关
    void initSocketWarnParasetTab();
    void updateSocketWarnPage();
    void onSocketWarnPrevPage();
    void onSocketWarnNextPage();
    void onSocketWarnEditClicked();
    void onSocketWarnNumberInputFormFinished( QString value );

    // 缝头报警相关
    void initFengtouWarnParasetTab();
    void updateFengtouWarnPage();
    void onFengtouWarnPrevPage();
    void onFengtouWarnNextPage();
    void onFengtouWarnEditClicked();
    void onFengtouWarnNumberInputFormFinished( QString value );

    // User
    void initUserParasetPage();
    void initUserParasetTab();
    void onUserParasetEditClicked();
    void onUserFloatInputFormFinished( float value );

    void initOtherParasetTab();
    void onOtherParasetEditClicked();
    void onOtherFloatInputFormFinished( float value );

    // 发送用户参数
    void sendUserParam();

private slots:
    void onHomeBtnClicked();               //测试界面返回 主菜单键按下槽函数
    void onMenuBtnGroupClicked( int id );  //菜单按键组按下槽函数

    // 电眼设置
    void onEyeParamsEdited( quint8 type, quint8 index, quint16 params );
    // 新增处理电眼配置帧的槽函数
    void onEEyeConfigFrameReceived( qint16 size, quint8* data );
    void onEEyeBtnGroupClicked( int );

    // KTF
    void onKtfMenuBtnGroupClicked( int id );  // KTF菜单按键组按下槽函数
    //    void onSocketWarnParasetTableItemClicked( QTableWidgetItem* item );
    //    void onFengtouWarnParasetTableItemClicked( QTableWidgetItem* item );
};

#endif  // ONEKEYFORM_H
