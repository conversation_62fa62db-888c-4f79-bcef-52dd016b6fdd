#ifndef SELECTORVIEW_H
#define SELECTORVIEW_H

#include <QDebug>
#include <QLabel>
#include <QPainter>
#include <QPen>
#include <QWidget>

class SelectorView : public QLabel
{
    Q_OBJECT
public:
    SelectorView( quint8 buffer = 0, QWidget* parent = nullptr ) : QLabel( parent ), _buffer( buffer )
    {
        //此处值为默认初始值
        w0 = this->width();
        h0 = this->height();
    }
    void setData( quint8 buffer )
    {
        _buffer = buffer;
        this->update();
    }

protected:
    void paintEvent( QPaintEvent* event ) override
    {
        //此处值为执行主程序中后修正过的值
        int w = this->width();
        int h = this->height();

        QPainter painter( this );
        painter.setPen( QPen( Qt::red, 10 ) );

        for ( qint8 index = 0; index <= 7; index++ )
        {
            bool state = ( _buffer >> ( 7 - index ) ) & 0x01;
            qDebug() << "state" << state;
            /*1：出针 0：不出*/
            if ( !state )
            {
                // 选中时有2px宽的边框，pen宽度为3，所以第一个刷子从第2+1=3px开始
                painter.drawLine( 5 + index * 10, h, 5 + index * 10, h - 8 );
            }
            else
            {
                painter.drawLine( 5 + index * 10, h, 5 + index * 10, 0 );
            }
        }
    }

private:
    int    w0;  //自定义控件类构造函数中QLabel宽度，为默认值
    int    h0;  //自定义控件类构造函数中QLabel高度，为默认值
    quint8 _buffer;
};

#endif  // SELECTORVIEW_H
