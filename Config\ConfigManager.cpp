#include "ConfigManager.h"
#include <QCoreApplication>
#include <QDir>
#include <QStandardPaths>

ConfigManager::ConfigManager( QObject* parent ) : QObject( parent )
{
    // 配置文件保存在程序目录下的config.ini
    QString configPath = QCoreApplication::applicationDirPath() + "/config.ini";
    settings           = new QSettings( configPath, QSettings::IniFormat );
    settings->setIniCodec( "UTF-8" );
}

ConfigManager::~ConfigManager()
{
    if ( settings )
    {
        delete settings;
        settings = nullptr;
    }
}

QString ConfigManager::getLastProgramFile() const
{
    return settings->value( LAST_PROGRAM_FILE, "" ).toString();
}

void ConfigManager::setLastProgramFile( const QString& filePath )
{
    settings->setValue( LAST_PROGRAM_FILE, filePath );
    settings->sync();
}

QString ConfigManager::getLastCraftFile() const
{
    return settings->value( LAST_CRAFT_FILE, "" ).toString();
}

void ConfigManager::setLastCraftFile( const QString& filePath )
{
    settings->setValue( LAST_CRAFT_FILE, filePath );
    settings->sync();
}

// 获取机器名称
QString ConfigManager::getMachineName() const
{
    return settings->value( MACHINE_NAME, "MWW001" ).toString();
}

// 设置机器名称
void ConfigManager::setMachineName( const QString& name )
{
    settings->setValue( MACHINE_NAME, name );
    settings->sync();
}

// 获取机器型号
QString ConfigManager::getMachineModel() const
{
    return settings->value( MACHINE_MODEL, "M615" ).toString();
}

// 设置机器型号
void ConfigManager::setMachineModel( const QString& model )
{
    settings->setValue( MACHINE_MODEL, model );
    settings->sync();
}

// 获取软件版本
QString ConfigManager::getEdition() const
{
    return settings->value( EDITION, "v0.1" ).toString();
}

// 设置软件版本
void ConfigManager::setEdition( const QString& edition )
{
    settings->setValue( EDITION, edition );
    settings->sync();
}
