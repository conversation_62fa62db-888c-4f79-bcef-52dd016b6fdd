#ifndef MQTTCLIENT_H
#define MQTTCLIENT_H

#include "RunWidget/MachineInfo.h"
#include "uppermsgmaker.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QString>
#include <QtMqtt/qmqttclient.h>

class mqttClient : public QObject
{
    Q_OBJECT
public:
    mqttClient( QObject* parent = nullptr, QString hostAddr = "", MachineInfo* machineInfo = nullptr );
    ~mqttClient();

    void publish( QString message );
    void onMessageReceived( const QByteArray& message, const QMqttTopicName& topic );
    void onDisconnected();

private:
    QMqttClient* m_client;
    MachineInfo* machineInfo;
    void         Handler( const QByteArray& message );
};

#endif  // MQTTCLIENT_H
