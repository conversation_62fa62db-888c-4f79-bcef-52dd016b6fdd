<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OneKeyForm</class>
 <widget class="QWidget" name="OneKeyForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Microsoft YaHei</family>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>用户参数设置</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
	background-color: #f5f5f5;
	font-family: &quot;Microsoft YaHei&quot;, &quot;SimHei&quot;, sans-serif;
}</string>
  </property>
  <widget class="QPushButton" name="OneKeyFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>8</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="OnKey_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>419</x>
     <y>10</y>
     <width>191</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>用户参数</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>60</y>
     <width>1021</width>
     <height>541</height>
    </rect>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="page">
    <widget class="QWidget" name="">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>130</y>
       <width>941</width>
       <height>231</height>
      </rect>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QPushButton" name="OneKey_user_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/userparam.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_10">
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>用户参数</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QPushButton" name="OneKey_warn_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/MPIS-AlarmTps.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_14">
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>报警设置</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <widget class="QPushButton" name="OneKey_eye_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
	background-color: rgb(193, 193, 193);
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(://eye.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed {
	background-color: #c1c1c1;
}
</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_12">
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>电眼</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <widget class="QPushButton" name="OneKey_weight_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
	background-color: rgb(193, 193, 193);
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(://chenzhong.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed {
	background-color: #c1c1c1;
}
</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_13">
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>称重</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <item>
         <widget class="QPushButton" name="OneKey_ktf_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/shaxian.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_15">
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>纱线 
感应器</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QWidget" name="page_2">
    <widget class="QTabWidget" name="tabWidget">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>10</y>
       <width>1024</width>
       <height>531</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabBar::tab{
	height: 50px;
	width:140px;
	border-radius: 5px 5px 0 0;
	margin-right: 2px;
	padding: 5px;
	background-color: #e0e0e0;
}
QTabBar::tab:selected {
	background-color: #3498db;
	color: white;
	font-weight: bold;
}
QTabWidget::pane {
	border: 1px solid #c0c0c0;
	background-color: #f5f5f5;
}
QWidget{
	background-color: #f5f5f5;
}</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="UserPara_tab">
      <property name="styleSheet">
       <string notr="true">QWidget {
    background-color: white;
}

QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 4px;
    padding: 4px;
    font-size: 9pt;
}

MyLineEdit {
    background-color: #f0f0f0;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 9pt;
}

MyLineEdit:hover {
    border: 1px solid #3498db;
    background-color: #e8f4fc;
}</string>
      </property>
      <attribute name="title">
       <string>用户参数</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="OtherPara_tab">
      <property name="styleSheet">
       <string notr="true">QWidget {
    background-color: white;
}

QLabel {
    background-color: #3498db;
    color: white;
    border-radius: 4px;
    padding: 4px;
    font-size: 9pt;
}

MyLineEdit {
    background-color: #f0f0f0;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 9pt;
}

MyLineEdit:hover {
    border: 1px solid #3498db;
    background-color: #e8f4fc;
}</string>
      </property>
      <attribute name="title">
       <string>其他参数</string>
      </attribute>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_3">
    <widget class="QTabWidget" name="tabWidget_warn">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>20</y>
       <width>1024</width>
       <height>521</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>8</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabBar::tab{
	height: 50px;
	width:400px;
	border-radius: 5px 5px 0 0;
	margin-right: 2px;
	padding: 5px;
	background-color: #e0e0e0;
}
QTabBar::tab:selected {
	background-color: #3498db;
	color: white;
	font-weight: bold;
}
QTabWidget::pane {
	border: 1px solid #c0c0c0;
	background-color: #f5f5f5;
}
QWidget{
	background-color: #f5f5f5;
}</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="FengtouWarnPara_tab">
      <attribute name="title">
       <string>缝头报警</string>
      </attribute>
      <widget class="QPushButton" name="pbtn_FengtouWarnEnAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>30</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #2ecc71;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #27ae60;
}

QPushButton:pressed {
	background-color: #1e8449;
}</string>
       </property>
       <property name="text">
        <string>使能全开</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_FengtouWarnDirNAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>240</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #9b59b6;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #8e44ad;
}

QPushButton:pressed {
	background-color: #6c3483;
}</string>
       </property>
       <property name="text">
        <string>方向全反</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_FengtouWarnDisAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>100</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #e74c3c;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #c0392b;
}

QPushButton:pressed {
	background-color: #922b21;
}</string>
       </property>
       <property name="text">
        <string>使能全关</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_FengtouWarnDirPAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>170</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #f39c12;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #d35400;
}

QPushButton:pressed {
	background-color: #a04000;
}</string>
       </property>
       <property name="text">
        <string>方向全正</string>
       </property>
      </widget>
      <widget class="QGroupBox" name="gbox_fengtouWarn">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>851</width>
         <height>441</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>10</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
	border: 2px solid #3498db;
	border-radius: 8px;
	margin-top: 10px;
	background-color: white;
}

QGroupBox::title {
	subcontrol-origin: margin;
	subcontrol-position: top center;
	padding: 0 5px;
	color: #3498db;
}</string>
       </property>
       <property name="title">
        <string>缝头报警设置</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_FengtouWarnPrev">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>310</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #3498db;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #2980b9;
}

QPushButton:pressed {
	background-color: #1c6ea4;
}</string>
       </property>
       <property name="text">
        <string>上一页</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/arrow-left.png</normaloff>:/arrow-left.png</iconset>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_FengtouWarnNext">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>380</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #3498db;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #2980b9;
}

QPushButton:pressed {
	background-color: #1c6ea4;
}</string>
       </property>
       <property name="text">
        <string>下一页</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/arrow-right.png</normaloff>:/arrow-right.png</iconset>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="SocketWarnPara_tab">
      <attribute name="title">
       <string>织袜报警</string>
      </attribute>
      <widget class="QGroupBox" name="gbox_socketWarn">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>851</width>
         <height>441</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>10</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox {
	border: 2px solid #3498db;
	border-radius: 8px;
	margin-top: 10px;
	background-color: white;
}

QGroupBox::title {
	subcontrol-origin: margin;
	subcontrol-position: top center;
	padding: 0 5px;
	color: #3498db;
}</string>
       </property>
       <property name="title">
        <string>织袜报警设置</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_SocketWarnDirPAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>170</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #f39c12;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #d35400;
}

QPushButton:pressed {
	background-color: #a04000;
}</string>
       </property>
       <property name="text">
        <string>方向全正</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_SocketWarnEnAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>30</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #2ecc71;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #27ae60;
}

QPushButton:pressed {
	background-color: #1e8449;
}</string>
       </property>
       <property name="text">
        <string>使能全开</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_SocketWarnDisAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>100</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #e74c3c;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #c0392b;
}

QPushButton:pressed {
	background-color: #922b21;
}</string>
       </property>
       <property name="text">
        <string>使能全关</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_SocketWarnDirNAll">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>240</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #9b59b6;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #8e44ad;
}

QPushButton:pressed {
	background-color: #6c3483;
}</string>
       </property>
       <property name="text">
        <string>方向全反</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_SocketWarnPrev">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>310</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #3498db;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #2980b9;
}

QPushButton:pressed {
	background-color: #1c6ea4;
}</string>
       </property>
       <property name="text">
        <string>上一页</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/arrow-left.png</normaloff>:/arrow-left.png</iconset>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_SocketWarnNext">
       <property name="geometry">
        <rect>
         <x>880</x>
         <y>380</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei</family>
         <pointsize>9</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	background-color: #3498db;
	color: white;
	border-radius: 8px;
	border: none;
}

QPushButton:hover {
	background-color: #2980b9;
}

QPushButton:pressed {
	background-color: #1c6ea4;
}</string>
       </property>
       <property name="text">
        <string>下一页</string>
       </property>
       <property name="icon">
        <iconset>
         <normaloff>:/arrow-right.png</normaloff>:/arrow-right.png</iconset>
       </property>
      </widget>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_4">
    <widget class="QPushButton" name="btn_Craft_eye_5">
     <property name="geometry">
      <rect>
       <x>730</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>默认参数</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_4">
     <property name="geometry">
      <rect>
       <x>620</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>自动测试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_3">
     <property name="geometry">
      <rect>
       <x>400</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>重新学习</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_eyeNum">
     <property name="geometry">
      <rect>
       <x>260</x>
       <y>0</y>
       <width>151</width>
       <height>31</height>
      </rect>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QTabWidget" name="tabWidget_2">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>40</y>
       <width>1001</width>
       <height>441</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabWidget::pane {
    border: 1px solid #d0d0d0;
    border-radius: 8px;
    background-color: #f5f5f5;
    padding: 5px;
}

QTabBar::tab {
    height: 40px;
    width: 140px;
    background-color: #e0e0e0;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 5px;
    margin-right: 2px;
    font-size: 9pt;
}

QTabBar::tab:hover {
    background-color: #f0f0f0;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
    border-bottom: none;
}

QWidget {
    background-color: #f5f5f5;
}</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_eye_1">
      <attribute name="title">
       <string>灵敏度</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tab_eye_2">
      <attribute name="title">
       <string>检测时间</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tab_eye_3">
      <attribute name="title">
       <string>运动频率</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tab_eye_4">
      <attribute name="title">
       <string>停止频率</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tab_eye_5">
      <attribute name="title">
       <string>断线阈值</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tab_eye_6">
      <attribute name="title">
       <string>缠线阈值</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tab_eye_7">
      <attribute name="title">
       <string>梭子映射</string>
      </attribute>
     </widget>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_7">
     <property name="geometry">
      <rect>
       <x>840</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>保存参数</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_6">
     <property name="geometry">
      <rect>
       <x>290</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>单独编址</string>
     </property>
    </widget>
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>0</y>
       <width>221</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电眼工作数量[1~36]</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_8">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>单个测试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_2">
     <property name="geometry">
      <rect>
       <x>180</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>重新编址</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_Craft_eye_1">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #007aff;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>状态刷新</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_5">
    <widget class="QGroupBox" name="gb_weight_1">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>10</y>
       <width>320</width>
       <height>461</height>
      </rect>
     </property>
     <property name="title">
      <string>初始重量</string>
     </property>
     <widget class="QLabel" name="label_2">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>50</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#01</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_3">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>100</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#02</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_4">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>150</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#03</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_5">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>200</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#04</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_6">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>250</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#05</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_7">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>300</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#06</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_8">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>350</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#07</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_11">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>400</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#08</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_1">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>50</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_2">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>100</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_3">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>150</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_4">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>200</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_5">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>250</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_6">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>300</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_7">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>350</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_1_8">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>400</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="btn_weight_stop">
     <property name="geometry">
      <rect>
       <x>260</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>结束</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_weight_clear">
     <property name="geometry">
      <rect>
       <x>720</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>重量清除</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gb_weight_2">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>10</y>
       <width>320</width>
       <height>461</height>
      </rect>
     </property>
     <property name="title">
      <string>实时重量</string>
     </property>
     <widget class="QLabel" name="lbl_wight_2_2">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>100</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_8">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>400</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_24">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>200</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#04</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_4">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>200</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_26">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>250</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#05</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_27">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>350</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#07</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_28">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>50</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#01</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_29">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>300</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#06</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_30">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>100</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#02</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_6">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>300</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_1">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>50</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_3">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>150</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_34">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>400</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#08</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_5">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>250</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_2_7">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>350</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_37">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>150</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#03</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="btn_weight_start">
     <property name="geometry">
      <rect>
       <x>150</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>开始</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gb_weight_3">
     <property name="geometry">
      <rect>
       <x>690</x>
       <y>10</y>
       <width>320</width>
       <height>461</height>
      </rect>
     </property>
     <property name="title">
      <string>使用重量</string>
     </property>
     <widget class="QLabel" name="lbl_wight_3_2">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>100</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_8">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>400</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_40">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>200</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#04</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_4">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>200</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_42">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>250</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#05</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_43">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>350</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#07</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_44">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>50</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#01</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_45">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>300</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#06</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_46">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>100</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#02</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_6">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>300</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_1">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>50</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_3">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>150</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_50">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>400</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#08</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_5">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>250</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="lbl_wight_3_7">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>350</y>
        <width>191</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>0g</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_53">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>150</y>
        <width>67</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
	font-size:9pt;
	background-color: rgb(255, 255 ,255);
}</string>
      </property>
      <property name="text">
       <string>#03</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="btn_weight_log">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>数据记录</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_weight_cal">
     <property name="geometry">
      <rect>
       <x>610</x>
       <y>490</y>
       <width>110</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>标称参数</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_6">
    <widget class="QPushButton" name="Ktf_sel_all_btn">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>470</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>一键全选</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gb_ktf_dir">
     <property name="geometry">
      <rect>
       <x>690</x>
       <y>80</y>
       <width>320</width>
       <height>361</height>
      </rect>
     </property>
     <property name="title">
      <string>方向设置</string>
     </property>
     <widget class="QRadioButton" name="rb_ktf_1_4">
      <property name="geometry">
       <rect>
        <x>170</x>
        <y>60</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>逆时针</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_2_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>140</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>顺时针</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_4_4">
      <property name="geometry">
       <rect>
        <x>170</x>
        <y>300</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>逆时针</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_4_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>300</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>顺时针</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_1_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>60</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>顺时针</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_19">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>260</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 4</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_20">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>30</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 1</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_21">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>180</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 3</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_2_4">
      <property name="geometry">
       <rect>
        <x>170</x>
        <y>140</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>逆时针</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_22">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>100</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 2</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_3_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>220</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>顺时针</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_3_4">
      <property name="geometry">
       <rect>
        <x>170</x>
        <y>220</y>
        <width>121</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>逆时针</string>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="Ktf_sel_clockwise_btn">
     <property name="geometry">
      <rect>
       <x>700</x>
       <y>470</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>一键顺时针</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gb_ktf_install">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>80</y>
       <width>320</width>
       <height>361</height>
      </rect>
     </property>
     <property name="title">
      <string>安装设置</string>
     </property>
     <widget class="QLabel" name="label_16">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>30</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 1</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_17">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>100</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 2</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_18">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>190</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 3</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_23">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>270</y>
        <width>81</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>PYF 4</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_1_1">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>60</y>
        <width>161</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Mpp (Elan)</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_1_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>60</y>
        <width>101</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Plus</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_2_1">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>140</y>
        <width>161</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Mpp (Elan)</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_3_1">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>220</y>
        <width>161</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Mpp (Elan)</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_4_1">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>300</y>
        <width>161</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Mpp (Elan)</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_2_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>140</y>
        <width>101</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Plus</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_3_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>220</y>
        <width>101</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Plus</string>
      </property>
     </widget>
     <widget class="QRadioButton" name="rb_ktf_4_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>300</y>
        <width>101</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QRadioButton::indicator:unchecked{
border-image: url(:/radio_button);
Width:30px;
Height:30px;
}
QRadioButton::indicator:checked{
border-image: url(:/radio-button-checked.png);
Width:30px;
Height:30px;
}</string>
      </property>
      <property name="text">
       <string>Plus</string>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="Ktf_sel_anticlockwise_btn">
     <property name="geometry">
      <rect>
       <x>870</x>
       <y>470</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>一键逆时针</string>
     </property>
    </widget>
    <widget class="QPushButton" name="Ktf_desel_all_btn">
     <property name="geometry">
      <rect>
       <x>190</x>
       <y>470</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>一键取消</string>
     </property>
    </widget>
    <widget class="QPushButton" name="Ktf_sel_plus_btn">
     <property name="geometry">
      <rect>
       <x>540</x>
       <y>470</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>一键Plus</string>
     </property>
    </widget>
    <widget class="QPushButton" name="Ktf_sel_elan_btn">
     <property name="geometry">
      <rect>
       <x>370</x>
       <y>470</y>
       <width>120</width>
       <height>45</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>一键Mpp </string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_ktf_Save">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>30</y>
       <width>121</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string>保存</string>
     </property>
    </widget>
    <widget class="QGroupBox" name="gb_ktf_enable">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>80</y>
       <width>320</width>
       <height>361</height>
      </rect>
     </property>
     <property name="title">
      <string>启用设置</string>
     </property>
     <widget class="QCheckBox" name="chx_ktf_enable_1">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>50</y>
        <width>182</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
      </property>
      <property name="text">
       <string>纱线感应器 Pyf +1</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="chx_ktf_enable_2">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>130</y>
        <width>182</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
      </property>
      <property name="text">
       <string>纱线感应器 Pyf +2</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="chx_ktf_enable_3">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>210</y>
        <width>182</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
      </property>
      <property name="text">
       <string>纱线感应器 Pyf + 3</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="chx_ktf_enable_4">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>290</y>
        <width>182</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox{
	background-color: rgb(255, 255, 255);
	width:70px;
	height:40px;
}
QCheckBox::indicator:unchecked{
	border-image: url(:/checkbox.png);
	Width:30px;
	Height:30px;
}
QCheckBox::indicator:checked{
	border-image: url(:/checkbox_checked.png);
	Width:30px;
	Height:30px;
}</string>
      </property>
      <property name="text">
       <string>纱线感应器 Pyf +4</string>
      </property>
     </widget>
    </widget>
   </widget>
  </widget>
  <widget class="QPushButton" name="pbtn_save">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>120</width>
     <height>50</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>10</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius: 5px;					    /* 按钮边框的圆角设置 */
	background-color: #67c23a;
	color:#fff;
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: #4e8e2f;
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string>保存</string>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
