﻿#include "FileSelectForm.h"
#include "ui_FileSelectForm.h"
#include <QScreen>

FileSelectForm::FileSelectForm( QWidget* parent, QString dir_str, quint8 filter ) : QWidget( parent ), ui( new Ui::FileSelectForm )
{
    ui->setupUi( this );
    this->addr = dir_str;
    this->fileFilter = filter;

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    // 创建内容容器
    QWidget* container = new QWidget(this);
    container->setObjectName("container");
    container->setStyleSheet(
        "#container {"
        "    background-color: white;"
        "    border: 1px solid #cccccc;"
        "    border-radius: 8px;"
        "}"
    );

    // 创建内容布局
    QVBoxLayout* containerLayout = new QVBoxLayout(container);
    containerLayout->setContentsMargins(10, 10, 10, 10);
    containerLayout->setSpacing(10);

    // 添加标题栏
    QWidget* titleBar = new QWidget(container);
    titleBar->setFixedHeight(30);
    QHBoxLayout* titleLayout = new QHBoxLayout(titleBar);
    titleLayout->setContentsMargins(5, 0, 5, 0);

    // 根据过滤器类型设置标题
    QString titleText = (filter == 0) ? "选择SOK文件" : "选择CFT文件";
    QLabel* titleLabel = new QLabel(titleText, titleBar);
    titleLabel->setStyleSheet(
        "font-size: 9pt;"
        "font-weight: bold;"
        "color: #2980b9;"
        "padding: 2px;"
    );

    QPushButton* closeButton = new QPushButton("×", titleBar);
    closeButton->setFixedSize(20, 20);
    closeButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    background-color: transparent;"
        "    color: #666666;"
        "    font-size: 12pt;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    color: #ff0000;"
        "}"
    );
    connect(closeButton, &QPushButton::clicked, this, &FileSelectForm::close);

    titleLayout->addWidget(titleLabel);
    titleLayout->addStretch();
    titleLayout->addWidget(closeButton);

    // 添加分隔线
    QFrame* line = new QFrame(container);
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    line->setStyleSheet(
        "background-color: #e0e0e0;"
        "height: 1px;"
        "margin: 0 5px;"
    );

    // 创建文件列表容器
    fileListContainer = new QWidget(container);
    QVBoxLayout* fileListLayout = new QVBoxLayout(fileListContainer);
    fileListLayout->setContentsMargins(5, 5, 5, 5);
    fileListLayout->setSpacing(8);

    // 初始化文件列表
    refreshFileList();

    // 添加分页按钮区域
    QWidget* paginationArea = new QWidget(container);
    QHBoxLayout* paginationLayout = new QHBoxLayout(paginationArea);
    paginationLayout->setContentsMargins(5, 5, 5, 5);

    // 上一页按钮
    QPushButton* prevButton = new QPushButton("上一页");
    prevButton->setFixedSize(80, 30);
    prevButton->setStyleSheet(
        "QPushButton {"
        "    font-size: 8pt;"
        "    background-color: #f5f5f5;"
        "    border: 1px solid #dddddd;"
        "    border-radius: 5px;"
        "    padding: 5px;"
        "    color: #333333;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "    border: 1px solid #bbbbbb;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d0d0d0;"
        "    border: 1px solid #aaaaaa;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #f0f0f0;"
        "    border: 1px solid #e0e0e0;"
        "    color: #a0a0a0;"
        "}"
    );
    connect(prevButton, &QPushButton::clicked, this, &FileSelectForm::onPrevPageClicked);
    paginationLayout->addWidget(prevButton);

    // 页码信息标签
    pageInfoLabel = new QLabel("第 1 / 1 页", paginationArea);
    pageInfoLabel->setAlignment(Qt::AlignCenter);
    pageInfoLabel->setStyleSheet(
        "font-size: 8pt;"
        "color: #333333;"
        "padding: 0 10px;"
    );
    paginationLayout->addWidget(pageInfoLabel);

    // 下一页按钮
    QPushButton* nextButton = new QPushButton("下一页");
    nextButton->setFixedSize(80, 30);
    nextButton->setStyleSheet(
        "QPushButton {"
        "    font-size: 8pt;"
        "    background-color: #f5f5f5;"
        "    border: 1px solid #dddddd;"
        "    border-radius: 5px;"
        "    padding: 5px;"
        "    color: #333333;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "    border: 1px solid #bbbbbb;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d0d0d0;"
        "    border: 1px solid #aaaaaa;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #f0f0f0;"
        "    border: 1px solid #e0e0e0;"
        "    color: #a0a0a0;"
        "}"
    );
    connect(nextButton, &QPushButton::clicked, this, &FileSelectForm::onNextPageClicked);
    paginationLayout->addWidget(nextButton);

    // 更新分页信息
    updatePageInfo();

    // 添加底部按钮区域
    QWidget* buttonArea = new QWidget(container);
    QHBoxLayout* buttonLayout = new QHBoxLayout(buttonArea);
    buttonLayout->setContentsMargins(5, 5, 5, 5);

    buttonLayout->addStretch();

    QPushButton* cancelButton = new QPushButton("取消");
    cancelButton->setFixedSize(100, 35);
    cancelButton->setStyleSheet(
        "QPushButton {"
        "    font-size: 10pt;"
        "    background-color: #f5f5f5;"
        "    border: 1px solid #dddddd;"
        "    border-radius: 5px;"
        "    padding: 5px;"
        "    color: #333333;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "    border: 1px solid #bbbbbb;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d0d0d0;"
        "    border: 1px solid #aaaaaa;"
        "}"
    );
    connect(cancelButton, &QPushButton::clicked, this, &FileSelectForm::close);

    buttonLayout->addWidget(cancelButton);

    // 添加所有组件到容器布局
    containerLayout->addWidget(titleBar);
    containerLayout->addWidget(line);
    containerLayout->addWidget(fileListContainer, 1); // 1表示可拉伸
    containerLayout->addWidget(paginationArea);
    containerLayout->addWidget(buttonArea);

    // 添加容器到主布局
    mainLayout->addWidget(container);

    // 初始化文件列表
    updateFileList();

    // 计算窗口大小
    int width = 420;
    // 固定高度，每页显示8个文件
    int height = (ITEMS_PER_PAGE * 48) + 200; // 文件列表高度 + 标题栏 + 分页按钮 + 底部按钮 + 边距

    this->setFixedSize(width, height);
    // 居中
    this->move(QApplication::primaryScreen()->geometry().center() - this->rect().center());
}

FileSelectForm::~FileSelectForm()
{
    delete ui;
}

void FileSelectForm::onPushButtonClicked()
{
    QPushButton* button = qobject_cast< QPushButton* >( sender() );
    if (!button) return;

    // 获取文件名属性
    QVariant itemName = button->property( "name" );

    // 发送选中信号
    emit itemSelected( this->addr + itemName.toString() );
    this->close();
}

void FileSelectForm::closeEvent( QCloseEvent* event )
{
    QWidget::closeEvent( event );
    emit finished();
}

// 更新文件列表显示
void FileSelectForm::updateFileList()
{
    // 先删除旧的布局
    if (fileListContainer->layout()) {
        QLayout* oldLayout = fileListContainer->layout();

        // 清除布局中的所有子控件
        QLayoutItem* child;
        while ((child = oldLayout->takeAt(0)) != nullptr) {
            if (child->widget()) {
                delete child->widget();
            }
            delete child;
        }

        // 删除旧布局
        delete oldLayout;
    }

    // 创建新布局
    QVBoxLayout* fileListLayout = new QVBoxLayout(fileListContainer);
    fileListLayout->setContentsMargins(5, 5, 5, 5);
    fileListLayout->setSpacing(8);

    // 计算当前页的起始和结束索引
    int startIndex = currentPage * ITEMS_PER_PAGE;
    int endIndex = qMin(startIndex + ITEMS_PER_PAGE, fileInfoList.size());

    // 添加当前页的文件按钮
    for (int i = startIndex; i < endIndex; i++) {
        const QFileInfo& fileInfo = fileInfoList.at(i);

        // 使用原始的文件名编码转换方式
        QTextCodec* codec = QTextCodec::codecForLocale();
        QString convertedFileName = codec->fromUnicode(fileInfo.fileName());

        // 创建按钮并设置文本
        QPushButton* button = new QPushButton(convertedFileName);
        button->setFixedSize(380, 40);
        button->setStyleSheet(
            "QPushButton {"
            "    font-size: 8pt;"
            "    background-color: #f8f8f8;"
            "    border: 1px solid #dddddd;"
            "    border-radius: 5px;"
            "    padding: 5px 10px;"
            "    color: #333333;"
            "    font-weight: normal;"
            "    text-align: left;"
            "}"
            "QPushButton:hover {"
            "    background-color: #e8f4fc;"
            "    border: 1px solid #3498db;"
            "    color: #2980b9;"
            "}"
            "QPushButton:pressed {"
            "    background-color: #3498db;"
            "    color: white;"
            "    border: 1px solid #2980b9;"
            "}"
        );

        // 设置文本对齐
        button->setLayoutDirection(Qt::LeftToRight);
        // 确保属性中的文件名也是正确的
        button->setProperty("name", fileInfo.fileName());
        connect(button, &QPushButton::clicked, this, &FileSelectForm::onPushButtonClicked);
        fileListLayout->addWidget(button);
    }

    // 如果当前页的文件数量少于 ITEMS_PER_PAGE，添加空白占位
    if (endIndex - startIndex < ITEMS_PER_PAGE) {
        fileListLayout->addStretch();
    }

    // 更新页码信息
    updatePageInfo();
}

// 更新页码信息
void FileSelectForm::updatePageInfo()
{
    // 如果页码标签不存在，直接返回
    if (!pageInfoLabel) {
        return;
    }

    // 更新页码文本
    pageInfoLabel->setText(QString("第 %1 / %2 页").arg(currentPage + 1).arg(qMax(1, totalPages)));

    // 获取上一页和下一页按钮
    QPushButton* prevButton = nullptr;
    QPushButton* nextButton = nullptr;

    QWidget* parentWidget = pageInfoLabel->parentWidget();
    if (!parentWidget || !parentWidget->layout()) {
        return;
    }

    QLayout* layout = parentWidget->layout();
    for (int i = 0; i < layout->count(); i++) {
        QLayoutItem* item = layout->itemAt(i);
        if (item && item->widget()) {
            QPushButton* button = qobject_cast<QPushButton*>(item->widget());
            if (button) {
                if (button->text() == "上一页") {
                    prevButton = button;
                } else if (button->text() == "下一页") {
                    nextButton = button;
                }
            }
        }
    }

    // 设置按钮状态
    if (prevButton) {
        prevButton->setEnabled(currentPage > 0);
    }

    if (nextButton) {
        nextButton->setEnabled(currentPage < totalPages - 1);
    }
}

// 上一页按钮点击
void FileSelectForm::onPrevPageClicked()
{
    if (currentPage > 0) {
        currentPage--;
        updateFileList();
    }
}

// 下一页按钮点击
void FileSelectForm::onNextPageClicked()
{
    if (currentPage < totalPages - 1) {
        currentPage++;
        updateFileList();
    }
}

// 重写show函数，自动刷新文件列表
void FileSelectForm::show()
{
    // 每次显示时刷新文件列表
    refreshFileList();
    // 调用父类的show函数
    QWidget::show();
}

// 刷新文件夹中的文件列表
void FileSelectForm::refreshFileList()
{
    // 遍历文件夹
    QDir        dir( this->addr );
    QStringList filters;
    if ( this->fileFilter == 0 )
        filters << "*.SOK";  // 只匹配SOK文件
    else if ( this->fileFilter == 1 )
        filters << "*.CFT";
    dir.setNameFilters( filters );
    dir.setFilter( QDir::Files | QDir::NoSymLinks );
    dir.setSorting( QDir::Name );  // 按名称排序

    // 获取文件列表并计算总页数
    fileInfoList = dir.entryInfoList();
    totalPages = (fileInfoList.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;

    // 如果当前页超出了总页数，重置到第一页
    if (currentPage >= totalPages) {
        currentPage = 0;
    }

    // 确保至少有一页
    if (totalPages == 0) {
        totalPages = 1;
    }

    // 更新文件列表显示
    updateFileList();
}
