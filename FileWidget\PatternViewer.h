﻿#ifndef PATTERNVIEW_H
#define PATTERNVIEW_H
#include <QColor>
#include <QDebug>
#include <QMap>
#include <QtWidgets>

static QMap< quint32, QColor > ZhusuoColorMap = {
    { 0x00, QColor( 255, 255, 255 ) }, { 0x01, QColor( 0, 255, 0 ) },   { 0x02, QColor( 0, 0, 255 ) }, { 0x03, QColor( 255, 255, 0 ) },
    { 0x04, QColor( 0, 255, 255 ) },   { 0x05, QColor( 255, 0, 255 ) }, { 0x06, QColor( 127, 0, 0 ) }, { 0x07, QColor( 0, 127, 0 ) },

};

static QMap< quint32, QColor > SuoziColorMap = {
    { 0x00, QColor( 255, 255, 255 ) },   { 0x01, QColor( 0, 255, 0 ) },           { 0x02, QColor( 0, 0, 255 ) },           { 0x03, Q<PERSON>olor( 255, 255, 0 ) },   { 0x04, QColor( 0, 255, 255 ) },
    { 0x05, QColor( 255, 0, 255 ) },     { 0x06, QColor( 127, 0, 0 ) },           { 0x07, QColor( 0, 127, 0 ) },           { 0x08, QColor( 0, 0, 127 ) },     { 0x09, QColor( 127, 127, 0 ) },
    { 0x0a, QColor( 127, 0, 127 ) },     { 0x0b, QColor( 0, 127, 127 ) },         { 0x0c, QColor( 200, 0, 200 ) },         { 0x0d, QColor( 175, 0, 175 ) },   { 0x0e, QColor( 175, 175, 255 ) },
    { 0x0f, QColor( 0, 0, 127 ) },       { 0x10, QColor( 255, 170, 127 ) },       { 0x11, QColor( 175, 127, 175 ) },       { 0x12, QColor( 127, 100, 127 ) }, { 0x13, QColor( 203, 135, 203 ) },
    { 0x14, QColor( 255, 102, 255 ) },   { 0x15, QColor( 0, 85, 127 ) },          { 0x16, QColor( 0, 85, 255 ) },          { 0x17, QColor( 0, 170, 127 ) },   { 0x18, QColor( 145, 97, 145 ) },
    { 0x19, QColor( 170, 0, 0 ) },       { 0x1a, QColor( 127, 185, 0 ) },

    { 0x01000000, QColor( 255, 0, 0 ) }, { 0x00100000, QColor( 255, 255, 255 ) }, { 0x8000001c, QColor( 127, 127, 127 ) },
};

struct PointsAndPen
{
    QVector< QPoint > points;
    QColor            penColor;
};

class PatternViewer : public QWidget
{
    Q_OBJECT
private:
    QPixmap                 m_pixmap;
    quint32                 lastX = 0, lastY = 0;
    qreal                   m_scale    = 1.0;
    QColor                  m_penColor = Qt::black;
    QVector< QPoint >       m_points;
    QVector< PointsAndPen > m_userImage;
    void                    paintEvent( QPaintEvent* ) override
    {
        QPainter p{ this };
        // 画笔放大
        //        p.scale( m_scale, m_scale );
        //        p.drawPixmap( 0, 0, m_pixmap );
        // 图像放大
        p.drawPixmap( 0, 0, m_pixmap.scaled( m_pixmap.width() * m_scale, m_pixmap.height() * m_scale ) );

        for ( int index = 0; index < m_userImage.length(); index++ )
        {
            PointsAndPen pt = m_userImage.at( index );
            p.setPen( QPen( pt.penColor, m_scale, Qt::SolidLine ) );
            //            qDebug() << "index: " << index << pt.penColor;
            for ( int i = 0; i < pt.points.length() - 1; i++ )
            {
                // 划线的时候要取半个格子
                //                qDebug() << "opos" << pt.points[ i ].x() << pt.points[ i ].y() << pt.points[ i + 1 ].x() << pt.points[ i + 1 ].y();
                int x1 = int( pt.points[ i ].x() ) * m_scale + m_scale / 2;
                int y1 = int( pt.points[ i ].y() ) * m_scale + m_scale / 2;
                int x2 = int( pt.points[ i + 1 ].x() ) * m_scale + m_scale / 2;
                int y2 = int( pt.points[ i + 1 ].y() ) * m_scale + m_scale / 2;
                //                qDebug() << "pos" << x1 << y1 << x2 << y2;
                p.drawLine( x1, y1, x2, y2 );
            }
        }
    }

public:
    PatternViewer( QWidget* parent = nullptr ) : QWidget( parent )
    {
        setMouseTracking( true );  // 启用鼠标移动事件跟踪
    }
    ~PatternViewer() {}

    void setPixmap( const QPixmap& pix )
    {
        m_pixmap = pix;
        //        m_rect   = m_pixmap.rect();
        update();
    }
    void scale( qreal s )
    {
        m_scale = s;
        update();
    }
    void setPenColor( QColor color )
    {
        m_penColor = color;
    }
    void clearUserImage()
    {
        m_userImage.clear();
    }
    void saveImage( const QString addr )
    {
        QPixmap pixmap( this->size() );
        this->render( &pixmap );
        // 保存图像
        if ( pixmap.save( addr ) )
        {
            qDebug() << "imageSaved to" << addr;
        }
        else
        {
            qDebug() << "imageSaveFailed";
        }
    }
    QSize sizeHint() const override
    {
        return { 200, 1000 };
    }
    // 水平画线,用于显示当前编织位置
    void setHLine( quint32 vPos )
    {
        m_userImage.clear();
        m_points.clear();
        // Points
        QPoint pt1;
        pt1.setX( 0 );
        pt1.setY( vPos );
        m_points.append( pt1 );
        QPoint pt2;
        pt2.setX( m_pixmap.width() );
        pt2.setY( vPos );
        m_points.append( pt2 );
        // PointsAndPen
        PointsAndPen pts;
        pts.penColor = QColor( 0, 0, 0 );
        pts.points   = m_points;
        m_userImage.append( pts );
        update();
    }
signals:
    void mouseMove( quint32 x, quint32 y );

protected:
    void mouseMoveEvent( QMouseEvent* event ) override
    {
        quint32 x = event->x() / m_scale;
        quint32 y = event->y() / m_scale;
        if ( x != lastX || y != lastY )
        {
            emit mouseMove( x, y );
            lastX = x;
            lastY = y;
        }
    }
    void mousePressEvent( QMouseEvent* event ) override
    {
        m_points.clear();
        // 保存1x倍率下的坐标
        QPoint pt;
        pt.setX( event->x() / m_scale );
        pt.setY( event->y() / m_scale );
        m_points.append( pt );
    }
    void mouseReleaseEvent( QMouseEvent* event ) override
    {
        // 保存1x倍率下的坐标
        QPoint pt;
        pt.setX( event->x() / m_scale );
        pt.setY( event->y() / m_scale );
        m_points.append( pt );

        PointsAndPen pts;
        pts.penColor = m_penColor;
        pts.points   = m_points;
        m_userImage.append( pts );
        update();
    }
};
#endif  // PATTERNVIEW_H
