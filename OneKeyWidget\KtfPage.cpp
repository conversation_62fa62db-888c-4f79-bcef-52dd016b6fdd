#include "OneKeyForm.h"
#include "ui_OneKeyForm.h"

void OneKeyForm::initKtfPage()
{
    // Button Groups
    ktfMenuBtnGroup     = new QButtonGroup( this );
    ktf1InstallBtnGroup = new QButtonGroup( this );
    ktf2InstallBtnGroup = new QButtonGroup( this );
    ktf3InstallBtnGroup = new QButtonGroup( this );
    ktf4InstallBtnGroup = new QButtonGroup( this );
    ktf1DirBtnGroup     = new QButtonGroup( this );
    ktf2DirBtnGroup     = new QButtonGroup( this );
    ktf3DirBtnGroup     = new QButtonGroup( this );
    ktf4DirBtnGroup     = new QButtonGroup( this );

    // Add to Groups
    ktfMenuBtnGroup->addButton( ui->Ktf_sel_all_btn, 1 );
    ktfMenuBtnGroup->addButton( ui->Ktf_desel_all_btn, 2 );
    ktfMenuBtnGroup->addButton( ui->Ktf_sel_elan_btn, 3 );
    ktfMenuBtnGroup->addButton( ui->Ktf_sel_plus_btn, 4 );
    ktfMenuBtnGroup->addButton( ui->Ktf_sel_clockwise_btn, 5 );
    ktfMenuBtnGroup->addButton( ui->Ktf_sel_anticlockwise_btn, 6 );
    ktfMenuBtnGroup->addButton( ui->pbtn_ktf_Save, 7 );
    connect( ktfMenuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onKtfMenuBtnGroupClicked( int ) ) );

    // Sel Radios
    ktf1InstallBtnGroup->addButton( ui->rb_ktf_1_1 );
    ktf1InstallBtnGroup->addButton( ui->rb_ktf_1_2 );
    ktf1InstallBtnGroup->setExclusive( true );

    ktf2InstallBtnGroup->addButton( ui->rb_ktf_2_1 );
    ktf2InstallBtnGroup->addButton( ui->rb_ktf_2_2 );
    ktf2InstallBtnGroup->setExclusive( true );

    ktf3InstallBtnGroup->addButton( ui->rb_ktf_3_1 );
    ktf3InstallBtnGroup->addButton( ui->rb_ktf_3_2 );
    ktf3InstallBtnGroup->setExclusive( true );

    ktf4InstallBtnGroup->addButton( ui->rb_ktf_4_1 );
    ktf4InstallBtnGroup->addButton( ui->rb_ktf_4_2 );
    ktf4InstallBtnGroup->setExclusive( true );

    ktf1DirBtnGroup->addButton( ui->rb_ktf_1_3 );
    ktf1DirBtnGroup->addButton( ui->rb_ktf_1_4 );
    ktf1DirBtnGroup->setExclusive( true );

    ktf2DirBtnGroup->addButton( ui->rb_ktf_2_3 );
    ktf2DirBtnGroup->addButton( ui->rb_ktf_2_4 );
    ktf2DirBtnGroup->setExclusive( true );

    ktf3DirBtnGroup->addButton( ui->rb_ktf_3_3 );
    ktf3DirBtnGroup->addButton( ui->rb_ktf_3_4 );
    ktf3DirBtnGroup->setExclusive( true );

    ktf4DirBtnGroup->addButton( ui->rb_ktf_4_3 );
    ktf4DirBtnGroup->addButton( ui->rb_ktf_4_4 );
    ktf4DirBtnGroup->setExclusive( true );

    // Read Saved Config
    ReadOnekeyConfig::KtfCfgItem item1 = mainData->readOneKeyConfig->getKtfList()->value( 1 );
    if ( item1.enable )
        ui->chx_ktf_enable_1->setChecked( true );
    else
        ui->chx_ktf_enable_1->setChecked( false );
    if ( item1.install == 0 )
        ui->rb_ktf_1_1->setChecked( true );
    else
        ui->rb_ktf_1_2->setChecked( true );
    if ( item1.dir == 0 )
        ui->rb_ktf_1_3->setChecked( true );
    else
        ui->rb_ktf_1_4->setChecked( true );

    ReadOnekeyConfig::KtfCfgItem item2 = mainData->readOneKeyConfig->getKtfList()->value( 2 );
    if ( item2.enable )
        ui->chx_ktf_enable_2->setChecked( true );
    else
        ui->chx_ktf_enable_2->setChecked( false );
    if ( item2.install == 0 )
        ui->rb_ktf_2_1->setChecked( true );
    else
        ui->rb_ktf_2_2->setChecked( true );
    if ( item2.dir == 0 )
        ui->rb_ktf_2_3->setChecked( true );
    else
        ui->rb_ktf_2_4->setChecked( true );

    ReadOnekeyConfig::KtfCfgItem item3 = mainData->readOneKeyConfig->getKtfList()->value( 3 );
    if ( item3.enable )
        ui->chx_ktf_enable_3->setChecked( true );
    else
        ui->chx_ktf_enable_3->setChecked( false );
    if ( item3.install == 0 )
        ui->rb_ktf_3_1->setChecked( true );
    else
        ui->rb_ktf_3_2->setChecked( true );
    if ( item3.dir == 0 )
        ui->rb_ktf_3_3->setChecked( true );
    else
        ui->rb_ktf_3_4->setChecked( true );

    ReadOnekeyConfig::KtfCfgItem item4 = mainData->readOneKeyConfig->getKtfList()->value( 4 );
    if ( item4.enable )
        ui->chx_ktf_enable_4->setChecked( true );
    else
        ui->chx_ktf_enable_4->setChecked( false );
    if ( item4.install == 0 )
        ui->rb_ktf_4_1->setChecked( true );
    else
        ui->rb_ktf_4_2->setChecked( true );
    if ( item4.dir == 0 )
        ui->rb_ktf_4_3->setChecked( true );
    else
        ui->rb_ktf_4_4->setChecked( true );
}

void OneKeyForm::onKtfMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            ui->chx_ktf_enable_1->setChecked( true );
            ui->chx_ktf_enable_2->setChecked( true );
            ui->chx_ktf_enable_3->setChecked( true );
            ui->chx_ktf_enable_4->setChecked( true );
            break;
        case 2:
            ui->chx_ktf_enable_1->setChecked( false );
            ui->chx_ktf_enable_2->setChecked( false );
            ui->chx_ktf_enable_3->setChecked( false );
            ui->chx_ktf_enable_4->setChecked( false );
            break;
        case 3:
            ui->rb_ktf_1_1->setChecked( true );
            ui->rb_ktf_2_1->setChecked( true );
            ui->rb_ktf_3_1->setChecked( true );
            ui->rb_ktf_4_1->setChecked( true );
            break;
        case 4:
            ui->rb_ktf_1_2->setChecked( true );
            ui->rb_ktf_2_2->setChecked( true );
            ui->rb_ktf_3_2->setChecked( true );
            ui->rb_ktf_4_2->setChecked( true );
            break;
        case 5:
            ui->rb_ktf_1_3->setChecked( true );
            ui->rb_ktf_2_3->setChecked( true );
            ui->rb_ktf_3_3->setChecked( true );
            ui->rb_ktf_4_3->setChecked( true );
            break;
        case 6:
            ui->rb_ktf_1_4->setChecked( true );
            ui->rb_ktf_2_4->setChecked( true );
            ui->rb_ktf_3_4->setChecked( true );
            ui->rb_ktf_4_4->setChecked( true );
            break;
        case 7:
            saveKtfConfig();
            sendKtfConfigToLower();
            break;
    }
}

void OneKeyForm::saveKtfConfig()
{
    ReadOnekeyConfig::KtfCfgItem& item1 = mainData->readOneKeyConfig->getKtfList()->operator[]( 1 );
    if ( ui->chx_ktf_enable_1->isChecked() == true )
        item1.enable = 1;
    else
        item1.enable = 0;

    if ( ui->rb_ktf_1_1->isChecked() == true )
        item1.install = 0;
    else
        item1.install = 1;
    if ( ui->rb_ktf_1_3->isChecked() == true )
        item1.dir = 0;
    else
        item1.dir = 1;

    ReadOnekeyConfig::KtfCfgItem& item2 = mainData->readOneKeyConfig->getKtfList()->operator[]( 2 );
    if ( ui->chx_ktf_enable_2->isChecked() == true )
        item2.enable = 1;
    else
        item2.enable = 0;

    if ( ui->rb_ktf_2_1->isChecked() == true )
        item2.install = 0;
    else
        item2.install = 1;
    if ( ui->rb_ktf_2_3->isChecked() == true )
        item2.dir = 0;
    else
        item2.dir = 1;

    ReadOnekeyConfig::KtfCfgItem& item3 = mainData->readOneKeyConfig->getKtfList()->operator[]( 3 );
    if ( ui->chx_ktf_enable_3->isChecked() == true )
        item3.enable = 1;
    else
        item3.enable = 0;

    if ( ui->rb_ktf_3_1->isChecked() == true )
        item3.install = 0;
    else
        item3.install = 1;
    if ( ui->rb_ktf_3_3->isChecked() == true )
        item3.dir = 0;
    else
        item3.dir = 1;

    ReadOnekeyConfig::KtfCfgItem& item4 = mainData->readOneKeyConfig->getKtfList()->operator[]( 4 );
    if ( ui->chx_ktf_enable_4->isChecked() == true )
        item4.enable = 1;
    else
        item4.enable = 0;

    if ( ui->rb_ktf_4_1->isChecked() == true )
        item4.install = 0;
    else
        item4.install = 1;
    if ( ui->rb_ktf_4_3->isChecked() == true )
        item4.dir = 0;
    else
        item4.dir = 1;

    mainData->readOneKeyConfig->saveConfig( CFG_DIR + "OneKeyConfig.json" );
}

void OneKeyForm::sendKtfConfigToLower()
{
    quint8 data[ 4 ] = { 0 };
    data[ 0 ]        = 0x00;

    ReadOnekeyConfig::KtfCfgItem item1 = mainData->readOneKeyConfig->getKtfList()->value( 1 );
    if ( item1.enable )
        data[ 1 ] |= ( 1 << 0 );
    else
        data[ 1 ] &= ~( 1 << 0 );
    if ( item1.install == 0 )
        data[ 2 ] &= ~( 1 << 0 );
    else
        data[ 2 ] |= ( 1 << 0 );
    if ( item1.dir == 0 )
        data[ 3 ] &= ~( 1 << 0 );
    else
        data[ 3 ] |= ( 1 << 0 );

    ReadOnekeyConfig::KtfCfgItem item2 = mainData->readOneKeyConfig->getKtfList()->value( 2 );
    if ( item2.enable )
        data[ 1 ] |= ( 1 << 1 );
    else
        data[ 1 ] &= ~( 1 << 1 );
    if ( item2.install == 0 )
        data[ 2 ] &= ~( 1 << 1 );
    else
        data[ 2 ] |= ( 1 << 1 );
    if ( item2.dir == 0 )
        data[ 3 ] &= ~( 1 << 1 );
    else
        data[ 3 ] |= ( 1 << 1 );

    ReadOnekeyConfig::KtfCfgItem item3 = mainData->readOneKeyConfig->getKtfList()->value( 3 );
    if ( item3.enable )
        data[ 1 ] |= ( 1 << 2 );
    else
        data[ 1 ] &= ~( 1 << 2 );
    if ( item3.install == 0 )
        data[ 2 ] &= ~( 1 << 2 );
    else
        data[ 2 ] |= ( 1 << 2 );
    if ( item3.dir == 0 )
        data[ 3 ] &= ~( 1 << 2 );
    else
        data[ 3 ] |= ( 1 << 2 );

    ReadOnekeyConfig::KtfCfgItem item4 = mainData->readOneKeyConfig->getKtfList()->value( 4 );
    if ( item4.enable )
        data[ 1 ] |= ( 1 << 3 );
    else
        data[ 1 ] &= ~( 1 << 3 );
    if ( item4.install == 0 )
        data[ 2 ] &= ~( 1 << 3 );
    else
        data[ 2 ] |= ( 1 << 3 );
    if ( item4.dir == 0 )
        data[ 3 ] &= ~( 1 << 3 );
    else
        data[ 3 ] |= ( 1 << 3 );

    comm->pushDataTobuffer( 0x10, data, 4 );
}
