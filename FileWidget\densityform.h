﻿#ifndef DENSITYFORM_H
#define DENSITYFORM_H

#include "CommonWidget/mylineedit.h"
#include "FATParser.h"
#include <QLabel>
#include <QWidget>

namespace Ui
{
class DensityForm;
}

class DensityForm : public QWidget
{
    Q_OBJECT

public:
    explicit DensityForm( QWidget* parent = nullptr, QSharedPointer< FATParser::DensityData > density = nullptr );
    ~DensityForm();
    void showDensity();
    void setDensityData( QSharedPointer< FATParser::DensityData > density );

signals:
    void finished();

protected:
    void closeEvent( QCloseEvent* event ) override;

private:
    QSharedPointer< FATParser::DensityData > _density = nullptr;
    quint16                                  TempDensity[ 6 ][ 24 ];  // 保存临时密度数据
    int                                      currentPage            = 0;
    MyLineEdit*                              currentLineEdit        = nullptr;
    bool                                     firsTimeToEditLineEdit = false;
    Ui::DensityForm*                         ui;
private slots:
    void onLineEditClicked();
    void onLineEditFinished();
    void onNumerBtnClicked();
    void saveCurrentLineEdit();
};

#endif  // DENSITYFORM_H
