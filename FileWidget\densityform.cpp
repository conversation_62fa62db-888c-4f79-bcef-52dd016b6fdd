﻿#include "densityform.h"
#include "ui_densityform.h"
#include <QDebug>
#include <QMessageBox>
#include <QScreen>

DensityForm::DensityForm( QWidget* parent, QSharedPointer< FATParser::DensityData > density ) : QWidget( parent ), ui( new Ui::DensityForm )
{
    ui->setupUi( this );
    this->_density = density;
    memcpy( this->TempDensity, &this->_density->Density, sizeof( quint16 ) * 6 * 24 );
    //    qDebug() << this->TempDensity[ 3 ][ 0 ] << this->TempDensity[ 3 ][ 1 ] << this->TempDensity[ 3 ][ 2 ] << this->TempDensity[ 3 ][ 3 ];
    //    memcpy( this->TempDensity, ( char* )this->_density.data()->Density, sizeof( quint16 ) * 6 * 24 );
    this->showDensity();

    // 按钮事件
    connect( this->ui->pbtn_next, &QPushButton::clicked, this, [&]() {
        this->saveCurrentLineEdit();
        if ( this->currentPage < 2 )
        {
            this->currentPage++;
            showDensity();
        }
        else
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到最后1页" ) );
    } );
    connect( this->ui->pbtn_prev, &QPushButton::clicked, this, [&]() {
        this->saveCurrentLineEdit();
        if ( this->currentPage > 0 )
        {
            this->currentPage--;
            showDensity();
        }
        else
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到第1页" ) );
    } );
    connect( this->ui->pbtn_quit, &QPushButton::clicked, this, [&]() {
        // 退出不保存，则将原来的数据恢复，因为再次打开窗口时，不再重复复制TempDensity
        memcpy( this->TempDensity, ( char* )this->_density.data()->Density, sizeof( quint16 ) * 6 * 24 );
        if ( this->currentLineEdit != nullptr )
        {
            this->currentLineEdit->setStyleSheet( "" );
            this->currentLineEdit        = nullptr;
            this->firsTimeToEditLineEdit = false;
            this->currentPage            = 0;
        }
        this->close();
    } );
    // 密度数据保存
    connect( this->ui->pbtn_ok, &QPushButton::clicked, this, [&]() {
        if ( this->currentLineEdit != nullptr )
        {
            this->currentLineEdit->setStyleSheet( "" );
            this->saveCurrentLineEdit();
            this->firsTimeToEditLineEdit = false;
            this->currentPage            = 0;
        }
        memcpy( ( char* )this->_density.data()->Density, this->TempDensity, sizeof( quint16 ) * 6 * 24 );
        this->close();
    } );
    // 数字按钮事件
    connect( this->ui->pbtn_0, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_1, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_2, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_3, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_4, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_5, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_6, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_7, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_8, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_9, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );
    connect( this->ui->pbtn_back, &QPushButton::clicked, this, &DensityForm::onNumerBtnClicked );

    // 居中
    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );
}

DensityForm::~DensityForm()
{
    delete ui;
}

void DensityForm::closeEvent( QCloseEvent* event )
{
    QWidget::closeEvent( event );
    emit finished();
}

void DensityForm::setDensityData( QSharedPointer< FATParser::DensityData > density )
{
    this->_density = density;
    memcpy( this->TempDensity, &this->_density->Density, sizeof( quint16 ) * 6 * 24 );
}

// 把上一个未保存的数据保存好
void DensityForm::saveCurrentLineEdit()
{
    if ( this->currentLineEdit != nullptr )
    {
        int rowId = this->currentLineEdit->property( "rowId" ).toInt();
        int colId = this->currentLineEdit->property( "colId" ).toInt();
        qDebug() << rowId << colId;
        this->TempDensity[ rowId ][ colId ] = this->currentLineEdit->text().toUShort();
        // clear pointer
        this->currentLineEdit = nullptr;
    }
}

void DensityForm::showDensity()
{
    // 冗余，检查page是否合规  正常为6行24列的数据 ，每次显示8列，即页码应该在0~2
    if ( this->currentPage > 2 || this->currentPage < 0 )
    {
        QMessageBox::information( nullptr, ( "错误提示" ), ( "页码不正确" ) );
        return;
    }

    // 清空控件表
    QLayout* layout = this->ui->main_View->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    // 插入控件

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->main_View );

    QHBoxLayout* rowLayout = new QHBoxLayout;
    QLabel*      label1    = new QLabel( "  " );
    QLabel*      label2    = new QLabel( "针筒密度" );
    QLabel*      label3    = new QLabel( "沉降密度" );
    QLabel*      label4    = new QLabel( "生克密度" );
    QLabel*      label5    = new QLabel( "哈弗盘" );
    QLabel*      label6    = new QLabel( "左棱角" );
    QLabel*      label7    = new QLabel( "右棱角" );
    label1->setAlignment( Qt::AlignHCenter );
    label2->setAlignment( Qt::AlignHCenter );
    label3->setAlignment( Qt::AlignHCenter );
    label4->setAlignment( Qt::AlignHCenter );
    label5->setAlignment( Qt::AlignHCenter );
    label6->setAlignment( Qt::AlignHCenter );
    label7->setAlignment( Qt::AlignHCenter );
    rowLayout->addWidget( label1 );
    rowLayout->addWidget( label2 );
    rowLayout->addWidget( label3 );
    rowLayout->addWidget( label4 );
    rowLayout->addWidget( label5 );
    rowLayout->addWidget( label6 );
    rowLayout->addWidget( label7 );
    rowLayout->setStretch( 0, 1 );
    rowLayout->setStretch( 1, 2 );
    rowLayout->setStretch( 2, 2 );
    rowLayout->setStretch( 3, 2 );
    rowLayout->setStretch( 4, 2 );
    rowLayout->setStretch( 5, 2 );
    rowLayout->setStretch( 6, 2 );
    mainLayout->addLayout( rowLayout );
    mainLayout->setStretch( 0, 1 );

    // 只显示8行
    for ( quint8 index = 0; index < 8; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString::number( this->currentPage * 8 + index + 1 ) );
        label->setAlignment( Qt::AlignCenter );
        label->setMinimumHeight( 40 );
        rowLayoutx->addWidget( label );
        rowLayoutx->setStretch( 0, 1 );

        for ( int col_index = 0; col_index < 6; col_index++ )
        {
            MyLineEdit* lineEdit = new MyLineEdit;
            lineEdit->setAlignment( Qt::AlignHCenter );
            //            lineEdit->setText( QString::number( this->_density->Density[ col_index ][ this->currentPage * 8 + index ] ) );
            lineEdit->setText( QString::number( this->TempDensity[ col_index ][ this->currentPage * 8 + index ] ) );
            lineEdit->setMinimumHeight( 40 );
            rowLayoutx->addWidget( lineEdit );
            rowLayoutx->setStretch( col_index + 1, 2 );
            // 注意实际数据存储与显示的行和列是反的
            lineEdit->setProperty( "rowId", col_index );
            lineEdit->setProperty( "colId", this->currentPage * 8 + index );
            // 点击跳出修改窗口
            connect( lineEdit, &MyLineEdit::mouseRelease, this, &DensityForm::onLineEditClicked );
            //            connect( lineEdit, &MyLineEdit::editingFinished, this, &DensityForm::onLineEditFinished );
        }

        mainLayout->addLayout( rowLayoutx );
        mainLayout->setStretch( index + 1, 1 );
    }
}

void DensityForm::onLineEditClicked()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        // 把上一个的外框效果删除，再赋值新一个
        if ( this->currentLineEdit != nullptr )
        {
            this->currentLineEdit->setStyleSheet( "" );
            // 保存数据
            this->saveCurrentLineEdit();
        }
        this->currentLineEdit = senderLineEdit;
        // 第一次点击后全选，可以全部删除
        senderLineEdit->selectAll();
        this->firsTimeToEditLineEdit = true;
        senderLineEdit->setStyleSheet( "border: 1px solid #FC5531;" );
    }
}

void DensityForm::onLineEditFinished()
{
    MyLineEdit* senderLineEdit = qobject_cast< MyLineEdit* >( sender() );
    if ( senderLineEdit != nullptr )
    {
        senderLineEdit->setStyleSheet( "" );
    }
}

void DensityForm::onNumerBtnClicked()
{
    QPushButton* button = qobject_cast< QPushButton* >( sender() );

    if ( this->firsTimeToEditLineEdit )
    {
        // 全选时，则删除全部内容
        this->currentLineEdit->setText( "" );
        this->firsTimeToEditLineEdit = false;
    }

    if ( button == this->ui->pbtn_0 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '0' );
    }
    else if ( button == this->ui->pbtn_1 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '1' );
    }
    else if ( button == this->ui->pbtn_2 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '2' );
    }
    else if ( button == this->ui->pbtn_3 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '3' );
    }
    else if ( button == this->ui->pbtn_4 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '4' );
    }
    else if ( button == this->ui->pbtn_5 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '5' );
    }
    else if ( button == this->ui->pbtn_6 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '6' );
    }
    else if ( button == this->ui->pbtn_7 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '7' );
    }
    else if ( button == this->ui->pbtn_8 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '8' );
    }
    else if ( button == this->ui->pbtn_9 )
    {
        this->currentLineEdit->setText( this->currentLineEdit->text() + '9' );
    }
    else if ( button == ui->pbtn_back )
    {
        QString text = this->currentLineEdit->text();
        text.chop( 1 );
        this->currentLineEdit->setText( text );
    }
}
