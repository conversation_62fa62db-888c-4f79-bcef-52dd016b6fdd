﻿#ifndef INPUTDIALOG_H
#define INPUTDIALOG_H

#include <QDialog>

namespace Ui
{
class InputDialog;
}

class InputDialog : public QDialog
{
    Q_OBJECT

public:
    explicit InputDialog( QWidget* parent = nullptr );
    explicit InputDialog( QWidget* parent = nullptr, QString title = "Title", QString hint = "Input:" );
    ~InputDialog();
    QString getInputText();
    void    changeHint( QString hint );

signals:
    void InputFinished( QString text );

private:
    Ui::InputDialog* ui;
    bool             isUpperCase = false;
    void             onBtnClicked();
};

#endif  // INPUTDIALOG_H
