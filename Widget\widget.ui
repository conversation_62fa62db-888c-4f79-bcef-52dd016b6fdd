<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Widget</string>
  </property>
  <widget class="QFrame" name="frame">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1024</width>
     <height>600</height>
    </rect>
   </property>
   <property name="frameShape">
    <enum>QFrame::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Raised</enum>
   </property>
   <widget class="QStackedWidget" name="stackedWidget">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>1024</width>
      <height>600</height>
     </rect>
    </property>
    <property name="currentIndex">
     <number>1</number>
    </property>
    <widget class="QWidget" name="page">
     <widget class="QLabel" name="label_9">
      <property name="geometry">
       <rect>
        <x>160</x>
        <y>110</y>
        <width>661</width>
        <height>91</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>15</pointsize>
       </font>
      </property>
      <property name="text">
       <string>铭维为棉袜编织系统</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="page_2">
     <widget class="QPushButton" name="MainWin_run_btn">
      <property name="geometry">
       <rect>
        <x>131</x>
        <y>121</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
	background-color: rgb(193, 193, 193);
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(://run.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { 
	background-color: #c1c1c1;
}
</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLabel" name="label">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>240</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>运转信息</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_2">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>240</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>机器手动</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_test_btn">
      <property name="geometry">
       <rect>
        <x>351</x>
        <y>121</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/AfterclassTest-Outlined.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_gaiji_btn">
      <property name="geometry">
       <rect>
        <x>581</x>
        <y>361</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/userparam.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLabel" name="label_6">
      <property name="geometry">
       <rect>
        <x>560</x>
        <y>480</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>用户参数</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_5">
      <property name="geometry">
       <rect>
        <x>770</x>
        <y>230</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>传感器检测</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_fengtou_btn">
      <property name="geometry">
       <rect>
        <x>801</x>
        <y>111</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/sensor.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLabel" name="label_4">
      <property name="geometry">
       <rect>
        <x>550</x>
        <y>230</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>文件操作</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_file_btn">
      <property name="geometry">
       <rect>
        <x>581</x>
        <y>111</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/file.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_help_btn">
      <property name="geometry">
       <rect>
        <x>801</x>
        <y>361</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/help.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLabel" name="label_7">
      <property name="geometry">
       <rect>
        <x>770</x>
        <y>480</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>日志及帮助</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_set_btn">
      <property name="geometry">
       <rect>
        <x>351</x>
        <y>361</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/Setting (3).png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLabel" name="label_3">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>480</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>系统参数</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_8">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>480</y>
        <width>150</width>
        <height>40</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
        <stylestrategy>PreferDefault</stylestrategy>
       </font>
      </property>
      <property name="text">
       <string>工艺参数</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="MainWin_craft_btn">
      <property name="geometry">
       <rect>
        <x>131</x>
        <y>361</y>
        <width>100</width>
        <height>100</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>100</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </widget>
   </widget>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
