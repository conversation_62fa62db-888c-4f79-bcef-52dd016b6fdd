﻿#include "inputdialog.h"
#include "ui_inputdialog.h"
#include <QScreen>

InputDialog::InputDialog( QWidget* parent ) : QDialog( parent ), ui( new Ui::InputDialog )
{
    ui->setupUi( this );
}

InputDialog::InputDialog( QWidget* parent, QString title, QString hint ) : QDialog( parent ), ui( new Ui::InputDialog )
{
    ui->setupUi( this );

    this->setWindowTitle( title );
    ui->label_Type->setText( hint );

    connect( ui->btn_A, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_B, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_C, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_D, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_E, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_F, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_G, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_H, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_I, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_J, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_K, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_L, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_M, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_N, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_O, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_P, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_Q, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_R, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_S, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_T, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_U, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_V, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_W, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_X, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_Y, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_Z, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_0, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_1, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_2, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_3, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_4, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_5, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_6, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_7, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_8, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_9, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_line, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_Shift, &QPushButton::clicked, this, &InputDialog::onBtnClicked );
    connect( ui->btn_backspace, &QPushButton::clicked, this, &InputDialog::onBtnClicked );

    // 取消和确定按钮
    connect( ui->pushBtn_OK, &QPushButton::clicked, this, &InputDialog::accept );
    connect( ui->pushBtn_Cancel, &QPushButton::clicked, this, &InputDialog::reject );

    // 居中
    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );
}

InputDialog::~InputDialog()
{
    delete ui;
}

void InputDialog::changeHint( QString hint )
{
    ui->label_Type->setText( hint );
    ui->lineEdit->setText( "" );
}

void InputDialog::onBtnClicked()
{
    QObject* senderObj = sender();
    if ( senderObj == ui->btn_0 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '0' );
    else if ( senderObj == ui->btn_1 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '1' );
    else if ( senderObj == ui->btn_2 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '2' );
    else if ( senderObj == ui->btn_3 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '3' );
    else if ( senderObj == ui->btn_4 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '4' );
    else if ( senderObj == ui->btn_5 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '5' );
    else if ( senderObj == ui->btn_6 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '6' );
    else if ( senderObj == ui->btn_7 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '7' );
    else if ( senderObj == ui->btn_8 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '8' );
    else if ( senderObj == ui->btn_9 )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '9' );
    else if ( senderObj == ui->btn_A )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'A' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'a' );
    }
    else if ( senderObj == ui->btn_B )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'B' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'b' );
    }
    else if ( senderObj == ui->btn_C )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'C' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'c' );
    }
    else if ( senderObj == ui->btn_D )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'D' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'd' );
    }
    else if ( senderObj == ui->btn_E )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'E' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'e' );
    }
    else if ( senderObj == ui->btn_F )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'F' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'f' );
    }
    else if ( senderObj == ui->btn_G )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'G' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'g' );
    }
    else if ( senderObj == ui->btn_H )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'H' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'h' );
    }
    else if ( senderObj == ui->btn_I )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'I' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'i' );
    }
    else if ( senderObj == ui->btn_J )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'J' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'j' );
    }
    else if ( senderObj == ui->btn_K )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'K' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'k' );
    }
    else if ( senderObj == ui->btn_L )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'L' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'l' );
    }
    else if ( senderObj == ui->btn_M )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'M' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'm' );
    }
    else if ( senderObj == ui->btn_N )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'N' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'n' );
    }
    else if ( senderObj == ui->btn_O )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'O' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'o' );
    }
    else if ( senderObj == ui->btn_P )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'P' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'p' );
    }
    else if ( senderObj == ui->btn_Q )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'Q' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'q' );
    }
    else if ( senderObj == ui->btn_R )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'R' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'r' );
    }
    else if ( senderObj == ui->btn_S )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'S' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 's' );
    }
    else if ( senderObj == ui->btn_T )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'T' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 't' );
    }
    else if ( senderObj == ui->btn_U )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'U' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'u' );
    }
    else if ( senderObj == ui->btn_V )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'V' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'v' );
    }
    else if ( senderObj == ui->btn_W )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'W' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'w' );
    }
    else if ( senderObj == ui->btn_X )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'X' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'x' );
    }
    else if ( senderObj == ui->btn_Y )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'Y' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'y' );
    }
    else if ( senderObj == ui->btn_Z )
    {
        if ( this->isUpperCase )
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'Z' );
        else
            this->ui->lineEdit->setText( this->ui->lineEdit->text() + 'z' );
    }
    else if ( senderObj == ui->btn_line )
        this->ui->lineEdit->setText( this->ui->lineEdit->text() + '-' );

    else if ( senderObj == ui->btn_Shift )
    {
        this->isUpperCase = !this->isUpperCase;
        if ( this->isUpperCase )
        {
            this->ui->btn_Shift->setStyleSheet( "color:#e64340" );
        }
        else
        {
            this->ui->btn_Shift->setStyleSheet( "color:#fff" );
        }
    }
    else if ( senderObj == ui->btn_backspace )
    {
        QString text = this->ui->lineEdit->text();
        text.chop( 1 );
        this->ui->lineEdit->setText( text );
    }
}

QString InputDialog::getInputText()
{
    return ui->lineEdit->text();
}
