<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CraftParamForm</class>
 <widget class="QWidget" name="CraftParamForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QPushButton" name="CraftFormHome_btn">
   <property name="geometry">
    <rect>
     <x>971</x>
     <y>3</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="Craft_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>5</y>
     <width>191</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>工艺参数</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>50</y>
     <width>1021</width>
     <height>551</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="page_3">
    <widget class="QWidget" name="layoutWidget">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>20</y>
       <width>971</width>
       <height>149</height>
      </rect>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QPushButton" name="Craft_1_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_8">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>速度</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QPushButton" name="Craft_2_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_9">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>密度</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_11">
        <item>
         <widget class="QPushButton" name="Craft_3_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_18">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>橡筋 1</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_12">
        <item>
         <widget class="QPushButton" name="Craft_4_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_19">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>橡筋 2</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_13">
        <item>
         <widget class="QPushButton" name="Craft_5_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_20">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>圆盘剪刀</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <widget class="QPushButton" name="Craft_6_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_10">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>生克</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="layoutWidget">
     <property name="geometry">
      <rect>
       <x>30</x>
       <y>190</y>
       <width>971</width>
       <height>157</height>
      </rect>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <widget class="QPushButton" name="Craft_7_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_11">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>生克罩
摆动</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_6">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <item>
         <widget class="QPushButton" name="Craft_8_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_12">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>节约</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_7">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_15">
        <item>
         <widget class="QPushButton" name="Craft_9_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_22">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>吸风</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_8">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_14">
        <item>
         <widget class="QPushButton" name="Craft_10_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_21">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>机头高度</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_9">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_6">
        <item>
         <widget class="QPushButton" name="Craft_11_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_13">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>头跟结束
回转距离</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_10">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_7">
        <item>
         <widget class="QPushButton" name="Craft_12_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_14">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>橡筋剪刀
角度</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="layoutWidget">
     <property name="geometry">
      <rect>
       <x>31</x>
       <y>370</y>
       <width>971</width>
       <height>157</height>
      </rect>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_8">
        <item>
         <widget class="QPushButton" name="Craft_13_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_15">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>KTF 张力</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_11">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_9">
        <item>
         <widget class="QPushButton" name="Craft_14_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_16">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>加油件数</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_12">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_10">
        <item>
         <widget class="QPushButton" name="Craft_15_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_17">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>梭子状态</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_13">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_16">
        <item>
         <widget class="QPushButton" name="Craft_16_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_23">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>程序梭子
命令</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_14">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_17">
        <item>
         <widget class="QPushButton" name="Craft_17_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_24">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>程序命令</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_15">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_18">
        <item>
         <widget class="QPushButton" name="Craft_18_btn">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>100</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
    border-radius:15px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/gongyicanshu.png);
	background-color: rgb(255, 255, 255);
}

QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_25">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferDefault</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>导入导出</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QWidget" name="page_5">
    <widget class="QPushButton" name="pbtn_craft_save">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>499</y>
       <width>120</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #2ecc71;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
}

QPushButton:hover {
    background-color: #27ae60;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #16a085;
    color: white;
}</string>
     </property>
     <property name="text">
      <string>保存参数</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_craft_size">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>500</y>
       <width>141</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">MyLineEdit {
    background-color: white;
    color: #333333;
    border-radius: 5px;
    border: 1px solid #c0c0c0;
    padding: 5px;
    font-size: 9pt;
}

MyLineEdit:hover {
    border: 1px solid #3498db;
}

MyLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QStackedWidget" name="stackedWidget_small">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>10</y>
       <width>1024</width>
       <height>481</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabWidget::pane {
    border: 1px solid #d0d0d0;
    border-radius: 8px;
    background-color: #f5f5f5;
    padding: 5px;
}

QTabBar::tab {
    height: 50px;
    width: 140px;
    background-color: #e0e0e0;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 5px;
    margin-right: 2px;
    font-size: 9pt;
}

QTabBar::tab:hover {
    background-color: #f0f0f0;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
    border-bottom: none;
}

QWidget {
    background-color: #f5f5f5;
}</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="stackedWidget_2Page1">
      <widget class="QFrame" name="speedFrame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>781</width>
         <height>461</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_speed_inc">
       <property name="geometry">
        <rect>
         <x>840</x>
         <y>90</y>
         <width>120</width>
         <height>41</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 8pt;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #1a5276;
    color: white;
}</string>
       </property>
       <property name="text">
        <string>全部增加5</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pbtn_speed_dec">
       <property name="geometry">
        <rect>
         <x>840</x>
         <y>180</y>
         <width>120</width>
         <height>41</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #e74c3c;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 8pt;
}

QPushButton:hover {
    background-color: #c0392b;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #922b21;
    color: white;
}</string>
       </property>
       <property name="text">
        <string>全部减少5</string>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page2">
      <widget class="QFrame" name="graduationFrame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>1001</width>
         <height>461</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page3">
      <widget class="QFrame" name="elastic1Frame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>1001</width>
         <height>461</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page4">
      <widget class="QFrame" name="elastic2Frame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>1001</width>
         <height>461</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page5">
      <widget class="QFrame" name="sawFrame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>1001</width>
         <height>461</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page6">
      <widget class="QFrame" name="sinkerFrame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>1001</width>
         <height>461</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page7">
      <widget class="QFrame" name="sinkerAngularFrame">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>1001</width>
         <height>461</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QFrame {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
}

QLabel {
    background-color: #f0f0f0;
    color: #333333;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 4px;
    font-size: 8pt;
}

QLineEdit:hover {
    border: 1px solid #3498db;
}

QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #f0f8ff;
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page8"/>
     <widget class="QWidget" name="stackedWidget_2Page9">
      <widget class="QTableWidget" name="tableWidget_lycra">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>1021</width>
         <height>431</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <column>
        <property name="text">
         <string>部位</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>步数</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Old:起始值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>新建列</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Old:结束值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:起始值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:结束值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:类型</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page10">
      <widget class="QTableWidget" name="tableWidget_yoyo1">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>1021</width>
         <height>431</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <column>
        <property name="text">
         <string>部位</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>步数</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Old:起始值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>新建列</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Old:结束值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:起始值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:结束值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:类型</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
      </widget>
     </widget>
     <widget class="QWidget" name="stackedWidget_2Page11">
      <widget class="QTableWidget" name="tableWidget_yoyo5">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>1021</width>
         <height>431</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>8</pointsize>
        </font>
       </property>
       <column>
        <property name="text">
         <string>部位</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>步数</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Old:起始值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>新建列</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Old:结束值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:起始值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:结束值</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>New:类型</string>
        </property>
        <property name="font">
         <font>
          <pointsize>8</pointsize>
         </font>
        </property>
       </column>
      </widget>
     </widget>
    </widget>
    <widget class="ClickableLabel" name="lbl_CFTFile">
     <property name="geometry">
      <rect>
       <x>160</x>
       <y>500</y>
       <width>201</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>CFT文件</string>
     </property>
    </widget>
    <widget class="ClickableLabel" name="lbl_SOKFile">
     <property name="geometry">
      <rect>
       <x>390</x>
       <y>500</y>
       <width>211</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>SOK文件</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_prev">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>500</y>
       <width>120</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #9b59b6;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
    border: none;
}

QPushButton:hover {
    background-color: #8e44ad;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #6c3483;
    color: white;
}

QPushButton:disabled {
    background-color: #d1d1d1;
    color: #a0a0a0;
}</string>
     </property>
     <property name="text">
      <string>上一页</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_next">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>500</y>
       <width>120</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    background-color: #9b59b6;
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 10pt;
    border: none;
}

QPushButton:hover {
    background-color: #8e44ad;
}

QPushButton:pressed, QPushButton:checked {
    background-color: #6c3483;
    color: white;
}

QPushButton:disabled {
    background-color: #d1d1d1;
    color: #a0a0a0;
}</string>
     </property>
     <property name="text">
      <string>下一页</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_6">
    <widget class="QPushButton" name="pbtn_toUSB">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>420</y>
       <width>61</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>&gt;&gt;</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_toThis">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>110</y>
       <width>61</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string>&lt;&lt;</string>
     </property>
    </widget>
    <widget class="QTableView" name="File_tableView_usb">
     <property name="geometry">
      <rect>
       <x>550</x>
       <y>20</y>
       <width>461</width>
       <height>521</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
	selection-background-color: #007aff;
	selection-color: white;
}</string>
     </property>
    </widget>
    <widget class="QTableView" name="File_tableView_this">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>20</y>
       <width>461</width>
       <height>521</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableView {
	selection-background-color: #007aff;
	selection-color: white;
}</string>
     </property>
    </widget>
   </widget>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
  <customwidget>
   <class>ClickableLabel</class>
   <extends>QLabel</extends>
   <header>CommonWidget/clickablelabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
