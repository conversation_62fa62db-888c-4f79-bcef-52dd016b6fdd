#ifndef PARASETPROTOCOL_H
#define PARASETPROTOCOL_H

#include <QTypeInfo>

#pragma pack( 1 )

// 气阀参数数据结构ID定义
typedef union
{
    uint16_t san_jiao_par[ 80 ];
    struct
    {
        quint16 left_hlt_pos;          // 左活络头位置
        quint16 righ_hlt_pos;          // 右活络头位置
        quint16 q_left_pos;            // 左揿针位置
        quint16 q_righ_pos;            // 右揿针位置
        quint16 left_pick_pos;         // 左挑位置(140)
        quint16 right_pick_pos;        // 右挑位置(140)
        quint16 m_for_pos;             // 正向毛刀位置
        quint16 m_rev_pos;             // 反向毛刀位置
        quint16 triangle1_pos;         // 1口打松三角位置(0度)
        quint16 triangle2_pos;         // 2口打松三角位置(30度)
        quint16 half_in_half_pos;      // 哈夫半位进半位置(280)
        quint16 half_in_full_pos;      // 哈夫半位进全位置(130)
        quint16 half_out_half_pos;     // 哈夫半位出半位置(230)
        quint16 half_out_full_pos;     // 哈夫半位出全位置(90)
        quint16 half_in_flower_pos;    // 哈夫半位进花位置(140)
        quint16 t1_check_pos;          // T1检查位置(180)
        quint16 t2_circle_pos;         // T2圆位置(200)
        quint16 left_act_half_pos;     // 左活络头半位(70)
        quint16 left_act_full_pos;     // 左活络头全位(250)
        quint16 right_act_half_pos;    // 右活络头半位(70)
        quint16 right_act_full_pos;    // 右活络头全位(250)
        quint16 back_half_pos;         // 退圈刀半位置(230)
        quint16 back_full_pos;         // 退圈刀全位置(50)
        quint16 left_angle_half_pos;   // 左棱角半位置(270)
        quint16 left_angle_full_pos;   // 左棱角全位置(180)
        quint16 right_angle_half_pos;  // 右棱角半位置(100)
        quint16 right_angle_full_pos;  // 右棱角全位置(280)
        quint16 rubber_half_pos;       // 橡筋半位置(210)
        quint16 rubber_full_pos;       // 橡筋全位置(30)
        quint16 pick_blade_pos;        // 挑刀位置(260)
        quint16 press_blade_pos;       // 压刀位置(240)
        quint16 right_press_pos;       // 右压选针器刀位置(240)
        quint16 left_press_pos;        // 左压选针器刀位置(200)
        quint16 foot_blade_pos;        // 脚低加固起针刀位置(170)
        quint16 head_blade_pos;        // 缝头转移起针刀位置(200)
        quint16 boat_tri1_pos;         // 船三角1位置(180)
        quint16 boat_tri2_pos;         // 船三角2位置(180)
        quint16 boat_tri3_pos;         // 船三角3位置(180)
    } par;
} SANJIAO_PAR;

// 缝头参数结构体
typedef struct
{
    quint8  calibrationMode;      // 缝齿盘校准模式
    quint16 stopDeceleration;     // 缝头停车减速提前
    quint16 motorGearNum;         // 缝头电机齿轮数
    quint16 needleGearNum;        // 缝头针筒齿轮数
    quint16 stitchWidth;          // 缝头完毕每针宽度
    quint16 firstNeedlePosition;  // 缝头第1针0位置
    quint16 swingArmPos1;         // 摆臂上下接袜1位置
    quint16 swingArmPos2;         // 摆臂上下接袜2位置
    quint16 swingArmPos3;         // 摆臂上下接袜3位置
    quint16 swingArmPos4;         // 摆臂上下接袜4位置
    quint16 swingArmPos5;         // 摆臂上下移出5位置
    quint16 swingArmPos6;         // 摆臂左右等待6位置
    quint16 gripperPos7;          // 抓手上下等待7位置
    quint16 gripperPos8;          // 抓手上下翻袜8位置
    quint16 gripperPos9;          // 抓手上下复位9位置
    quint16 stopCompensation;     // 缝头停车补偿设置
    quint16 stopErrorTolerance;   // 缝头停车误差容限
    quint16 swingArmPos10;        // 摆臂左右套袜10位置
    quint16 needlePinTolerance;   // 针筒销子误差容限
    quint8  quickMode;            // 缝头动作快速模式
} SewingParams;

// 电机参数结构体
typedef struct
{
    quint16 runCurrent;
    quint16 fixedCurrent;
    quint16 runFrequency;
    quint16 resetFrequency;
    quint16 runRange;
    quint8  direction;
    quint8  zeroPosition;
    quint8  detectEnable;
    quint8  enable;
} MotorParamsItem;

// 伺服电机参数
typedef struct
{
    quint16 overCurProtThres;     // 过流保护阈值
    quint16 overVolProtThres;     // 过压保护阈值
    quint16 underVolProtThres;    // 欠压保护阈值
    quint16 maxSpeedOfSpeedLoop;  // 速度环最大转速
    quint16 accTime;              // 加速时间
    quint16 maxSpeedOfPosLoop;    // 位置环最大转速
    quint16 servo_kp;
    quint16 servo_ki;
} ServoMotorParamsItem;

// 机器参数结构体
typedef struct
{
    quint8 fun_flag;  // 参数更新标志=1，表示收到参数，更新目前的参数
    // 基本参数
    quint8  machine_type;  // 机器类型
    quint16 max_niddle;    // 最大针数
    quint16 motor_zero;    // 机器零位
    quint8  feng_auto;     // 自动吸风
    // 袜跟控制参数
    quint16 g_left_pos;    // 袜跟左活络头位置，这个参数决定了挑针、揿针正转变反转的回头位置 默认60
    quint16 g_righ_pos;    // 袜跟右活络头位置，这个参数决定了挑针、揿针反转变正转的回头位置  默认260
    quint8  wg_fast_en;    // 开启此选项打跟的正反转边幅系统会根据挑针和揿针自动调整：位置自动调整
    quint8  wg_dec_auto;   // 自动调减速点
    quint8  wg_dec_en;     //	袜跟边幅不够或者过大时可通过这个参数调节,防止过冲 :手动调减速点
    quint16 no1_wg_pos;    // 袜跟第一圈 1C 选针器抄完针的正转位置
    quint16 no2_righ_pos;  // 袜跟结束时反转变正转的回头角度 默认76,即最后一次不到260
    // 纱线控制
    quint8 eye_enable;     // 电眼使能
    quint8 ktf_start_num;  // 所有 KET 的初始张力(/10)
    quint8 ktf_step_num;   // 向发送一个信号 KTE 增加和减少的张力值(/10)

    // 选针器配置
    quint8 xuan_jxing;     // 压针极性 1：通电压针，0：通电不压针
    quint8 xuan_pai;       // 选针器排列  分为1：向+，:2：反向+:3：正向-:4：反向-。
    quint8 duan_num;       // 段数
    quint8 no1_hua_start;  // 提花针第1排起始针位
    quint8 no1_hua_end;    // 提花针第1排结束针位
    quint8 no1_hua_num;    // 提花针第1排组数
    quint8 no2_hua_start;  // 提花针第2排起始针位
    quint8 no2_hua_end;    // 提花针第2排结束针位
    quint8 no2_hua_num;    // 提花针第2排组数
    quint8 hua_zu_num;     // 提花针组数
    quint8 xuan_ahead;     // 选针器总的提前角度4.5
    // 拉毛选针器配置
    quint8 lmao_jxing;  // 压针极性 1：通电压针，0：通电不压针
    quint8 lmao_pai;    // 排列
    quint8 lmao_num;    // 刀数
    // 选针器位置参数
    quint16 xj_x_pos;   // 橡筋选针器位置18
    quint16 jq_x_pos;   // 集圈选针器位置79
    quint16 no1_x_pos;  // 1C选针器位置100
    quint16 no2_x_pos;  // 2C选针器位置130
    quint16 no3_x_pos;  // 3C选针器位置165
    quint16 no4_x_pos;  // 4C选针器位置195
    quint16 no5_x_pos;  // 5C选针器位置225
    quint16 no6_x_pos;  // 6C选针器位置258
    quint16 f1_x_pos;   // 主色选针器位置287
    // 拉毛选针器位置
    quint16 lmao_for_pos;  // 拉毛选针正位置212.9
    quint16 lmao_neg_pos;  // 拉毛选针反位置246.1
    // 选针器提前角度
    quint8 xj_x_ahead;   // 橡筋提前角度4.5
    quint8 jq_x_ahead;   // 集圈提前角度4.5
    quint8 no1_x_ahead;  // 1C提前角度4.5
    quint8 no2_x_ahead;  // 2C提前角度2.0
    quint8 no3_x_ahead;  // 3C提前角度4.5
    quint8 no4_x_ahead;  // 4C提前角度4.5
    quint8 no5_x_ahead;  // 5C提前角度4.5
    quint8 no6_x_ahead;  // 6C提前角度4.5
    quint8 f1_x_ahead;   // 主色提前角度4.5

    // 主梭参数
    quint16 m_sz_pos;         // 主梭起始位置 8号主梭位置20
    quint16 m_sz_ahead;       // 主梭提前量ui0
    quint16 m_sz_jian;        // 主梭间距3
    quint8  m_sz_half_delay;  // 主梭全位打进后延时多少角度切换为半位20
    quint8  m_sz_xj1;         // 主梭橡筋副梭组数1
    quint8  m_sz_xj_qh;       // 主梭橡筋(切换)副梭
    quint8  m_sz_jq1;         // 主梭集圈（氨纶）副梭组数1
    quint8  m_sz_jq2;         // 主梭集圈（氨纶）副梭组数2
    // 副梭参数
    quint16 nozzle_ahead;    // 副梭提前量 45
    quint8  tian_jiange;     // 两个添纱梭的重叠角度5
    quint16 xj_sz_pos;       // 橡筋副梭18
    quint16 jq_sz_pos;       // 集圈副梭79
    quint16 no1_sz_pos;      // 1C添纱副梭外设位置140
    quint16 no2_sz_pos;      // 2C添纱副梭外设位置170
    quint16 no3_sz_pos;      // 3C添纱副梭外设位置200
    quint16 no4_sz_pos;      // 4C添纱副梭外设位置230
    quint16 no5_sz_pos;      // 5C添纱副梭外设位置260
    quint16 no6_sz_pos;      // 6C添纱副梭外设位置278
    quint8  triangle_ahead;  // （打松）三角提前量
    // 出袜时间
    quint16 out_wg_time;     // 出袜风门电机保持时间

    // 三角相关参数 主要是位置参数
    SANJIAO_PAR sanjiao_par;

    // 缝头参数
    SewingParams sewing_par;

    // 电机参数
    MotorParamsItem motor_par[ 24 ];

    // 伺服电机参数
    ServoMotorParamsItem servo_motor_par;
} MachineParams;

struct SensorCfgItem
{
    quint16 id;
    quint8  enable;
    quint16 sensitivity;
    quint8  direction;
};

// 用户参数结构体
typedef struct
{
    quint8  fun_flag;       // 参数更新标志
    quint8  feng_auto;      // 自动吸风
    quint8  poweroff_en;    // 开启断电续织功能
    quint8  speed_gain;     // 实际速度的百分比
    quint8  wg_xl;          // LV1-5 袜跟加减速的快慢，LV5最快
    quint8  lcd_timer;      // 屏幕保护时间
    quint8  auto_oil_en;    // 开启自动加油模式
    quint8  oil_model;      // 加油模式(时间/产量/圈数)
    quint8  oil_timer;      // 加油间隔(时间/产量/圈数)
    quint8  oil_keep;       // 加油保持时间(秒)
    quint8  yi_w_timer;     // 移袜检测信号检测时间
    quint8  sz_tui_pos;     // 打跟准备时添纱梭延时退出角度
    quint8  start_x_timer;  // 开机选针器预热模式
    quint16 jog_speed;      // 点动转速
    quint16 slow_speed;     // 动作默认速度
    quint16 mid_speed;      // 控制条中速
    quint16 fast_speed;     // 控制条快速
    quint8  jpan_speed;     // 剪刀盘电机默认速率
    quint16 xuan_xs1;       // 选针器乱花附加参数1(速度系数)
    quint16 xuan_xs2;       // 选针器乱花附加参数2(备用)
    quint8  midu_model;     // 密度参数来源选择
    quint8  qiu_model;      // 揿针动作跟随模式
    quint8  xj_low_speed;   // 橡筋三角动作低速速度

    quint8  yi_w_dir;         // 移袜感应方向
    quint16 stop_m_timer;     // 冷机时间
    quint8  start_m_timer;    // 热机时间
    quint8  stop_start_auto;  // 冷机后强制低速启动
    quint8  fast_res_stop;    // 快速复位后停车
    quint8  lamp_auto;        // 自动照明控制
    quint16 xj_w_righ_pos;    // 橡筋-袜跟右2位置
    quint16 xj_jian_pos;      // 橡筋-剪线位置
    quint16 tc_fan_pos;       // 套刺翻转销退11位置
    quint8  xj_hua_tui;       // 橡筋跟随花型退出延时

    quint8  xj_speed_model;     // 橡筋速度跟随模式
    quint8  c_xuan_timer;       // 抄刀选针器通电时间(ms)
    quint8  wg_md_ahead;        // 袜跟换色M-D进提前
    quint8  wg_md_leg;          // 袜跟换色M-D退滞后
    quint8  feng_en;            // 缝头功能开关
    quint8  anl_dao_open_leg;   // 裸氨剪刀打开延时角度
    quint16 anl_dao_open_pos;   // 裸氨剪刀打开角度
    quint8  anl_dao_close_leg;  // 裸氨剪刀退出延时角度
    quint16 anl_dao_close_pos;  // 裸氨剪刀关闭角度

    quint8  start_zero_en;     // 启动前零位检测
    quint8  feng_close_timer;  // 停车后织袜风门延时(秒)
    quint8  feng_zhi_zero;     // 缝齿电机零位11位置
    quint16 f2_5msz_pos;       // 第二主梭5号梭外设位置
    quint8  f2_5_ahead;        // 第二主梭下梭提前量
    quint8  lamp_power;        // 照明灯亮度
    quint8  yiwa_timer;        // 移袜检测报警时间
    quint8  jt_yi_pos;         // 机头升降移袜位置

    // 增加传感器报警配置
    SensorCfgItem seamHeadWarnCfg[ 42 ];
    SensorCfgItem sockWarnCfg[ 50 ];
} UserParams;

#pragma pack()

#endif  // PARASETPROTOCOL_H
