﻿#include "mylineedit.h"

MyLineEdit::MyLineEdit( QWidget* parent ) : QLineEdit( parent ) {}

// void MyLineEdit::focusInEvent(QFocusEvent * e)
//{
//    emit GetFocus();
//}

// void MyLineEdit::focusOutEvent( QFocusEvent* e )
//{
//    emit LostFocus();
//}

void MyLineEdit::mousePressEvent( QMouseEvent* )
{
    emit mousePress();
}

void MyLineEdit::mouseReleaseEvent( QMouseEvent* )
{
    emit mouseRelease();
}

void MyLineEdit::mouseDoubleClickEvent( QMouseEvent* )
{
    emit mouseDoubleClick();
}
