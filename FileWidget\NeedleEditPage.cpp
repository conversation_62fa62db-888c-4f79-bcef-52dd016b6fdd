#include "CommonWidget/MyPlainTextEdit.h"
#include "CommonWidget/inputdialog.h"
#include "FATParser.h"
#include "FileWidget/NeedleView.h"
#include "FileWidget/PatternViewer.h"
#include "FileWidget/needleitemview.h"
#include "fileform.h"
#include "ui_fileform.h"
#include <QDebug>
#include <QDir>
#include <QInputDialog>
#include <QListView>
#include <QMessageBox>
#include <QStringListModel>
#include <QtWidgets>

// 选针资料库
void FileForm::initNeedleEditPage()
{
    if ( this->isNeedleListPageInited == false )
    {
        // 选针上一页和下一页按钮槽函数
        connect( this->ui->pbtn_NeedleListNext, &QPushButton::clicked, this, [ & ]() {
            this->currentNeedleListPage++;
            ShowNeedleList( 0 );
        } );
        connect( this->ui->pbtn_NeedleListPrev, &QPushButton::clicked, this, [ & ]() {
            if ( this->currentNeedleListPage > 0 )
            {
                this->currentNeedleListPage--;
                ShowNeedleList( 0 );
            }
            else
                QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到第1页" ) );
        } );
        // 返回动作与选针
        connect( this->ui->pbtn_toValveAction, &QPushButton::clicked, this, [ & ]() { this->ui->File_stackedWidget->setCurrentIndex( 3 ); } );
        // 块清除
        connect( this->ui->pbtn_NeedleClear, &QPushButton::clicked, this, [ & ]() {
            if ( this->currentSelectedNeedleView != nullptr )
            {
                int index = this->currentSelectedNeedleView->property( "index" ).toInt();
                memset( this->tmpNeedle_data[ index ], 0, 25 );
                this->currentSelectedNeedleView->setData( this->tmpNeedle_data[ index ] );
                //                this->currentSelectedNeedleView = nullptr;
                //                this->currentSelectedNeedleItem = nullptr;
                //                ShowNeedleList( index );
                ShowNeedleListItem( index );
            }
        } );
        // 块填充
        connect( this->ui->pbtn_NeedleFill, &QPushButton::clicked, this, [ & ]() {
            if ( this->currentSelectedNeedleView != nullptr )
            {
                int index = this->currentSelectedNeedleView->property( "index" ).toInt();
                memset( this->tmpNeedle_data[ index ], 0xff, 25 );
                this->currentSelectedNeedleView->setData( this->tmpNeedle_data[ index ] );
                //                this->currentSelectedNeedleView = nullptr;
                //                this->currentSelectedNeedleItem = nullptr;
                //                ShowNeedleList( index );
                ShowNeedleListItem( index );
            }
        } );
        // 块复制
        connect( this->ui->pbtn_NeedleCopy, &QPushButton::clicked, this, [ & ]() {
            if ( this->currentSelectedNeedleView != nullptr )
            {
                int index = this->currentSelectedNeedleView->property( "index" ).toInt();
                memcpy( this->tmpBloclNeedle_data, this->tmpNeedle_data[ index ], 25 );
            }
        } );
        // 块粘贴
        connect( this->ui->pbtn_NeedPaste, &QPushButton::clicked, this, [ & ]() {
            if ( this->currentSelectedNeedleView != nullptr )
            {
                int index = this->currentSelectedNeedleView->property( "index" ).toInt();
                memcpy( this->tmpNeedle_data[ index ], this->tmpBloclNeedle_data, 25 );
                this->currentSelectedNeedleView->setData( this->tmpNeedle_data[ index ] );
                ShowNeedleListItem( index );
            }
        } );
        // 复制
        connect( this->ui->pbtn_NeedleCopyItem, &QPushButton::clicked, this, [ & ]() {
            if ( this->currentSelectedNeedleView != nullptr )
            {
                int index = this->currentSelectedNeedleView->property( "index" ).toInt();
                memcpy( this->tmpBloclNeedle_data, this->tmpNeedle_data[ index ], 25 );
                if ( _needleItemCopyFrom == nullptr )
                {
                    this->_needleItemCopyFrom = new NeedItemCopyForm( this->tmpBloclNeedle_data );
                    connect( this->_needleItemCopyFrom, &NeedItemCopyForm::finished, this, [ & ]() {
                        int index = this->currentSelectedNeedleView->property( "index" ).toInt();
                        memcpy( this->tmpNeedle_data[ index ], this->tmpBloclNeedle_data, 25 );
                        this->currentSelectedNeedleView->setData( this->tmpNeedle_data[ index ] );
                        ShowNeedleListItem( index );
                    } );
                }
                else
                {
                    this->_needleItemCopyFrom->setData( this->tmpBloclNeedle_data );
                }
                this->_needleItemCopyFrom->show();
            }
        } );
        // 保存
        connect( this->ui->pbtn_NeedleSave, &QPushButton::clicked, this, [ & ]() {
            // 保存数据到选针库
            FATParser::NeedleValveDataBase* db = fatDataMain->NeedleActionDB.data();
            memcpy( db->Needle_data, &this->tmpNeedle_data, 25 * 32 );
            QMessageBox::information( nullptr, ( "操作提示" ), ( "保存成功" ) );
        } );
        this->isNeedleListPageInited = true;
    }

    // copy选针库数据到临时数据块，用于暂存和修改
    FATParser::NeedleValveDataBase* db = fatDataMain->NeedleActionDB.data();
    memcpy( &this->tmpNeedle_data, db->Needle_data, 25 * 32 );

    ShowNeedleList( 0 );
    ShowNeedleListItem( 0 );
}

// sel_index指的是让第1个选中
void FileForm::ShowNeedleList( quint8 sel_index )
{
    FATParser::NeedleValveDataBase* db = fatDataMain->NeedleActionDB.data();
    // 选针总数
    quint8 count = db->needle_cnt;
    // 先判断页码是否准确,这里每页显示了8行
    if ( this->currentNeedleListPage * 8 >= count )
    {
        QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到最后1页" ) );
        this->currentNeedleListPage--;
        return;
    }

    // Clear QGroupBox first
    QLayout* layout = this->ui->gbox_NeedleStorage->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_NeedleStorage );
    // 只显示10Line
    for ( quint8 index = 0; index < 8; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        if ( this->currentNeedleListPage * 8 + index < count )
        {
            QLabel* label = new QLabel( "No." + QString::number( this->currentNeedleListPage * 8 + index + 1, 10 ) );
            label->setAlignment( Qt::AlignCenter );
            rowLayoutx->addWidget( label );

            quint8 data[ 25 ];
            memcpy( data, ( quint8* )&this->tmpNeedle_data[ this->currentNeedleListPage * 8 + index ][ 0 ], 25 );

            NeedleView* myNeedleView = new NeedleView( data, nullptr );
            myNeedleView->setProperty( "index", this->currentNeedleListPage * 8 + index );
            connect( myNeedleView, &NeedleView::mouseReleased, this, [ & ]() {
                NeedleView* senderNeedView = qobject_cast< NeedleView* >( sender() );
                if ( this->currentSelectedNeedleView != nullptr )
                {
                    this->currentSelectedNeedleView->setStyleSheet( "" );
                }
                this->currentSelectedNeedleView = senderNeedView;
                senderNeedView->setStyleSheet( "border: 2px solid #000000;" );
                int needle_index = senderNeedView->property( "index" ).toInt();
                ShowNeedleListItem( needle_index );
            } );

            if ( sel_index == index )
            {
                myNeedleView->setStyleSheet( "border: 2px solid #000000;" );
                this->currentSelectedNeedleView = myNeedleView;
            }
            rowLayoutx->addWidget( myNeedleView );
            // 选中时有2px宽的边框，因此宽为604
            myNeedleView->setMinimumWidth( 604 );
            myNeedleView->setFixedHeight( 30 );
        }
        else
        {
            QLabel* label = new QLabel( "" );
            label->setAlignment( Qt::AlignCenter );
            rowLayoutx->addWidget( label );

            QLabel* qLabel = new QLabel( "" );
            rowLayoutx->addWidget( qLabel );
            // 选中时有2px宽的边框，因此宽为604
            qLabel->setMinimumWidth( 604 );
            qLabel->setMinimumHeight( 40 );
        }
        rowLayoutx->setStretch( 0, 1 );
        rowLayoutx->setStretch( 1, 4 );

        mainLayout->addLayout( rowLayoutx );
    }
}

void FileForm::ShowNeedleListItem( quint8 needle_index )
{
    // 清除子控件
    QLayout* layout = ui->scrollArea_NeedleItem->widget()->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    QHBoxLayout* rowLayout = new QHBoxLayout();
    rowLayout->setSpacing( 5 );
    quint8* data = this->tmpNeedle_data[ needle_index ];
    for ( int index = 0; index < 200; index++ )
    {
        quint8          value = *( data + index / 8 );
        bool            state = ( value >> ( index % 8 ) ) & 0x01;
        NeedleItemView* item  = new NeedleItemView( state, nullptr );
        // 19的pen宽度 + 2像素的边距*2
        item->setFixedWidth( 23 );
        item->setMinimumHeight( 44 );
        // 默认选中第1个
        if ( index == 0 )
        {
            item->setStyleSheet( "border: 2px solid #3F48CC;" );
            this->currentSelectedNeedleItem = item;
            QString labelText               = "No." + QString::number( needle_index + 1 ) + " - 200/1";
            this->ui->label_NeedleItemIndex->setText( labelText );
        }
        else
        {
            item->setStyleSheet( "border: 2px solid #000000;" );
        }
        item->setProperty( "index", index + 1 );
        connect( item, &NeedleItemView::mouseReleased, this, [ & ]() {
            NeedleItemView* senderNeedleItem = qobject_cast< NeedleItemView* >( sender() );
            QString         labelText        = "";
            if ( this->currentSelectedNeedleView != nullptr )
            {
                labelText = labelText + "No." + QString::number( this->currentSelectedNeedleView->property( "index" ).toInt() + 1 ) + " - ";
            }
            labelText = labelText + "200/" + QString::number( senderNeedleItem->property( "index" ).toInt() );
            if ( this->currentSelectedNeedleItem != nullptr )
            {
                this->currentSelectedNeedleItem->setStyleSheet( "border: 2px solid #000000;" );
                this->currentSelectedNeedleItem = senderNeedleItem;
            }
            senderNeedleItem->setStyleSheet( "border: 2px solid #3F48CC;" );
            this->ui->label_NeedleItemIndex->setText( labelText );
        } );
        rowLayout->addWidget( item );
    }
    ui->scrollArea_NeedleItem->widget()->setLayout( rowLayout );
}
