#ifndef UPPERMSGMAKER_H
#define UPPERMSGMAKER_H

#include "RunWidget/MachineInfo.h"
#include <QString>

class UpperMsgMaker
{
public:
    UpperMsgMaker();
    ~UpperMsgMaker();

    QString MakeProductInfoMsg( MachineInfo* machineInfo );
    QString MakeMachineInfoMsg( MachineInfo* machineInfo );
    QString MakeRunningStatusMsg( MachineInfo* machineInfo );
    QString MakeAlarmMsg( MachineInfo* machineInfo );
    QString MakePowerConsumptionMsg( MachineInfo* machineInfo );
    QString MakeFileDownloadingMsg( MachineInfo* machineInfo, QString filename, quint32 fileLen, QString fileContent );
    QString MakeFileListMsg( MachineInfo* machineInfo, QString content );
    QString MakeOprationResultMsg( MachineInfo* machineInfo, bool success, quint16 code, QString message );
    QString MakeConnectionMsg( MachineInfo* machineInfo, QString message );
};

#endif  // UPPERMSGMAKER_H
