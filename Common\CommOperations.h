#ifndef COMMOPERATIONS_H
#define COMMOPERATIONS_H

#include "Common/defines.h"
#include "Communicate/Communication.h"
#include "Communicate/circleDataProtocol.h"
#include "Config/readresetconfig.h"
#include <QObject>
#include <QTimer>

class CommOperations : public QObject
{
    Q_OBJECT
public:
    explicit CommOperations( QObject* parent = nullptr );
    ~CommOperations();

    // 初始化通信操作，传入MainWidgetData而不是ReadMachineFileConfig
    void init( Communication* comm, MainWidgetData* mainData );

    // 发送机器参数
    void sendMachineParam();

    // 发送用户参数
    void sendUserParam();

    // 开始发送参数（包括重试机制）
    void startSendParams();

    // 发送织袜复位动作
    // isFirstSend: 是否是第一次发送，如果是则重置圈计数
    void sendSockResetAction( bool isFirstSend = false );

    // 发送步骤数据
    void sendStepData( CircleData* circleData );

signals:
    // 参数发送成功信号
    void machineParamSendSuccessful();
    void userParamSendSuccessful();
    void sockResetSendSuccessful();
    void stepDataSendSuccessful();

    // 参数发送失败信号（达到最大重试次数）
    void machineParamSendFailed();
    void userParamSendFailed();
    void sockResetSendFailed();
    void stepDataSendFailed();

private slots:
    // 处理机器参数发送定时器超时
    void onMachineParamSendTimerTimeout();

    // 处理用户参数发送定时器超时
    void onUserParamSendTimerTimeout();

    // 处理织袜复位动作发送定时器超时
    void onSockResetSendTimerTimeout();

    // 处理步骤数据发送定时器超时
    void onStepDataSendTimerTimeout();

private:
    // 通信对象指针
    Communication* m_comm = nullptr;

    // 主数据对象指针
    MainWidgetData* m_mainData = nullptr;

    // 机器参数发送相关
    QTimer m_machineParamSendTimer;
    bool   m_machineParamSendSuccess = false;
    int    m_retryCountMachineParam  = 0;

    // 用户参数发送相关
    QTimer m_userParamSendTimer;
    bool   m_userParamSendSuccess = false;
    int    m_retryCountUserParam  = 0;

    // 织袜复位动作发送相关
    QTimer m_sockResetSendTimer;
    bool   m_sockResetSendSuccess = false;
    int    m_retryCountSockReset  = 0;

    // 步骤数据发送相关
    QTimer      m_stepDataSendTimer;
    bool        m_stepDataSendSuccess = false;
    int         m_retryCountStepData  = 0;
    CircleData* m_currentCircleData   = nullptr;  // 当前正在发送的步骤数据

    // 最大重试次数和重试间隔
    const int MAX_RETRIES    = 3;
    const int RETRY_INTERVAL = 100;  // 毫秒
};

#endif  // COMMOPERATIONS_H
