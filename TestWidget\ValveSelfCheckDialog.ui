<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ValveSelfCheckDialog</class>
 <widget class="QDialog" name="ValveSelfCheckDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>950</width>
    <height>550</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QGroupBox" name="gbox_selfCheck">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>10</y>
     <width>761</width>
     <height>531</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>7</pointsize>
    </font>
   </property>
   <property name="title">
    <string>气阀自动检测</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_14">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>40</y>
     <width>60</width>
     <height>33</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>电压</string>
   </property>
  </widget>
  <widget class="MyLineEdit" name="le_Voltage">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>70</y>
     <width>121</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="MyLineEdit" name="le_Threshold">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>140</y>
     <width>121</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_15">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>110</y>
     <width>60</width>
     <height>33</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>阈值</string>
   </property>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>320</y>
     <width>131</width>
     <height>221</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QPushButton" name="pbtn_run">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>开始检测</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pbtn_prev">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>上页</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pbtn_next">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>下页</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pbtn_quit">
      <property name="font">
       <font>
        <pointsize>8</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 8px;
}

QPushButton:hover {
    background-color: #ecf5ff;
    color: #409eff;
}

QPushButton:pressed, QPushButton:checked {
    border: 1px solid #3a8ee6;
    color: #409eff;
}
</string>
      </property>
      <property name="text">
       <string>退出</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
