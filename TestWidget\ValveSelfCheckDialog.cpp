#include "ValveSelfCheckDialog.h"
#include "ui_ValveSelfCheckDialog.h"
#include <QScreen>

ValveSelfCheckDialog::ValveSelfCheckDialog( QWidget* parent, quint8 valve_size, Communication* comm ) : QDialog( parent ), ui( new Ui::ValveSelfCheckDialog ), comm( comm ), valve_size( valve_size )
{
    ui->setupUi( this );
    this->move( QApplication::primaryScreen()->geometry().center() - this->rect().center() );

    // 填充数值
    ui->le_Voltage->setText( "0.0" );
    ui->le_Threshold->setText( "0.0" );

    connect( this->ui->le_Voltage, &MyLineEdit::mouseRelease, this, &ValveSelfCheckDialog::showFloatInputForm );
    connect( this->ui->le_Threshold, &MyLineEdit::mouseRelease, this, &ValveSelfCheckDialog::showFloatInputForm );
    connect( comm, &Communication::valveSelfCheckFrameReceived, this, &ValveSelfCheckDialog::onSelfCheckResultReceived );

    // 按钮事件
    connect( this->ui->pbtn_next, &QPushButton::clicked, this, [&]() {
        if ( this->currentPage < 1 )
        {
            this->currentPage++;
            showTestResult();
        }
        else
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到最后1页" ) );
    } );
    connect( this->ui->pbtn_prev, &QPushButton::clicked, this, [&]() {
        if ( this->currentPage > 0 )
        {
            this->currentPage--;
            showTestResult();
        }
        else
            QMessageBox::warning( nullptr, ( "错误提示" ), ( "已翻到第1页" ) );
    } );
    connect( this->ui->pbtn_quit, &QPushButton::clicked, this, [&]() {
        this->currentPage = 0;
        this->close();
    } );
    connect( this->ui->pbtn_run, &QPushButton::clicked, this, [&]() {
        //  发送检测消息
        sendSelfCheckCommandFrame();
    } );

    // 填充空值
    // Key start from 1
    if ( resultList.size() == 0 )
    {
        for ( int i = 0; i < valve_size; i++ )
        {
            ValveTestResultItemStruct it;
            it.valve_id = i + 1;
            it.result   = 0.0;
            resultList.insert( it.valve_id, it );
        }
    }
    showTestResult();
}

ValveSelfCheckDialog::~ValveSelfCheckDialog()
{
    disconnect( comm, &Communication::valveSelfCheckFrameReceived, this, &ValveSelfCheckDialog::onSelfCheckResultReceived );
    delete ui;
}

void ValveSelfCheckDialog::showTestResult()
{
    // 冗余，检查page是否合规  正常为6行24列的数据 ，每次显示8列，即页码应该在0~2
    if ( this->currentPage > 1 || this->currentPage < 0 )
    {
        QMessageBox::information( nullptr, ( "错误提示" ), ( "页码不正确" ) );
        return;
    }

    // 清空控件表
    QLayout* layout = this->ui->gbox_selfCheck->layout();
    if ( layout )
    {
        QLayoutItem* item;
        while ( ( item = layout->takeAt( 0 ) ) )
        {
            QLayoutItem* itemson;
            while ( ( itemson = item->layout()->takeAt( 0 ) ) )
            {
                if ( QWidget* widget = itemson->widget() )
                {
                    delete widget;
                }
                delete itemson;
            }
            delete item->widget();
            delete item;
        }
        delete layout->widget();
        delete layout;
    }

    // 插入控件
    QVBoxLayout* mainLayout = new QVBoxLayout( this->ui->gbox_selfCheck );
    // 只显示8行
    for ( quint8 index = 0; index < 12; index++ )
    {
        QHBoxLayout* rowLayoutx = new QHBoxLayout;
        QLabel*      label      = new QLabel( QString( "No.%1~%2" )
                                        .arg( QString::number( this->currentPage * 96 + index * 8 + 1, 10 ).rightJustified( 3, '0' ) )
                                        .arg( QString::number( this->currentPage * 96 + index * 8 + 8, 10 ).rightJustified( 3, '0' ) ) );
        label->setAlignment( Qt::AlignCenter );
        rowLayoutx->addWidget( label );

        for ( int col_index = 0; col_index < 8; col_index++ )
        {
            MyLineEdit* lineEdit = new MyLineEdit;
            lineEdit->setAlignment( Qt::AlignHCenter );
            // Key start from 1
            lineEdit->setText( QString::number( resultList.value( this->currentPage * 96 + index * 8 + col_index + 1 ).result ) );
            rowLayoutx->addWidget( lineEdit );
        }

        mainLayout->addLayout( rowLayoutx );
    }
}

void ValveSelfCheckDialog::showFloatInputForm()
{
    this->currentLineEdit = qobject_cast< MyLineEdit* >( sender() );
    // 清空之前的内存
    if ( this->floatInputFrm != nullptr )
    {
        delete this->floatInputFrm;
        this->floatInputFrm = nullptr;
    }
    this->floatInputFrm = new FloatInputForm();
    connect( this->floatInputFrm, &FloatInputForm::InputFinished, this, &ValveSelfCheckDialog::onFloatInputFormFinished );
    this->floatInputFrm->show();
}

void ValveSelfCheckDialog::onFloatInputFormFinished( float value )
{
    if ( this->currentLineEdit != nullptr )
    {
        this->currentLineEdit->setText( QString::number( value ) );
    }
}

void ValveSelfCheckDialog::onSelfCheckResultReceived( quint16 size, quint8* data )
{
    float result[ size / 4 ];
    memcpy( result, data, size );
    for ( int i = 0; i < size / 4; i++ )
    {
        qDebug() << "res" << i << result[ i ];
        resultList[ i + 1 ].result = result[ i ];
    }
    showTestResult();
}

void ValveSelfCheckDialog::sendSelfCheckCommandFrame()
{
    if ( comm != nullptr )
    {
        // TODO 构建消息帧
        bool  ok1, ok2;
        float value1 = ui->le_Voltage->text().toFloat( &ok1 );
        float value2 = ui->le_Threshold->text().toFloat( &ok2 );

        if ( ok1 && ok2 )
        {
            uint8_t data[ 8 ];
            memcpy( &data[ 0 ], &value1, sizeof( value1 ) );
            memcpy( &data[ 4 ], &value2, sizeof( value2 ) );
            comm->pushDataTobuffer( 0x0E, data, 8 );
            QMessageBox::information( nullptr, ( "操作提示" ), ( "已向下位发送自检测请求" ) );
        }
    }
}
