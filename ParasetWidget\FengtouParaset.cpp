#include "Config/readmachinefileconfig.h"
#include "parasetform.h"
#include "ui_parasetform.h"
#include <QDebug>
#include <QLayout>
#include <QWidget>

void ParaSetForm::initFengtouParasetTab()
{
    QHBoxLayout* mainLayout    = new QHBoxLayout( this->ui->FengtouPara_tab );
    int          totalElements = this->readMachineFileConfig->getFengtouCfgList()->count();
    // 计算QVBoxLayer和QHBoxLayer的个数, 每个面板放n个
    int numsPerLayer  = 8;
    int numVBoxLayers = ( totalElements + numsPerLayer - 1 ) / numsPerLayer;

    for ( int i = 0; i < numVBoxLayers; i++ )
    {
        QVBoxLayout* vboxLayout = new QVBoxLayout();
        mainLayout->addLayout( vboxLayout );

        for ( int j = 0; j < numsPerLayer; j++ )
        {
            QHBoxLayout* hboxLayer = new QHBoxLayout();
            vboxLayout->addLayout( hboxLayer );
            vboxLayout->setMargin( 0 );
            vboxLayout->setSpacing( 0 );

            if ( i * numsPerLayer + j < totalElements )
            {
                ReadMachineFileConfig::MachineFlieConfigItem item  = this->readMachineFileConfig->getFengtouCfgList()->value( i * numsPerLayer + j + 1 );
                QLabel*                                      label = new QLabel( QString( "%1.%2" ).arg( i * numsPerLayer + j + 1 ).arg( item.title ) );
                label->setStyleSheet( "background-color: #3498db; color: white; border-radius: 4px; padding: 4px;" );
                label->setMinimumWidth( 220 );
                label->setMinimumHeight(40);
                QFont labelFont = label->font();
                labelFont.setPointSize(8);
                labelFont.setBold(false);
                label->setFont(labelFont);

                MyLineEdit* lineEdit = new MyLineEdit();
                lineEdit->setStyleSheet( "background-color: #f0f0f0; border: 1px solid #c0c0c0; border-radius: 4px; padding: 4px;" );
                lineEdit->setAlignment( Qt::AlignHCenter );
                lineEdit->setMinimumHeight(40);
                QFont editFont = lineEdit->font();
                editFont.setPointSize(8);
                editFont.setBold(false);
                lineEdit->setFont(editFont);
                // 配置index选项，用于获取和修改数据
                lineEdit->setProperty( "index", i * numsPerLayer + j + 1 );
                if ( item.editable )
                {
                    connect( lineEdit, &MyLineEdit::mouseRelease, this, &ParaSetForm::onFengtouParasetEditClicked );
                }
                if ( item.unit == "enum" )
                {
                    lineEdit->setText( item.candidate[ item.value ] );
                }
                else
                {
                    lineEdit->setText( QString( "%1%2" ).arg( item.value ).arg( item.unit ) );
                }
                hboxLayer->addWidget( label );
                hboxLayer->addWidget( lineEdit );
            }
            else
            {
                QLabel* label = new QLabel( QString( "%1.暂无选项" ).arg( i * numsPerLayer + j + 1 ) );
                label->setStyleSheet( "background-color: #3498db; color: white; border-radius: 4px; padding: 4px;" );
                label->setMinimumWidth( 220 );
                label->setMinimumHeight(40);
                QFont labelFont = label->font();
                labelFont.setPointSize(8);
                labelFont.setBold(false);
                label->setFont(labelFont);

                MyLineEdit* lineEdit = new MyLineEdit();
                lineEdit->setStyleSheet( "background-color: #f0f0f0; border: 1px solid #c0c0c0; border-radius: 4px; padding: 4px;" );
                lineEdit->setAlignment( Qt::AlignHCenter );
                lineEdit->setMinimumHeight(40);
                QFont editFont = lineEdit->font();
                editFont.setPointSize(8);
                editFont.setBold(false);
                lineEdit->setFont(editFont);
                hboxLayer->addWidget( label );
                hboxLayer->addWidget( lineEdit );
            }
        }
    }
}

void ParaSetForm::onFengtouParasetEditClicked()
{
    MyLineEdit* senderLineEdit      = qobject_cast< MyLineEdit* >( sender() );
    this->currentSelectedMyLineEdit = senderLineEdit;
    QVariant variant                = senderLineEdit->property( "index" );
    if ( variant.isValid() )
    {
        int                                          index = variant.toInt();
        ReadMachineFileConfig::MachineFlieConfigItem item  = this->readMachineFileConfig->getFengtouCfgList()->value( index );
        // 枚举类型跳出Combo选择框
        if ( item.unit == "enum" )
        {
            // 生成Combo选择框所需的key-value对
            QMap< int, QString > list;
            for ( int i = 0; i < item.candidate.length(); i++ )
            {
                list.insert( i, item.candidate[ i ] );
            }
            if ( this->comboFrm == nullptr )
            {
                this->comboFrm = new ComboForm( nullptr, &list );
                this->comboFrm->setAttribute( Qt::WA_DeleteOnClose );
                connect( this->comboFrm, &QObject::destroyed, this, [this]() { this->comboFrm = nullptr; } );
            }
            connect( this->comboFrm, &ComboForm::itemSelected, this, [&]( int id ) {
                //                qDebug() << id;
                QVariant variant = this->currentSelectedMyLineEdit->property( "index" );
                if ( variant.isValid() )
                {
                    int                                           index = variant.toInt();
                    ReadMachineFileConfig::MachineFlieConfigItem& item  = ( *this->readMachineFileConfig->getFengtouCfgList() )[ index ];
                    item.value                                          = id;
                    this->currentSelectedMyLineEdit->setText( item.candidate[ id ] );
                }
                this->comboFrm->close();
            } );
            this->comboFrm->show();
        }
        else
        {
            if ( this->floatInputFrm == nullptr )
            {
                this->floatInputFrm = new FloatInputForm();
                this->floatInputFrm->setAttribute( Qt::WA_DeleteOnClose );
                connect( this->floatInputFrm, &FloatInputForm::InputFinished, this, &ParaSetForm::onFengtouFloatInputFormFinished );
                connect( this->floatInputFrm, &QObject::destroyed, this, [this]() { this->floatInputFrm = nullptr; } );
            }
            this->floatInputFrm->show();
        }
    }
}

void ParaSetForm::onFengtouFloatInputFormFinished( float value )
{
    if ( this->currentSelectedMyLineEdit != nullptr )
    {
        QVariant variant = this->currentSelectedMyLineEdit->property( "index" );
        if ( variant.isValid() )
        {
            int                                           index = variant.toInt();
            ReadMachineFileConfig::MachineFlieConfigItem& item  = ( *this->readMachineFileConfig->getFengtouCfgList() )[ index ];
            item.value                                          = value;
            this->currentSelectedMyLineEdit->setText( QString( "%1%2" ).arg( value ).arg( item.unit ) );
        }
    }
}
