<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SensorForm</class>
 <widget class="QWidget" name="SensorForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QPushButton" name="SensorFormHome_btn">
   <property name="geometry">
    <rect>
     <x>970</x>
     <y>3</y>
     <width>50</width>
     <height>45</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>15</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/home.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="Sensor_Tiltle_label">
   <property name="geometry">
    <rect>
     <x>419</x>
     <y>5</y>
     <width>191</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>传感器测试</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QTabWidget" name="tabWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>60</y>
     <width>1024</width>
     <height>540</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QTabWidget::pane {
    border: 1px solid #d0d0d0;
    border-radius: 8px;
    background-color: #f5f5f5;
    padding: 5px;
}

QTabBar::tab {
    height: 50px;
    width: 160px;
    background-color: #e0e0e0;
    color: #333333;
    border: 1px solid #c0c0c0;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 5px;
    margin-right: 2px;
    font-size: 10pt;
}

QTabBar::tab:hover {
    background-color: #f0f0f0;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
    border-bottom: none;
}

QWidget {
    background-color: #f5f5f5;
}</string>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="tab_sockeWarn">
    <attribute name="title">
     <string>袜机输入</string>
    </attribute>
   </widget>
   <widget class="QWidget" name="tab_fengtouWarn">
    <attribute name="title">
     <string>缝头输入</string>
    </attribute>
   </widget>
   <widget class="QWidget" name="tab_zeroSensor">
    <attribute name="title">
     <string>马达零位</string>
    </attribute>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
