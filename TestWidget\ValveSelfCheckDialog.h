#ifndef VALVESELFCHECKDIALOG_H
#define VALVESELFCHECKDIALOG_H

#include "CommonWidget/floatinputform.h"
#include "CommonWidget/mylineedit.h"
#include "Communicate/Communication.h"
#include "Config/readtestconfig.h"
#include <QDialog>
#include <QMessageBox>

namespace Ui
{
class ValveSelfCheckDialog;
}

class ValveSelfCheckDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ValveSelfCheckDialog( QWidget* parent = nullptr, quint8 valve_size = 192, Communication* comm = nullptr );
    ~ValveSelfCheckDialog();

#pragma pack( 1 )
    struct ValveTestResultItemStruct
    {
        quint8 valve_id;
        double result;
    };
#pragma pack()

    void setReslult( QMap< int, ValveTestResultItemStruct >* resultList );

private:
    Ui::ValveSelfCheckDialog*              ui;
    QMap< int, ValveTestResultItemStruct > resultList;
    Communication*                         comm            = nullptr;
    FloatInputForm*                        floatInputFrm   = nullptr;
    MyLineEdit*                            currentLineEdit = nullptr;
    quint8                                 valve_size      = 192;
    int                                    currentPage     = 0;

    void showTestResult();
    void sendSelfCheckCommandFrame();

private slots:
    void showFloatInputForm();
    void onFloatInputFormFinished( float value );
    void onSelfCheckResultReceived( quint16 size, quint8* data );
};

#endif  // VALVESELFCHECKDIALOG_H
