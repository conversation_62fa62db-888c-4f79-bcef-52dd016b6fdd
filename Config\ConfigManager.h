#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>

class ConfigManager : public QObject
{
    Q_OBJECT
public:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 获取上次打开的程序文件路径
    QString getLastProgramFile() const;
    // 设置上次打开的程序文件路径
    void setLastProgramFile(const QString &filePath);

    // 获取上次打开的工艺文件路径
    QString getLastCraftFile() const;
    // 设置上次打开的工艺文件路径
    void setLastCraftFile(const QString &filePath);

    // 获取机器名称
    QString getMachineName() const;
    // 设置机器名称
    void setMachineName(const QString &name);

    // 获取机器型号
    QString getMachineModel() const;
    // 设置机器型号
    void setMachineModel(const QString &model);

    // 获取软件版本
    QString getEdition() const;
    // 设置软件版本
    void setEdition(const QString &edition);

private:
    QSettings *settings;
    const QString LAST_PROGRAM_FILE = "Files/LastProgramFile";
    const QString LAST_CRAFT_FILE = "Files/LastCraftFile";
    const QString MACHINE_NAME = "Main/MachineName";
    const QString MACHINE_MODEL = "Main/Model";
    const QString EDITION = "Main/Edition";
};

#endif // CONFIGMANAGER_H