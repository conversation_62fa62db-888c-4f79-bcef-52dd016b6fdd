#include "WorkForm.h"
#include "ui_WorkForm.h"

void WorkForm::initInfoPage()
{
    if ( !isInfoPageInited )
    {
        connect( ui->WorkFormHome_pbtn_back_2, &QPushButton::clicked, this, [&]() { ui->stackedWidget->setCurrentIndex( 0 ); } );
        connect( ui->pbtn_info_reset, &QPushButton::clicked, this, [&]() {
            sokBusiness->resetCurrentRunningStepIdx();
            sokBusiness->getAndCheckNextCircle();
            // 设置"运行"标志
            sokBusiness->setControlFlag(5, true);
            sokBusiness->sendNextStepData();
            refreshFingerAndValve();
            refreshInfoPage( 0, 0 );
        } );
        connect( ui->pbtn_info_next, &QPushButton::clicked, this, [&]() {
            sokBusiness->getAndCheckNextCircle();
            // 设置"运行"标志
            sokBusiness->setControlFlag(5, true);
            sokBusiness->sendNextStepData();
            refreshFingerAndValve();
            refreshInfoPage( 0, 0 );
            int speed = sokBusiness->getSpeedSet();
            if ( speed > 0 )
                ui->le_set_speed_2->setText( QString::number( speed ) );
        } );
        connect( ui->pbtn_info_prev, &QPushButton::clicked, this, [&]() {
            sokBusiness->decrementCurrentRunningStepIdx();
            sokBusiness->getAndCheckNextCircle();
            // 设置"运行"标志
            sokBusiness->setControlFlag(5, true);
            sokBusiness->sendNextStepData();

            refreshFingerAndValve();
            refreshInfoPage( 0, 0 );
            int speed = sokBusiness->getSpeedSet();
            if ( speed > 0 )
                ui->le_set_speed_2->setText( QString::number( speed ) );
        } );

        isInfoPageInited = true;
    }
}

void WorkForm::refreshFingerAndValve()
{
    auto currentCircleData = sokBusiness->getCurrentStepData();
    // 主梭
    QString fingerStr;
    for ( const auto& finger : currentCircleData->main_finger )
    {
        if ( finger.id != 0 )
        {
            if ( finger.work_on_angle > 0 )
                fingerStr += QString( "%1(%2°,%3) " ).arg( finger.id, 2, 10, QChar( '0' ) ).arg( finger.work_on_angle ).arg( "进" );
            else if ( finger.work_off_angle > 0 )
                fingerStr += QString( "%1(%2°,%3) " ).arg( finger.id, 2, 10, QChar( '0' ) ).arg( finger.work_off_angle ).arg( "出" );
        }
    }
    // 将activeMainFingers中的编号和位置格式化为字符串
    ui->label_main_finger->setText( fingerStr );

    // 气阀
    QString valveStr;
    for ( int i = 0; i < currentCircleData->valve_count; i++ )
    {
        const auto& valve = currentCircleData->valve_actions[ i ];
        if ( valve.valve_id != 0 )
        {
            valveStr += QString( "%1(%2°,%3) " ).arg( valve.valve_id, 3, 10, QChar( '0' ) ).arg( valve.angle ).arg( valve.valve_state == 1 ? "进" : "出" );
        }
    }
    ui->label_valve->setText( valveStr );

    // 特殊功能，暂时从dataMain->chainData中获取
    ui->label_function->setText( sokBusiness->getSpecialFunctionStr() );
}

void WorkForm::refreshInfoPage( quint16 needle, float abAngel )
{
    auto currentCircleData = sokBusiness->getCurrentStepData();

    if ( ui->stackedWidget->currentIndex() != 2 )
        return;
    if ( currentCircleData == nullptr || needle > 199 )
        return;

    ui->le_curStepIdx_2->setText( QString::number( sokBusiness->getCurrentRunningStepIdx() ) );
    auto needleData = currentCircleData->needle_data[ needle ];
    ui->le_selector->setText( QString( "%1" ).arg( needleData.selector, 9, 2, QChar( '0' ) ) );
    ui->le_nozzle->setText( QString( "%1" ).arg( needleData.nozzle, 3, 2, QChar( '0' ) ) );
}
