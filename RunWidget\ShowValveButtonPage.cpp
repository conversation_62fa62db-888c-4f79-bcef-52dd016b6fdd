void RunForm::ShowValveButtonPage()
{
    // 获取配置数据
    QMap<int, QString>* specialFunList = mainData->readCfg->getSpecialFunList();
    QMap<int, QString>* camList = mainData->readCfg->getCamList();
    QMap<int, QString>* yarnFingerList = mainData->readCfg->getYarnFingerList();

    // 每行显示的按钮数量
    const int buttonsPerRow = 10;

    // 生成特殊功能tab
    if (!specialFunList->isEmpty()) {
        QHBoxLayout* specialFunLayout = new QHBoxLayout(this->ui->tabValve_SpecialFun);
        int size = specialFunList->size();
        int lines = (size - 1) / buttonsPerRow + 1;

        auto iter = specialFunList->begin();
        for (int line = 0; line < (lines > 5 ? lines : 5); line++) {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for (int col = 0; col < buttonsPerRow; col++) {
                if (line * buttonsPerRow + col < size) {
                    QPushButton* btn = new QPushButton(iter.value());
                    btn->setProperty("id", iter.key());
                    iter++;
                    btn->setStyleSheet("font-size: 8pt;");
                    btn->setMinimumHeight(40);
                    connect(btn, &QPushButton::clicked, this, &RunForm::onValveButtonClicked);
                    columnLayout->addWidget(btn);
                } else {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget(widget);
                }
            }
            specialFunLayout->addLayout(columnLayout);
        }
    }

    // 生成凸轮tab
    if (!camList->isEmpty()) {
        QHBoxLayout* camLayout = new QHBoxLayout(this->ui->tabValve_Cam);
        int size = camList->size();
        int lines = (size - 1) / buttonsPerRow + 1;

        auto iter = camList->begin();
        for (int line = 0; line < (lines > 5 ? lines : 5); line++) {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for (int col = 0; col < buttonsPerRow; col++) {
                if (line * buttonsPerRow + col < size) {
                    QPushButton* btn = new QPushButton(iter.value());
                    btn->setProperty("id", iter.key());
                    iter++;
                    btn->setStyleSheet("font-size: 8pt;");
                    btn->setMinimumHeight(40);
                    connect(btn, &QPushButton::clicked, this, &RunForm::onValveButtonClicked);
                    columnLayout->addWidget(btn);
                } else {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget(widget);
                }
            }
            camLayout->addLayout(columnLayout);
        }
    }

    // 生成纱嘴tab
    if (!yarnFingerList->isEmpty()) {
        QHBoxLayout* yarnFingerLayout = new QHBoxLayout(this->ui->tabValve_YarnFinger);
        int size = yarnFingerList->size();
        int lines = (size - 1) / buttonsPerRow + 1;

        auto iter = yarnFingerList->begin();
        for (int line = 0; line < (lines > 5 ? lines : 5); line++) {
            QVBoxLayout* columnLayout = new QVBoxLayout;

            for (int col = 0; col < buttonsPerRow; col++) {
                if (line * buttonsPerRow + col < size) {
                    QPushButton* btn = new QPushButton(iter.value());
                    btn->setProperty("id", iter.key());
                    iter++;
                    btn->setStyleSheet("font-size: 8pt;");
                    btn->setMinimumHeight(40);
                    connect(btn, &QPushButton::clicked, this, &RunForm::onValveButtonClicked);
                    columnLayout->addWidget(btn);
                } else {
                    QWidget* widget = new QWidget();
                    columnLayout->addWidget(widget);
                }
            }
            yarnFingerLayout->addLayout(columnLayout);
        }
    }
}

// 处理阀门按钮点击事件的槽函数
void RunForm::onValveButtonClicked()
{
    QPushButton* btn = qobject_cast<QPushButton*>(sender());
    if (btn) {
        int id = btn->property("id").toInt();
        // TODO: 处理按钮点击事件
        // 可以根据id执行相应的阀门操作
    }
} 